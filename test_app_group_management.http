### 应用组管理接口测试

### 新增应用组接口测试

POST http://localhost:8080/srv6/addAppGroup
Content-Type: application/json;charset=UTF-8
X-Param-Length: 66
X-Access-Token: your_token_here

{
  "appGroupId": "APP_GROUP_001",
  "appGroupName": "核心业务应用组"
}

###

POST http://localhost:8080/srv6/addAppGroup
Content-Type: application/json;charset=UTF-8
X-Param-Length: 72
X-Access-Token: your_token_here

{
  "appGroupId": "APP_GROUP_002", 
  "appGroupName": "移动端应用组"
}

### 查询应用组列表接口测试

GET http://localhost:8080/srv6/queryAppGroupList
Content-Type: application/json;charset=UTF-8
X-Param-Length: 0
X-Access-Token: your_token_here

### 删除应用组接口测试

DELETE http://localhost:8080/srv6/delAppGroup/APP_GROUP_001
Content-Type: application/json;charset=UTF-8
X-Param-Length: 0
X-Access-Token: your_token_here

###

DELETE http://localhost:8080/srv6/delAppGroup/APP_GROUP_002
Content-Type: application/json;charset=UTF-8
X-Param-Length: 0
X-Access-Token: your_token_here

### 修改应用组接口测试

PUT http://localhost:8080/srv6/modifyAppGroup
Content-Type: application/json;charset=UTF-8
X-Param-Length: 74
X-Access-Token: your_token_here

{
  "appGroupId": "APP_GROUP_001",
  "appGroupName": "核心业务应用组（已修改）"
}

###

PUT http://localhost:8080/srv6/modifyAppGroup
Content-Type: application/json;charset=UTF-8
X-Param-Length: 72
X-Access-Token: your_token_here

{
  "appGroupId": "APP_GROUP_002",
  "appGroupName": "移动端应用组（已修改）"
}

###

# 再次查询应用组列表（验证修改结果）
GET http://localhost:8080/srv6/queryAppGroupList
Content-Type: application/json;charset=UTF-8
X-Param-Length: 0
X-Access-Token: your_token_here

### 错误测试用例

# 删除不存在的应用组
DELETE http://localhost:8080/srv6/delAppGroup/NON_EXISTENT_GROUP
Content-Type: application/json;charset=UTF-8
X-Param-Length: 0
X-Access-Token: your_token_here

###

# 修改不存在的应用组
PUT http://localhost:8080/srv6/modifyAppGroup
Content-Type: application/json;charset=UTF-8
X-Param-Length: 60
X-Access-Token: your_token_here

{
  "appGroupId": "NON_EXISTENT_GROUP",
  "appGroupName": "不存在的应用组"
}

###

# 新增重复的应用组ID
POST http://localhost:8080/srv6/addAppGroup
Content-Type: application/json;charset=UTF-8
X-Param-Length: 66
X-Access-Token: your_token_here

{
  "appGroupId": "APP_GROUP_001",
  "appGroupName": "重复的应用组ID"
} 