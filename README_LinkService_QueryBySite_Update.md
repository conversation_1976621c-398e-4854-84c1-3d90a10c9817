# 查询站点下链路信息接口逻辑修改说明

## 修改概述

根据用户需求，对 `LinkServiceImpl.queryLinkInfoListBySiteId` 方法进行了重大逻辑调整，从基于数据库链路信息查询改为基于站点下设备的平台节点ID直接查询平台链路信息。

## 修改背景

### 原有问题
- **查询不完整**：基于数据库中的链路定制信息去平台查询，会导致部分平台上存在但数据库中没有记录的链路查询不到
- **数据依赖性强**：依赖 `link_custom_info` 表的数据完整性
- **维护成本高**：需要保证数据库链路信息与平台链路信息的同步

### 解决方案
- **直接查询平台**：基于站点下设备的平台节点ID，直接调用平台链路查询接口
- **提高完整性**：能够查询到平台上所有相关的链路信息
- **减少依赖**：不再依赖数据库中的链路记录

## 修改详情

### 1. 主要方法修改

#### `queryLinkInfoListBySiteId` 方法
**修改前逻辑：**
```java
// 1. 验证站点存在
// 2. 查询站点及子站点ID
// 3. 查询站点关联的链路定制信息 (link_custom_info表)
// 4. 逐条处理每个链路，调用平台接口获取详情
```

**修改后逻辑：**
```java
// 1. 验证站点存在
// 2. 查询站点及子站点ID
// 3. 获取站点下所有设备的平台节点ID
// 4. 调用平台接口查询链路信息（设置srcNodeIdList和destNodeIdList）
// 5. 处理每个链路，构建完整的链路信息
```

### 2. 新增方法

#### `getAllPlatformNodeIdsBySiteIds`
- **功能**：根据站点ID列表获取所有设备的平台节点ID
- **逻辑**：查询 `device_custom_info` 表，获取站点下所有设备的 `platform_node_id`
- **去重**：确保平台节点ID不重复

#### `queryPlatformLinksByNodeIds`
- **功能**：根据设备平台节点ID列表查询平台链路信息
- **参数**：同时设置 `srcNodeIdList` 和 `destNodeIdList` 为所有设备的平台节点ID
- **分页**：使用合理的分页逻辑（页面大小50）处理大量链路数据
- **优化**：添加分页查询延迟（100ms），避免对平台造成过大压力

#### `buildLinkInfoFromPlatformData`
- **功能**：从平台数据构建链路信息DTO
- **数据源**：平台返回的链路数据 + 数据库定制信息
- **智能合并**：根据平台linkId查询数据库中的linkType、linkLevel等定制字段
- **兼容性**：保持原有的数据结构和字段映射

#### `getLinkCustomInfoByPlatformLinkId`
- **功能**：根据平台链路ID查询链路定制信息
- **用途**：获取linkType、linkLevel等定制字段
- **逻辑**：查询不到返回空串，确保数据完整性

#### `setPlatformLinkPerformanceData`
- **功能**：设置平台链路性能数据
- **数据源**：通过srcTpId和destTpId调用链路总览接口获取实时性能数据
- **性能指标**：delay、jitter、packetLossRate、bandwidth等
- **带宽利用率**：自动计算发送和接收带宽利用率

#### `setDeviceInfoFromPlatformNodeId`
- **功能**：根据平台节点ID设置设备信息
- **双重查询**：
  - 查询 `device_custom_info` 表获取设备ID和站点信息
  - 调用平台接口获取设备名称和IP

### 3. 数据流程对比

#### 修改前数据流程
```
站点ID → 站点及子站点ID → 链路定制信息(数据库) → 平台链路详情(逐条查询)
```

#### 修改后数据流程  
```
站点ID → 站点及子站点ID → 设备平台节点ID → 平台链路信息(批量查询) → 链路详情
```

## 技术实现要点

### 1. 平台接口调用
```java
PlatformGetLinkRequestDTO request = new PlatformGetLinkRequestDTO();
request.setSrcNodeIdList(platformNodeIds);  // 源设备节点ID列表
request.setDestNodeIdList(platformNodeIds); // 目标设备节点ID列表
```

### 2. 分页处理
- 使用合理的页面大小（50条/页），避免单次查询数据量过大
- 支持多页查询，确保获取所有相关链路
- 添加分页查询延迟（100ms），避免对平台造成过大压力
- 完善的分页终止条件判断（总页数、空页面检测）

### 3. 数据过滤
- 只处理属于查询站点范围内的设备链路
- 通过设备的 `deviceSiteId` 进行站点范围验证

### 4. 错误处理
- 完善的异常处理和日志记录
- 单个链路处理失败不影响其他链路
- 提供详细的调试信息

## 兼容性保证

### 1. 接口兼容
- 保持原有的HTTP接口不变
- 请求参数和响应格式完全兼容

### 2. 数据结构兼容
- 返回的 `LinkInfoDTO` 结构保持不变
- 字段映射逻辑保持一致

### 3. 业务逻辑兼容
- 站点及子站点查询逻辑不变
- 设备信息获取逻辑保持一致

## 性能优化

### 1. 批量查询
- 一次性获取所有相关链路，减少接口调用次数
- 避免了原有的逐条查询模式

### 2. 数据去重
- 平台节点ID去重，避免重复查询
- 链路信息自动去重（平台接口特性）

### 3. 分页支持
- 使用合理的分页大小（50条/页），避免单次查询数据量过大
- 智能分页终止条件，提高查询效率
- 分页查询延迟机制，保护平台接口稳定性

## 测试验证

### 1. 功能测试
- 正常站点查询
- 空站点查询
- 不存在站点查询
- 参数验证测试

### 2. 数据完整性测试
- 对比修改前后的查询结果
- 验证新增链路是否能被正确查询到

### 3. 性能测试
- 大量链路数据的查询性能
- 接口响应时间对比

## 部署注意事项

### 1. 数据依赖
- 确保 `device_custom_info` 表中的 `platform_node_id` 字段数据完整
- 验证设备与站点的关联关系正确

### 2. 平台接口
- 确认平台链路查询接口的可用性
- 验证 `srcNodeIdList` 和 `destNodeIdList` 参数的支持情况

### 3. 日志监控
- 关注新增的日志信息
- 监控平台接口调用的成功率和响应时间

## 总结

此次修改显著提升了查询站点下链路信息的完整性和准确性，通过直接查询平台接口避免了数据库同步问题，同时保持了良好的向后兼容性和性能表现。
