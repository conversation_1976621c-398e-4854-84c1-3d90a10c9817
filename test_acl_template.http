### ACL模板管理接口测试
###
### 测试说明：
### 1. 新增ACL模板：只要返回状态码200就认为成功，平台不返回数据
### 2. 500错误时会返回具体错误信息：{"errorInfo":{"errorType":"application","errorCode":"2503","errorPath":"/qos/acl","errorMessage":"ACL名称不合法。"}}
### 3. 新增成功后会自动调用查询接口获取ACL ID
### 4. 查询支持分页，系统会自动分页查询直到找到匹配的ACL名称
###

### 1. 新增IPv4 ACL模板
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 2001,
  "appName": "测试ACL应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "**************/32",
  "sourcePort": "8080",
  "destinationIPList": [
    "*************/32&443",
    "*************/32&8080"
  ],
  "appGroupName": "ACL测试应用组"
}

### 2. 新增IPv4+IPv6混合ACL模板
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 2002,
  "appName": "混合ACL应用",
  "appType": "五元组",
  "internetAppDSCP": "AF31",
  "agreementType": "TCP",
  "sourceIP": "**************/32",
  "sourcePort": "3306",
  "destinationIPList": [
    "*************/32&3306"
  ],
  "sourceIPv6": "2001:db8::100/64",
  "sourcePortIPv6": "3306",
  "destinationIPv6List": [
    "2001:db8::200/64&3306"
  ],
  "appGroupName": "ACL测试应用组"
}

### 3. 测试ACL名称不合法的情况（应该返回500错误）
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 2003,
  "appName": "非法ACL名称!@#$%",
  "appType": "五元组",
  "agreementType": "TCP",
  "sourceIP": "**************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "*************/32&80"
  ],
  "appGroupName": "ACL测试应用组"
}

### 4. 删除之前创建的ACL应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/2001
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 5. 删除混合ACL应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/2002
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### ============ 预期结果说明 ============
###
### 新增成功时的响应：
### {
###   "requestId": "req_xxx",
###   "result": 1,
###   "failReason": "操作成功"
### }
###
### 新增失败时的响应（如ACL名称不合法）：
### {
###   "requestId": "req_xxx",
###   "result": 0,
###   "failReason": "创建ACL模板失败"
### }
###
### 删除成功时的响应：
### {
###   "requestId": "req_xxx",
###   "result": 1,
###   "failReason": "删除成功"
### }
###
### 删除失败时的响应（如部分ACL删除失败）：
### {
###   "requestId": "req_xxx",
###   "result": 0,
###   "failReason": "部分ACL模板删除失败，失败ACL ID：[12345]，失败原因：IPv4 ACL模板(ID:12345)删除失败"
### }
### 