import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 简单的验证脚本，测试PlatformApiException类的基本功能
 */
public class test_platform_exception {
    public static void main(String[] args) {
        try {
            // 检查PlatformApiException类文件是否存在
            String exceptionPath = "src/main/java/com/h3c/dzkf/common/exceptions/PlatformApiException.java";
            if (Files.exists(Paths.get(exceptionPath))) {
                System.out.println("✅ PlatformApiException.java 文件存在");
                
                // 读取文件内容并检查关键方法
                String content = new String(Files.readAllBytes(Paths.get(exceptionPath)));
                
                if (content.contains("public class PlatformApiException extends RuntimeException")) {
                    System.out.println("✅ PlatformApiException 正确继承 RuntimeException");
                }
                
                if (content.contains("private final String errorCode")) {
                    System.out.println("✅ 包含 errorCode 字段");
                }
                
                if (content.contains("private final String rawResponse")) {
                    System.out.println("✅ 包含 rawResponse 字段");
                }
                
                if (content.contains("private final Integer httpStatus")) {
                    System.out.println("✅ 包含 httpStatus 字段");
                }
                
                if (content.contains("public String getErrorCode()")) {
                    System.out.println("✅ 包含 getErrorCode() 方法");
                }
                
                if (content.contains("public String getRawResponse()")) {
                    System.out.println("✅ 包含 getRawResponse() 方法");
                }
                
                if (content.contains("public Integer getHttpStatus()")) {
                    System.out.println("✅ 包含 getHttpStatus() 方法");
                }
                
                System.out.println("✅ PlatformApiException 类验证通过");
            } else {
                System.out.println("❌ PlatformApiException.java 文件不存在");
            }
            
            // 检查全局异常处理器是否已更新
            String handlerPath = "src/main/java/com/h3c/dzkf/common/exceptions/GlobalExceptionHandler.java";
            if (Files.exists(Paths.get(handlerPath))) {
                String handlerContent = new String(Files.readAllBytes(Paths.get(handlerPath)));
                
                if (handlerContent.contains("@ExceptionHandler(PlatformApiException.class)")) {
                    System.out.println("✅ GlobalExceptionHandler 已添加 PlatformApiException 处理");
                } else {
                    System.out.println("❌ GlobalExceptionHandler 未添加 PlatformApiException 处理");
                }
            }
            
            // 检查PlatformApiServiceImpl是否已更新
            String servicePath = "src/main/java/com/h3c/dzkf/service/impl/PlatformApiServiceImpl.java";
            if (Files.exists(Paths.get(servicePath))) {
                String serviceContent = new String(Files.readAllBytes(Paths.get(servicePath)));
                
                if (serviceContent.contains("import com.h3c.dzkf.common.exceptions.PlatformApiException")) {
                    System.out.println("✅ PlatformApiServiceImpl 已导入 PlatformApiException");
                } else {
                    System.out.println("❌ PlatformApiServiceImpl 未导入 PlatformApiException");
                }
                
                if (serviceContent.contains("throw new PlatformApiException")) {
                    System.out.println("✅ PlatformApiServiceImpl 已使用 PlatformApiException");
                } else {
                    System.out.println("❌ PlatformApiServiceImpl 未使用 PlatformApiException");
                }
            }
            
            System.out.println("\n🎉 平台接口异常处理机制验证完成！");
            
        } catch (Exception e) {
            System.err.println("验证过程中出现异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
