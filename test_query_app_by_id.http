### 查询单个应用接口测试

### 1. 查询单个应用 - 成功场景
GET http://localhost:8080/srv6/queryAppInfoListByAppId?appId=1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 2. 查询单个应用 - 应用ID不存在场景
GET http://localhost:8080/srv6/queryAppInfoListByAppId?appId=99999
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 3. 查询单个应用 - 缺少应用ID参数（预期返回400）
GET http://localhost:8080/srv6/queryAppInfoListByAppId
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 4. 查询单个应用 - 应用ID为空（预期返回400）
GET http://localhost:8080/srv6/queryAppInfoListByAppId?appId=
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 5. 查询单个应用 - 无Token场景（预期返回401）
GET http://localhost:8080/srv6/queryAppInfoListByAppId?appId=1001
Content-Type: application/json;charset=UTF-8

### 6. 查询单个应用 - 空Token场景（预期返回401）  
GET http://localhost:8080/srv6/queryAppInfoListByAppId?appId=1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: 

### 7. 查询单个应用 - 不同应用ID测试
GET http://localhost:8080/srv6/queryAppInfoListByAppId?appId=1002
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 8. 查询单个应用 - 生产环境测试
GET http://prod-server:8080/srv6/queryAppInfoListByAppId?appId={{app_id}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{production_token}} 