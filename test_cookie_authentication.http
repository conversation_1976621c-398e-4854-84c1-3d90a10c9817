### 测试Cookie认证方式的ADWAN平台接口调用

# 1. 测试新增设备（使用Cookie认证）
POST http://localhost:8080/srv6/addDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJpR2pleUoxYzJWeVRtRnRaU0k2SW1Ga2JXbHVJaXdpYVdRaU9pSXhJaXdpY21WbmFXOXVTV1FpT2lJd0lpd2lhWEJCWkdSeVpYTnpJam9pTVRBdU1UQXVNVEk0TGpFeU9TSjl4R3puIiwianRpIjoiNTA2ODQ0IiwiaWF0IjoxNzUwMjM1MDQwfQ.gjMvh2vnYvGJHL7kzqt0GXgdXnOjweTG_qD6KS7NQIW1z8ALRiW1ultzWuXO2PyNppRYja5QB4yDFLjyeGsSag

{
  "deviceId": 1001,
  "deviceName": "测试设备Cookie认证",
  "deviceModel": "H3C-Test",
  "deviceType": "交换机",
  "managementIp": "*************",
  "deviceSiteId": 1,
  "deviceStatus": "在线",
  "serialNumber": "SN123456789",
  "macAddress": "00:0C:29:F9:C8:6D"
}

###

# 2. 测试新增链路（使用Cookie认证）
POST http://localhost:8080/srv6/addLink
Content-Type: application/json;charset=UTF-8
X-Access-Token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJpR2pleUoxYzJWeVRtRnRaU0k2SW1Ga2JXbHVJaXdpYVdRaU9pSXhJaXdpY21WbmFXOXVTV1FpT2lJd0lpd2lhWEJCWkdSeVpYTnpJam9pTVRBdU1UQXVNVEk0TGpFeU9TSjl4R3puIiwianRpIjoiNTA2ODQ0IiwiaWF0IjoxNzUwMjM1MDQwfQ.gjMvh2vnYvGJHL7kzqt0GXgdXnOjweTG_qD6KS7NQIW1z8ALRiW1ultzWuXO2PyNppRYja5QB4yDFLjyeGsSag

{
  "linkName": "测试链路Cookie认证",
  "sourceDeviceId": 1001,
  "sourceDeviceName": "源设备",
  "sourceDevicePort": "GigabitEthernet1/1/0",
  "targetDeviceId": 1002,
  "targetDeviceName": "目标设备",
  "targetDevicePort": "GigabitEthernet1/1/0",
  "linkType": "专线",
  "linkLevel": "重要",
  "linkStatus": "在线"
}

###

# 3. 测试查询设备（使用Cookie认证）
GET http://localhost:8080/srv6/queryDeviceInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8
X-Access-Token: eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJpR2pleUoxYzJWeVRtRnRaU0k2SW1Ga2JXbHVJaXdpYVdRaU9pSXhJaXdpY21WbmFXOXVTV1FpT2lJd0lpd2lhWEJCWkdSeVpYTnpJam9pTVRBdU1UQXVNVEk0TGpFeU9TSjl4R3puIiwianRpIjoiNTA2ODQ0IiwiaWF0IjoxNzUwMjM1MDQwfQ.gjMvh2vnYvGJHL7kzqt0GXgdXnOjweTG_qD6KS7NQIW1z8ALRiW1ultzWuXO2PyNppRYja5QB4yDFLjyeGsSag

###

# 4. 直接测试ADWAN平台接口（验证Cookie格式）
POST http://************:30000/adwan/proxy/nfm/physicalNetwork/link/getLink
Content-Type: application/json
Cookie: X-Subject-Token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJpR2pleUoxYzJWeVRtRnRaU0k2SW1Ga2JXbHVJaXdpYVdRaU9pSXhJaXdpY21WbmFXOXVTV1FpT2lJd0lpd2lhWEJCWkdSeVpYTnpJam9pTVRBdU1UQXVNVEk0TGpFeU9TSjl4R3puIiwianRpIjoiNTA2ODQ0IiwiaWF0IjoxNzUwMjM1MDQwfQ.gjMvh2vnYvGJHL7kzqt0GXgdXnOjweTG_qD6KS7NQIW1z8ALRiW1ultzWuXO2PyNppRYja5QB4yDFLjyeGsSag

{
  "pageNum": 0,
  "pageSize": 10
}

###

# 5. 测试错误的Token（应该返回401或403）
POST http://localhost:8080/srv6/addDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: invalid-token-test

{
  "deviceId": 9999,
  "deviceName": "测试无效Token",
  "deviceModel": "H3C-Test",
  "deviceType": "交换机",
  "managementIp": "*************",
  "deviceSiteId": 1,
  "deviceStatus": "在线",
  "serialNumber": "SN999999999",
  "macAddress": "00:0C:29:F9:C8:99"
}

###

# 6. 测试空Token（应该返回认证失败）
POST http://localhost:8080/srv6/addDevice
Content-Type: application/json;charset=UTF-8

{
  "deviceId": 8888,
  "deviceName": "测试空Token",
  "deviceModel": "H3C-Test",
  "deviceType": "交换机",
  "managementIp": "*************",
  "deviceSiteId": 1,
  "deviceStatus": "在线",
  "serialNumber": "SN888888888",
  "macAddress": "00:0C:29:F9:C8:88"
} 