### LAN口管理接口测试

### 1. 新增LAN口接口 - 成功案例
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 1,
#   "failReason": null,
#   "optionField": null,
#   "lanId": 1
# }
POST http://localhost:8080/srv6/addLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "deviceId": 123456,
  "interfaceName": "eth0/0/1"
}

### 2. 新增LAN口接口 - 设备不存在
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "设备不存在",
#   "optionField": null,
#   "lanId": null
# }
POST http://localhost:8080/srv6/addLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "deviceId": 999999,
  "interfaceName": "eth0/0/2"
}

### 3. 新增LAN口接口 - 重复接口名称
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "设备下已存在相同接口名称",
#   "optionField": null,
#   "lanId": null
# }
POST http://localhost:8080/srv6/addLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "deviceId": 123456,
  "interfaceName": "eth0/0/1"
}

### 4. 新增LAN口接口 - 参数验证失败（设备ID为空）
POST http://localhost:8080/srv6/addLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "interfaceName": "eth0/0/3"
}

### 5. 新增LAN口接口 - 参数验证失败（接口名称为空）
POST http://localhost:8080/srv6/addLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "deviceId": 123456
}

### 6. 新增LAN口接口 - 缺少Token
POST http://localhost:8080/srv6/addLanInterface
Content-Type: application/json;charset=UTF-8

{
  "deviceId": 123456,
  "interfaceName": "eth0/0/4"
}

### ================================= 修改LAN口接口测试 =================================

### 7. 修改LAN口接口 - 成功案例
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 1,
#   "failReason": null,
#   "optionField": null
# }
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "lanId": 1,
  "deviceId": 123456,
  "interfaceName": "eth0/0/2"
}

### 8. 修改LAN口接口 - LAN口不存在
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "LAN口不存在",
#   "optionField": null
# }
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "lanId": 999999,
  "deviceId": 123456,
  "interfaceName": "eth0/0/3"
}

### 9. 修改LAN口接口 - 设备不存在
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "设备不存在",
#   "optionField": null
# }
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "lanId": 1,
  "deviceId": 999999,
  "interfaceName": "eth0/0/4"
}

### 10. 修改LAN口接口 - 接口名称冲突
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "设备下已存在相同接口名称",
#   "optionField": null
# }
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "lanId": 2,
  "deviceId": 123456,
  "interfaceName": "eth0/0/1"
}

### 11. 修改LAN口接口 - 参数验证失败（LAN口ID为空）
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "deviceId": 123456,
  "interfaceName": "eth0/0/5"
}

### 12. 修改LAN口接口 - 参数验证失败（设备ID为空）
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "lanId": 1,
  "interfaceName": "eth0/0/6"
}

### 13. 修改LAN口接口 - 参数验证失败（接口名称为空）
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
  "lanId": 1,
  "deviceId": 123456
}

### 14. 修改LAN口接口 - 缺少Token
PUT http://localhost:8080/srv6/updateLanInterface
Content-Type: application/json;charset=UTF-8

{
  "lanId": 1,
  "deviceId": 123456,
  "interfaceName": "eth0/0/7"
}

### ================================= 查询LAN口接口测试 =================================

### 15. 查询LAN口接口 - 成功案例（有数据）
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 1,
#   "failReason": null,
#   "optionField": null,
#   "lanList": [
#     {
#       "lanId": 1,
#       "deviceId": 123456,
#       "interfaceName": "eth0/0/1"
#     },
#     {
#       "lanId": 2,
#       "deviceId": 123456,
#       "interfaceName": "eth0/0/2"
#     }
#   ]
# }
GET http://localhost:8080/srv6/queryLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

### 16. 查询LAN口接口 - 成功案例（无数据）
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 1,
#   "failReason": null,
#   "optionField": null,
#   "lanList": []
# }
GET http://localhost:8080/srv6/queryLanInterface
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

### 17. 查询LAN口接口 - 缺少Token
GET http://localhost:8080/srv6/queryLanInterface
Content-Type: application/json;charset=UTF-8

### ================================= 删除LAN口接口测试 =================================

### 18. 删除LAN口接口 - 成功案例
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 1,
#   "failReason": null,
#   "optionField": null
# }
DELETE http://localhost:8080/srv6/deleteLanInterface/1
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

### 19. 删除LAN口接口 - LAN口不存在
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "LAN口不存在",
#   "optionField": null
# }
DELETE http://localhost:8080/srv6/deleteLanInterface/999999
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

### 20. 删除LAN口接口 - 重复删除（已被软删除的LAN口）
# 预期响应格式：
# {
#   "requestId": "3c064eae70dc4503b60cbfc4494e9a6a",
#   "result": 0,
#   "failReason": "LAN口不存在",
#   "optionField": null
# }
DELETE http://localhost:8080/srv6/deleteLanInterface/1
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

### 21. 删除LAN口接口 - 缺少Token
DELETE http://localhost:8080/srv6/deleteLanInterface/2
Content-Type: application/json;charset=UTF-8

### 22. 删除LAN口接口 - 无效的LAN口ID
DELETE http://localhost:8080/srv6/deleteLanInterface/abc
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here 