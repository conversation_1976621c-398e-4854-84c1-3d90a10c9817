# 修改应用接口实现说明

## 概述

根据工商总行的需求，实现了修改应用接口，该接口通过对比新旧配置数据，智能管理SDWAN中QoS ACL模板的增删操作，满足总行API标准需要。

## 接口信息

- **接口协议**: PUT
- **接口地址**: `/srv6/modifyApp`
- **描述**: 修改应用，通过对比新旧数据重新梳理ACL模板，删除不需要的并新增新的ACL模板

## 主要功能

### 1. 智能配置对比
- **配置变化检测**: 对比新传入参数与定制库保存的数据
- **字段级对比**: 检查源IP、源端口、协议类型、目标IP列表等关键字段
- **IP版本分离**: 分别处理IPv4和IPv6配置的变化
- **ACL生命周期管理**: 根据对比结果决定ACL模板的删除和新增

### 2. IP版本级别的智能ACL管理逻辑
```
IPv4/IPv6配置对比 → 按版本决策 → 删除变化版本ACL → 创建新ACL → 保留不变版本ACL → 更新应用信息
```

#### 详细流程：
1. **IPv4配置对比**: 检查源IP、源端口、协议类型、目标IP列表是否变化
2. **IPv6配置对比**: 检查源IPv6、源IPv6端口、协议类型、目标IPv6列表是否变化
3. **按版本决策**: 
   - **IPv4变化** → 删除所有IPv4 ACL，重新创建IPv4 ACL
   - **IPv4未变化** → 保留所有IPv4 ACL不动
   - **IPv6变化** → 删除所有IPv6 ACL，重新创建IPv6 ACL  
   - **IPv6未变化** → 保留所有IPv6 ACL不动
4. **版本独立处理**: IPv4和IPv6的ACL管理完全独立，互不影响
5. **同步定制库**: 更新应用信息和ACL关联关系

### 3. IP版本级别的精细管理
每个IP版本对应一个ACL模板，包含该版本的所有目标规则，实现版本级别的精确更新：

#### 配置变化检测规则：
**IPv4 ACL重建触发条件：**
- 源IP地址变化 (`sourceIP`)
- 源端口变化 (`sourcePort`)  
- 协议类型变化 (`agreementType`)
- 目标IP列表变化 (`destinationIPList`)

**IPv6 ACL重建触发条件：**
- 源IPv6地址变化 (`sourceIPv6`)
- 源IPv6端口变化 (`sourcePortIPv6`)
- 协议类型变化 (`agreementType`)  
- 目标IPv6列表变化 (`destinationIPv6List`)

#### ACL模板命名规则：
- **IPv4 ACL**: `{应用名}_ipv4`
- **IPv6 ACL**: `{应用名}_ipv6`
- **示例**: `WebApp_ipv4`, `WebApp_ipv6`

#### 管理优势：
- ✅ **版本隔离**: IPv4和IPv6 ACL完全独立管理，互不影响
- ✅ **精确判断**: 基于源、协议、目标的完整配置对比
- ✅ **最小变更**: 只重建实际变化的IP版本ACL
- ✅ **简单高效**: 一个IP版本一个ACL模板，管理简洁

## 技术实现

### 1. 核心组件

#### 数据传输层
- `ModifyAppRequestDTO.java` - 修改应用请求参数DTO

#### 业务逻辑层
- `AppService.modifyApp()` - 修改应用服务接口方法
- `AppServiceImpl.modifyApp()` - 修改应用业务逻辑实现
- `AclCompareResult` - ACL对比结果内部类

#### 控制层
- `AppController.modifyApp()` - PUT接口处理器

### 2. 关键业务方法

#### 配置对比方法
```java
private AclCompareResult compareAclTemplates(AppCustomInfo existingApp, ModifyAppRequestDTO request, String requestId)
```
- 对比新旧配置数据
- 识别需要删除和新增的ACL模板
- 返回对比结果对象

#### ACL删除方法
```java
private List<Integer> deleteObsoleteAcls(List<Integer> aclIdsToDelete, String requestId)
```
- 批量删除过时的ACL模板
- 调用平台删除接口
- 返回成功删除的ACL ID列表

#### ACL创建方法
```java
private List<Integer> createAclTemplatesForModify(ModifyAppRequestDTO request, String requestId, Long appCustomId)
```
- 为修改应用创建新的ACL模板
- 支持IPv4和IPv6分别创建
- 保存ACL关联关系

### 3. 平台接口调用

#### 删除ACL模板
- **接口**: `DELETE /qostrs/qos/acl/all?aclId={aclId}`
- **说明**: 删除指定ID的ACL模板

#### 新增ACL模板
- **接口**: `POST /qostrs/qos/acl`
- **说明**: 创建新的ACL模板
- **ACL命名规则**: `{appName}_ipv4` 或 `{appName}_ipv6`

## 接口参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|----------|----------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 认证token | Y |

### Body参数
| 接口参数名 | 接口参数描述 | 是否必填 | 数据类型 |
|------------|--------------|----------|----------|
| appId | 应用ID | Y | Integer |
| appName | 应用名称 | Y | String |
| appType | 应用类型，仅支持'五元组' | Y | String |
| internetAppDSCP | 网络应用DSCP | N | String |
| agreementType | 协议类型（如TCP/UDP） | Y | String |
| sourceIP | 源IP地址及掩码（格式：*******/32） | Y | String |
| sourcePort | 源端口(如80） | Y | String |
| destinationIPList | 目的IP及端口列表（格式：ip&port） | Y | List<String> |
| sourceIPv6 | IPv6源IP地址及掩码 | N | String |
| sourcePortIPv6 | IPv6源端口（非必须） | N | String |
| destinationIPv6List | IPv6目的IP及端口列表（非必须） | N | List<String> |
| appGroupName | 所属应用分组 | Y | String |

## 响应体

### 成功响应（状态200）
```json
{
  "requestId": "req_20240630_001",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

### 失败响应
```json
{
  "requestId": "req_20240630_001",
  "result": 0,
  "failReason": "应用不存在",
  "optionField": null
}
```

## 业务逻辑重点

### 1. 智能对比算法
```java
// 检查关键配置是否发生变化
boolean configChanged = !Objects.equals(existingApp.getSourceIP(), request.getSourceIP()) ||
                       !Objects.equals(existingApp.getSourcePort(), request.getSourcePort()) ||
                       !Objects.equals(existingApp.getAgreementType(), request.getAgreementType()) ||
                       !Objects.equals(oldIPv4Destinations, request.getDestinationIPList()) ||
                       !Objects.equals(oldIPv6Destinations, request.getDestinationIPv6List());
```

### 2. 事务处理
- 使用`@Transactional`注解保证数据一致性
- ACL删除失败时回滚操作
- 部分失败时提供详细错误信息

### 3. 错误处理
- **应用不存在**: 返回明确错误信息
- **应用分组不存在**: 验证应用分组有效性
- **ACL删除失败**: 提供失败的ACL ID和原因
- **ACL创建失败**: 回滚已删除的ACL操作

## 测试场景

### 1. 正常修改场景
- 仅修改应用名称和DSCP（不触发ACL重建）
- 修改源IP和端口（触发ACL重建）
- 添加IPv6配置
- 更换应用组
- 更换协议类型

### 2. 错误场景测试
- 修改不存在的应用
- 使用不存在的应用分组
- 使用无效的协议类型
- 格式错误的IP端口参数

## 性能考虑

### 1. 配置对比优化
- 只对关键字段进行深度比较
- 使用`Objects.equals()`进行安全比较
- 避免不必要的ACL操作

### 2. 批量操作
- 按IP版本分组处理ACL模板
- 批量删除和创建操作
- 减少平台接口调用次数

### 3. 日志记录
- 详细记录配置对比结果
- 跟踪ACL删除和创建过程
- 便于问题诊断和性能分析

## 文件清单

### 新增文件
- `src/main/java/com/h3c/dzkf/entity/dto/ModifyAppRequestDTO.java`
- `test_modify_app.http`
- `README_Modify_App_Interface.md`

### 修改文件
- `src/main/java/com/h3c/dzkf/service/AppService.java`
- `src/main/java/com/h3c/dzkf/service/impl/AppServiceImpl.java`
- `src/main/java/com/h3c/dzkf/controller/AppController.java`

## 注意事项

1. **数据一致性**: 修改应用时确保ACL模板和定制库数据的一致性
2. **平台依赖**: 依赖ADWAN平台的ACL管理接口，需要确保平台接口的可用性
3. **配置对比**: 对比逻辑需要准确识别配置变化，避免不必要的ACL重建
4. **错误恢复**: 部分操作失败时提供清晰的错误信息和恢复建议
5. **测试验证**: 建议在测试环境充分验证各种场景后再部署到生产环境

## 部署说明

1. 确保应用分组数据已准备就绪
2. 验证ADWAN平台连接配置
3. 运行相关的HTTP测试用例
4. 监控日志输出确认功能正常
5. 验证定制库数据更新正确性 