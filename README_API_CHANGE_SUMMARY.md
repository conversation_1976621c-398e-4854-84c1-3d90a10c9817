# API接口变更总结

## 概述
本次修改将`queryDeviceInfoDetail`方法中的设备数据获取逻辑从旧的UC2SessionFactory方式改为直接调用HTTP API接口，并移除了失败时的回退机制。

## 主要变更

### 1. 新增接口调用方式
- **资源查询接口**: `POST /ucpresource/v2/resources`
  - 根据IP地址精准匹配，获取设备ID
- **设备详情接口**: `GET /resrs/device/{deviceId}`
  - 根据设备ID获取设备详情（MAC地址、软件版本等）
- **性能数据接口**: `GET /perfrs/data/queryTaskPerfData?devId={deviceId}&taskId={taskId}`
  - 根据设备ID和任务ID获取性能数据

### 2. 移除回退机制
- **旧行为**: 新接口失败时会自动回退到UC2SessionFactory方式
- **新行为**: 新接口失败时直接抛出异常，返回错误信息给调用方
- **影响**: 提高了错误的可见性，避免了静默失败

### 3. 错误处理策略
- 资源查询失败：抛出`RuntimeException`，提示无法获取设备ID
- 设备详情查询失败：抛出`RuntimeException`，提示未查询到设备详情
- 性能数据查询失败：收集所有失败的任务，抛出包含详细信息的`RuntimeException`

### 4. 配置项变更
新增以下配置项（可配置基础URL）：
```yaml
device:
  api:
    base-url: http://************:30000
    resource-query-url: /ucpresource/v2/resources
    device-detail-url: /resrs/device
    performance-data-url: /perfrs/data/queryTaskPerfData
```

## 接口行为变化

### 成功场景
行为与之前一致，返回完整的设备详情信息。

### 失败场景
- **之前**: 新接口失败时静默回退到旧接口，可能掩盖问题
- **现在**: 新接口失败时立即返回错误响应，包含具体失败原因

### 错误响应示例
```json
{
  "requestId": "xxx",
  "result": 0,
  "failReason": "查询设备详细信息失败：无法通过IP获取设备ID，设备IP：************"
}
```

## 注意事项
1. 告警查询逻辑保持不变，仍使用原有方式
2. 硬件版本查询和端口信息查询保持不变
3. 确保新的HTTP接口服务可用，否则会导致功能不可用
4. 建议在部署前先验证新接口的可访问性

## 测试建议
1. 验证正常IP地址的设备详情查询
2. 验证不存在IP地址的错误处理
3. 验证性能数据查询的各种场景
4. 验证错误信息的完整性和准确性 