# 外部配置目录

## 目录说明
此目录包含应用运行时需要的外部配置文件，与项目代码分离，便于运维管理和配置更新。

## 配置文件说明

### device-models.json
设备型号配置文件，包含所有支持的H3C设备型号列表。

**文件格式：** JSON数组
**使用接口：** GET /srv6/queryDeviceModel

## 配置更新
1. 可以直接编辑此目录下的配置文件
2. 修改后无需重启应用，下次API调用时会自动读取最新配置
3. 建议在修改前备份原文件

## 项目配置
- 开发环境：application-dev.yml
- 生产环境：application-prod.yml

两个环境都配置为使用此外部目录下的配置文件。

## 注意事项
1. 确保JSON格式正确，可使用在线JSON验证工具检查
2. 修改配置文件时注意文件权限
3. 重要变更建议先在开发环境测试 