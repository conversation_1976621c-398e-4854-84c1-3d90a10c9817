### 测试IPv4和IPv6参数二选一验证逻辑

### 1. 测试只提供IPv4参数（应该成功）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1001,
  "appName": "测试应用IPv4",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "destinationIPList": ["***********/32&443", "***********/32&8080"],
  "appGroupName": "业务应用组"
}

### 2. 测试只提供IPv6参数（应该成功）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1002,
  "appName": "测试应用IPv6",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIPv6": "2001:db8::1/64",
  "sourcePortIPv6": "80",
  "destinationIPv6List": ["2001:db8::2/64&443", "2001:db8::3/64&8080"],
  "appGroupName": "业务应用组"
}

### 3. 测试同时提供IPv4和IPv6参数（应该成功）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1003,
  "appName": "测试应用混合",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "destinationIPList": ["***********/32&443"],
  "sourceIPv6": "2001:db8::1/64",
  "sourcePortIPv6": "80",
  "destinationIPv6List": ["2001:db8::2/64&443"],
  "appGroupName": "业务应用组"
}

### 4. 测试IPv4参数不完整 - 缺少sourceIP（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1004,
  "appName": "测试应用IPv4不完整1",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourcePort": "80",
  "destinationIPList": ["***********/32&443"],
  "appGroupName": "业务应用组"
}

### 5. 测试IPv4参数不完整 - 缺少sourcePort（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1005,
  "appName": "测试应用IPv4不完整2",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "destinationIPList": ["***********/32&443"],
  "appGroupName": "业务应用组"
}

### 6. 测试IPv4参数不完整 - 缺少destinationIPList（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1006,
  "appName": "测试应用IPv4不完整3",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "appGroupName": "业务应用组"
}

### 7. 测试IPv6参数不完整 - 缺少sourceIPv6（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1007,
  "appName": "测试应用IPv6不完整1",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourcePortIPv6": "80",
  "destinationIPv6List": ["2001:db8::2/64&443"],
  "appGroupName": "业务应用组"
}

### 8. 测试IPv6参数不完整 - 缺少destinationIPv6List（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1008,
  "appName": "测试应用IPv6不完整2",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIPv6": "2001:db8::1/64",
  "sourcePortIPv6": "80",
  "appGroupName": "业务应用组"
}

### 9. 测试完全没有IP参数（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1009,
  "appName": "测试应用无IP参数",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "appGroupName": "业务应用组"
}

### 10. 测试IPv4目的IP格式错误（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1010,
  "appName": "测试应用IPv4格式错误",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "destinationIPList": ["***********/32:443"],
  "appGroupName": "业务应用组"
}

### 11. 测试IPv6目的IP格式错误（应该失败）
POST http://localhost:8080/api/app/addApp
Content-Type: application/json
X-Access-Token: test-token

{
  "appId": 1011,
  "appName": "测试应用IPv6格式错误",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIPv6": "2001:db8::1/64",
  "sourcePortIPv6": "80",
  "destinationIPv6List": ["2001:db8::2/64:443"],
  "appGroupName": "业务应用组"
}
