### 修改应用接口测试
###
### 说明：修改应用时，系统会根据字段变化类型决定处理方式
### - 当appType、internetAppDSCP变化时，仅更新定制库
### - 当其他字段变化时，需要调用平台修改ACL接口并更新定制库和ACL关系
###

### 1. 修改应用（仅更新非ACL相关字段）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "AF31",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 2. 修改应用（更新ACL相关字段 - 应用名称）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "修改后的Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 3. 修改应用（更新ACL相关字段 - 协议类型）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "UDP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 4. 修改应用（更新ACL相关字段 - 源IP地址）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 5. 修改应用（更新ACL相关字段 - 目的IP列表）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080",
    "************/32&9090"
  ],
  "appGroupName": "业务应用组"
}

### 6. 修改应用（包含IPv6）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1002,
  "appName": "修改的数据库应用",
  "appType": "五元组",
  "internetAppDSCP": "AF31",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "3306",
  "destinationIPList": [
    "************/32&3306"
  ],
  "sourceIPv6": "2001:db8::2/64",
  "sourcePortIPv6": "3306",
  "destinationIPv6List": [
    "2001:db8::20/64&3306",
    "2001:db8::21/64&3306"
  ],
  "appGroupName": "数据库应用组"
}

### 7. 测试错误场景 - 应用不存在
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 9999,
  "appName": "不存在的应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443"
  ],
  "appGroupName": "业务应用组"
}

### 8. 测试错误场景 - 无效的应用类型
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "四元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443"
  ],
  "appGroupName": "业务应用组"
}

### 9. 测试错误场景 - 应用组不存在
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443"
  ],
  "appGroupName": "不存在的应用组"
}

### 10. 重建ACL场景 - 仅修改应用名称（需要删除重建）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "邮件服务应用_重命名",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 11. 重建ACL场景 - 仅修改协议类型（需要删除重建）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "ICMP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 12. 重建ACL场景 - 仅修改应用分组（需要删除重建）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "企业应用组"
}

### 13. 重建ACL场景 - 同时修改多个需要重建的字段
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "通信服务应用_全新版",
  "appType": "五元组",
  "internetAppDSCP": "AF31",
  "agreementType": "UDP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "通信应用组"
}

### 14. 仅更新ACL场景 - 修改源IP（不需要重建）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 15. 仅更新ACL场景 - 修改目的IP列表（不需要重建）
PUT http://localhost:28000/gszh-srv6-api/srv6/modifyApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080",
    "************/32&9090"
  ],
  "appGroupName": "业务应用组"
} 