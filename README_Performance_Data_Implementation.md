# 链路性能数据获取实现说明

## 概述

在修改后的查询站点下链路信息接口中，完整实现了 `setPlatformLinkPerformanceData` 方法，参考 `setSingleLinkPerformanceData` 方法的逻辑，通过平台链路总览接口获取实时性能数据。

## 实现对比

### 原有方法：`setSingleLinkPerformanceData`
- **数据来源**：通过 `LinkCustomInfo` 中的 `sourceInterfaceId` 和 `targetInterfaceId`
- **适用场景**：基于数据库链路定制信息的查询
- **接口ID获取**：从数据库定制信息中获取

### 新实现方法：`setPlatformLinkPerformanceData`
- **数据来源**：通过 `PlatformGetLinkResponseDTO.GetLinkVo` 中的 `srcTpId` 和 `destTpId`
- **适用场景**：基于平台链路数据的查询
- **接口ID获取**：直接从平台链路数据中获取

## 核心实现逻辑

### 1. 接口ID收集
```java
// 1. 构建接口ID列表（使用平台链路的源和目标接口ID）
List<Long> interfaceIds = new ArrayList<>();
if (platformLink.getSrcTpId() != null) {
    interfaceIds.add(platformLink.getSrcTpId());
}
if (platformLink.getDestTpId() != null) {
    interfaceIds.add(platformLink.getDestTpId());
}
```

**特点：**
- 直接使用平台链路数据中的接口ID
- 无需额外的数据库查询
- 确保数据的实时性和准确性

### 2. 链路总览数据查询
```java
// 2. 查询链路总览数据
List<PlatformLinkOverViewResponseDTO.LinkOverViewInfo> allLinkList = 
    getLinkOverViewDataByInterfaceIds(interfaceIds);
```

**复用现有方法：**
- 使用已有的 `getLinkOverViewDataByInterfaceIds` 方法
- 保持查询逻辑的一致性
- 利用现有的错误处理和日志记录

### 3. 正向和反向链路识别
```java
// 3. 从查询结果中区分正向和反向链路
PlatformLinkOverViewResponseDTO.LinkOverViewInfo forwardLinkInfo = null;
PlatformLinkOverViewResponseDTO.LinkOverViewInfo reverseLinkInfo = null;

for (PlatformLinkOverViewResponseDTO.LinkOverViewInfo linkInfo : allLinkList) {
    if (platformLink.getLinkId().toString().equals(linkInfo.getLinkId())) {
        // 当前链路ID匹配的是正向链路
        forwardLinkInfo = linkInfo;
    } else {
        // 其他的就是反向链路
        reverseLinkInfo = linkInfo;
    }
}
```

**识别逻辑：**
- 通过链路ID精确匹配正向链路
- 其他链路视为反向链路
- 支持双向链路的性能数据获取

## 性能数据设置

### 1. 延迟数据（Delay）
```java
if (forwardLinkInfo.getDelay() != null && forwardLinkInfo.getDelay() >= 0) {
    linkInfoDTO.setSendDelay(forwardLinkInfo.getDelay());
    linkInfoDTO.setReceiveDelay(forwardLinkInfo.getDelay());
}
```

**设置规则：**
- 发送和接收延迟使用相同值（正向链路数据）
- 非负值验证，确保数据有效性
- 空值保护，避免设置无效数据

### 2. 抖动数据（Jitter）
```java
if (forwardLinkInfo.getJitter() != null && forwardLinkInfo.getJitter() >= 0) {
    linkInfoDTO.setSendJitter(forwardLinkInfo.getJitter().doubleValue());
    linkInfoDTO.setReceiveJitter(forwardLinkInfo.getJitter().doubleValue());
}
```

**数据转换：**
- 平台数据转换为Double类型
- 发送和接收抖动使用相同值
- 数值有效性验证

### 3. 丢包率数据（Packet Loss Rate）
```java
if (forwardLinkInfo.getPacketLossRate() != null && forwardLinkInfo.getPacketLossRate() >= 0) {
    linkInfoDTO.setSendLoss(forwardLinkInfo.getPacketLossRate());
    linkInfoDTO.setReceiveLoss(forwardLinkInfo.getPacketLossRate());
}
```

**数据特点：**
- 直接使用平台返回的丢包率
- 发送和接收丢包率使用相同值
- 百分比数据，范围0-100

### 4. 带宽数据（Bandwidth）
```java
// sendRate：使用当前链路带宽，平台返回kbps，转换为bps
if (forwardLinkInfo.getBandwidth() != null) {
    linkInfoDTO.setSendRate(forwardLinkInfo.getBandwidth().doubleValue() * 1000);
}

// receiveRate：仅从反向链路获取，平台返回kbps，转换为bps
if (reverseLinkInfo != null && reverseLinkInfo.getBandwidth() != null) {
    linkInfoDTO.setReceiveRate(reverseLinkInfo.getBandwidth().doubleValue() * 1000);
}
```

**单位转换：**
- 平台返回：kbps（千比特每秒）
- 接口返回：bps（比特每秒）
- 转换公式：bps = kbps × 1000

**方向区分：**
- 发送速率：使用正向链路数据
- 接收速率：使用反向链路数据（如果存在）

### 5. 带宽利用率计算
```java
// 6. 计算带宽利用率
calculateBandwidthUtilization(linkInfoDTO, platformLink, reverseLinkInfo);
```

**计算逻辑：**
- 复用现有的带宽利用率计算方法
- 发送利用率 = 发送速率 / 链路带宽 × 100%
- 接收利用率 = 接收速率 / 反向链路带宽 × 100%

## 异常处理

### 1. 接口ID缺失处理
```java
// 如果没有接口ID，设置默认值并返回
if (interfaceIds.isEmpty()) {
    setDefaultPerformanceData(linkInfoDTO);
    log.debug("链路{}没有接口ID信息，设置默认性能数据", platformLink.getLinkId());
    return;
}
```

### 2. 正向链路数据缺失处理
```java
if (forwardLinkInfo != null) {
    // 设置性能数据
} else {
    // 如果没有找到正向链路信息，设置默认值
    setDefaultPerformanceData(linkInfoDTO);
}
```

### 3. 全局异常处理
```java
} catch (Exception e) {
    log.error("设置链路{}性能数据异常", platformLink.getLinkId(), e);
    // 异常时设置默认值
    setDefaultPerformanceData(linkInfoDTO);
}
```

## 默认值设置

### `setDefaultPerformanceData` 方法
```java
private void setDefaultPerformanceData(QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO) {
    linkInfoDTO.setSendDelay(null);
    linkInfoDTO.setReceiveDelay(null);
    linkInfoDTO.setSendJitter(null);
    linkInfoDTO.setReceiveJitter(null);
    linkInfoDTO.setSendLoss(null);
    linkInfoDTO.setReceiveLoss(null);
    linkInfoDTO.setSendRate(null);
    linkInfoDTO.setReceiveRate(null);
    linkInfoDTO.setSendBwUsedPercent(null);
    linkInfoDTO.setReceiveBwUsedPercent(null);
}
```

**使用场景：**
- 接口ID缺失时
- 查询链路总览数据失败时
- 正向链路信息缺失时
- 发生异常时的兜底处理

## 性能优化

### 1. 数据查询优化
- 一次性查询所有相关接口的链路总览数据
- 避免多次调用平台接口
- 减少网络开销和响应时间

### 2. 数据处理优化
- 在内存中进行正向/反向链路识别
- 避免额外的数据库查询
- 提高数据处理效率

### 3. 异常处理优化
- 分层异常处理，精确定位问题
- 优雅降级，确保接口稳定性
- 详细的日志记录，便于问题排查

## 数据质量保证

### 1. 数据有效性验证
- 非空检查：确保数据不为null
- 非负值检查：确保性能数据合理
- 类型转换：安全的数据类型转换

### 2. 数据一致性保证
- 单位统一：统一转换为bps
- 精度保持：使用Double类型保持精度
- 格式统一：统一的数据格式和结构

### 3. 容错机制
- 部分数据缺失时的处理
- 异常情况下的默认值设置
- 确保接口始终返回完整结构

## 总结

通过完整实现 `setPlatformLinkPerformanceData` 方法，新的查询接口能够：

1. **获取实时性能数据**：通过平台链路总览接口获取最新的性能指标
2. **保持数据完整性**：即使部分数据缺失也能正常返回
3. **确保系统稳定性**：完善的异常处理和容错机制
4. **提供准确数据**：正确的单位转换和数据格式化

这种实现方式既保证了数据的实时性和准确性，又确保了系统的稳定性和可靠性。
