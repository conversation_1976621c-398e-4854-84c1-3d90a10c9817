# ServiceClass单一分配优化说明

## 概述

根据用户需求，对隧道组ServiceClass分配逻辑进行了优化：**新增隧道组时，一次只获取一个serviceClass，正反向都使用同一个serviceClass**。

## 修改内容

### 1. ServiceClassPoolService接口修改

**文件**: `src/main/java/com/h3c/dzkf/service/ServiceClassPoolService.java`

**主要变更**:
- 修改 `allocateServiceClass` 方法返回类型从 `ServiceClassPair` 改为 `Integer`
- 更新方法注释说明正反向使用同一个ServiceClass
- 为 `ServiceClassPair` 类新增构造函数，支持正反向使用同一个ServiceClass

```java
// 新增构造函数：正反向使用同一个ServiceClass
public ServiceClassPair(Integer serviceClass) {
    this.positiveServiceClass = serviceClass;
    this.negativeServiceClass = serviceClass;
}
```

### 2. ServiceClassPoolServiceImpl实现类修改

**文件**: `src/main/java/com/h3c/dzkf/service/impl/ServiceClassPoolServiceImpl.java`

**主要变更**:
- 简化 `allocateServiceClass` 方法逻辑，只分配一个ServiceClass
- 使用 `policy_direction = "BOTH"` 标识正反向共用
- 移除了原来分配两个ServiceClass的复杂逻辑

**修改前**:
```java
// 分配正向Policy的ServiceClass
Integer positiveServiceClass = getNextAvailableServiceClass();
// 分配反向Policy的ServiceClass  
Integer negativeServiceClass = getNextAvailableServiceClass();
```

**修改后**:
```java
// 分配一个ServiceClass供正反向Policy共同使用
Integer serviceClass = getNextAvailableServiceClass();
// policy_direction设置为BOTH表示正反向共用
int result = serviceClassPoolMapper.allocateServiceClass(serviceClass, teGroupId, "BOTH");
```

### 3. TeGroupServiceImpl业务逻辑修改

**文件**: `src/main/java/com/h3c/dzkf/service/impl/TeGroupServiceImpl.java`

**主要变更**:
- 在 `addTeGroup` 方法中调用新的ServiceClass分配逻辑
- 创建 `ServiceClassPair` 对象以保持代码兼容性

```java
// 4. 分配ServiceClass（正反向使用同一个）
Integer serviceClass = serviceClassPoolService.allocateServiceClass(request.getId());
if (serviceClass == null) {
    log.error("ServiceClass分配失败，隧道组ID：{}", request.getId());
    return ApiResponseDTO.fail(requestId, "ServiceClass资源不足");
}

// 创建ServiceClassPair对象以保持兼容性（正反向使用同一个ServiceClass）
ServiceClassPoolService.ServiceClassPair serviceClassPair = new ServiceClassPoolService.ServiceClassPair(serviceClass);
```

### 4. 数据库相关修改

**文件**: 
- `src/main/java/com/h3c/dzkf/entity/ServiceClassPool.java`
- `sql/service_class_pool.sql`

**主要变更**:
- 更新 `policy_direction` 字段注释，支持 `BOTH` 值
- 更新SQL文件中的注释说明

```sql
-- 修改前
`policy_direction` varchar(10) DEFAULT NULL COMMENT 'Policy方向：POSITIVE-正向，NEGATIVE-反向（按顺序递增分配）'

-- 修改后  
`policy_direction` varchar(10) DEFAULT NULL COMMENT 'Policy方向：POSITIVE-正向，NEGATIVE-反向，BOTH-正反向共用'
```

## 优化效果

### 1. 资源利用率提升
- **修改前**: 每个隧道组需要2个ServiceClass（正向1个，反向1个）
- **修改后**: 每个隧道组只需要1个ServiceClass（正反向共用）
- **资源节省**: ServiceClass使用量减少50%

### 2. 分配逻辑简化
- 移除了复杂的正反向分别分配逻辑
- 减少了数据库操作次数
- 降低了分配失败的风险

### 3. 兼容性保持
- 保留了 `ServiceClassPair` 类的原有接口
- 数据库表结构保持不变
- 现有的查询和显示逻辑无需修改

## 测试验证

创建了完整的单元测试 `ServiceClassPoolServiceTest.java`，验证了：

1. ✅ ServiceClass分配成功场景
2. ✅ 无可用ServiceClass场景
3. ✅ 分配失败场景
4. ✅ ServiceClass回收场景
5. ✅ ServiceClassPair新构造函数功能
6. ✅ ServiceClassPair兼容性功能

所有测试用例均通过验证。

## 数据库迁移建议

对于已存在的隧道组数据，建议执行以下SQL更新：

```sql
-- 将现有的POSITIVE和NEGATIVE记录合并为BOTH
-- 注意：这个操作需要根据实际业务需求谨慎执行
UPDATE service_class_pool 
SET policy_direction = 'BOTH' 
WHERE policy_direction IN ('POSITIVE', 'NEGATIVE') 
  AND te_group_id IS NOT NULL;

-- 删除重复的ServiceClass记录（保留较小的service_class值）
-- 具体SQL需要根据实际数据情况编写
```

## 注意事项

1. **向后兼容**: 修改保持了向后兼容性，现有代码无需大幅调整
2. **数据一致性**: TeGroupCustomInfo表中仍保留 `positive_service_class` 和 `negative_service_class` 字段，新增时两个字段存储相同值
3. **业务逻辑**: 修改隧道组时不涉及ServiceClass重新分配，保持原有逻辑不变

## 总结

此次优化成功实现了用户需求，将隧道组ServiceClass分配从"正反向分别分配"改为"正反向共用单一ServiceClass"，在提升资源利用率的同时保持了系统的稳定性和兼容性。
