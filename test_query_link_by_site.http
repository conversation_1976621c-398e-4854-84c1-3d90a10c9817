### 测试查询站点下链路信息接口

# 1. 测试基本查询（站点ID=1）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

###

# 2. 测试不存在的站点
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=999
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

###

# 3. 测试多级站点查询（站点ID=2）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=2
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

###

# 4. 测试大型站点查询（站点ID=3）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=3
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

###

# 5. 测试无效Token
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8
X-Access-Token: invalid-token

###

# 6. 测试缺少siteId参数
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

###

# 7. 测试性能监控场景（查看平台数据集成效果）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-performance

###

# 8. 测试设备级联场景
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=10
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345 