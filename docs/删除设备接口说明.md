# 删除设备接口说明

## 4.2.2 删除设备接口

### 接口描述
工商总行定义删除设备接口，定制开发根据总行下发的设备参数，对SDWAN设备删除接口进行封装，以满足总行api标准需要。通过设备ID查找定制库中的关联信息，调用平台删除设备接口实现设备删除，并同步删除定制库数据。

### 接口信息
- **接口协议**: DELETE
- **接口地址**: `/srv6/delDevice/{deviceId}`
- **Content-Type**: `application/json;charset=UTF-8`

### 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### 路径参数
| 参数名称 | 参数描述 | 是否必填 | 数据类型 |
|---------|---------|---------|---------|
| deviceId | 设备ID，工行控制器统一设定唯一标识 | Y | Integer |

### 接口逻辑流程
1. **参数验证**: 验证deviceId参数有效性
2. **Token验证**: 验证X-Access-Token有效性（测试模式可跳过）
3. **查询设备**: 根据deviceId查询device_custom_info表获取设备信息
4. **平台删除**: 调用ADWAN平台删除接口删除设备
5. **清理数据**: 删除device_custom_info表中的记录
6. **返回结果**: 返回删除操作结果

### 请求示例
```bash
DELETE /gszh-api/srv6/delDevice/1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here
```

### 平台接口映射

接口会自动查询定制库获取平台设备信息：

**查询流程：**
1. 根据`deviceId`查询`device_custom_info`表
2. 获取`platform_node_id`字段值
3. 构建平台删除请求参数

**ADWAN平台请求示例：**
```json
{
    "nodeIds": [283268141809666]
}
```

**平台接口信息：**
- **URL**: `/nfm/physicalNetwork/node/deleteMaintainedNodes`
- **方法**: POST
- **参数**: nodeIds数组

**平台响应示例：**
```json
{
    "message": "SUCCESS",
    "code": "SUCCESS",
    "result": null,
    "successful": true
}
```

### 响应体
接口响应体分为两种状态，状态为200时，响应体如下：

| 字段名称 | 字段描述 |
|---------|---------|
| requestId | 请求id，保存，用于溯源 |
| result | 状态：0:失败，1：成功 |
| failReason | 异常状态下，返回异常原因 |
| optionField | 涉及的属性 |

### 成功响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": "设备删除成功"
}
```

### 失败响应示例

#### 设备不存在
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "设备不存在",
    "optionField": null
}
```

#### 平台删除失败
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "调用平台接口删除设备失败",
    "optionField": null
}
```

#### Token验证失败
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "Token无效或已过期",
    "optionField": null
}
```

## 测试模式说明

删除设备接口同样支持测试模式：

### 测试模式配置
```yaml
test:
  mode:
    enabled: true                    # 启用测试模式
    skipTokenValidation: true        # 跳过token验证
    mockAdwanPlatform: true         # 模拟ADWAN平台调用
```

### 测试模式特性
1. **跳过Token验证**: 当`skipTokenValidation=true`时，接口不会验证X-Access-Token的有效性
2. **模拟平台调用**: 当`mockAdwanPlatform=true`时，不会实际调用ADWAN平台删除接口，直接返回成功
3. **快速测试**: 避免了外部依赖，可以快速验证删除逻辑

### 测试示例

**Postman请求配置:**
- **方法**: DELETE
- **URL**: `http://localhost:28000/gszh-api/srv6/delDevice/1001`
- **Headers**: 
  ```
  Content-Type: application/json;charset=UTF-8
  X-Access-Token: test-token-123456
  ```

**前置条件**: 需要先通过新增设备接口创建设备记录

## 配置说明

### ADWAN平台配置
```yaml
adwan:
  platform:
    baseUrl: http://***********:30000
    deleteDeviceUrl: /nfm/physicalNetwork/node/deleteMaintainedNodes
```

## 注意事项

1. **接口日志记录**: 该接口受到`ApiLogAspect`切面影响，会自动记录API调用日志
2. **事务管理**: 删除操作使用事务管理，确保数据一致性
3. **设备存在性**: 只能删除已存在的设备，不存在的设备ID会返回失败
4. **平台关联**: 如果设备没有关联的平台节点ID，会跳过平台删除步骤，直接删除定制库记录
5. **测试模式安全**: 生产环境必须关闭测试模式
6. **路径参数**: deviceId必须是有效的Integer类型
7. **删除确认**: 删除操作不可逆，请谨慎操作
8. **依赖关系**: 删除设备前请确认没有其他业务依赖该设备

## 错误码说明

| 错误信息 | 说明 | 解决方案 |
|---------|------|---------|
| 设备不存在 | 指定的deviceId在系统中不存在 | 检查deviceId是否正确 |
| Token无效或已过期 | 访问Token验证失败 | 重新获取有效的Token |
| 调用平台接口删除设备失败 | ADWAN平台删除操作失败 | 检查平台连接和设备状态 |
| 平台节点ID格式错误 | 存储的平台节点ID不是有效的数字 | 检查数据库中的platform_node_id字段 |
| 系统异常 | 服务器内部错误 | 查看系统日志排查具体原因 | 