# getNodes接口使用说明

## 接口概述

`PlatformApiService.getNodes()` 是用于查询ADWAN平台设备信息的核心接口，在调度策略管理中用于获取设备的管理IP地址。

## 接口定义

```java
/**
 * 调用平台接口查询设备信息
 *
 * @param request 平台查询设备请求参数
 * @return 设备信息响应
 */
PlatformGetNodesResponseDTO getNodes(PlatformGetNodesRequestDTO request);
```

## 请求参数

### PlatformGetNodesRequestDTO

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 页大小，默认15 |
| dataId | Long | 否 | 设备数据ID（单个查询） |
| dataIds | List<Long> | 否 | 设备数据ID列表（批量查询） |

### 构造函数

```java
// 单个设备查询
PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO(dataId);

// 批量设备查询
PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO(dataIds);

// 默认构造函数
PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO();
```

## 响应结果

### PlatformGetNodesResponseDTO

| 字段名 | 类型 | 说明 |
|--------|------|------|
| message | String | 响应消息 |
| code | String | 响应代码 |
| successful | Boolean | 是否成功 |
| result | GetNodesResult | 查询结果 |

### GetNodesResult

| 字段名 | 类型 | 说明 |
|--------|------|------|
| pageNum | Integer | 页码 |
| pageSize | Integer | 页大小 |
| totalItem | Integer | 总条数 |
| totalPage | Integer | 总页数 |
| records | List<NodeRecord> | 设备记录列表 |

### NodeRecord（关键字段）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| dataId | Long | 设备数据ID |
| nodeName | String | 设备名称 |
| manageIp | String | **管理IP（调度策略需要的字段）** |
| nodeDisplay | Integer | 设备状态显示 |
| category | Integer | 设备分类 |
| nodeModel | String | 设备型号 |

## 在调度策略中的使用

### 1. 数据流转过程

```
设备ID (device_custom_info.device_id) 
    ↓ 查询device_custom_info表
platform_node_id (String)
    ↓ 转换为Long类型
dataId (Long)
    ↓ 调用getNodes接口
NodeRecord.manageIp (String)
    ↓ 用于流策略命名
policy_192.168.1.1
```

### 2. 代码实现

```java
/**
 * 根据平台节点ID获取设备IP
 */
private String getDeviceIpByPlatformNodeId(String platformNodeId) {
    try {
        // 将platformNodeId转换为Long类型的dataId
        Long dataId = Long.valueOf(platformNodeId);
        
        // 调用PlatformApiService.getNodes()接口获取设备IP
        PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO(dataId);
        
        PlatformGetNodesResponseDTO response = platformApiService.getNodes(request);
        if (response != null && response.getSuccessful() != null && response.getSuccessful() &&
            response.getResult() != null && response.getResult().getRecords() != null && 
            !response.getResult().getRecords().isEmpty()) {
            
            PlatformGetNodesResponseDTO.NodeRecord nodeRecord = response.getResult().getRecords().get(0);
            String deviceIp = nodeRecord.getManageIp();
            log.info("查询到设备IP，平台节点ID：{}，设备IP：{}", platformNodeId, deviceIp);
            return deviceIp;
        } else {
            log.warn("未查询到设备信息，平台节点ID：{}", platformNodeId);
            return null;
        }
    } catch (NumberFormatException e) {
        log.error("平台节点ID格式错误，无法转换为Long类型，平台节点ID：{}", platformNodeId, e);
        return null;
    } catch (Exception e) {
        log.error("查询设备IP失败，平台节点ID：{}", platformNodeId, e);
        return null;
    }
}
```

### 3. 错误处理

#### 常见错误场景

1. **平台节点ID格式错误**
   - 错误：platform_node_id不是有效的数字格式
   - 处理：捕获NumberFormatException，记录错误日志

2. **设备不存在**
   - 错误：平台中不存在对应的设备
   - 处理：检查response.getSuccessful()和records是否为空

3. **网络异常**
   - 错误：平台接口调用失败
   - 处理：捕获Exception，记录错误日志

#### 错误日志示例

```
ERROR [ScheduleServiceImpl] 平台节点ID格式错误，无法转换为Long类型，平台节点ID：abc123
WARN  [ScheduleServiceImpl] 未查询到设备信息，平台节点ID：999999
ERROR [ScheduleServiceImpl] 查询设备IP失败，平台节点ID：123456
```

## 性能考虑

### 1. 单个查询 vs 批量查询

```java
// 单个查询（当前实现）
for (Integer deviceId : deviceIds) {
    String platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
    String deviceIp = getDeviceIpByPlatformNodeId(platformNodeId);
}

// 批量查询（优化建议）
List<Long> dataIds = deviceIds.stream()
    .map(this::getPlatformNodeIdByDeviceId)
    .filter(Objects::nonNull)
    .map(Long::valueOf)
    .collect(Collectors.toList());
    
PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO(dataIds);
PlatformGetNodesResponseDTO response = platformApiService.getNodes(request);
```

### 2. 缓存机制

建议对设备IP信息进行缓存，避免重复查询：

```java
// 使用本地缓存
private final Map<String, String> deviceIpCache = new ConcurrentHashMap<>();

private String getDeviceIpByPlatformNodeIdWithCache(String platformNodeId) {
    return deviceIpCache.computeIfAbsent(platformNodeId, this::getDeviceIpByPlatformNodeId);
}
```

## 测试模式

在测试模式下，getNodes接口会返回模拟数据：

```java
private PlatformGetNodesResponseDTO mockSingleGetNodes(PlatformGetNodesRequestDTO request) {
    PlatformGetNodesResponseDTO response = new PlatformGetNodesResponseDTO();
    response.setSuccessful(true);
    
    PlatformGetNodesResponseDTO.NodeRecord record = new PlatformGetNodesResponseDTO.NodeRecord();
    record.setDataId(request.getDataId());
    record.setNodeName("测试设备名称");
    record.setManageIp("*************"); // 模拟IP
    
    // ... 设置其他字段
    return response;
}
```

## 监控建议

### 1. 关键指标

- getNodes接口调用次数
- 接口响应时间
- 接口成功率
- 设备IP获取成功率

### 2. 告警规则

- 接口响应时间 > 5秒
- 接口成功率 < 95%
- 设备IP获取失败率 > 10%

### 3. 日志监控

```bash
# 监控getNodes接口调用
grep "开始调用平台接口查询设备" /opt/gszh-srv6-api/logs/application.log

# 监控设备IP获取成功
grep "查询到设备IP" /opt/gszh-srv6-api/logs/application.log

# 监控设备IP获取失败
grep "未查询到设备信息\|查询设备IP失败" /opt/gszh-srv6-api/logs/application.log
```

## 总结

getNodes接口是调度策略管理中获取设备IP的关键接口，正确使用该接口需要：

1. **数据类型转换**：platform_node_id (String) → dataId (Long)
2. **错误处理**：处理格式错误、设备不存在、网络异常等情况
3. **性能优化**：考虑批量查询和缓存机制
4. **监控告警**：监控接口调用情况和成功率

通过正确使用该接口，可以确保调度策略中流策略的命名准确性和业务逻辑的完整性。
