# 删除隧道组接口说明

## 接口概述

删除隧道组接口用于根据工商总行定义的隧道组ID集合删除隧道组，同时删除ADWAN平台对应的SRv6 Policy应用组。

**重要更新**：隧道组ID已从String类型更改为Integer类型，以符合工商总行的标准要求。

## 接口信息

- **接口协议**: POST
- **接口地址**: `/srv6/deleteTeGroup`
- **Content-Type**: `application/json;charset=UTF-8`

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|----------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### Body参数
| 接口参数名 | 接口参数描述 | 是否必填 | 数据类型 |
|-----------|-------------|---------|---------|
| teGroupIds | 隧道组ID集合，LIST | Y | List<Integer> |

### 请求示例
```json
{
    "teGroupIds": [1001, 1002, 1003]
}
```

## 响应参数

### 响应字段说明
| 字段名称 | 字段描述 | 数据类型 |
|---------|----------|---------|
| requestId | 请求id，保存，用于溯源 | String |
| result | 状态：0:失败，1：成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |

### 成功响应示例
```json
{
    "requestId": "req_20240703_001",
    "result": 1
}
```

### 失败响应示例
```json
{
    "requestId": "req_20240703_002",
    "result": 0,
    "failReason": "隧道组1001不存在或已被删除; 隧道组1002平台删除失败"
}
```

## 业务流程

### 删除隧道组流程
1. **参数验证**：验证隧道组ID集合不能为空
2. **逐个处理**：遍历隧道组ID集合，逐个执行删除操作
3. **查询隧道组**：根据隧道组ID查询本地数据库记录
4. **平台删除**：调用ADWAN平台接口删除对应的SRv6 Policy应用组
5. **资源回收**：回收隧道组使用的ServiceClass资源
6. **本地删除**：软删除本地数据库记录
7. **结果汇总**：统计成功和失败的数量，返回处理结果

### 平台接口调用
- **接口地址**: `DELETE /vas/srv6PolicyGroup/deleteSrv6PolicyGroup?groupId={groupId}`
- **认证方式**: Cookie认证，使用X-Subject-Token
- **响应格式**: JSON格式，包含successful字段表示是否成功

### 错误处理
- **部分成功**：如果部分隧道组删除成功，部分失败，返回失败状态并说明具体原因
- **全部失败**：如果所有隧道组都删除失败，返回失败状态并汇总所有失败原因
- **全部成功**：只有当所有隧道组都删除成功时，才返回成功状态

### ServiceClass管理
- **自动回收**：删除隧道组时自动回收相关的ServiceClass资源
- **失败处理**：ServiceClass回收失败不影响删除流程，只记录警告日志

## 注意事项

1. **批量处理**：接口支持批量删除多个隧道组
2. **事务处理**：每个隧道组的删除操作独立处理，单个失败不影响其他隧道组
3. **幂等性**：重复删除已删除的隧道组会返回"不存在或已被删除"的提示
4. **资源清理**：删除隧道组会同时清理平台资源和本地资源
5. **日志记录**：详细记录每个步骤的执行情况，便于问题排查

## 测试模式

在测试模式下（`test.mode.mockAdwanPlatform=true`），平台接口调用会被模拟，不会真实调用ADWAN平台接口。
