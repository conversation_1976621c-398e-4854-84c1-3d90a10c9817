# 批量导入设备模板接口说明

## 接口描述
工商总行定义批量导入设备接口，定制开发根据总行下发的要求，对SDWAN新增设备接口进行封装，以满足总行api标准需要。通过Excel文件的形式，接收设备模板数据，并保持与新增设备相同实现逻辑，实现设备批量增加。

## 接口信息
- **接口协议**: POST
- **接口地址**: `/srv6/import_device_template`
- **Content-Type**: `multipart/form-data`

## 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：multipart/form-data | Y |
| X-Access-Token | 4.1获取到的token | Y |

## 请求参数
| 接口参数名 | 接口参数描述 | 是否必填 | 平台参数名 | 平台参数描述 | 是否必填 |
|-----------|-------------|---------|-----------|-------------|---------|
| file | 设备批量文件 | Y | 无 | Excel格式的设备模板文件 | Y |

## Excel文件格式要求

### 文件格式
- 文件扩展名：`.xlsx`
- 编码格式：UTF-8

### 表头格式（第一行必须严格按照以下顺序）
| 列序号 | 列名 | 是否必填 | 数据类型 | 说明 |
|-------|------|---------|---------|------|
| 1 | 设备ID | 是 | 整数 | 工行控制器统一设定唯一标识 |
| 2 | 设备名称 | 是 | 字符串 | 设备名称，1~255个字符 |
| 3 | IP地址 | 是 | 字符串 | 管理IP地址，IPv4格式 |
| 4 | SN号 | 否 | 字符串 | 设备序列号 |
| 5 | 设备型号 | 是 | 字符串 | 设备型号 |
| 6 | 设备角色 | 是 | 字符串 | 设备角色：PE、P、ASBR_PE等 |
| 7 | 所属站点 | 是 | 字符串 | 所属站点名称 |
| 8 | 是否RR设备 | 是 | 字符串 | 填写"是"或"否" |
| 9 | 厂商名称 | 是 | 字符串 | H3C、HP、UNIS等 |
| 10 | 是否纳管 | 否 | 字符串 | 填写"是"或"否" |
| 11 | IPv6地址 | 否 | 字符串 | IPv6地址格式 |
| 12 | 设备分组 | 否 | 字符串 | 设备分组名称 |

### 数据示例
```
设备ID | 设备名称 | IP地址 | SN号 | 设备型号 | 设备角色 | 所属站点 | 是否RR设备 | 厂商名称 | 是否纳管 | IPv6地址 | 设备分组
1001 | 核心交换机01 | ************* | SN123456789 | S12500 | PE | 北京数据中心 | 是 | H3C | 是 | 2001:db8::1 | group1
1002 | 核心交换机02 | ************* | SN123456790 | S12500 | PE | 上海数据中心 | 否 | H3C | 是 | 2001:db8::2 | group1
```

## 响应体
接口响应体分为两种状态，状态为200时，响应体如下：

| 字段名称 | 字段描述 | 数据类型 |
|---------|---------|---------|
| requestId | 请求id，保存，用于溯源 | String |
| result | 状态：0:失败，1：成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 自定义结果 | Object |

### optionField字段说明
| 字段名称 | 字段描述 | 数据类型 |
|---------|---------|---------|
| totalNum | 设备总数 | Integer |
| succNum | 成功总数 | Integer |
| failNum | 失败总数 | Integer |
| addDeviceResults | 设备添加结果，数组，用于显示每一个设备的执行结果 | Array |

### addDeviceResults数组元素说明
| 字段名称 | 字段描述 | 数据类型 |
|---------|---------|---------|
| deviceName | 设备名称 | String |
| status | 同步状态：0:失败，1：成功 | Integer |
| errorMsg | 失败原因 | String |

## 成功响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": {
        "totalNum": 2,
        "succNum": 1,
        "failNum": 1,
        "addDeviceResults": [
            {
                "deviceName": "核心交换机01",
                "status": 1,
                "errorMsg": null
            },
            {
                "deviceName": "核心交换机02",
                "status": 0,
                "errorMsg": "设备ID已存在"
            }
        ]
    }
}
```

## 失败响应示例

### 文件格式错误
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "文件格式错误，请上传Excel文件(.xlsx)",
    "optionField": null
}
```

### Excel解析失败
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "Excel文件解析失败：表头格式错误，第1列应为'设备ID'，实际为'设备编号'",
    "optionField": null
}
```

### Token无效
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "Token无效或已过期",
    "optionField": null
}
```

## 平台涉及接口
新增设备接口：`/nfm/physicalNetwork/node/addNode`

## 重点逻辑
1. **严格按照新增设备的实现逻辑进行处理**：每个设备的处理逻辑与单个新增设备接口完全一致
2. **数据验证**：对Excel中的每行数据进行严格验证，包括必填字段检查、数据格式验证等
3. **错误处理**：针对每个设备的处理结果进行详细记录，包括成功/失败状态和具体错误信息
4. **统计封装**：返回设备总数、成功数、失败数及单个设备的同步状态信息
5. **事务处理**：每个设备的处理是独立的，单个设备失败不影响其他设备的处理

## 数据映射规则
批量导入的数据映射规则与单个新增设备接口完全一致：

### 工行字段到平台字段映射
| 工行字段 | 平台字段 | 映射规则 |
|---------|---------|---------|
| 设备ID | - | 存储在自定义表device_custom_info中 |
| 设备名称 | nodeName | 直接映射 |
| IP地址 | manageIp | 直接映射 |
| SN号 | serialNumbers | 直接映射 |
| 设备型号 | nodeModel | 直接映射 |
| 设备角色 | - | 存储在自定义表中，平台nodeRole固定为2(PE) |
| 所属站点 | - | 存储在自定义表中 |
| 是否RR设备 | - | 存储在自定义表中 |
| 厂商名称 | company | H3C=0, HP=1, UNIS=5, 其他=65535 |
| 是否纳管 | - | 存储在自定义表中 |
| IPv6地址 | - | 存储在自定义表中 |
| 设备分组 | - | 存储在自定义表中 |

## 使用示例

### 请求示例（使用curl）
```bash
curl -X POST \
  http://localhost:28000/gszh-api/srv6/import_device_template \
  -H 'Content-Type: multipart/form-data' \
  -H 'X-Access-Token: your_token_here' \
  -F 'file=@设备导入模板.xlsx'
```

### 请求示例（使用Postman）
1. 方法：POST
2. URL：`http://localhost:28000/gszh-api/srv6/import_device_template`
3. Headers：
   - `X-Access-Token`: your_token_here
4. Body：
   - 选择 `form-data`
   - Key: `file`，Type: `File`
   - Value: 选择Excel文件

## 注意事项
1. **文件大小限制**：建议单次导入设备数量不超过1000个，以避免处理超时
2. **数据唯一性**：设备ID必须全局唯一，包括已存在的设备
3. **网络超时**：大批量导入时可能需要较长处理时间，建议设置合适的超时时间
4. **错误处理**：即使部分设备导入失败，接口仍会返回成功状态，需要检查具体的设备处理结果
5. **Token有效期**：确保Token在整个导入过程中保持有效
6. **数据备份**：建议在批量导入前备份现有数据

## 错误码说明
| 错误类型 | 错误信息 | 解决方案 |
|---------|---------|---------|
| 文件格式错误 | 文件格式错误，请上传Excel文件(.xlsx) | 确保上传的是.xlsx格式的Excel文件 |
| 文件为空 | 上传文件不能为空 | 选择有效的Excel文件进行上传 |
| 表头错误 | 表头格式错误，第X列应为'XXX'，实际为'YYY' | 按照模板格式修正表头 |
| 数据验证失败 | 第X行：XXX不能为空 | 补充必填字段的数据 |
| 设备ID重复 | 设备ID已存在 | 使用唯一的设备ID |
| Token无效 | Token无效或已过期 | 重新获取有效的Token | 