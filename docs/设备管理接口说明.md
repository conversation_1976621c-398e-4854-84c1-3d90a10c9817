# 设备管理接口说明

## 4.2.1 新增设备接口

### 接口描述
工商总行定义新增设备参数，定制开发根据总行下发的设备参数，对SDWAN设备新增接口进行封装，以满足总行api标准需要。

### 接口信息
- **接口协议**: POST
- **接口地址**: `/srv6/addDevice`
- **Content-Type**: `application/json;charset=UTF-8`

### 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### 请求体参数
| 接口参数名 | 接口参数描述 | 是否必填 | 平台参数名 | 平台参数描述 | 是否必填 |
|-----------|-------------|---------|-----------|-------------|---------|
| deviceId | 设备ID，工行控制器统一设定唯一标识 | Y | 无 | 系统自动生成id，需单独建表保存该字段进行关联 | |
| deviceManufacturer | 厂商名称 | Y | company | 设备厂商信息,仅支持H3C、HP、UNIS，其他一律归为Unknown | N |
| deviceName | 设备名称 | Y | nodeName | 设备名称 | Y |
| deviceIp | 管理IP地址 | Y | manageIp | 管理IP地址 | Y |
| deviceSn | 设备序列号 | N | serialNumbers | 设备序列号 | N |
| deviceModel | 设备型号 | Y | nodeModel | 设备型号 | N |
| deviceRole | 设备角色 | Y | nodeRole | 设备角色，仅支持P、PE、ASBR_PE三种 | N |
| deviceSiteId | 设备所属站点 | Y | 无 | 需单独建表保存进行关联 | |
| deviceSite | 所属站点名称 | Y | 无 | 需单独建表保存进行关联 | |
| isRR | 是否为RR设备 | Y | 无 | 需单独建表保存进行关联 | |
| devicePlaneId | 所属平面ID | N | 无 | 需单独建表保存进行关联 | |
| isMarkDevice | 是否纳管（0:不纳管，1:纳管） | N | 无 | 不需要，在查询的时候根据isolationStatus进行判定 | |

### 请求示例
```json
{
    "deviceId": 1001,
    "deviceManufacturer": "H3C",
    "deviceName": "核心交换机01",
    "deviceIp": "*************",
    "deviceSn": "SN123456789",
    "deviceModel": "S12500",
    "deviceRole": "spoke",
    "deviceSiteId": 101,
    "deviceSite": "北京数据中心",
    "isRR": true,
    "devicePlaneId": 201,
    "isMarkDevice": 1,
    "deviceIpv6": "2001:db8::1",
    "deviceGroup": "group1"
}
```

### 平台接口映射

接口会将工行参数映射为平台参数格式：

**映射规则：**
- `deviceManufacturer` → `company` (数字类型): H3C=0, HP=1, UNIS=5, 其他=65535
- `deviceRole` → 存储在自定义表中，ADWAN平台的`nodeRole`固定为2(PE)
- `deviceName` → `nodeName`
- `deviceIp` → `manageIp`
- `deviceSn` → `serialNumbers`
- `deviceModel` → `nodeModel`
- `nodeType` 固定为1（真实设备）
- `nodeLevel` 默认为1（一级）
- `deviceIpv6`、`deviceGroup`、`deviceRole` 等字段存储在自定义表中

**ADWAN平台请求示例：**
```json
{
    "nodeName": "核心交换机01",
    "manageIp": "*************",
    "nodeType": 1,
    "company": 0,
    "nodeRole": 2,
    "serialNumbers": "SN123456789",
    "sysObjectOid": "",
    "softVersion": "",
    "nodeModel": "S12500",
    "netconfTemplateId": 0,
    "snmpTemplateId": 0,
    "nodeLevel": 1
}
```

**平台响应示例：**
```json
{
    "message": "SUCCESS",
    "code": "SUCCESS",
    "result": {
        "dataId": 293554735087618
    },
    "successful": true
}
```

### 响应体
接口响应体分为两种状态，状态为200时，响应体如下：

| 字段名称 | 字段描述 |
|---------|---------|
| requestId | 请求id，保存，用于溯源 |
| result | 状态：0:失败，1：成功 |
| failReason | 异常状态下，返回异常原因 |
| optionField | 涉及的属性 |

### 成功响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": "设备新增成功"
}
```

### 失败响应示例
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "设备ID已存在",
    "optionField": null
}
```

## 数据库表结构

### device_custom_info 表
存储平台中不存在的设备自定义信息：

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 主键ID |
| device_id | varchar(64) | 设备ID，工行控制器统一设定唯一标识 |
| platform_node_id | varchar(64) | 平台设备节点ID，关联平台设备 |
| device_site_id | varchar(64) | 设备所属站点ID |
| device_site | varchar(255) | 所属站点名称 |
| is_rr | tinyint(1) | 是否为RR设备：0-否，1-是 |
| device_plane_id | varchar(64) | 所属平面ID |
| create_time | datetime | 创建时间（首次上线时间） |
| update_time | datetime | 更新时间 |
| is_deleted | tinyint(1) | 是否删除：0-未删除，1-已删除 |

## 部署说明

1. 执行SQL脚本创建数据表：
   ```sql
   -- 执行 sql/device_custom_info.sql
   ```

2. 确保配置文件中ADWAN平台连接信息正确：
   ```yaml
   adwan:
     platform:
       baseUrl: http://***********:30000
       addDeviceUrl: /nfm/physicalNetwork/node/addNode
   ```

3. 配置测试模式（可选）：
   ```yaml
   test:
     mode:
       # 是否启用测试模式
       enabled: true
       # 是否跳过token验证
       skipTokenValidation: true
       # 是否模拟ADWAN平台调用
       mockAdwanPlatform: true
       # 模拟返回的设备ID范围
       mockDeviceIdRange:
         min: 100000000
         max: 999999999
   ```

4. 启动应用后，接口地址为：
     ```
     POST http://localhost:28000/gszh-api/srv6/addDevice
     DELETE http://localhost:28000/gszh-api/srv6/delDevice/{deviceId}
     POST http://localhost:28000/gszh-api/srv6/updateDevice
     ```

## 测试模式说明

系统提供了测试模式，方便开发和测试：

### 测试模式配置
```yaml
test:
  mode:
    enabled: true                    # 启用测试模式
    skipTokenValidation: true        # 跳过token验证
    mockAdwanPlatform: true         # 模拟ADWAN平台调用
    mockDeviceIdRange:
      min: 100000000                # 模拟设备ID最小值
      max: 999999999                # 模拟设备ID最大值
```

### 测试模式特性
1. **跳过Token验证**: 当`skipTokenValidation=true`时，接口不会验证X-Access-Token的有效性
2. **模拟平台调用**: 当`mockAdwanPlatform=true`时，不会实际调用ADWAN平台接口，而是返回随机生成的设备ID
3. **随机设备ID**: 在配置的范围内生成随机设备ID，模拟真实的平台响应
4. **快速测试**: 避免了外部依赖，可以快速验证接口逻辑

### 环境配置建议
- **开发环境**: 启用测试模式，方便调试
- **测试环境**: 根据需要选择性启用
- **生产环境**: 必须关闭测试模式

## 注意事项

1. **接口日志记录**：该接口受到`ApiLogAspect`切面影响，会自动记录API调用日志。切面和接口使用统一的requestId，确保日志记录与接口返回的requestId一致
2. **平台适配**：接口适配ADWAN平台规范，非uclinker平台
3. **设备厂商映射**：H3C=0, HP=1, UNIS=5, 其他=65535
4. **设备角色处理**：deviceRole保存在自定义表中，ADWAN平台的nodeRole固定为2(PE)
5. **新字段支持**：新增deviceIpv6(IPv6地址)和deviceGroup(设备组)字段
6. **测试模式安全**：生产环境必须关闭测试模式，避免安全风险
7. 需要先调用Token接口获取有效的X-Access-Token（测试模式下可跳过）
8. 设备ID必须唯一，重复添加会返回失败
9. 创建时间会自动记录，用于查询时作为首次上线时间使用
10. 平台返回的`dataId`会存储在`platform_node_id`字段中，用于关联平台设备

## 4.2.2 删除设备接口

### 接口描述
根据设备ID删除设备，通过查询定制库获取平台设备信息，调用ADWAN平台删除接口，并清理定制库数据。

### 接口信息
- **接口协议**: DELETE
- **接口地址**: `/srv6/delDevice/{deviceId}`

### 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### 路径参数
| 参数名称 | 参数描述 | 是否必填 | 数据类型 |
|---------|---------|---------|---------|
| deviceId | 设备ID，工行控制器统一设定唯一标识 | Y | Integer |

### 请求示例
```bash
DELETE /gszh-api/srv6/delDevice/1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here
```

### 成功响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": "设备删除成功"
}
```

### 失败响应示例
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "设备不存在",
    "optionField": null
}
```

### 平台接口映射
- 查询`device_custom_info`表获取`platform_node_id`
- 调用ADWAN平台接口：`/nfm/physicalNetwork/node/deleteMaintainedNodes`
- 删除成功后清理定制库记录

## 4.2.3 更新设备名称接口

### 接口描述
根据设备ID更新设备名称，通过查询定制库获取平台设备信息，先查询平台设备详细信息，然后调用平台更新接口修改设备名称。

### 接口信息
- **接口协议**: POST
- **接口地址**: `/srv6/updateDevice`

### 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### 请求体参数
| 参数名称 | 参数描述 | 是否必填 | 数据类型 |
|---------|---------|---------|---------|
| deviceId | 设备ID，工行控制器统一设定唯一标识 | Y | Integer |
| deviceName | 新设备名称 | Y | String |
| isMarkDevice | 是否纳管状态(0/1) | Y | Integer |

### 请求示例
```json
{
    "deviceId": 1001,
    "deviceName": "更新后的设备名称",
    "isMarkDevice": 1
}
```

### 成功响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": "设备名称更新成功"
}
```

### 失败响应示例
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "设备不存在",
    "optionField": null
}
```

### 平台接口映射
- 查询`device_custom_info`表获取`platform_node_id`
- 调用ADWAN平台查询接口：`/nfm/physicalNetwork/node/getNodes`
- 调用ADWAN平台更新接口：`/nfm/physicalNetwork/node/updateNode` 