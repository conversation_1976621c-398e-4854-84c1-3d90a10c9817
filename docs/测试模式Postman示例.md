# 测试模式Postman请求示例

## 测试环境配置

确保`application-dev.yml`中测试模式已启用：

```yaml
test:
  mode:
    enabled: true
    skipTokenValidation: true
    mockAdwanPlatform: true
    mockDeviceIdRange:
      min: 100000000
      max: 999999999
```

## Postman请求配置

### 1. 基本信息
- **方法**: POST
- **URL**: `http://localhost:28000/gszh-api/srv6/addDevice`
- **Content-Type**: `application/json;charset=UTF-8`

### 2. 请求头（Headers）
```
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456
```

**注意**: 测试模式下，X-Access-Token可以是任意值，系统会跳过验证。

### 3. 请求体（Body）
```json
{
    "deviceId": 1001,
    "deviceManufacturer": "H3C",
    "deviceName": "测试设备01",
    "deviceIp": "*************",
    "deviceSn": "SN123456789",
    "deviceModel": "S12500",
    "deviceRole": "spoke",
    "deviceSiteId": 101,
    "deviceSite": "北京测试中心",
    "isRR": true,
    "devicePlaneId": 201,
    "deviceIpv6": "2001:db8::1",
    "deviceGroup": "test-group"
}
```

### 4. 预期响应
```json
{
    "requestId": "req_20241201_123456789",
    "result": 1,
    "failReason": null,
    "optionField": "设备新增成功"
}
```

## 测试场景

### 场景1：正常新增设备
使用上述完整请求体，验证设备新增功能。

### 场景2：最小参数测试
```json
{
    "deviceId": 1002,
    "deviceManufacturer": "H3C",
    "deviceName": "最小参数测试",
    "deviceIp": "*************",
    "deviceModel": "S5500",
    "deviceRole": "hub",
    "deviceSiteId": 102,
    "deviceSite": "上海测试中心",
    "isRR": false
}
```

### 场景3：不同厂商测试
```json
{
    "deviceId": 1003,
    "deviceManufacturer": "HP",
    "deviceName": "HP设备测试",
    "deviceIp": "*************",
    "deviceModel": "HP5900",
    "deviceRole": "agg",
    "deviceSiteId": 103,
    "deviceSite": "广州测试中心",
    "isRR": false
}
```

### 场景4：参数验证测试
```json
{
    "deviceId": "",
    "deviceManufacturer": "",
    "deviceName": "",
    "deviceIp": "invalid-ip",
    "deviceModel": "",
    "deviceRole": "",
    "deviceSiteId": "",
    "deviceSite": "",
    "isRR": "invalid-boolean"
}
```

## 验证要点

### 1. 日志验证
查看控制台日志，应该看到：
```
测试模式：跳过Token验证，请求ID：xxx
测试模式：模拟ADWAN平台新增设备调用，设备名称：xxx
测试模式：生成模拟设备ID：xxx
```

### 2. 数据库验证
检查`device_custom_info`表，确认数据正确插入：
```sql
SELECT * FROM device_custom_info ORDER BY create_time DESC LIMIT 5;
```

### 3. 响应验证
- `requestId`不为空
- `result`为1（成功）
- `failReason`为null
- `optionField`为"设备新增成功"

## 常见问题

### 1. Token验证失败
**问题**: 即使配置了跳过Token验证，仍然提示Token无效
**解决**: 检查`test.mode.skipTokenValidation`配置是否为true

### 2. 平台调用失败
**问题**: 提示平台接口调用失败
**解决**: 检查`test.mode.mockAdwanPlatform`配置是否为true

### 3. 参数验证失败
**问题**: 提示参数格式错误
**解决**: 检查必填字段是否完整，数据类型是否正确

### 4. 数据库连接失败
**问题**: 提示数据库操作失败
**解决**: 检查数据库连接配置和表结构是否正确

## 删除设备接口测试

### 1. 基本信息
- **方法**: DELETE
- **URL**: `http://localhost:28000/gszh-api/srv6/delDevice/{deviceId}`
- **Content-Type**: `application/json;charset=UTF-8`

### 2. 请求头（Headers）
```
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456
```

### 3. 路径参数
将URL中的`{deviceId}`替换为要删除的设备ID，例如：
```
http://localhost:28000/gszh-api/srv6/delDevice/1001
```

### 4. 预期响应
```json
{
    "requestId": "req_20241201_123456789",
    "result": 1,
    "failReason": null,
    "optionField": "设备删除成功"
}
```

### 5. 测试步骤
1. **先新增设备**: 使用新增设备接口创建一个测试设备
2. **删除设备**: 使用删除设备接口删除刚创建的设备
3. **验证删除**: 确认数据库中记录已被删除

### 6. 常见测试场景

#### 场景1：正常删除设备
```
DELETE http://localhost:28000/gszh-api/srv6/delDevice/1001
```

#### 场景2：删除不存在的设备
```
DELETE http://localhost:28000/gszh-api/srv6/delDevice/99999
```
预期返回：
```json
{
    "requestId": "req_xxx",
    "result": 0,
    "failReason": "设备不存在",
    "optionField": null
}
```

#### 场景3：无效的设备ID格式
```
DELETE http://localhost:28000/gszh-api/srv6/delDevice/abc
```

## 完整测试流程

### 步骤1：新增设备
```
POST http://localhost:28000/gszh-api/srv6/addDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "deviceId": 2001,
    "deviceManufacturer": "H3C",
    "deviceName": "删除测试设备",
    "deviceIp": "*************",
    "deviceSn": "TEST123456",
    "deviceModel": "TEST5500",
    "deviceRole": "spoke",
    "deviceSiteId": 201,
    "deviceSite": "测试删除中心",
    "isRR": false
}
```

### 步骤2：验证设备已创建
检查数据库或通过其他查询接口确认设备已创建。

### 步骤3：删除设备
```
DELETE http://localhost:28000/gszh-api/srv6/delDevice/2001
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456
```

### 步骤4：验证设备已删除
再次检查数据库确认记录已被删除。

## 更新设备名称接口测试

### 1. 基本信息
- **方法**: POST
- **URL**: `http://localhost:28000/gszh-api/srv6/updateDevice`
- **Content-Type**: `application/json;charset=UTF-8`

### 2. 请求头（Headers）
```
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456
```

### 3. 请求体（Body）
```json
{
    "deviceId": 1001,
    "deviceName": "更新后的设备名称",
    "isMarkDevice": 1
}
```

### 4. 预期响应
```json
{
    "requestId": "req_20241201_123456789",
    "result": 1,
    "failReason": null,
    "optionField": "设备名称更新成功"
}
```

### 5. 测试步骤
1. **先新增设备**: 使用新增设备接口创建一个测试设备
2. **更新设备名称**: 使用更新设备接口修改设备名称
3. **验证更新**: 确认设备名称已成功更新

### 6. 常见测试场景

#### 场景1：正常更新设备名称
```json
{
    "deviceId": 1001,
    "deviceName": "新的设备名称",
    "isMarkDevice": 1
}
```

#### 场景2：更新不存在的设备
```json
{
    "deviceId": 99999,
    "deviceName": "不存在设备的名称",
    "isMarkDevice": 1
}
```
预期返回：
```json
{
    "requestId": "req_xxx",
    "result": 0,
    "failReason": "设备不存在",
    "optionField": null
}
```

#### 场景3：空设备名称测试
```json
{
    "deviceId": 1001,
    "deviceName": "",
    "isMarkDevice": 1
}
```

## 完整测试流程（包含更新）

### 步骤1：新增设备
```json
POST http://localhost:28000/gszh-api/srv6/addDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "deviceId": 3001,
    "deviceManufacturer": "H3C",
    "deviceName": "原始设备名称",
    "deviceIp": "*************",
    "deviceSn": "UPDATE123456",
    "deviceModel": "UPDATE5500",
    "deviceRole": "spoke",
    "deviceSiteId": 301,
    "deviceSite": "更新测试中心",
    "isRR": false
}
```

### 步骤2：验证设备已创建
检查数据库或通过其他查询接口确认设备已创建。

### 步骤3：更新设备名称
```json
POST http://localhost:28000/gszh-api/srv6/updateDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "deviceId": 3001,
    "deviceName": "更新后的设备名称",
    "isMarkDevice": 1
}
```

### 步骤4：验证设备名称已更新
通过查询接口或数据库确认设备名称已更新。

### 步骤5：删除测试设备
```json
DELETE http://localhost:28000/gszh-api/srv6/delDevice/3001
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456
```

## 切换到生产模式

当需要测试真实环境时，修改配置：

```yaml
test:
  mode:
    enabled: false
    skipTokenValidation: false
    mockAdwanPlatform: false
```

然后使用真实的：
- X-Access-Token（通过Token接口获取）
- ADWAN平台连接（确保网络可达） 