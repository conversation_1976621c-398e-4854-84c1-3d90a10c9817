# 调度策略名称变化不同步流分类流行为名称修改总结

## 修改背景

用户要求：修改调度策略时，当调度策略名称发生变化时，不要同步修改流分类和流行为的名称。

## 修改前的行为

在修改调度策略时，如果调度策略名称发生变化，系统会：
1. 自动生成新的流分类名称（基于新的调度策略名称）
2. 自动生成新的流行为名称（基于新的调度策略名称）
3. 同步更新平台和本地数据库中的流分类和流行为名称

## 修改后的行为

修改调度策略时，无论调度策略名称是否发生变化：
1. 流分类名称保持原有名称不变
2. 流行为名称保持原有名称不变
3. 只更新流分类的匹配规则（matchList）和流行为的remark信息

## 具体修改内容

### 1. 修改 `modifySchedule` 主方法

**文件位置：** `src/main/java/com/h3c/dzkf/service/impl/ScheduleServiceImpl.java`  
**行号：** 158-175 → 158-165

**修改前：**
```java
// 5. 更新调度策略主记录
// 检查名称是否发生变化
String newClassifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;
String newBehaviorName = request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX;
boolean nameChanged = !request.getAppScheduleName().equals(existingSchedule.getAppScheduleName());

existingSchedule.setAppScheduleName(request.getAppScheduleName());
existingSchedule.setAppGroupName(request.getAppGroupName());
existingSchedule.setDrainageType(request.getDrainageType());
existingSchedule.setNetworkIds(JSON.toJSONString(request.getNetworkIds()));
existingSchedule.setVpnId(request.getVpnId());

// 只有在名称发生变化时才更新流分类和流行为名称
if (nameChanged) {
    existingSchedule.setClassifierName(newClassifierName);
    existingSchedule.setBehaviorName(newBehaviorName);
    log.info("调度策略名称发生变化，同步更新流分类和流行为名称");
}
```

**修改后：**
```java
// 5. 更新调度策略主记录
existingSchedule.setAppScheduleName(request.getAppScheduleName());
existingSchedule.setAppGroupName(request.getAppGroupName());
existingSchedule.setDrainageType(request.getDrainageType());
existingSchedule.setNetworkIds(JSON.toJSONString(request.getNetworkIds()));
existingSchedule.setVpnId(request.getVpnId());

// 注意：调度策略名称变化时，不再同步修改流分类和流行为名称
```

### 2. 修改 `modifyClassifier` 方法

**文件位置：** `src/main/java/com/h3c/dzkf/service/impl/ScheduleServiceImpl.java`  
**行号：** 1725-1757 → 1716-1734

**修改前：**
```java
// 6. 检查流分类名称是否需要更新
String newClassifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;
boolean needUpdateName = !newClassifierName.equals(classifierInfo.getClassifierName());

// 7. 构建修改流分类请求
PlatformQosClassifierUpdateRequestDTO updateRequest = new PlatformQosClassifierUpdateRequestDTO();
updateRequest.setClassifierId(classifierInfo.getClassifierId());
updateRequest.setClassifierName(needUpdateName ? newClassifierName : classifierInfo.getClassifierName());
// ... 其他设置

// 8. 调用平台接口修改流分类
boolean updateSuccess = platformApiService.updateQosClassifier(updateRequest);
if (updateSuccess) {
    // 9. 只有在名称发生变化时才更新本地数据库中的流分类名称
    if (needUpdateName) {
        classifierInfo.setClassifierName(newClassifierName);
        scheduleClassifierInfoMapper.updateById(classifierInfo);
        // 日志记录名称已更新
    } else {
        // 日志记录名称无变化
    }
    return true;
}
```

**修改后：**
```java
// 6. 构建修改流分类请求（保持原有名称不变）
PlatformQosClassifierUpdateRequestDTO updateRequest = new PlatformQosClassifierUpdateRequestDTO();
updateRequest.setClassifierId(classifierInfo.getClassifierId());
updateRequest.setClassifierName(classifierInfo.getClassifierName()); // 保持原有名称不变
// ... 其他设置

// 7. 调用平台接口修改流分类
boolean updateSuccess = platformApiService.updateQosClassifier(updateRequest);
if (updateSuccess) {
    log.info("修改流分类成功，调度策略ID：{}，流分类ID：{}，名称保持不变：{}，新matchList数量：{}",
            request.getAppScheduleId(), classifierInfo.getClassifierId(), classifierInfo.getClassifierName(), matchList.size());
    return true;
}
```

### 3. 修改 `modifyBehavior` 方法

**文件位置：** `src/main/java/com/h3c/dzkf/service/impl/ScheduleServiceImpl.java`  
**行号：** 1810-1836 → 1796-1809

**修改前：**
```java
// 4. 检查流行为名称是否需要更新
String newBehaviorName = request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX;
boolean needUpdateName = !newBehaviorName.equals(behaviorInfo.getBehaviorName());

// 5. 基于查询到的现有配置构建修改请求，保持原有配置不变，只修改remarkList和名称（如果需要）
String targetBehaviorName = needUpdateName ? newBehaviorName : behaviorInfo.getBehaviorName();
PlatformQosBehaviorUpdateRequestDTO updateRequest = buildBehaviorUpdateRequest(detailResponse.getOutput(), remarkList, targetBehaviorName);

// 6. 调用平台接口修改流行为
boolean updateSuccess = platformApiService.updateQosBehavior(updateRequest);
if (updateSuccess) {
    // 7. 只有在名称发生变化时才更新本地数据库中的流行为名称
    if (needUpdateName) {
        behaviorInfo.setBehaviorName(newBehaviorName);
        scheduleBehaviorInfoMapper.updateById(behaviorInfo);
        // 日志记录名称已更新
    } else {
        // 日志记录名称无变化
    }
    return true;
}
```

**修改后：**
```java
// 4. 基于查询到的现有配置构建修改请求，保持原有配置不变，只修改remarkList（名称保持不变）
PlatformQosBehaviorUpdateRequestDTO updateRequest = buildBehaviorUpdateRequest(detailResponse.getOutput(), remarkList, behaviorInfo.getBehaviorName());

// 5. 调用平台接口修改流行为
boolean updateSuccess = platformApiService.updateQosBehavior(updateRequest);
if (updateSuccess) {
    log.info("修改流行为成功，调度策略ID：{}，流行为ID：{}，名称保持不变：{}",
            request.getAppScheduleId(), behaviorInfo.getBehaviorId(), behaviorInfo.getBehaviorName());
    return true;
}
```

### 4. 更新辅助方法注释

**文件位置：** `src/main/java/com/h3c/dzkf/service/impl/ScheduleServiceImpl.java`  
**行号：** 2192-2205

更新了 `buildBehaviorUpdateRequest` 方法的参数名称和注释，使其更准确地反映当前的使用方式。

## 修改影响

### 正面影响
1. **简化逻辑**：移除了复杂的名称变化检查和同步更新逻辑
2. **保持一致性**：流分类和流行为名称保持稳定，不会因调度策略名称变化而改变
3. **减少错误风险**：避免了名称同步可能导致的数据不一致问题

### 注意事项
1. **数据库一致性**：调度策略主表中的 `classifierName` 和 `behaviorName` 字段将不再与实际的流分类和流行为名称同步
2. **日志记录**：更新了日志信息，明确记录名称保持不变的状态
3. **向后兼容**：现有的调度策略不受影响，只影响后续的修改操作

## 测试建议

1. **功能测试**：验证修改调度策略名称后，流分类和流行为名称确实保持不变
2. **数据一致性测试**：确认平台接口调用和本地数据库更新都正确执行
3. **日志验证**：检查日志输出是否正确反映了修改后的行为

## 总结

此次修改成功实现了用户需求：在修改调度策略时，即使调度策略名称发生变化，也不会同步修改流分类和流行为的名称。修改简化了代码逻辑，提高了系统的稳定性和可维护性。
