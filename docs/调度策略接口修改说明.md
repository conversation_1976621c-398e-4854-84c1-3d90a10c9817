# 调度策略接口修改说明

## 修改概述

根据需求反馈，对调度策略管理接口进行了以下关键修改，以确保业务逻辑的正确性和完整性。

## 修改内容详情

### 1. 流分类处理优化

#### 1.1 应用组查询逻辑修正
**修改前：** 直接使用应用组的主键ID查询应用
**修改后：** 先根据应用组名称获取app_group_id，再根据app_group_id查询应用

```java
// 修改后的逻辑
LambdaQueryWrapper<AppGroupCustomInfo> appGroupQuery = new LambdaQueryWrapper<>();
appGroupQuery.eq(AppGroupCustomInfo::getAppGroupName, appGroupName);
AppGroupCustomInfo appGroup = appGroupCustomInfoMapper.selectOne(appGroupQuery);

LambdaQueryWrapper<AppCustomInfo> appQuery = new LambdaQueryWrapper<>();
appQuery.eq(AppCustomInfo::getAppGroupId, appGroup.getAppGroupId());
```

#### 1.2 流分类名称规则调整
**修改前：** 直接使用appScheduleName作为流分类名称
**修改后：** appScheduleName + "_classifier"

```java
String classifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;
```

#### 1.3 ACL模板查询逻辑重构
**修改前：** 使用应用名称 + "_ipv4"作为ACL模板名称
**修改后：** 根据app_custom_info的acl_id_list中的aclId调用平台查询ACL模板列表接口获取aclName

```java
// 解析ACL ID列表
List<Integer> aclIds = parseAclIdList(app.getAclIdList());

// 为每个ACL ID生成匹配规则
for (Integer aclId : aclIds) {
    String aclName = getAclNameById(aclId);
    // 生成匹配规则...
}
```

#### 1.4 流分类分页查询支持
**修改前：** 单次查询，可能遗漏数据
**修改后：** 支持分页查询，确保能找到所有匹配的流分类

```java
int start = 0;
int size = 50; // 每页查询50条

while (true) {
    // 分页查询逻辑
    // 检查是否还有更多数据
    if (response.getOutput().getList().size() < size) {
        break; // 已经是最后一页
    }
    start += size; // 查询下一页
}
```

### 2. 流策略处理增强

#### 2.1 设备IP获取逻辑修正
**修改前：** 直接从作用域中提取设备IP
**修改后：** 从作用域中提取设备ID，然后调用平台接口获取设备IP

```java
// teGroupDcs字段值示例处理
[{"dstDeviceIds": [100, 101], "srcDeviceIds": [102, 103], 
  "dstDeviceNames": ["Device1", "Device2"], "srcDeviceNames": ["Device4", "Device5"]}]

// 提取设备ID
Set<Integer> deviceIds = extractDeviceIds(scopeList);
for (Integer deviceId : deviceIds) {
    String deviceIp = getDeviceIpById(deviceId); // 调用平台接口获取IP
}
```

#### 2.2 流策略修改接口支持
**新增功能：** 当流策略已存在时，在原策略的cbpairList中新增记录

```java
// 新增平台接口
PUT /qostrs/qos/policy/all

// 更新逻辑
private Integer updateExistingPolicy(PlatformQosPolicyListResponseDTO.PolicyInfo existingPolicy, 
                                   Integer classifierId, Integer behaviorId, String scheduleName) {
    // 构建更新请求，包含原有cbpair和新增cbpair
}
```

#### 2.3 流策略命名规则优化
**修改前：** classifierName和behaviorName使用scheduleName
**修改后：** 使用正确的流分类和流行为名称

```java
// 新增流策略时的正确命名
cbPair.setClassifierName(scheduleName + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX);
cbPair.setBehaviorName(scheduleName + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX);
```

### 3. 新增平台接口支持

#### 3.1 流策略修改接口
```java
@Override
public boolean updateQosPolicy(PlatformQosPolicyUpdateRequestDTO request) {
    String url = platformBaseUrl + updateQosPolicyUrl;
    // PUT请求实现
}
```

#### 3.2 相关DTO类
- `PlatformQosPolicyUpdateRequestDTO` - 流策略修改请求DTO

### 4. 常量管理优化

#### 4.1 新增命名规则常量
```java
public static class NamingRule {
    /** 流分类名称后缀 */
    public static final String CLASSIFIER_NAME_SUFFIX = "_classifier";
    /** 流行为名称后缀 */
    public static final String BEHAVIOR_NAME_SUFFIX = "_behavior";
    /** 流策略名称前缀 */
    public static final String POLICY_NAME_PREFIX = "policy_";
}
```

### 5. 配置文件更新

#### 5.1 新增流策略修改接口URL配置
```yaml
# 开发环境
updateQosPolicyUrl: /qostrs/qos/policy/all

# 生产环境  
updateQosPolicyUrl: /adwan/proxy/qostrs/qos/policy/all
```

## 待确认需求

### 隧道组ID处理
**当前问题：** 传入的隧道组ID是List列表，但创建流行为时只能使用一个隧道组的信息
**当前处理：** 暂时使用第一个隧道组的信息创建流行为
**需要确认：** 
1. 是否需要为每个隧道组创建单独的流行为？
2. 还是使用统一的流行为配置？

## 业务流程更新

### 修改后的完整流程
```
1. 参数验证
   ├── 检查应用组是否存在（使用app_group_id）
   └── 检查隧道组是否存在

2. 流分类处理
   ├── 根据应用组名称获取应用列表
   ├── 解析每个应用的ACL ID列表
   ├── 调用平台接口获取ACL模板名称
   ├── 生成匹配规则
   ├── 创建流分类（名称：appScheduleName + "_classifier"）
   └── 分页查询获取流分类ID

3. 流行为处理
   ├── 获取隧道组模板信息
   ├── 生成remark规则（类型1、16、17）
   ├── 创建流行为（名称：appScheduleName + "_behavior"）
   └── 获取流行为ID

4. 流策略处理
   ├── 解析隧道组作用域信息
   ├── 提取设备ID列表
   ├── 调用平台接口获取设备IP
   ├── 检查流策略是否存在
   ├── 存在则更新cbpairList，不存在则创建新策略
   └── 使用正确的流分类和流行为名称

5. 策略部署
   ├── 查询设备信息
   ├── 查询设备接口信息
   └── 部署策略到设备接口

6. 数据保存
   └── 保存调度策略信息（使用正确的名称）
```

## 影响评估

### 1. 兼容性影响
- **数据库**：无结构变更，仅业务逻辑调整
- **接口**：请求响应格式无变化
- **配置**：新增URL配置，向后兼容

### 2. 性能影响
- **ACL查询**：新增ACL模板查询，可能增加响应时间
- **分页查询**：流分类查询支持分页，提高查询准确性
- **设备IP查询**：新增设备IP查询，增加平台接口调用

### 3. 功能增强
- **策略合并**：支持流策略的更新和合并
- **数据准确性**：使用实际的ACL模板名称和设备IP
- **查询完整性**：分页查询确保数据完整性

## 测试建议

### 1. 单元测试
- ACL ID列表解析测试
- 设备ID提取测试
- 流策略更新逻辑测试

### 2. 集成测试
- 完整业务流程测试
- 平台接口调用测试
- 错误场景处理测试

### 3. 性能测试
- 大量ACL ID的处理性能
- 分页查询性能
- 并发调用测试

## 部署注意事项

1. **配置更新**：确保新增的URL配置正确
2. **平台接口**：确认平台支持流策略修改接口
3. **数据准备**：确保测试环境有完整的ACL模板数据
4. **监控加强**：增加对新增接口调用的监控
