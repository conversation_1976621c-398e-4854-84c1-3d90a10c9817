{"info": {"_postman_id": "device-management-api", "name": "设备管理接口测试", "description": "工商总行设备管理接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 获取Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "eyJ1c2VybmFtZSI6ImFkbWluIiwicGFzc3dvcmQiOiJlbmNyeXB0ZWRfcGFzc3dvcmQiLCJ0aW1lc3RhbXAiOiIxNjk5OTk5OTk5In0="}, "url": {"raw": "{{base_url}}/srv6/oauth/token", "host": ["{{base_url}}"], "path": ["srv6", "o<PERSON>h", "token"]}, "description": "获取访问Token，用于后续接口调用"}, "response": []}, {"name": "2. 新增设备", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=UTF-8", "type": "text"}, {"key": "X-Access-Token", "value": "{{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"ICBC_DEV_001\",\n    \"deviceManufacturer\": \"H3C\",\n    \"deviceName\": \"核心交换机01\",\n    \"deviceIp\": \"*************\",\n    \"deviceSn\": \"SN123456789\",\n    \"deviceModel\": \"S12500\",\n    \"deviceRole\": \"PE\",\n    \"deviceSiteId\": \"SITE_001\",\n    \"deviceSite\": \"北京数据中心\",\n    \"isRR\": 1,\n    \"devicePlaneId\": \"PLANE_001\",\n    \"isMarkDevice\": 1\n}"}, "url": {"raw": "{{base_url}}/srv6/addDevice", "host": ["{{base_url}}"], "path": ["srv6", "addDevice"]}, "description": "新增设备接口测试"}, "response": []}, {"name": "3. 新增设备 - 参数验证测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=UTF-8", "type": "text"}, {"key": "X-Access-Token", "value": "{{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"deviceManufacturer\": \"H3C\",\n    \"deviceName\": \"核心交换机02\",\n    \"deviceIp\": \"*************\",\n    \"deviceModel\": \"S12500\",\n    \"deviceRole\": \"P\",\n    \"deviceSiteId\": \"SITE_002\",\n    \"deviceSite\": \"上海数据中心\",\n    \"isRR\": 0\n}"}, "url": {"raw": "{{base_url}}/srv6/addDevice", "host": ["{{base_url}}"], "path": ["srv6", "addDevice"]}, "description": "测试缺少必填参数deviceId的情况"}, "response": []}, {"name": "4. 新增设备 - 重复设备ID测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json;charset=UTF-8", "type": "text"}, {"key": "X-Access-Token", "value": "{{access_token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"deviceId\": \"ICBC_DEV_001\",\n    \"deviceManufacturer\": \"HP\",\n    \"deviceName\": \"核心交换机03\",\n    \"deviceIp\": \"*************\",\n    \"deviceModel\": \"A5800\",\n    \"deviceRole\": \"ASBR_PE\",\n    \"deviceSiteId\": \"SITE_003\",\n    \"deviceSite\": \"深圳数据中心\",\n    \"isRR\": 0\n}"}, "url": {"raw": "{{base_url}}/srv6/addDevice", "host": ["{{base_url}}"], "path": ["srv6", "addDevice"]}, "description": "测试重复设备ID的情况"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:28000/gszh-api", "type": "string"}, {"key": "access_token", "value": "your_access_token_here", "type": "string"}]}