# 查询站点下所有设备信息接口说明

## 接口描述
工商总行定义查询站点下所有设备信息接口，定制开发根据总行下发的设备参数，对SDWAN设备查询接口进行封装，以满足总行api标准需要。

## 实现方案
定义get接口，接收站点id参数，并根据站点id，查找定制库(站点数据维护表)，查找到本站点及所有下级站点id,再根据站点id关联查询到平台设备id，并调用平台的设备查询接口。

## 接口位置
**控制器**: DeviceController.java (设备管理控制器)
**服务实现**: DeviceServiceImpl.java

## 接口信息
- **接口协议**: GET
- **接口地址**: `/srv6/queryDeviceInfoListBySiteId`

## 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

## 请求参数
| 接口参数名 | 接口参数描述 | 是否必填 | 平台参数名 | 平台参数描述 | 是否必填 |
|-----------|-------------|---------|-----------|-------------|---------|
| siteId | 站点id | Y | 无 | 通过siteId管理查询所有下级站点，并根据查询结果关联查询平台设备id集合 | |

## 请求示例
```bash
GET /gszh-api/srv6/queryDeviceInfoListBySiteId?siteId=1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here
```

## 响应体
接口响应体分为两种状态，状态为200时，响应体如下：

| 字段名称 | 字段描述 |
|---------|---------|
| RequestId | 请求id，保存，用于溯源 |
| total | 设备总数 |
| deviceInfoList | 设备对象集合 |

### deviceInfoList字段说明
| 字段名称 | 字段类型 | 字段描述 |
|---------|---------|---------|
| deviceId | Integer | 设备ID |
| siteId | Integer | 站点ID |
| deviceName | String | 设备名称 |
| deviceManufacturer | String | 设备厂商 |
| deviceModel | String | 设备型号 |
| deviceMac | String | 设备MAC地址 |
| deviceSn | String | 设备序列号 |
| deviceStatus | String | 设备状态（on/off） |
| deviceIp | String | 设备IP地址 |
| deviceSite | String | 设备所属站点 |
| deviceRole | String | 设备角色 |
| isRR | Boolean | 是否RR设备 |
| isMarkDevice | Integer | 是否纳管设备（0：未纳管，1：已纳管） |
| optionField | Object | 可选字段 |

## 响应示例
```json
{
  "requestId": "req-123456789",
  "total": 2,
  "deviceInfoList": [
    {
      "deviceId": 1001,
      "siteId": 1001,
      "deviceName": "核心交换机-01",
      "deviceManufacturer": "H3C",
      "deviceModel": "S12500-X",
      "deviceMac": "00:11:22:33:44:55",
      "deviceSn": "210235A1ABC0001",
      "deviceStatus": "on",
      "deviceIp": "*************",
      "deviceSite": "总部机房",
      "deviceRole": "PE",
      "isRR": true,
      "isMarkDevice": 1
    },
    {
      "deviceId": 1002,
      "siteId": 1001,
      "deviceName": "接入交换机-01",
      "deviceManufacturer": "H3C",
      "deviceModel": "S5560-EI",
      "deviceMac": "00:11:22:33:44:56",
      "deviceSn": "210235A1ABC0002",
      "deviceStatus": "on",
      "deviceIp": "*************",
      "deviceSite": "总部机房",
      "deviceRole": "P",
      "isRR": false,
      "isMarkDevice": 1
    }
  ]
}
```

## 业务逻辑说明

### 1. 站点查询逻辑
- 首先验证输入的站点ID是否存在
- 递归查询该站点下的所有子站点
- 支持多级站点层次结构

### 2. 设备查询逻辑
- 根据站点ID列表查询定制库中的设备信息
- 调用平台接口获取设备详细信息
- 合并定制库和平台数据

### 3. 字段映射逻辑
- **厂商映射**: 平台company字段映射为厂商名称
- **状态映射**: 平台nodeDisplay字段映射为设备状态，简化为在线/下线两种状态
  - nodeDisplay = 3（灰色，设备下线）→ "off"
  - nodeDisplay = 1,2,4,5（其他状态）→ "on"
  - nodeDisplay = null 或未知 → "off"
- **角色映射**: 平台nodeRole字段映射为设备角色
- **纳管状态**: 根据isolationStatus判断（0：未纳管，1：已纳管）

### 4. 异常处理
- 站点不存在时返回空设备列表
- 平台查询失败时使用默认值填充
- 确保接口稳定性，不会因个别设备异常影响整体查询

## 涉及的平台接口
- **设备查询接口**: `/nfm/physicalNetwork/node/getNodes`

## 实现的类文件
- **QueryDeviceInfoListBySiteIdResponseDTO.java**: 响应DTO
- **DeviceController.java**: 接口控制器（新增方法：queryDeviceInfoListBySiteId）
- **DeviceService.java**: 服务接口（新增方法声明）
- **DeviceServiceImpl.java**: 服务实现（新增完整业务逻辑）

## 测试说明
1. 确保测试环境有站点数据和设备数据
2. 可在测试模式下跳过Token验证
3. 建议测试包含多层级站点结构的场景
4. 验证设备信息映射的正确性

## 注意事项
1. 站点ID必须存在且未被逻辑删除
2. 如果站点下没有设备，返回空列表
3. 设备信息主要来自ADWAN平台，如果平台接口异常，会影响返回结果
4. 接口性能与站点下设备数量成正比，设备较多时响应时间会增长 