# 候选路径处理示例

## 最简实现方式

### 直接遍历原有候选路径列表

```java
// 在buildUpdateSrv6PolicyGroupRequest方法中，遍历originalDetail.getSrv6PolicyGroupParamsList()时
for (GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupParamsVO originalParam : originalDetail.getSrv6PolicyGroupParamsList()) {
    UpdateSrv6PolicyGroupRequestDTO.UpdateSrv6PolicyGroupParamsDTO updateParam =
        new UpdateSrv6PolicyGroupRequestDTO.UpdateSrv6PolicyGroupParamsDTO();

    updateParam.setDataId(originalParam.getDataId());
    // ... 其他参数设置

    // 构建候选路径约束，直接遍历原有候选路径列表
    List<UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity> constraints = new ArrayList<>();
    List<GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupCandidatePathVO> originalConstraints = originalParam.getCandiPathConstraints();

    if (originalConstraints != null) {
        for (GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupCandidatePathVO originalConstraint : originalConstraints) {
            UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity constraint =
                new UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity();

            // 基于原有路径配置
            constraint.setDataId(originalConstraint.getDataId());
            constraint.setPathIndex(originalConstraint.getPathIndex());
            constraint.setPreference(originalConstraint.getPreference());
            constraint.setAffinityType(originalConstraint.getAffinityType());
            constraint.setPreferColor(originalConstraint.getPreferColor());
            constraint.setIncludeAffinityAny(originalConstraint.getIncludeAffinityAny());
            constraint.setIncludeAffinityAll(originalConstraint.getIncludeAffinityAll());
            constraint.setExcludeAffinity(originalConstraint.getExcludeAffinity());
            constraint.setInitSidPathNum(originalConstraint.getInitSidPathNum());
            constraint.setMaxSidPathNum(originalConstraint.getMaxSidPathNum());

            // hopLimit从请求参数取值，如果没有则保持原有值
            if (request.getHopLimit() != null) {
                constraint.setHopLimit(request.getHopLimit());
            } else {
                constraint.setHopLimit(originalConstraint.getHopLimit());
            }

            constraints.add(constraint);
        }
    }

    updateParam.setCandiPathConstraints(constraints);
    paramsList.add(updateParam);
}
```

## 处理场景示例

### 场景1：修改现有隧道组（有原有路径）

**原有路径配置**：
```json
[
  {
    "dataId": 12345,
    "pathIndex": 0,
    "preference": 100,
    "affinityType": 0,
    "preferColor": 50,
    "hopLimit": 20,
    "initSidPathNum": 1,
    "maxSidPathNum": 8
  },
  {
    "dataId": 12346,
    "pathIndex": 1,
    "preference": 90,
    "affinityType": 0,
    "preferColor": 60,
    "hopLimit": 20,
    "initSidPathNum": 1,
    "maxSidPathNum": 8
  }
]
```

**修改请求**：
```json
{
  "id": 1001,
  "hopLimit": 30,
  // ... 其他参数
}
```

**处理结果**：
- 路径1和路径2的所有原有配置保持不变
- 只有hopLimit更新为30（从请求参数）
- dataId保持原值，确保更新操作正确

### 场景2：修改隧道组（无原有路径）

**修改请求**：
```json
{
  "id": 1002,
  "hopLimit": 25,
  // ... 其他参数
}
```

**处理结果**：
```json
[
  {
    "pathIndex": 0,
    "preference": 100,
    "affinityType": 0,
    "hopLimit": 25,
    "initSidPathNum": 1,
    "maxSidPathNum": 8
  },
  {
    "pathIndex": 1,
    "preference": 90,
    "affinityType": 0,
    "hopLimit": 25,
    "initSidPathNum": 1,
    "maxSidPathNum": 8
  }
]
```

## 参数来源总结

| 参数名称 | 有原有路径时 | 无原有路径时 | 备注 |
|---------|-------------|-------------|------|
| dataId | 原有值 | null | 用于更新操作 |
| pathIndex | 原有值 | 0,1 | 路径索引 |
| preference | 原有值 | 100,90 | 路径优先级 |
| affinityType | 原有值 | 0 | 亲和属性类型 |
| preferColor | 原有值 | null | 优选链路着色 |
| includeAffinityAny | 原有值 | null | 亲和属性包含 |
| includeAffinityAll | 原有值 | null | 亲和属性必选 |
| excludeAffinity | 原有值 | null | 亲和属性排除 |
| hopLimit | 请求参数或原有值 | 请求参数或255 | 最大跳数 |
| initSidPathNum | 原有值 | 1 | 初始分段个数 |
| maxSidPathNum | 原有值 | 8 | 最大分段个数 |

## 扩展支持

### 添加隧道模板参数

如果需要从隧道模板取值的参数，可以在相应位置添加：

```java
// 在有原有路径的分支中
if (originalPath != null) {
    // ... 现有代码
    
    // 从隧道模板取值的参数示例
    if (tunnelTemplate.getSomeParameter() != null) {
        constraint.setSomeParameter(tunnelTemplate.getSomeParameter());
    }
}

// 在无原有路径的分支中
else {
    // ... 现有代码
    
    // 从隧道模板取值的参数示例
    constraint.setSomeParameter(tunnelTemplate.getSomeParameter() != null ? 
        tunnelTemplate.getSomeParameter() : defaultValue);
}
```

这样的设计提供了良好的扩展性，可以根据业务需求灵活添加更多参数的处理逻辑。
