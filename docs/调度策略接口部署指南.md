# 调度策略接口部署指南

## 概述

本文档描述了调度策略管理接口的部署步骤和注意事项。

## 部署前准备

### 1. 数据库准备

#### 创建调度策略表
```sql
-- 执行 sql/schedule_custom_info.sql 中的建表语句
CREATE TABLE `schedule_custom_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_schedule_id` int(11) NOT NULL COMMENT '调度策略唯一标识ID（工行控制器设定）',
    `app_schedule_name` varchar(255) NOT NULL COMMENT '策略名称',
    `app_group_name` varchar(255) NOT NULL COMMENT '应用组名称',
    `drainage_type` int(11) DEFAULT NULL COMMENT '引流类型 0:五元组、1:dscp、4:Vpn',
    `network_ids` text NOT NULL COMMENT '隧道组ID列表（JSON格式存储）',
    `vpn_id` int(11) DEFAULT NULL COMMENT 'VPN ID',
    `classifier_id` int(11) DEFAULT NULL COMMENT '流分类ID',
    `classifier_name` varchar(255) DEFAULT NULL COMMENT '流分类名称',
    `behavior_id` int(11) DEFAULT NULL COMMENT '流行为ID',
    `behavior_name` varchar(255) DEFAULT NULL COMMENT '流行为名称',
    `policy_info` text DEFAULT NULL COMMENT '流策略信息（JSON格式存储，包含设备IP和对应的策略ID）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_app_schedule_id_not_deleted` (`app_schedule_id`, `is_deleted`),
    KEY `idx_app_schedule_name` (`app_schedule_name`),
    KEY `idx_app_group_name` (`app_group_name`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度策略定制信息表';
```

#### 准备基础数据
```sql
-- 确保存在测试用的应用组
INSERT INTO app_group_custom_info (app_group_id, app_group_name) 
VALUES (1, '测试应用组') ON DUPLICATE KEY UPDATE app_group_name = app_group_name;

-- 确保存在测试用的应用
INSERT INTO app_custom_info (app_id, app_name, app_group_id) 
VALUES (1, '测试应用', 1) ON DUPLICATE KEY UPDATE app_name = app_name;

-- 确保存在测试用的隧道组
INSERT INTO tunnel_group_custom_info (te_group_id, name, schedule_strategy_id, te_group_dcs, positive_service_class) 
VALUES (1001, '测试隧道组', 1, '[{"srcDeviceIds":["192.168.1.1"],"dstDeviceIds":["192.168.1.2"]}]', 1) 
ON DUPLICATE KEY UPDATE name = name;

-- 确保存在测试用的隧道模板
INSERT INTO tunnel_template_custom_info (strategy_id, strategy_name, sla_id) 
VALUES (1, '测试模板', 'EF') ON DUPLICATE KEY UPDATE strategy_name = strategy_name;
```

### 2. 配置文件检查

#### 开发环境配置 (application-dev.yml)
```yaml
adwan:
  platform:
    # QoS相关接口路径（开发环境直连）
    addQosClassifierUrl: /qostrs/qos/classifier
    getQosClassifierListUrl: /qostrs/qos/classifier
    addQosBehaviorUrl: /qostrs/qos/behavior
    addQosPolicyUrl: /qostrs/qos/policy
    getQosPolicyListUrl: /qostrs/qos/policy
    deployQosPolicyUrl: /qostrs/qos/policy/deploy/devif
    getQosDeviceListUrl: /qostrs/qos/device
    getQosDeviceInterfaceListUrl: /qostrs/qos/device/if

# 测试模式配置（开发环境可启用）
test:
  mode:
    enabled: true
    mockAdwanPlatform: true  # 启用模拟平台调用
```

#### 生产环境配置 (application-prod.yml)
```yaml
adwan:
  platform:
    # QoS相关接口路径（生产环境通过代理）
    addQosClassifierUrl: /adwan/proxy/qostrs/qos/classifier
    getQosClassifierListUrl: /adwan/proxy/qostrs/qos/classifier
    addQosBehaviorUrl: /adwan/proxy/qostrs/qos/behavior
    addQosPolicyUrl: /adwan/proxy/qostrs/qos/policy
    getQosPolicyListUrl: /adwan/proxy/qostrs/qos/policy
    deployQosPolicyUrl: /adwan/proxy/qostrs/qos/policy/deploy/devif
    getQosDeviceListUrl: /adwan/proxy/qostrs/qos/device
    getQosDeviceInterfaceListUrl: /adwan/proxy/qostrs/qos/device/if

# 测试模式配置（生产环境关闭）
test:
  mode:
    enabled: false
    mockAdwanPlatform: false
```

## 部署步骤

### 1. 代码部署

1. **编译打包**
```bash
./gradlew clean build -x test
```

2. **检查JAR包**
```bash
ls -la build/libs/gszh-srv6-api-*.jar
```

3. **部署到服务器**
```bash
# 停止现有服务
sudo systemctl stop gszh-srv6-api

# 备份现有JAR包
cp /opt/gszh-srv6-api/gszh-srv6-api.jar /opt/gszh-srv6-api/gszh-srv6-api.jar.backup

# 部署新JAR包
cp build/libs/gszh-srv6-api-*.jar /opt/gszh-srv6-api/gszh-srv6-api.jar

# 启动服务
sudo systemctl start gszh-srv6-api
```

### 2. 服务验证

#### 检查服务状态
```bash
# 检查服务状态
sudo systemctl status gszh-srv6-api

# 检查日志
tail -f /opt/gszh-srv6-api/logs/application.log
```

#### 健康检查
```bash
# 检查应用健康状态
curl http://localhost:28000/gszh-srv6-api/actuator/health

# 检查新接口是否可用
curl -X POST "http://localhost:28000/gszh-srv6-api/srv6/addSchedule" \
  -H "Content-Type: application/json" \
  -H "X-Access-Token: test-token" \
  -d '{"appScheduleId":1001,"appScheduleName":"测试","appGroupName":"测试应用组","networkIds":[1001]}'
```

### 3. 功能测试

#### 基本功能测试
```bash
# 测试新增调度策略
curl -X POST "http://localhost:28000/gszh-srv6-api/srv6/addSchedule" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your-access-token" \
  -d '{
    "appScheduleId": 2001,
    "appScheduleName": "生产测试调度策略",
    "appGroupName": "办公应用组",
    "drainageType": 0,
    "networkIds": [1001, 1002],
    "vpnId": 100
  }'
```

#### 错误场景测试
```bash
# 测试参数验证
curl -X POST "http://localhost:28000/gszh-srv6-api/srv6/addSchedule" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your-access-token" \
  -d '{
    "appScheduleName": "缺少ID的测试"
  }'

# 测试Token验证
curl -X POST "http://localhost:28000/gszh-srv6-api/srv6/addSchedule" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -d '{
    "appScheduleId": 2002,
    "appScheduleName": "无Token测试",
    "appGroupName": "测试应用组",
    "networkIds": [1001]
  }'
```

## 监控和维护

### 1. 日志监控

#### 关键日志位置
- 应用日志：`/opt/gszh-srv6-api/logs/application.log`
- 错误日志：`/opt/gszh-srv6-api/logs/error.log`

#### 日志监控命令
```bash
# 实时监控调度策略相关日志
tail -f /opt/gszh-srv6-api/logs/application.log | grep "调度策略"

# 监控错误日志
tail -f /opt/gszh-srv6-api/logs/application.log | grep "ERROR"

# 查看特定请求的完整日志
grep "req_20250708_001" /opt/gszh-srv6-api/logs/application.log
```

### 2. 性能监控

#### 数据库监控
```sql
-- 监控调度策略表的增长
SELECT COUNT(*) as total_schedules, 
       COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_schedules
FROM schedule_custom_info 
WHERE is_deleted = 0;

-- 监控最近的调度策略创建情况
SELECT app_schedule_id, app_schedule_name, create_time 
FROM schedule_custom_info 
WHERE is_deleted = 0 
ORDER BY create_time DESC 
LIMIT 10;
```

#### 接口性能监控
```bash
# 监控接口响应时间
grep "addSchedule" /opt/gszh-srv6-api/logs/application.log | grep "耗时"

# 监控平台接口调用情况
grep "平台接口" /opt/gszh-srv6-api/logs/application.log | tail -20
```

### 3. 故障排查

#### 常见问题及解决方案

1. **调度策略创建失败**
```bash
# 检查应用组是否存在
mysql -u user -p -e "SELECT * FROM app_group_custom_info WHERE app_group_name = '应用组名称';"

# 检查隧道组是否存在
mysql -u user -p -e "SELECT * FROM tunnel_group_custom_info WHERE te_group_id = 1001;"

# 检查平台接口连通性
curl -H "X-Access-Token: token" http://platform-url/qostrs/qos/classifier
```

2. **平台接口调用失败**
```bash
# 检查网络连通性
ping platform-host

# 检查Token是否有效
# 查看Token验证日志
grep "Token验证" /opt/gszh-srv6-api/logs/application.log | tail -10
```

3. **数据库连接问题**
```bash
# 检查数据库连接
mysql -u user -p -h db-host -e "SELECT 1;"

# 检查连接池状态
grep "HikariPool" /opt/gszh-srv6-api/logs/application.log | tail -10
```

## 回滚方案

### 1. 应用回滚
```bash
# 停止服务
sudo systemctl stop gszh-srv6-api

# 恢复备份
cp /opt/gszh-srv6-api/gszh-srv6-api.jar.backup /opt/gszh-srv6-api/gszh-srv6-api.jar

# 启动服务
sudo systemctl start gszh-srv6-api
```

### 2. 数据库回滚
```sql
-- 如果需要删除新增的表
DROP TABLE IF EXISTS schedule_custom_info;

-- 如果需要清理测试数据
DELETE FROM schedule_custom_info WHERE app_schedule_id >= 9000;
```

## 注意事项

1. **生产环境部署前必须关闭测试模式**
2. **确保数据库备份完成后再进行部署**
3. **部署后及时验证核心功能**
4. **监控系统资源使用情况**
5. **保留足够的回滚时间窗口**
