# 修改隧道组接口说明

## 接口概述

修改隧道组接口用于根据工商总行定义的隧道组参数修改现有隧道组，对应ADWAN平台的SRv6 Policy应用组。该接口实现了隧道组的完整修改功能，包括基本信息修改、隧道模板重新应用、作用域更新等。

## 接口信息

- **接口协议**: POST
- **接口地址**: `/srv6/modifyTeGroup`
- **Content-Type**: `application/json;charset=UTF-8`

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|----------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### Body参数
参考新增隧道组入参 `ModifyTeGroupRequestDTO`，字段与新增隧道组完全一致：

| 接口参数名 | 接口参数描述 | 是否必填 | 数据类型 | 示例值 |
|-----------|-------------|---------|---------|--------|
| id | 隧道组ID | Y | Integer | 1001 |
| name | 隧道组名称 | Y | String | "ModifiedGroup01" |
| isLoose | 选路方式（true:智能选路, false:自定义） | N | Boolean | true |
| balanceMode | 路径模式（1-主备模式 2-负载模式） | Y | Integer | 1 |
| balanceProportion | 负载模式下比例 | Y | String | "1:2" |
| scheduleStrategyId | 隧道模板ID | Y | Integer | 1 |
| color | 隧道组的color值 | Y | Integer | 150 |
| pathSize | 路径条数 | Y | Integer | 3 |
| planeRoutingType | 平面选路类型（1-同平面, 2-跨平面） | Y | Integer | 1 |
| isVirtualNet | 配置方式（true:按业务网络, false:按设备） | Y | Boolean | false |
| virtualNetId | 业务网络ID | N | String | "vnet001" |
| teGroupDcs | 隧道组源目的信息 | N | Array | 见下方说明 |
| hopLimit | 最大跳数 | N | Integer | 10 |

### teGroupDcs参数说明
当`isVirtualNet`为`false`时必填：

| 参数名称 | 参数描述 | 是否必填 | 数据类型 |
|---------|----------|---------|---------|
| srcDeviceIds | 源设备ID列表 | Y | Array<Long> |
| srcDeviceNames | 源设备名称列表 | Y | Array<String> |
| dstDeviceIds | 目的设备ID列表 | Y | Array<Long> |
| dstDeviceNames | 目的设备名称列表 | Y | Array<String> |

### 请求示例

#### 按设备配置修改
```json
{
  "id": 1001,
  "name": "ModifiedGroup01",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 150,
  "pathSize": 3,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [1001, 1002],
      "srcDeviceNames": ["Device-1001", "Device-1002"],
      "dstDeviceIds": [1003, 1004],
      "dstDeviceNames": ["Device-1003", "Device-1004"]
    }
  ],
  "hopLimit": 15
}
```

#### 按业务网络配置修改
```json
{
  "id": 1002,
  "name": "ModifiedGroup02",
  "isLoose": false,
  "balanceMode": 2,
  "balanceProportion": "2:3",
  "scheduleStrategyId": 2,
  "color": 200,
  "pathSize": 2,
  "planeRoutingType": 2,
  "isVirtualNet": true,
  "virtualNetId": "vnet002",
  "hopLimit": 20
}
```

## 响应体

接口响应体分为两种状态：

### 成功响应（状态为200时）
```json
{
  "requestId": "req_1234567890",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

### 失败响应
```json
{
  "requestId": "req_1234567890",
  "result": 0,
  "failReason": "异常原因描述",
  "optionField": null
}
```

| 字段名称 | 字段描述 |
|---------|----------|
| requestId | 请求id，保存，用于溯源 |
| result | 状态：0:失败，1：成功 |
| failReason | 异常状态下，返回异常原因 |
| optionField | 涉及的属性 |

## 重点逻辑

### 1. color和pathSize字段的特殊处理
修改隧道组时，`color`和`pathSize`字段有特殊的处理逻辑：
- **可以在请求中传入**：用户可以修改这两个字段的值
- **更新到本地定制表**：新的值会保存到`TeGroupCustomInfo`表中
- **不更新到平台应用组**：平台应用组的`color`和`pathSize`保持原有值不变

这样设计的目的是保持平台配置的稳定性，同时允许本地定制表记录用户的最新配置。

### 2. 隧道模板重新应用
修改隧道组时，与隧道模板相关的数据会重新查询隧道模板内容，进行填充：
- 带宽参数
- 丢包率参数
- 时延参数
- 抖动参数
- 其他QoS参数

### 3. 作用域修改
当传入的`teGroupDcs`参数不为null且不是空列表时，需要修改作用域：
- **先获取原始作用域**：查询现有作用域，如果获取失败则返回false
- **保持groupId和dataId**：保持应用组ID和作用域数据ID不变
- **生成全新NetworkInfo**：根据用户传入的TeGroupDcs生成全新的网络信息，不设置NetworkInfo的dataId
- **调用平台接口更新**：使用新的网络信息更新作用域

### 4. 候选路径配置
修改隧道组时，候选路径基于查询出来的原有路径进行赋值：
- **固定2条路径**：无论是主备模式还是负载模式，都固定处理2条候选路径
- **基于原有路径**：保持原有路径的配置参数（dataId、pathIndex、preference、affinityType等）
- **隧道模板参数**：涉及隧道模板的参数从隧道模板取值
- **请求参数覆盖**：hopLimit等参数优先使用请求中的值

#### 候选路径参数来源
- **保持不变的参数**：dataId、pathIndex、preference、affinityType、includeAffinityAll、excludeAffinity
- **从请求参数取值**：hopLimit（如果请求中提供）
- **从隧道模板取值**：
  - **preferColor**：候选路径1用模板的`upAllowLinkPriority`，路径2不配置
  - **includeAffinityAny**：对应隧道模板的`downAllowLinkPriority`字段
- **根据业务模式计算**：
  - **initSidPathNum**：负载模式取`pathSize`，主备模式默认1
  - **maxSidPathNum**：负载模式取`pathSize`，主备模式默认1

### 5. 平台接口调用顺序
1. 查询原有隧道组信息
2. 查询平台应用组详情
3. 查询隧道模板信息
4. 查询BFD模板ID
5. 构建更新请求并调用平台更新接口
6. 更新作用域（如果需要）
7. 更新本地数据库记录

## 平台接口调用

### 查询应用组详情
- **接口地址**: `POST /vas/srv6PolicyGroup/getSrv6PolicyGroupDetail`
- **请求参数**: `{"groupId": 应用组ID}`
- **认证方式**: Cookie认证，使用X-Subject-Token

### 更新应用组
- **接口地址**: `POST /vas/srv6PolicyGroup/updateSrv6PolicyGroup`
- **请求参数**: 更新应用组的完整参数
- **认证方式**: Cookie认证，使用X-Subject-Token

### 查询作用域
- **接口地址**: `POST /vas/srv6PolicyGroupNetwork/getCustomNetworkScope`
- **请求参数**: `{"groupId": 应用组ID, "pageSize": -1}`
- **认证方式**: Cookie认证，使用X-Subject-Token

### 更新作用域
- **接口地址**: `POST /vas/srv6PolicyGroupNetwork/updateNetwork`
- **请求参数**: 作用域网络配置参数
- **认证方式**: Cookie认证，使用X-Subject-Token

## 错误处理

### 常见错误情况
1. **隧道组不存在**: 当指定的隧道组ID在数据库中不存在时
2. **隧道模板不存在**: 当指定的隧道模板ID不存在时
3. **平台应用组查询失败**: 当无法查询到对应的平台应用组详情时
4. **平台更新失败**: 当调用平台更新接口失败时
5. **作用域更新失败**: 当更新作用域配置失败时
6. **参数验证失败**: 当请求参数不符合业务规则时

### 参数验证规则
- 按业务网络配置时，`virtualNetId`不能为空
- 按设备配置时，`teGroupDcs`不能为空
- 隧道组名称只能包含英文、数字、下划线、短横线，最大长度15个字符

## 注意事项

1. **事务性**: 整个修改操作在事务中执行，任何步骤失败都会回滚
2. **幂等性**: 相同参数的重复调用应该产生相同的结果
3. **数据一致性**: 本地数据库与平台数据保持一致
4. **日志记录**: 详细记录操作过程和错误信息，便于问题排查
