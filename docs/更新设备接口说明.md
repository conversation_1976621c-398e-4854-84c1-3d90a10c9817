# 更新设备接口说明

## 4.2.3 更新设备名称接口

### 接口描述
工商总行定义更新设备名称接口，定制开发根据总行下发的设备参数，对SDWAN设备修改设备名称接口进行封装，以满足总行api标准需要。通过设备ID查找定制库中的关联信息，先查询平台设备详细信息，然后调用平台更新接口修改设备名称。

### 接口信息
- **接口协议**: POST
- **接口地址**: `/srv6/updateDevice`
- **Content-Type**: `application/json;charset=UTF-8`

### 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### 请求体参数
| 参数名称 | 参数描述 | 是否必填 | 数据类型 | 备注 |
|---------|---------|---------|---------|------|
| deviceId | 设备ID，工行控制器统一设定唯一标识 | Y | Integer | |
| deviceName | 新设备名称 | Y | String | |
| isMarkDevice | 是否纳管状态(0/1) | Y | Integer | 兼容性保留，平台无该字段 |

### 接口逻辑流程
1. **参数验证**: 验证请求参数有效性
2. **Token验证**: 验证X-Access-Token有效性（测试模式可跳过）
3. **查询设备**: 根据deviceId查询device_custom_info表获取设备信息
4. **平台查询**: 调用ADWAN平台接口查询设备详细信息
5. **平台更新**: 调用ADWAN平台接口更新设备名称
6. **返回结果**: 返回更新操作结果

### 请求示例
```json
POST /gszh-api/srv6/updateDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here

{
    "deviceId": 1001,
    "deviceName": "更新后的设备名称",
    "isMarkDevice": 1
}
```

### 平台接口映射

接口会自动查询定制库和平台获取设备信息：

#### 查询流程：
1. 根据`deviceId`查询`device_custom_info`表
2. 获取`platform_node_id`字段值
3. 调用平台查询接口获取设备详细信息
4. 构建平台更新请求参数

#### 1. 平台查询设备接口
**URL**: `/nfm/physicalNetwork/node/getNodes`
**方法**: POST

**请求示例：**
```json
{
    "pageNum": 1,
    "pageSize": 15,
    "dataId": 293540759666689
}
```

**响应示例：**
```json
{
    "message": "SUCCESS",
    "code": "SUCCESS",
    "result": {
        "pageNum": 1,
        "pageSize": 5,
        "totalItem": 2,
        "totalPage": 1,
        "records": [
            {
                "dataId": 293540759666689,
                "nodeName": "********",
                "manageIp": "********",
                "nodeType": 1,
                "company": 0,
                "nodeRole": 65535
            }
        ]
    },
    "successful": true
}
```

#### 2. 平台更新设备接口
**URL**: `/nfm/physicalNetwork/node/updateNode`
**方法**: POST

**请求示例：**
```json
{
    "dataId": 282504583446529,
    "nodeName": "更新后的设备名称",
    "manageIp": "*************"
}
```

**响应示例：**
```json
{
    "message": "SUCCESS",
    "code": "SUCCESS",
    "result": null,
    "successful": true
}
```

### 响应体
接口响应体分为两种状态，状态为200时，响应体如下：

| 字段名称 | 字段描述 |
|---------|---------|
| requestId | 请求id，保存，用于溯源 |
| result | 状态：0:失败，1：成功 |
| failReason | 异常状态下，返回异常原因 |
| optionField | 涉及的属性 |

### 成功响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": "设备名称更新成功"
}
```

### 失败响应示例

#### 设备不存在
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "设备不存在",
    "optionField": null
}
```

#### 设备未关联平台节点ID
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "设备未关联平台节点ID",
    "optionField": null
}
```

#### 查询平台设备信息失败
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "查询平台设备信息失败",
    "optionField": null
}
```

#### 平台更新失败
```json
{
    "requestId": "abc123def456",
    "result": 0,
    "failReason": "调用平台接口更新设备名称失败",
    "optionField": null
}
```

## 测试模式说明

更新设备接口同样支持测试模式：

### 测试模式配置
```yaml
test:
  mode:
    enabled: true                    # 启用测试模式
    skipTokenValidation: true        # 跳过token验证
    mockAdwanPlatform: true         # 模拟ADWAN平台调用
```

### 测试模式特性
1. **跳过Token验证**: 当`skipTokenValidation=true`时，接口不会验证X-Access-Token的有效性
2. **模拟平台调用**: 当`mockAdwanPlatform=true`时，不会实际调用ADWAN平台接口，直接返回成功
3. **模拟查询响应**: 自动生成模拟的设备查询响应数据
4. **快速测试**: 避免了外部依赖，可以快速验证更新逻辑

### 测试示例

**Postman请求配置:**
- **方法**: POST
- **URL**: `http://localhost:28000/gszh-api/srv6/updateDevice`
- **Headers**: 
  ```
  Content-Type: application/json;charset=UTF-8
  X-Access-Token: test-token-123456
  ```
- **Body**:
  ```json
  {
      "deviceId": 1001,
      "deviceName": "测试更新的设备名称",
      "isMarkDevice": 1
  }
  ```

**前置条件**: 需要先通过新增设备接口创建设备记录

## 配置说明

### ADWAN平台配置
```yaml
adwan:
  platform:
    baseUrl: http://***********:30000
    getNodesUrl: /nfm/physicalNetwork/node/getNodes
    updateNodeUrl: /nfm/physicalNetwork/node/updateNode
```

## 注意事项

1. **接口日志记录**: 该接口受到`ApiLogAspect`切面影响，会自动记录API调用日志
2. **事务管理**: 更新操作使用事务管理，确保数据一致性
3. **设备存在性**: 只能更新已存在的设备，不存在的设备ID会返回失败
4. **平台关联**: 设备必须有关联的平台节点ID，否则无法进行更新
5. **两步操作**: 先查询平台设备信息获取管理IP，再进行更新操作
6. **测试模式安全**: 生产环境必须关闭测试模式
7. **参数验证**: 所有必填参数都会进行格式和内容验证
8. **名称限制**: 设备名称应符合平台的命名规范

## 错误码说明

| 错误信息 | 说明 | 解决方案 |
|---------|------|---------|
| 设备不存在 | 指定的deviceId在系统中不存在 | 检查deviceId是否正确 |
| 设备未关联平台节点ID | 设备记录中没有platform_node_id | 检查设备是否正确创建 |
| 查询平台设备信息失败 | 无法从平台获取设备详细信息 | 检查平台连接和设备状态 |
| 调用平台接口更新设备名称失败 | 平台更新操作失败 | 检查设备名称格式和平台状态 |
| Token无效或已过期 | 访问Token验证失败 | 重新获取有效的Token |
| 平台节点ID格式错误 | 存储的平台节点ID不是有效的数字 | 检查数据库中的platform_node_id字段 |
| 系统异常 | 服务器内部错误 | 查看系统日志排查具体原因 |

## 使用场景

1. **设备重命名**: 当设备物理位置或用途发生变化时
2. **标准化命名**: 统一设备命名规范时
3. **错误修正**: 修正设备创建时的命名错误
4. **批量管理**: 配合其他工具进行批量设备管理 