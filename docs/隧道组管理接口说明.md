# 隧道组管理接口说明

## 概述

隧道组管理接口提供了对SDWAN设备隧道组（对应ADWAN平台的SRv6 Policy应用组）的完整管理功能。本接口按照工商总行的API标准进行设计，实现了隧道组的新增、查询、修改、删除等功能。

## 接口列表

### 1. 新增隧道组

#### 接口描述
工商总行定义新增隧道组参数，定制开发根据总行下发的设备参数，对SDWAN设备隧道组接口进行封装。

#### 接口信息
- **请求方法**: POST
- **接口地址**: `/srv6/addTeGroup`
- **Content-Type**: `application/json;charset=UTF-8`

#### 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|----------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 获取到的token | Y |

#### 请求体参数
| 接口参数名 | 接口参数描述 | 是否必填 | 数据类型 | 平台参数名 | 平台参数描述 |
|-----------|-------------|---------|---------|------------|-------------|
| id | 隧道组ID | Y | String | 无 | 系统自动生成id，需单独建表保存该字段进行关联 |
| name | 隧道组名称【支持英文、数字、下划线、短横线，最大长度15个字符】 | Y | String | groupName | 应用组名称 |
| isLoose | 选路方式（true:智能选路, false:自定义） | N | Boolean | 无 | 本地入库 |
| balanceMode | 路径模式 1-主备模式 2-负载模式 | Y | Integer | candipathNum | 候选路径数，数据相反 |
| balanceProportion | 负载模式下比例 例如1:2或1:2:3 | Y | String | 无 | 本地入库 |
| scheduleStrategyId | 隧道模板ID | Y | Integer | 无 | 关联隧道模板获取参数配置 |
| color | 隧道组的color值 | Y | Integer | colorId | 用于隧道policy中的颜色id |
| pathSize | 路径条数 | Y | Integer | 无 | 本地入库 |
| planeRoutingType | 平面选路类型（1-同平面, 2-跨平面）【选路方式为智能选路时填写】 | Y | Integer | 无 | 本地入库 |
| isVirtualNet | 配置方式，是否按业务网络（true:按业务网络, false:按设备） | Y | Boolean | 无 | 本地入库 |
| virtualNetId | 业务网络ID 【配置方式为按业务网络时填写】 | N | String | 无 | 本地入库 |
| teGroupDcs | 隧道组源目的信息 【配置方式为按设备时填写】 | N | Array | 无 | 绑定到应用组作用域中 |
| ├─ srcDeviceIds | 源设备ID列表 | Y | Array[Long] | 无 | 绑定到应用组作用域中 |
| ├─ srcDeviceNames | 源设备名称列表 | Y | Array[String] | 无 | 绑定到应用组作用域中 |
| ├─ dstDeviceIds | 目的设备ID列表 | Y | Array[Long] | 无 | 绑定到应用组作用域中 |
| └─ dstDeviceNames | 目的设备名称列表 | Y | Array[String] | 无 | 绑定到应用组作用域中 |
| hopLimit | 最大跳数，取值范围1~********** | N | Integer | hopLimit | 对应Policy-候选路径约束下的最大跳数字段 |

#### 请求示例
```json
{
  "id": "tegroup001",
  "name": "TestGroup01",
  "isLoose": true,
  "balanceMode": 2,
  "balanceProportion": "1:2",
  "scheduleStrategyId": 1,
  "color": 100,
  "pathSize": 2,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [1001, 1002],
      "srcDeviceNames": ["Device-1001", "Device-1002"],
      "dstDeviceIds": [1003, 1004],
      "dstDeviceNames": ["Device-1003", "Device-1004"]
    }
  ],
  "hopLimit": 10
}
```

#### 响应体
接口响应体分为两种状态：

**成功响应（状态为200时）：**
```json
{
  "requestId": "req_1234567890",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

**失败响应：**
```json
{
  "requestId": "req_1234567890",
  "result": 0,
  "failReason": "异常原因描述",
  "optionField": null
}
```

#### 响应字段说明
| 字段名称 | 字段描述 | 数据类型 |
|---------|----------|---------|
| requestId | 请求id，保存，用于溯源 | String |
| result | 状态：0:失败，1：成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 涉及的属性 | Object |

## 业务流程

### 新增隧道组流程
1. **参数验证**：验证请求参数的合法性和完整性
2. **ID重复检查**：检查隧道组ID是否已存在
3. **隧道模板查询**：根据scheduleStrategyId查询隧道模板信息
4. **ServiceClass分配**：从ServiceClass池中分配正向和反向Policy的ServiceClass
5. **平台接口调用**：调用ADWAN平台创建SRv6 Policy应用组
6. **数据保存**：保存隧道组信息到本地数据库
7. **作用域处理**：如果是按设备配置，创建隧道组作用域并更新平台网络配置

### ServiceClass管理
- **池化管理**：ServiceClass采用池化管理，范围1-127
- **自动分配**：每个隧道组自动分配两个ServiceClass（正向和反向Policy各一个）
- **循环使用**：当ServiceClass超过127时从1开始，优先使用已回收的ServiceClass
- **自动回收**：删除隧道组时自动回收相关的ServiceClass

### 平台参数映射

#### SRv6 Policy应用组基本信息
- **groupName**: 隧道组名称
- **description**: "隧道组：" + 隧道组名称
- **networkingModel**: 固定值3
- **candipathNum**: 主备模式=1(单向)，负载模式=2(双向)
- **direction**: 固定值2
- **coRouted**: 固定值true
- **calcLimit**: 固定值"METRIC"

#### Policy参数映射
- **colorId**: 来自接口color字段
- **serviceClass**: 自动分配的ServiceClass
- **isPositive**: true（正向）/false（反向）
- **bandwidth**: 来自隧道模板的bandwidth字段（单位转换：bps → kbps，除以1000）
- **priority**: 来自隧道模板的slaId字段，映射规则：EF(5)、AF4(4)、AF3(3)、AF2(2)、AF1(1)、BE(0)
- **maxPacketLossRate**: 来自隧道模板的packetLossRate字段
- **maxJitter**: 来自隧道模板的networkJitter字段
- **maxDelay**: 来自隧道模板的delayTime字段

#### 候选路径约束
- **路径1（pathIndex=0）**: 使用隧道模板的upAllowLinkPriority作为preferColor
- **路径2（pathIndex=1）**: 使用隧道模板的downAllowLinkPriority作为includeAffinityAny
- **hopLimit**: 来自接口hopLimit字段
- **initSidPathNum/maxSidPathNum**: 负载模式使用pathSize，主备模式固定为1

## 错误码说明

| 错误场景 | 错误信息 |
|---------|----------|
| 隧道组ID重复 | 隧道组ID已存在 |
| 隧道模板不存在 | 隧道模板不存在 |
| ServiceClass资源不足 | ServiceClass资源不足 |
| 平台接口调用失败 | 平台创建应用组失败 |
| 数据保存失败 | 保存隧道组信息失败 |
| 作用域创建失败 | 创建隧道组作用域失败 |
| 参数验证失败 | 具体的参数错误信息 |

## 注意事项

1. **配置方式**：
   - 按业务网络配置时，必须提供virtualNetId
   - 按设备配置时，必须提供teGroupDcs且不能为空

2. **设备信息**：
   - 源设备ID和名称列表长度必须一致
   - 目的设备ID和名称列表长度必须一致

3. **隧道组名称**：
   - 只能包含英文、数字、下划线、短横线
   - 最大长度15个字符

4. **ServiceClass限制**：
   - 系统最多支持127个并发隧道组
   - 每个隧道组占用2个ServiceClass（正向和反向）

5. **事务处理**：
   - 整个流程使用事务管理，任何步骤失败都会回滚
   - ServiceClass分配失败时会自动回收已分配的资源

## 测试建议

1. **正常场景测试**：
   - 按设备配置新增隧道组
   - 按业务网络配置新增隧道组
   - 负载模式多路径配置

2. **异常场景测试**：
   - 隧道组ID重复
   - 参数格式错误
   - 缺少必要参数
   - 设备信息不匹配

3. **边界条件测试**：
   - ServiceClass资源耗尽
   - 网络异常导致平台接口调用失败
   - 数据库连接异常 