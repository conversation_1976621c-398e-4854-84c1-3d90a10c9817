# 调度策略管理接口说明

## 概述

工商总行调度策略管理接口，提供调度策略的新增功能。接口遵循总行API标准，通过定制开发封装ADWAN平台QoS相关接口，实现调度策略的完整生命周期管理。

## 接口列表

| 接口名称 | 接口方法 | 接口地址 | 功能描述 |
|---------|---------|---------|---------|
| 新增调度策略 | POST | `/srv6/addSchedule` | 新增调度策略到平台 |

## 业务流程

### 调度策略新增流程

```
接口请求 → 参数验证 → 流分类处理 → 流行为处理 → 流策略处理 → 策略部署 → 数据保存
```

#### 1. 流分类处理
- 根据应用组名称获取应用列表
- 为每个应用生成ACL匹配规则
- 调用平台新增流分类接口
- 查询获取流分类ID

#### 2. 流行为处理
- 根据隧道组ID获取隧道模板信息
- 生成三个remark规则（remarkType: 1, 16, 17）
- 调用平台新增流行为接口
- 获取流行为ID

#### 3. 流策略处理
- 解析隧道组作用域信息
- 为每个设备IP生成流策略
- 支持策略合并（同名策略追加cbpair）
- 调用平台新增流策略接口

#### 4. 策略部署
- 查询设备信息获取设备UUID
- 查询设备接口信息获取接口UUID
- 调用平台部署流策略到设备接口

## 接口详情

### 新增调度策略

**接口地址：** `POST /srv6/addSchedule`

**请求头：**
```
Content-Type: application/json;charset=UTF-8
X-Access-Token: {token}
```

**请求参数：**
```json
{
    "appScheduleId": 1001,
    "appScheduleName": "测试调度策略",
    "appGroupName": "办公应用组",
    "drainageType": 0,
    "networkIds": [1001, 1002],
    "vpnId": 100
}
```

**参数说明：**
- `appScheduleId`: 调度策略唯一标识ID（必填）
- `appScheduleName`: 策略名称（必填）
- `appGroupName`: 应用组名称（必填）
- `drainageType`: 引流类型，0:五元组、1:dscp、4:Vpn（可选）
- `networkIds`: 隧道组ID列表（必填）
- `vpnId`: VPN ID（可选）

**响应示例：**
```json
{
    "requestId": "req_123456",
    "result": 1,
    "failReason": null,
    "optionField": null
}
```

## 数据映射

### SLA ID映射规则
- EF → 46
- AF4 → 34
- AF3 → 26
- AF2 → 18
- AF1 → 10
- BE → 0

### Remark类型说明
- remarkType=1: 使用隧道模板的sla_id转换值
- remarkType=16: 使用隧道组的positive_service_class
- remarkType=17: 使用隧道模板的sla_id转换值

## 数据库设计

### schedule_custom_info表
存储调度策略的定制信息，包括：
- 基本信息：调度策略ID、名称、应用组名称等
- 关联信息：流分类ID、流行为ID、流策略信息等
- 配置信息：引流类型、隧道组ID列表、VPN ID等

## 平台接口依赖

### QoS相关接口
1. **流分类接口**
   - 新增流分类：`POST /qostrs/qos/classifier`
   - 查询流分类列表：`GET /qostrs/qos/classifier`

2. **流行为接口**
   - 新增流行为：`POST /qostrs/qos/behavior`

3. **流策略接口**
   - 新增流策略：`POST /qostrs/qos/policy`
   - 查询流策略列表：`GET /qostrs/qos/policy`
   - 部署流策略：`POST /qostrs/qos/policy/deploy/devif`

4. **设备接口**
   - 查询QoS设备列表：`GET /qostrs/qos/device`
   - 查询设备接口列表：`GET /qostrs/qos/device/if`

## 错误处理

### 常见错误场景
1. **参数验证失败**
   - 应用组不存在
   - 隧道组不存在
   - 调度策略ID已存在

2. **平台接口调用失败**
   - 流分类创建失败
   - 流行为创建失败
   - 流策略创建失败
   - 策略部署失败

3. **数据处理异常**
   - 隧道组作用域信息解析失败
   - 设备信息查询失败
   - 接口信息查询失败

## 测试模式

系统提供测试模式，方便开发和调试：
- 模拟所有平台接口调用
- 返回随机生成的ID和数据
- 支持完整的业务流程验证

## 配置说明

### 开发环境配置
```yaml
adwan:
  platform:
    addQosClassifierUrl: /qostrs/qos/classifier
    getQosClassifierListUrl: /qostrs/qos/classifier
    # ... 其他QoS接口配置
```

### 生产环境配置
```yaml
adwan:
  platform:
    addQosClassifierUrl: /adwan/proxy/qostrs/qos/classifier
    getQosClassifierListUrl: /adwan/proxy/qostrs/qos/classifier
    # ... 其他QoS接口配置（带代理前缀）
```

## 注意事项

1. **事务管理**：整个调度策略创建过程使用事务管理，确保数据一致性
2. **错误恢复**：部分步骤失败不影响主流程，但会记录警告日志
3. **性能考虑**：大量设备的策略部署可能耗时较长，建议异步处理
4. **安全性**：所有接口都需要有效的X-Access-Token认证
