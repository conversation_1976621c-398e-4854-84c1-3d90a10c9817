# 候选路径配置说明

## 修改前后对比

### 修改前（根据BalanceMode判断）
```java
// 根据路径模式构建约束
int pathCount = request.getBalanceMode() == 1 ? 2 : 1; // 主备模式2条路径，负载模式1条路径
```

**问题**：
- 主备模式（balanceMode=1）创建2条路径
- 负载模式（balanceMode=2）创建1条路径
- 路径数量不固定，依赖业务模式
- 没有基于原有配置进行更新

### 修改后（最简的候选路径配置）
```java
// 直接遍历原有候选路径列表
List<GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupCandidatePathVO> originalConstraints = originalParam.getCandiPathConstraints();

if (originalConstraints != null) {
    for (GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupCandidatePathVO originalConstraint : originalConstraints) {
        UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity constraint =
            new UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity();

        // 基于原有路径配置
        constraint.setDataId(originalConstraint.getDataId());
        constraint.setPathIndex(originalConstraint.getPathIndex());
        // ... 其他配置

        constraints.add(constraint);
    }
}
```

**优势**：
- 实现最简洁，一个循环搞定
- 直接遍历原有候选路径，无需查找匹配
- 保持原有路径数量和配置
- 逻辑清晰易懂

## 候选路径详细配置

### 基于原有路径的配置逻辑
```java
if (originalPath != null) {
    // 保持原有路径的所有配置
    constraint.setDataId(originalPath.getDataId());
    constraint.setPathIndex(originalPath.getPathIndex());
    constraint.setPreference(originalPath.getPreference());
    constraint.setAffinityType(originalPath.getAffinityType());
    constraint.setPreferColor(originalPath.getPreferColor());
    constraint.setIncludeAffinityAny(originalPath.getIncludeAffinityAny());
    constraint.setIncludeAffinityAll(originalPath.getIncludeAffinityAll());
    constraint.setExcludeAffinity(originalPath.getExcludeAffinity());
    constraint.setInitSidPathNum(originalPath.getInitSidPathNum());
    constraint.setMaxSidPathNum(originalPath.getMaxSidPathNum());

    // hopLimit从请求参数取值，如果没有则保持原有值
    if (request.getHopLimit() != null) {
        constraint.setHopLimit(request.getHopLimit());
    }
} else {
    // 没有原有路径时使用默认值
    constraint.setPathIndex(i);
    constraint.setPreference(100 - i * 10);
    constraint.setAffinityType(0);
    constraint.setInitSidPathNum(1);
    constraint.setMaxSidPathNum(8);
    constraint.setHopLimit(request.getHopLimit() != null ? request.getHopLimit() : 255);
}
```

## 参数来源说明

### 保持原有配置的参数
- **dataId**：候选路径主键，用于更新操作
- **pathIndex**：候选路径索引（0或1）
- **preference**：候选路径优先级
- **affinityType**：亲和属性着色类型
- **preferColor**：优选链路着色
- **includeAffinityAny/All**：亲和属性包含/必选
- **excludeAffinity**：亲和属性排除
- **initSidPathNum**：初始分段个数
- **maxSidPathNum**：最大分段个数

### 从请求参数取值的参数
- **hopLimit**：最大跳数（如果请求中提供）

### 从隧道模板取值的参数
- 目前暂无，但架构支持扩展
- 可以根据需要添加其他隧道模板相关参数

## 适用场景

### 主备模式（balanceMode=1）
- 路径1作为主路径（优先级100）
- 路径2作为备路径（优先级90）
- 正常情况下使用主路径，主路径故障时切换到备路径

### 负载模式（balanceMode=2）
- 路径1和路径2都可以承载流量
- 根据负载均衡算法分配流量
- 两条路径提供更好的带宽利用率

## 技术优势

1. **配置连续性**：基于原有路径配置，保持平台配置的连续性
2. **智能更新**：只更新需要变更的参数，其他参数保持稳定
3. **模板支持**：支持从隧道模板重新应用相关参数
4. **参数覆盖**：支持请求参数覆盖特定字段
5. **故障恢复**：固定2条路径提供网络冗余
6. **平台兼容**：符合SDWAN平台的标准配置模式

## 实际效果

修改后，候选路径配置将具有：
- ✅ **智能继承**：基于原有路径配置进行更新
- ✅ **参数分层**：不同参数来源清晰分离
- ✅ **配置稳定**：保持平台配置的稳定性
- ✅ **灵活扩展**：支持隧道模板参数的扩展应用
- ✅ **精确控制**：支持特定参数的精确覆盖

这样的设计既保持了配置的连续性，又支持了灵活的参数更新，更符合企业级应用的实际需求。
