# 删除调度策略接口实现说明

## 概述

根据工商总行需求，实现了删除调度策略接口，通过删除流分类、流行为、流策略的方式进行删除，满足总行API标准需要。

## 接口信息

### 接口协议
- **方法**: DELETE
- **地址**: `/srv6/delSchedule/{appScheduleId}`

### 请求参数
- **Header参数**:
  - `Content-Type`: application/json;charset=UTF-8 (必填)
  - `X-Access-Token`: 认证token (必填)
- **Path参数**:
  - `appScheduleId`: 调度策略ID (必填)

### 响应体
```json
{
    "requestId": "请求ID",
    "result": 1,  // 0:失败，1：成功
    "failReason": "异常状态下，返回异常原因"
}
```

## 实现方案

### 删除逻辑流程
1. **参数验证**: 检查调度策略是否存在
2. **删除流分类**: 调用平台删除流分类接口
3. **删除流行为**: 调用平台删除流行为接口  
4. **删除流策略**: 根据是否有其他关联决定删除或更新
5. **清理本地数据**: 软删除本地记录

### 流策略删除策略
基于方案一的设计思路：
- 直接遍历调度策略的流策略记录，无需分组
- 根据设备IP构造流策略名称，按名称查询平台流策略
- 在查询结果中找到匹配policyId的流策略
- 过滤出需要保留的cbpair（不属于当前调度策略的）
- 如果没有剩余cbpair，删除整个流策略
- 如果有剩余cbpair，更新流策略移除对应的cbpair

### 判断逻辑
```java
private boolean shouldRemoveCbPair(CbPair cbPair,
                                  Integer classifierId, Integer behaviorId) {
    // 只有当classifier_id和behavior_id都匹配时才删除
    return cbPair.getClassifierId().equals(classifierId) &&
           cbPair.getBehaviorId().equals(behaviorId);
}
```

## 平台接口调用

### 删除流分类接口
- **URL**: `DELETE /qostrs/qos/classifier/all`
- **参数**: `classifierId` (query参数)
- **响应**: `{"output":"QJkVWPfpC8sRUeuBb9BVQoB1aNgwZsOW"}`

### 删除流行为接口  
- **URL**: `DELETE /qostrs/qos/behavior/all`
- **参数**: `policyId` (query参数)
- **响应**: 无响应体，返回200状态即代表成功

### 删除流策略接口
- **URL**: `DELETE /qostrs/qos/policy/all`
- **参数**: `policyId` (query参数)  
- **响应**: 无响应体，返回200状态即代表成功

### 修改流策略接口
- **URL**: `PUT /qostrs/qos/policy/all`
- **说明**: 使用已有的更新接口

## 数据表设计

### 独立存储表
- `schedule_classifier_info`: 流分类信息表
- `schedule_behavior_info`: 流行为信息表
- `schedule_policy_info`: 流策略信息表

### 数据关联
每个调度策略的流分类、流行为、流策略信息都单独存储，包含：
- 调度策略ID
- 平台资源ID（classifierId、behaviorId、policyId）
- 资源名称
- 设备信息（仅流策略表）

## 错误处理

### 容错机制
- 平台接口调用失败时记录警告日志但不中断流程
- 确保本地数据清理不依赖于平台操作成功
- 使用事务管理确保数据一致性

### 常见错误场景
1. **调度策略不存在**: 返回失败响应
2. **平台接口调用失败**: 记录警告日志继续执行
3. **数据库操作异常**: 回滚事务返回系统异常

## 测试模式支持

### 模拟方法
- `mockDeleteQosClassifier`: 模拟删除流分类
- `mockDeleteQosBehavior`: 模拟删除流行为  
- `mockDeleteQosPolicy`: 模拟删除流策略

### 配置开关
通过 `testModeConfig.isMockAdwanPlatform()` 控制是否使用模拟模式

## 关键特性

### 1. 数据一致性
- 始终以平台接口返回的数据为准
- 本地数据仅用于确定删除范围和构造查询条件

### 2. 精确匹配
- 按流策略名称查询，在结果中匹配policyId
- 删除cbpair时同时匹配classifierId和behaviorId
- 避免误删其他调度策略的资源

### 3. 简化处理
- 直接遍历流策略记录，无需复杂的分组逻辑
- 每个调度策略在每个设备上只有一条流策略记录
- 支持流策略的部分删除（更新）和完全删除
- 策略部署逻辑也已简化，移除了不必要的分组操作

## 使用示例

```bash
# 删除调度策略
curl -X DELETE \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your-token" \
  "http://localhost:8080/srv6/delSchedule/1001"
```

## 注意事项

1. **删除顺序**: 先删除流分类和流行为，最后处理流策略
2. **数据依赖**: 流策略可能被多个调度策略共享，需要谨慎处理
3. **平台同步**: 确保本地数据与平台数据的一致性
4. **事务管理**: 整个删除过程使用事务管理，确保原子性
