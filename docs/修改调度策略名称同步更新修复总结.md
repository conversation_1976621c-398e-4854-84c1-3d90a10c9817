# 修改调度策略名称同步更新修复总结

## 问题背景

用户发现了一个重要问题：当修改调度策略时，如果修改了`appScheduleName`，流分类和流行为的名称没有同步更新，但它们的名称都是根据`appScheduleName`生成的。

## 问题分析

### 命名规则
根据`ScheduleConstants.NamingRule`常量定义：
- **流分类名称**：`appScheduleName + "_classifier"`
- **流行为名称**：`appScheduleName + "_behavior"`
- **流策略名称**：`"policy_" + 设备IP`

### 问题现象
当修改调度策略的`appScheduleName`时：
1. ✅ 主表`ScheduleCustomInfo.appScheduleName`被正确更新
2. ❌ 流分类名称仍然使用旧的名称
3. ❌ 流行为名称仍然使用旧的名称
4. ❌ 平台接口调用时传递的是旧名称
5. ❌ 本地数据库中的名称字段没有同步更新

### 影响范围
- **平台一致性**：平台上的流分类和流行为名称与调度策略名称不匹配
- **查询问题**：基于名称的查询可能失败
- **管理混乱**：无法通过名称正确识别流分类和流行为的归属

## 修复方案

### ✅ 修改流分类名称同步

**修改前：**
```java
// 使用旧的流分类名称
updateRequest.setClassifierName(classifierInfo.getClassifierName());
```

**修改后：**
```java
// 生成新的流分类名称（基于新的appScheduleName）
String newClassifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;

// 使用新的流分类名称
updateRequest.setClassifierName(newClassifierName);

// 更新本地数据库中的流分类名称
classifierInfo.setClassifierName(newClassifierName);
scheduleClassifierInfoMapper.updateById(classifierInfo);
```

### ✅ 修改流行为名称同步

**修改前：**
```java
// 使用旧的流行为名称
updateRequest.setBehaviorName(existingBehavior.getBehaviorName());
```

**修改后：**
```java
// 生成新的流行为名称（基于新的appScheduleName）
String newBehaviorName = request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX;

// 使用新的流行为名称
updateRequest.setBehaviorName(newBehaviorName);

// 更新本地数据库中的流行为名称
behaviorInfo.setBehaviorName(newBehaviorName);
scheduleBehaviorInfoMapper.updateById(behaviorInfo);
```

### ✅ 修改主表名称同步

**修改前：**
```java
// 只更新基本字段，没有更新流分类和流行为名称
existingSchedule.setAppScheduleName(request.getAppScheduleName());
existingSchedule.setAppGroupName(request.getAppGroupName());
// ...
scheduleCustomInfoMapper.updateById(existingSchedule);
```

**修改后：**
```java
// 生成新的名称
String newClassifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;
String newBehaviorName = request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX;

// 更新所有相关字段
existingSchedule.setAppScheduleName(request.getAppScheduleName());
existingSchedule.setAppGroupName(request.getAppGroupName());
// ...
existingSchedule.setClassifierName(newClassifierName);  // 更新流分类名称
existingSchedule.setBehaviorName(newBehaviorName);      // 更新流行为名称
scheduleCustomInfoMapper.updateById(existingSchedule);
```

### ✅ 修改buildBehaviorUpdateRequest方法

**修改前：**
```java
private PlatformQosBehaviorUpdateRequestDTO buildBehaviorUpdateRequest(
        PlatformQosBehaviorDetailResponseDTO.Output existingBehavior,
        List<PlatformQosBehaviorUpdateRequestDTO.RemarkInfo> newRemarkList) {
    
    updateRequest.setBehaviorName(existingBehavior.getBehaviorName()); // 使用旧名称
}
```

**修改后：**
```java
private PlatformQosBehaviorUpdateRequestDTO buildBehaviorUpdateRequest(
        PlatformQosBehaviorDetailResponseDTO.Output existingBehavior,
        List<PlatformQosBehaviorUpdateRequestDTO.RemarkInfo> newRemarkList,
        String newBehaviorName) {  // 新增参数
    
    updateRequest.setBehaviorName(newBehaviorName);  // 使用新名称
}
```

## 修复详情

### 1. 流分类名称同步（modifyClassifier方法）
```java
// 第1533-1558行
// 6. 生成新的流分类名称（基于新的appScheduleName）
String newClassifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;

// 7. 构建修改流分类请求
updateRequest.setClassifierId(classifierInfo.getClassifierId());
updateRequest.setClassifierName(newClassifierName);  // 使用新的流分类名称

// 8. 调用平台接口修改流分类
boolean updateSuccess = platformApiService.updateQosClassifier(updateRequest);
if (updateSuccess) {
    // 9. 更新本地数据库中的流分类名称
    classifierInfo.setClassifierName(newClassifierName);
    scheduleClassifierInfoMapper.updateById(classifierInfo);
}
```

### 2. 流行为名称同步（modifyBehavior方法）
```java
// 第1620-1640行
// 4. 生成新的流行为名称（基于新的appScheduleName）
String newBehaviorName = request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX;

// 5. 基于查询到的现有配置构建修改请求，保持原有配置不变，只修改remarkList和名称
PlatformQosBehaviorUpdateRequestDTO updateRequest = buildBehaviorUpdateRequest(detailResponse.getOutput(), remarkList, newBehaviorName);

// 6. 调用平台接口修改流行为
boolean updateSuccess = platformApiService.updateQosBehavior(updateRequest);
if (updateSuccess) {
    // 7. 更新本地数据库中的流行为名称
    behaviorInfo.setBehaviorName(newBehaviorName);
    scheduleBehaviorInfoMapper.updateById(behaviorInfo);
}
```

### 3. 主表名称同步（modifySchedule方法）
```java
// 第157-168行
// 5. 更新调度策略主记录（包括流分类和流行为名称）
String newClassifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;
String newBehaviorName = request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX;

existingSchedule.setAppScheduleName(request.getAppScheduleName());
existingSchedule.setAppGroupName(request.getAppGroupName());
existingSchedule.setDrainageType(request.getDrainageType());
existingSchedule.setNetworkIds(JSON.toJSONString(request.getNetworkIds()));
existingSchedule.setVpnId(request.getVpnId());
existingSchedule.setClassifierName(newClassifierName);  // 更新流分类名称
existingSchedule.setBehaviorName(newBehaviorName);      // 更新流行为名称
scheduleCustomInfoMapper.updateById(existingSchedule);
```

## 修复效果

### 名称同步示例

**修改前：**
- 调度策略名称：`新的调度策略名称`
- 流分类名称：`旧的调度策略名称_classifier`（不一致）
- 流行为名称：`旧的调度策略名称_behavior`（不一致）

**修改后：**
- 调度策略名称：`新的调度策略名称`
- 流分类名称：`新的调度策略名称_classifier`（一致）
- 流行为名称：`新的调度策略名称_behavior`（一致）

### 数据库更新范围
1. **ScheduleCustomInfo表**
   - `app_schedule_name` - 调度策略名称
   - `classifier_name` - 流分类名称
   - `behavior_name` - 流行为名称

2. **ScheduleClassifierInfo表**
   - `classifier_name` - 流分类名称

3. **ScheduleBehaviorInfo表**
   - `behavior_name` - 流行为名称

### 平台接口更新
1. **流分类修改接口**：传递新的流分类名称
2. **流行为修改接口**：传递新的流行为名称

## 测试验证

更新了测试文件验证修复效果：
- `ModifyScheduleServiceTest.java` - 添加了`testModifyScheduleNameSynchronization()`测试方法

### 测试要点
1. 验证流分类名称基于新的appScheduleName生成
2. 验证流行为名称基于新的appScheduleName生成
3. 验证平台接口调用时传递新的名称
4. 验证本地数据库中所有相关表的名称字段都被更新

## 总结

通过修复修改调度策略时的名称同步问题：

1. **确保了命名一致性** - 流分类和流行为名称与调度策略名称保持一致
2. **修复了平台同步** - 平台上的名称与本地数据库保持一致
3. **完善了数据更新** - 所有相关表的名称字段都被正确更新
4. **提高了可维护性** - 通过名称可以正确识别流分类和流行为的归属
5. **避免了查询问题** - 基于名称的查询能够正常工作

修复后的代码确保了修改调度策略时所有相关组件的名称都能正确同步更新。
