# 查询设备型号接口说明

## 接口描述
工商总行定义查询设备型号接口，定制开发根据总行下发的要求，对SDWAN设备型号数据进行封装，以满足总行api标准需要。

## 实现方案
定义get接口，通过读取指定的配置文件中的json数据，获取到设备型号数据。

## 接口位置
**控制器**: DeviceController.java (设备管理控制器)  
**服务实现**: DeviceServiceImpl.java

## 接口信息
- **接口协议**: GET
- **接口地址**: `/srv6/queryDeviceModel`

## 请求头参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

## 请求参数
无请求参数

## 请求示例
```bash
GET /gszh-api/srv6/queryDeviceModel
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token-here
```

## 响应体
| 字段名称 | 字段描述 |
|---------|---------|
| RequestId | 请求id，保存，用于溯源 |
| deviceModelList | 设备型号数组 |

### deviceModelList字段说明
| 字段名称 | 字段类型 | 字段描述 |
|---------|---------|---------|
| deviceModel | String | 设备型号 |
| optionField | Object | 厂商扩展字段（当前为空） |

## 响应示例
```json
{
  "requestId": "req-123456789",
  "deviceModelList": [
    {
      "deviceModel": "S12500-X"
    },
    {
      "deviceModel": "S5560-EI"
    },
    {
      "deviceModel": "S5130-EI"
    }
  ]
}
```

## 配置说明

### 环境配置文件
**开发环境** (`application-dev.yml`) 和 **生产环境** (`application-prod.yml`) 配置：
```yaml
# 设备型号配置
device:
  model:
    config-file: config/device-models.json
```

### JSON配置文件格式
```json
[
  "S12500-X",
  "S5560-EI", 
  "S5130-EI"
]
``` 