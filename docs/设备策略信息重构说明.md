# 设备策略信息重构说明

## 重构背景

### 原有问题

在原有的调度策略实现中，存在一个关键问题：

1. **设备ID混淆**：`getDeviceInterfaceNames`方法需要使用定制表的设备ID来查询`lan_custom_info`表
2. **信息丢失**：在流策略处理过程中，原始的设备ID信息被丢失，只保留了设备IP
3. **查询错误**：无法正确查询设备接口信息，影响策略部署

### 根本原因

调度策略处理涉及两套设备ID体系：
- **定制表设备ID**：用于查询本地定制表（如`lan_custom_info`、`device_custom_info`）
- **平台设备ID**：用于调用ADWAN平台接口

原有实现在处理过程中丢失了定制表设备ID，导致无法正确查询本地数据。

## 重构方案

### 1. 创建DevicePolicyInfoDTO

创建一个完整的设备策略信息DTO，包含所有必要的ID信息：

```java
@Data
public class DevicePolicyInfoDTO {
    private Integer deviceId;        // 定制表设备ID
    private String platformNodeId;   // 平台节点ID
    private String deviceIp;         // 设备IP地址
    private Integer policyId;        // 流策略ID
}
```

### 2. 修改processPolicy方法

**修改前：**
```java
private Map<String, Integer> processPolicy(AddScheduleRequestDTO request, Integer classifierId, Integer behaviorId)
```

**修改后：**
```java
private List<DevicePolicyInfoDTO> processPolicy(AddScheduleRequestDTO request, Integer classifierId, Integer behaviorId)
```

### 3. 完整的数据流转

```
teGroupDcs解析 → 设备ID列表
    ↓
设备ID → 查询device_custom_info → platform_node_id
    ↓
platform_node_id → 调用getNodes接口 → 设备IP
    ↓
设备IP → 创建/更新流策略 → 策略ID
    ↓
DevicePolicyInfoDTO(设备ID, platform_node_id, 设备IP, 策略ID)
```

## 重构详情

### 1. processPolicy方法重构

#### 修改前
```java
Map<String, Integer> policyInfo = new HashMap<>();
// ... 处理逻辑
policyInfo.put(deviceIp, policyId);
return policyInfo;
```

#### 修改后
```java
List<DevicePolicyInfoDTO> devicePolicyList = new ArrayList<>();
// ... 处理逻辑
DevicePolicyInfoDTO devicePolicyInfo = new DevicePolicyInfoDTO(
    deviceId, platformNodeId, deviceIp, policyId);
devicePolicyList.add(devicePolicyInfo);
return devicePolicyList;
```

### 2. deployPolicies方法重构

#### 修改前
```java
private boolean deployPolicies(AddScheduleRequestDTO request, Map<String, Integer> policyInfo) {
    for (Map.Entry<String, Integer> entry : policyInfo.entrySet()) {
        String deviceIp = entry.getKey();
        Integer policyId = entry.getValue();
        // 无法获取设备ID，导致接口查询失败
        List<String> interfaceNames = getDeviceInterfaceNames(deviceIp); // 错误
    }
}
```

#### 修改后
```java
private boolean deployPolicies(AddScheduleRequestDTO request, List<DevicePolicyInfoDTO> devicePolicyList) {
    for (DevicePolicyInfoDTO devicePolicy : devicePolicyList) {
        Integer deviceId = devicePolicy.getDeviceId();
        String deviceIp = devicePolicy.getDeviceIp();
        Integer policyId = devicePolicy.getPolicyId();
        // 使用正确的设备ID查询接口信息
        List<String> interfaceNames = getDeviceInterfaceNames(deviceId); // 正确
    }
}
```

### 3. getDeviceInterfaceNames方法重构

#### 修改前
```java
private List<String> getDeviceInterfaceNames(String deviceIp) {
    // 无法根据设备IP查询lan_custom_info表
    // 只能返回默认值
    return Arrays.asList("GigabitEthernet1/0");
}
```

#### 修改后
```java
private List<String> getDeviceInterfaceNames(Integer deviceId) {
    // 根据设备ID查询lan_custom_info表
    LambdaQueryWrapper<LanCustomInfo> query = new LambdaQueryWrapper<>();
    query.eq(LanCustomInfo::getDeviceId, deviceId);
    List<LanCustomInfo> lanInfoList = lanCustomInfoMapper.selectList(query);
    
    return lanInfoList.stream()
        .map(LanCustomInfo::getInterfaceName)
        .filter(Objects::nonNull)
        .distinct()
        .collect(Collectors.toList());
}
```

### 4. 主流程调用调整

#### 修改前
```java
Map<String, Integer> policyInfo = processPolicy(request, classifierId, behaviorId);
deployPolicies(request, policyInfo);
saveScheduleInfo(request, ..., policyInfo);
```

#### 修改后
```java
List<DevicePolicyInfoDTO> devicePolicyList = processPolicy(request, classifierId, behaviorId);
deployPolicies(request, devicePolicyList);

// 为保存方法转换为Map格式
Map<String, Integer> policyInfo = devicePolicyList.stream()
    .collect(Collectors.toMap(DevicePolicyInfoDTO::getDeviceIp, DevicePolicyInfoDTO::getPolicyId));
saveScheduleInfo(request, ..., policyInfo);
```

## 重构优势

### 1. 数据完整性
- **保留所有关键信息**：设备ID、平台节点ID、设备IP、策略ID
- **避免信息丢失**：在整个处理流程中保持数据完整性

### 2. 业务正确性
- **正确的接口查询**：使用定制表设备ID查询`lan_custom_info`
- **准确的策略部署**：基于真实的接口信息进行部署

### 3. 代码清晰性
- **明确的数据结构**：`DevicePolicyInfoDTO`清晰表达了数据关系
- **类型安全**：避免了Map结构的类型不安全问题

### 4. 可维护性
- **易于扩展**：可以方便地添加新的设备相关信息
- **易于调试**：完整的数据结构便于问题排查

## 测试验证

### 1. 单元测试
```java
@Test
public void testDevicePolicyInfoDTO() {
    DevicePolicyInfoDTO devicePolicy = new DevicePolicyInfoDTO(100, "12345", "*************", 200);
    assertEquals(Integer.valueOf(100), devicePolicy.getDeviceId());
    assertEquals("12345", devicePolicy.getPlatformNodeId());
    assertEquals("*************", devicePolicy.getDeviceIp());
    assertEquals(Integer.valueOf(200), devicePolicy.getPolicyId());
}
```

### 2. 集成测试
```java
@Test
public void testDevicePolicyListProcessing() {
    List<DevicePolicyInfoDTO> devicePolicyList = Arrays.asList(
        new DevicePolicyInfoDTO(100, "12345", "*************", 200),
        new DevicePolicyInfoDTO(101, "12346", "*************", 201)
    );
    
    Map<String, Integer> policyInfo = devicePolicyList.stream()
        .collect(Collectors.toMap(DevicePolicyInfoDTO::getDeviceIp, DevicePolicyInfoDTO::getPolicyId));
    
    assertEquals(2, policyInfo.size());
    assertEquals(Integer.valueOf(200), policyInfo.get("*************"));
}
```

## 数据库依赖

### 1. lan_custom_info表结构
```sql
-- 确保lan_custom_info表包含必要字段
SELECT device_id, interface_name FROM lan_custom_info WHERE device_id = ?;
```

### 2. device_custom_info表结构
```sql
-- 确保device_custom_info表包含platform_node_id字段
SELECT device_id, platform_node_id FROM device_custom_info WHERE device_id = ?;
```

## 部署注意事项

### 1. 数据准备
- 确保`lan_custom_info`表有完整的设备接口数据
- 确保`device_custom_info`表的`platform_node_id`字段数据正确

### 2. 测试验证
- 验证设备接口查询功能
- 测试完整的策略部署流程
- 检查日志中的设备ID和接口信息

### 3. 性能监控
- 监控`lan_custom_info`表的查询性能
- 关注设备接口数量对部署时间的影响

## 总结

这次重构解决了调度策略管理中的一个关键问题：设备ID信息的正确传递和使用。通过引入`DevicePolicyInfoDTO`，我们：

1. **保持了数据完整性**：在整个处理流程中保留所有必要的设备信息
2. **修正了业务逻辑**：使用正确的设备ID查询本地定制表
3. **提高了代码质量**：使用强类型DTO替代Map结构
4. **增强了可维护性**：清晰的数据结构和处理流程

这个重构对于确保调度策略正确部署到设备接口具有重要意义。
