# 配置文件部署说明

## 外部配置文件管理

### 概述
为了实现配置与代码分离，在生产环境中，JSON配置文件应放置在项目外部，便于运维管理和配置更新，无需重新打包应用。

### Windows开发环境部署

#### 1. 创建外部配置目录
在项目根目录下创建外部配置目录：
```cmd
# 在项目根目录创建外部配置目录
mkdir external-config

# 复制配置文件（如果从现有项目迁移）
copy "src\main\resources\config\device-models.json" "external-config\device-models.json"
```

#### 2. 配置应用使用外部配置
修改 `application-dev.yml` 和 `application-prod.yml`：
```yaml
device:
  model:
    config-file: D:\IdeaProjects\other\h3c\gongshangzonghang\gszh-api\external-config\device-models.json
```

### Linux生产环境部署

#### 1. 创建配置目录
```bash
# 创建应用配置目录
sudo mkdir -p /opt/gszh-api/config

# 设置目录权限
sudo chown -R gszh:gszh /opt/gszh-api/config
sudo chmod 755 /opt/gszh-api/config
```

#### 2. 复制配置文件
将项目外部配置目录中的配置文件复制到生产环境：
```bash
# 从项目的external-config目录复制
cp external-config/device-models.json /opt/gszh-api/config/
```

#### 3. 验证配置文件
确保配置文件格式正确：
```bash
# 检查JSON格式
cat /opt/gszh-api/config/device-models.json | python -m json.tool
```

### 配置文件路径规则

应用会按以下优先级读取配置文件：

1. **外部文件系统路径**：
   - Unix系统：以 `/` 开头的绝对路径
   - Windows系统：如 `C:\path\to\file` 的绝对路径
   
2. **Classpath路径**（兜底方案）：
   - 如果外部文件不存在，会尝试从项目内classpath读取
   - 注意：当前项目已将配置文件完全外部化，classpath中不再包含配置文件

### 环境配置

#### 开发环境 (application-dev.yml)
```yaml
device:
  model:
    # 开发环境也使用外部文件系统路径
    config-file: D:\IdeaProjects\other\h3c\gongshangzonghang\gszh-api\external-config\device-models.json
```

#### 生产环境 (application-prod.yml)
```yaml
device:
  model:
    # Linux服务器路径示例
    config-file: /opt/gszh-api/config/device-models.json
    
    # Windows开发环境路径示例
    # config-file: D:\IdeaProjects\other\h3c\gongshangzonghang\gszh-api\external-config\device-models.json
```

### 运行时配置更新

#### 热更新配置
生产环境中可以直接修改外部配置文件，应用会在下次调用相关接口时读取最新配置：

```bash
# 编辑配置文件
sudo vi /opt/gszh-api/config/device-models.json

# 验证JSON格式
cat /opt/gszh-api/config/device-models.json | python -m json.tool
```

#### 启动参数覆盖
也可以通过JVM启动参数动态指定配置文件路径：
```bash
java -jar gszh-api.jar --device.model.config-file=/custom/path/device-models.json
```

### 日志说明

应用启动时会在日志中显示使用的配置文件路径：
- `使用外部文件系统配置文件：/opt/gszh-api/config/device-models.json`
- `使用classpath配置文件：config/device-models.json`

### 安全注意事项

1. **文件权限**：确保配置文件只有应用用户可读写
   ```bash
   chmod 640 /opt/gszh-api/config/device-models.json
   ```

2. **备份策略**：定期备份外部配置文件
   ```bash
   cp /opt/gszh-api/config/device-models.json /opt/gszh-api/config/device-models.json.bak.$(date +%Y%m%d)
   ```

3. **版本控制**：重要配置变更应记录在版本控制系统中

### 当前项目配置状态

当前项目已完成外部配置文件迁移：
- ✅ 配置文件位置：`external-config/device-models.json`
- ✅ 开发环境配置：使用外部文件系统路径
- ✅ 生产环境配置：使用外部文件系统路径  
- ✅ 原classpath配置文件已删除

### 故障排除

#### 配置文件不存在
如果外部配置文件不存在，应用会记录错误信息。由于当前项目已完全外部化配置，需要确保外部配置文件存在且可访问。

#### JSON格式错误
如果配置文件JSON格式错误，接口会返回空列表，并在日志中记录错误信息。

#### 权限问题
确保应用进程用户有读取配置文件的权限：
```bash
sudo -u gszh cat /opt/gszh-api/config/device-models.json
``` 