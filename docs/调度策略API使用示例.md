# 调度策略API使用示例

## 接口调用示例

### 1. 新增调度策略

#### 请求示例

```bash
curl -X POST "http://localhost:28000/gszh-srv6-api/srv6/addSchedule" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your-access-token" \
  -d '{
    "appScheduleId": 1001,
    "appScheduleName": "办公应用调度策略",
    "appGroupName": "办公应用组",
    "drainageType": 0,
    "networkIds": [1001, 1002],
    "vpnId": 100
  }'
```

#### 成功响应示例

```json
{
  "requestId": "req_20250708_001",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

#### 失败响应示例

```json
{
  "requestId": "req_20250708_002",
  "result": 0,
  "failReason": "应用组不存在：办公应用组",
  "optionField": null
}
```

## 业务场景示例

### 场景1：为办公应用配置调度策略

**背景：** 需要为办公应用组配置调度策略，确保办公流量通过指定的隧道组传输。

**步骤：**

1. **准备数据**
   - 确保应用组"办公应用组"已存在
   - 确保隧道组1001、1002已创建
   - 确保隧道组已配置相应的隧道模板

2. **调用接口**
```json
{
  "appScheduleId": 2001,
  "appScheduleName": "办公应用高优先级调度",
  "appGroupName": "办公应用组",
  "drainageType": 0,
  "networkIds": [1001, 1002]
}
```

3. **预期结果**
   - 创建流分类"办公应用高优先级调度"
   - 创建流行为"办公应用高优先级调度_behavior"
   - 为隧道组作用域内的每个设备创建流策略
   - 部署策略到设备接口

### 场景2：为VPN流量配置调度策略

**背景：** 需要为特定VPN的流量配置专用调度策略。

**步骤：**

1. **调用接口**
```json
{
  "appScheduleId": 2002,
  "appScheduleName": "VPN专线调度策略",
  "appGroupName": "VPN应用组",
  "drainageType": 4,
  "networkIds": [1003],
  "vpnId": 200
}
```

2. **预期结果**
   - 针对VPN流量创建专用的QoS策略
   - 确保VPN流量按照指定路径传输

## 错误处理示例

### 1. 参数验证错误

**请求：**
```json
{
  "appScheduleName": "测试策略"
  // 缺少必填字段 appScheduleId
}
```

**响应：**
```json
{
  "requestId": "req_20250708_003",
  "result": 0,
  "failReason": "调度策略唯一标识ID不能为空",
  "optionField": null
}
```

### 2. 业务逻辑错误

**请求：**
```json
{
  "appScheduleId": 1001,
  "appScheduleName": "重复策略",
  "appGroupName": "不存在的应用组",
  "networkIds": [9999]
}
```

**响应：**
```json
{
  "requestId": "req_20250708_004",
  "result": 0,
  "failReason": "应用组不存在：不存在的应用组",
  "optionField": null
}
```

### 3. 系统异常

**响应：**
```json
{
  "requestId": "req_20250708_005",
  "result": 0,
  "failReason": "系统异常：数据库连接超时",
  "optionField": null
}
```

## 测试环境使用

### 启用测试模式

在开发环境中，可以启用测试模式来模拟平台接口调用：

```yaml
test:
  mode:
    enabled: true
    mockAdwanPlatform: true
```

### 测试数据准备

1. **创建测试应用组**
```sql
INSERT INTO app_group_custom_info (app_group_id, app_group_name) 
VALUES (1, '测试应用组');
```

2. **创建测试应用**
```sql
INSERT INTO app_custom_info (app_id, app_name, app_group_id) 
VALUES (1, '测试应用', 1);
```

3. **创建测试隧道组**
```sql
INSERT INTO tunnel_group_custom_info (te_group_id, name, schedule_strategy_id, te_group_dcs) 
VALUES (1001, '测试隧道组', 1, '[{"srcDeviceIds":["192.168.1.1"],"dstDeviceIds":["192.168.1.2"]}]');
```

4. **创建测试隧道模板**
```sql
INSERT INTO tunnel_template_custom_info (strategy_id, strategy_name, sla_id) 
VALUES (1, '测试模板', 'EF');
```

## 监控和日志

### 日志查看

调度策略创建过程会产生详细的日志，可以通过以下方式查看：

```bash
# 查看应用日志
tail -f logs/application.log | grep "调度策略"

# 查看特定请求的日志
grep "req_20250708_001" logs/application.log
```

### 关键日志示例

```
2025-07-08 10:30:01 INFO  [ScheduleController] 接收到新增调度策略请求，请求ID：req_001，调度策略ID：1001
2025-07-08 10:30:02 INFO  [ScheduleServiceImpl] 开始处理流分类，应用组名称：办公应用组
2025-07-08 10:30:03 INFO  [ScheduleServiceImpl] 开始处理流行为，隧道组ID列表：[1001, 1002]
2025-07-08 10:30:04 INFO  [ScheduleServiceImpl] 开始处理流策略，隧道组ID列表：[1001, 1002]
2025-07-08 10:30:05 INFO  [ScheduleServiceImpl] 开始部署流策略到设备接口
2025-07-08 10:30:06 INFO  [ScheduleServiceImpl] 新增调度策略成功，请求ID：req_001，调度策略ID：1001
```

## 性能考虑

### 批量操作优化

对于大量设备的场景，建议：

1. **分批处理**：将设备列表分批处理，避免单次请求过大
2. **异步处理**：考虑将策略部署改为异步处理
3. **缓存优化**：缓存设备信息和接口信息查询结果

### 超时设置

```yaml
# 建议的超时配置
spring:
  transaction:
    default-timeout: 300  # 5分钟事务超时
```

## 故障排查

### 常见问题及解决方案

1. **流分类创建失败**
   - 检查应用组是否存在
   - 检查应用列表是否为空
   - 检查平台接口连通性

2. **流行为创建失败**
   - 检查隧道组配置
   - 检查隧道模板配置
   - 验证SLA ID映射

3. **策略部署失败**
   - 检查设备在线状态
   - 验证接口配置
   - 检查网络连通性

### 调试技巧

1. **启用详细日志**
```yaml
logging:
  level:
    com.h3c.dzkf.service.impl.ScheduleServiceImpl: DEBUG
```

2. **使用测试模式验证流程**
3. **分步骤验证每个环节**
