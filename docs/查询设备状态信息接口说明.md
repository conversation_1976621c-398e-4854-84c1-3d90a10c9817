# 查询设备状态信息接口说明

## 接口概述
查询指定站点及其所有子站点下的设备状态统计信息，返回设备总数、在线数量和离线数量。

## 接口协议
**方法**: GET  
**路径**: `/srv6/queryDeviceStatus`

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 | 示例值 |
|---------|---------|---------|--------|
| Content-Type | 内容类型 | Y | application/json;charset=UTF-8 |
| X-Access-Token | 访问令牌 | Y | 4.1获取到的token |

### Query参数
| 参数名称 | 参数描述 | 是否必填 | 数据类型 | 示例值 |
|---------|---------|---------|---------|--------|
| siteId | 站点ID | Y | Integer | 1 |

## 响应结果

### 成功响应 (HTTP 200)
```json
{
    "RequestId": "req_20241209_001",
    "total": 15,
    "onLine": 12,
    "offLine": 3
}
```

### 响应字段说明
| 字段名称 | 字段描述 | 数据类型 | 示例值 |
|---------|---------|---------|--------|
| RequestId | 请求ID，用于溯源 | String | req_20241209_001 |
| total | 设备总数 | Integer | 15 |
| onLine | 在线设备数量 | Integer | 12 |
| offLine | 离线设备数量 | Integer | 3 |

## 业务逻辑

### 1. 站点查询逻辑
- 根据传入的siteId，递归查询该站点及所有子站点
- 查询这些站点下的所有设备信息

### 2. 设备状态统计逻辑
- 提取设备的平台节点ID，批量调用ADWAN平台接口查询设备状态
- 根据平台返回的nodeDisplay字段判断设备状态：
  - nodeDisplay = 3：设备离线
  - 其他值：设备在线
- 对于没有平台节点ID或查询失败的设备，按离线处理

### 3. 平台接口调用
- **接口地址**: `/nfm/physicalNetwork/node/getNodes`
- **调用方式**: 批量查询（使用dataIds参数）
- **关键字段**: nodeDisplay（设备节点颜色状态）
- **性能优化**: 单次批量查询所有设备，避免逐个调用接口

### 4. nodeDisplay状态说明
| 值 | 颜色 | 状态描述 |
|----|------|----------|
| 1 | 绿色 | 设备在线无告警 |
| 2 | 蓝色 | 设备在线有警告告警 |
| 3 | 灰色 | 设备下线 |
| 4 | 橙色 | 设备在线有次要告警 |
| 5 | 红色 | 设备在线有重要告警 |

## 错误处理

### 参数验证失败
- 当siteId为空时，返回全零统计数据
- 当Token验证失败时，返回全零统计数据

### 系统异常
- 当发生系统异常时，返回全零统计数据
- 异常信息记录在日志中，可通过RequestId进行溯源

## 接口调用示例

### 请求示例
```bash
curl -X GET "http://localhost:8080/srv6/queryDeviceStatus?siteId=1" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your_access_token_here"
```

### 响应示例
```json
{
    "RequestId": "req_20241209_001",
    "total": 8,
    "onLine": 6,
    "offLine": 2
}
```

## 性能考虑

### 查询优化
- 使用递归查询获取所有子站点，避免多次数据库查询
- 使用批量接口查询所有设备状态，单次调用获取全部结果
- 对平台接口调用进行异常处理，确保统计结果的准确性

### 性能提升
- **批量查询**: 相比逐个查询，大幅减少网络请求次数
- **减少延迟**: 单次接口调用代替多次调用，显著降低总体响应时间
- **提高吞吐**: 适合大量设备场景，性能表现更加稳定

## 注意事项

1. **Token验证**: 接口需要有效的X-Access-Token，在测试模式下可跳过验证
2. **站点递归**: 会查询指定站点及其所有层级的子站点
3. **异常容错**: 单个设备查询失败不会影响其他设备的统计
4. **日志记录**: 所有关键操作都有详细的日志记录，便于问题排查 