# 查询隧道组接口实现说明

## 概述

根据工商总行需求，实现了《查询隧道组》接口，用于查询所有隧道组信息，包含部署状态。该接口对SDWAN的查询应用组接口进行封装，以满足总行API标准需要。

## 接口信息

### 基本信息
- **接口协议**: POST
- **接口地址**: `/srv6/getTeGroup`
- **功能描述**: 查询所有隧道组信息，包含部署状态

### 请求参数
- **Header参数**:
  - `Content-Type`: application/json;charset=UTF-8 (必填)
  - `X-Access-Token`: 4.1获取到的token (必填)
- **Body参数**: 无需请求参数

### 响应体结构

#### 成功响应
```json
{
  "requestId": "req_123456789",
  "result": 1,
  "failReason": null,
  "teGroups": [
    {
      "id": 1001,
      "name": "MyTeGroup",
      "isLoose": true,
      "balanceMode": 1,
      "balanceProportion": "1:2",
      "scheduleStrategyId": 1,
      "color": 100,
      "pathSize": 2,
      "planeRoutingType": 1,
      "isVirtualNet": false,
      "virtualNetId": 1001,
      "teGroupDcs": [
        {
          "srcDeviceIds": [1001, 1002],
          "srcDeviceNames": ["Device-1001", "Device-1002"],
          "dstDeviceIds": [1003, 1004],
          "dstDeviceNames": ["Device-1003", "Device-1004"]
        }
      ],
      "mandatoryNodeIds": [],
      "excludeNodeIds": [],
      "mandatoryLinkIds": [],
      "excludeLinkIds": [],
      "teTunnelManualList": [],
      "hopLimit": 10,
      "deployState": 3
    }
  ]
}
```

#### 失败响应
```json
{
  "requestId": "req_123456789",
  "result": 0,
  "failReason": "查询隧道组信息失败：具体错误信息",
  "teGroups": null
}
```

## 实现逻辑

### 1. 数据查询流程
1. **查询定制库**: 从`tunnel_group_custom_info`表查询所有隧道组信息
2. **单条查询部署状态**: 针对每个隧道组单独调用平台接口查询部署状态
3. **解析JSON配置**: 从数据库JSON字段中直接解析设备配置信息
4. **数据转换**: 将数据库实体转换为响应DTO格式

### 2. 部署状态映射规则
根据平台应用组的`deployStatus`字段进行状态转换：

| 平台状态 | 状态值 | 说明 |
|---------|--------|------|
| TO_BE_DEPLOY | 1 | 未部署 |
| DEPLOY_SUCCESS | 3 | 部署成功 |
| DEPLOY_FAILED | 4 | 部署失败 |
| TO_BE_DELETE | 5 | 待删除 |
| 其他/null | 1 | 默认未部署 |

### 3. JSON配置直接解析
- 从数据库`teGroupDcs`字段（JSON格式）中直接解析为响应DTO类型
- 利用字段完全一致的优势，避免中间转换步骤
- 原样返回数据库中保存的完整设备配置信息
- 包含设备ID和设备名称的完整数据

## 代码实现

### 1. 新增文件

#### GetTeGroupResponseDTO.java
```java
// 查询隧道组响应DTO，包含完整的响应结构定义
// 位置：src/main/java/com/h3c/dzkf/entity/dto/GetTeGroupResponseDTO.java
```

### 2. 修改文件

#### TeGroupController.java
- 新增`getTeGroup()`方法
- 添加相关导入

#### TeGroupService.java
- 新增`getTeGroup(String requestId)`方法定义

#### TeGroupServiceImpl.java
- 实现`getTeGroup(String requestId)`方法
- 新增辅助方法：
  - `getPlatformGroupDeployStatus()`: 单条查询平台应用组部署状态
  - `convertToTeGroupInfo()`: 转换隧道组实体为响应DTO
  - `convertDeployStatus()`: 转换部署状态

## 关键特性

### 1. 完整数据查询
- 查询所有隧道组信息，无分页限制
- 包含隧道组基本信息、配置信息、部署状态等完整数据

### 2. 部署状态单条查询
- 针对每个隧道组单独调用平台接口查询部署状态
- 避免一次性获取大量数据造成的性能问题
- 确保状态信息的准确性和时效性

### 3. 设备信息完整返回
- 直接返回数据库中保存的完整设备配置信息
- 包含设备ID和设备名称的完整数据

### 4. 异常处理
- 完善的异常处理机制
- 详细的错误日志记录
- 友好的错误信息返回

### 5. JSON数据解析优化
- 直接将JSON解析为目标响应DTO类型，避免中间转换
- 利用字段完全一致的优势，提高解析效率
- 原样返回数据库中保存的数据，保持数据一致性
- 类型转换处理（如String到Integer的转换）
- 空值和异常数据的安全处理
- 详细的错误日志记录便于问题排查

## 测试验证

### 测试文件
- `test_get_te_group.http`: HTTP接口测试文件

### 测试场景
1. **正常查询**: 查询存在隧道组数据的情况
2. **空数据查询**: 查询不存在隧道组数据的情况
3. **异常处理**: 验证各种异常情况的处理

## 注意事项

### 1. 性能考虑
- 当隧道组数量较多时，可能需要考虑分页查询
- 采用单条查询部署状态，避免批量查询的性能问题
- 平台接口调用的超时处理和异常处理

### 2. 数据一致性
- 定制库与平台数据的一致性保证
- 设备信息的同步更新

### 3. 扩展性
- 预留了其他约束字段的扩展空间
- 支持后续功能增强

## 总结

该接口成功实现了工商总行的查询隧道组需求，提供了完整的隧道组信息查询功能，包括：
- 完整的隧道组基本信息
- 单条查询的实时部署状态信息
- 从JSON字段直接解析的设备配置信息
- 原样返回数据库保存的完整数据
- 完善的异常处理机制

接口设计遵循了现有的代码规范和架构模式，采用了简洁高效的实现方式，确保了代码的可维护性和扩展性。
