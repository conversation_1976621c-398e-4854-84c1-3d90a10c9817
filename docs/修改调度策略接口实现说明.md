# 修改调度策略接口实现说明

## 概述

根据工商总行需求，实现了修改调度策略接口，通过修改流分类、流行为、流策略的方式进行更新，满足总行API标准需要。

## 接口信息

### 接口协议
- **方法**: PUT
- **地址**: `/srv6/modifySchedule`

### 请求参数
- **Header参数**:
  - `Content-Type`: application/json;charset=UTF-8 (必填)
  - `X-Access-Token`: 认证token (必填)
- **Body参数**:
  - `appScheduleId`: 调度策略唯一标识ID (必填)
  - `appScheduleName`: 策略名称 (必填)
  - `appGroupName`: 应用组名称 (必填)
  - `drainageType`: 引流类型 0:五元组、1:dscp、4:Vpn (可选)
  - `networkIds`: 隧道组ID列表 (必填)
  - `vpnId`: VPN ID (可选)

### 响应体
```json
{
    "requestId": "请求ID",
    "result": 1,  // 0:失败，1：成功
    "failReason": "异常状态下，返回异常原因"
}
```

## 核心实现逻辑

### 1. 修改流分类
- **查询现有配置**：使用 `GET /qostrs/qos/classifier` 接口查询现有流分类详情
- 根据应用组名称查询应用组信息
- 查询应用组下的所有应用
- 重新生成ACL匹配规则列表(matchList)，删除原有规则，重新生成
- 调用平台接口 `PUT /qostrs/qos/classifier/all` 修改流分类

### 2. 修改流行为
- **查询现有配置**：使用 `GET /qostrs/qos/behavior/detail` 接口查询现有流行为详情
- 根据隧道组ID重新获取service class和slaId数据
- 查询隧道组和隧道模板信息
- 基于现有配置构建更新请求，保持原有配置不变，只修改remarkList
- 调用平台接口 `PUT /qostrs/qos/behavior/all` 修改流行为

### 3. 修改流策略
- 获取新的设备列表（基于隧道组ID）
- 获取现有设备列表（从策略名称中提取IP）
- 计算设备变化：删除、新增、保持不变
- 对于删除的设备：查询策略是否被其他调度策略关联，决定删除整个策略或只删除cbpair
- 对于新增的设备：检查策略是否存在，决定创建新策略或添加cbpair
- 对于保持不变的设备：无需处理

## 平台接口调用

### 查询流分类接口
- **URL**: `GET /qostrs/qos/classifier?start=0&size=10&desc=false&isWithRule=true&classifierName=调度策略名称_classifier`
- **说明**: 查询现有流分类配置，获取当前matchList规则
- **响应**: 返回流分类列表及规则详情

### 查询流行为详情接口
- **URL**: `GET /qostrs/qos/behavior/detail?behaviorId=流行为id`
- **说明**: 查询现有流行为配置，获取完整的配置信息
- **响应**: 返回流行为详细配置，包括car、gts、queue、policy、filter、account、redirect、mirror、remarkList

### 查询流策略接口
- **URL**: `GET /qostrs/qos/policy/detail/rule`
- **说明**: 查询流策略的cbpair详情，用于策略修改时的对比分析

### 修改流分类接口
- **URL**: `PUT /qostrs/qos/classifier/all`
- **请求体**: 包含classifierId、classifierName、description、logic、matchList
- **响应**: 无响应体，返回200状态即代表成功

### 修改流行为接口
- **URL**: `PUT /qostrs/qos/behavior/all`
- **请求体**: 包含behaviorId、behaviorName、description、car、gts、queue、policy、filter、account、redirect、mirror、remarkList
- **响应**: 无响应体，返回200状态即代表成功

### 修改流策略接口
- **URL**: `PUT /qostrs/qos/policy/all`
- **说明**: 使用已有的更新接口

## 新增文件清单

### 请求DTO
1. `ModifyScheduleRequestDTO.java` - 修改调度策略请求DTO

### 平台接口DTO
2. `PlatformQosClassifierUpdateRequestDTO.java` - 修改流分类请求DTO
3. `PlatformQosBehaviorUpdateRequestDTO.java` - 修改流行为请求DTO
4. `PlatformQosBehaviorDetailRequestDTO.java` - 查询流行为详情请求DTO
5. `PlatformQosBehaviorDetailResponseDTO.java` - 查询流行为详情响应DTO

### 测试文件
6. `test_modify_schedule.http` - HTTP测试文件

### 文档文件
7. `修改调度策略接口实现说明.md` - 本文档

## 修改的文件清单

### 核心业务文件
1. `ScheduleController.java` - 添加PUT接口
2. `ScheduleService.java` - 添加修改调度策略方法
3. `ScheduleServiceImpl.java` - 实现修改调度策略业务逻辑
4. `PlatformApiService.java` - 添加修改流分类和流行为接口
5. `PlatformApiServiceImpl.java` - 实现修改流分类和流行为接口

### 配置文件
6. `application-dev.yml` - 添加修改流分类和流行为的URL配置

## 关键技术点

### 1. 充分利用查询接口
- **流分类修改**：先查询现有流分类配置，了解当前matchList规则，然后重新生成
- **流行为修改**：先查询现有流行为详情，保持原有配置不变，只修改需要变更的部分
- **流策略修改**：使用现有的查询流策略详情接口，精确分析cbpair变化

### 2. 设备变化分析
通过比较新旧设备列表，精确计算需要删除、新增和保持不变的设备，确保策略修改的准确性。

### 3. 策略共享处理
正确处理流策略被多个调度策略共享的情况，通过cbpair级别的操作避免误删除。

### 4. 配置保持策略
在修改流行为时，通过查询接口获取完整的现有配置，确保只修改必要的部分，其他配置保持不变。

### 5. 事务管理
使用`@Transactional`注解确保修改操作的原子性，出现异常时能够正确回滚。

### 6. 错误处理
完善的异常处理机制，确保在任何步骤失败时都能给出明确的错误信息。

## 测试验证

使用提供的HTTP测试文件进行接口测试：
```bash
# 测试文件位置
test_modify_schedule.http
```

## 注意事项

1. 修改操作会影响现有的流分类、流行为和流策略配置
2. 设备变化分析基于策略名称中的IP地址提取，确保命名规范的一致性
3. 隧道组作用域数据解析依赖TeGroupDcsDTO类的正确字段映射
4. 平台接口调用失败时会记录详细日志，便于问题排查
