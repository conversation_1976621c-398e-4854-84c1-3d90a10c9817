# ADWAN平台字段映射说明

## 字段对照表

| 工行接口字段 | ADWAN平台字段 | 类型 | 是否必选 | 映射说明 |
|-------------|---------------|------|---------|----------|
| deviceId(Integer) | - | - | 必选 | 设备ID，存储在自定义表中 |
| deviceName | nodeName | String | 必选 | 设备名称，1~255个字符 |
| deviceIp | manageIp | String | 必选 | 设备管理IP地址，支持IPv4和IPv6格式 |
| - | manageMac | String | 可选 | 设备管理IP所在接口的MAC地址（暂不使用） |
| - | nodeType | Integer | 可选 | 设备类型，固定为1（真实设备） |
| deviceManufacturer | company | Integer | 可选 | 设备厂商信息，见下表 |
| deviceRole | - | - | 可选 | 存储在自定义表中，ADWAN平台的nodeRole固定为2(PE) |
| deviceSn | serialNumbers | String | 可选 | 设备序列号，1~31个字符 |
| - | sysObjectOid | String | 可选 | 设备OID信息（暂为空） |
| - | softVersion | String | 可选 | 设备版本号（暂为空） |
| deviceModel | nodeModel | String | 可选 | 设备型号 |
| - | netconfTemplateId | Long | 必选 | NETCONF模板ID，固定为0 |
| - | snmpTemplateId | Long | 必选 | SNMP模板ID，固定为0 |
| - | nodeLevel | Integer | 可选 | 设备层级，固定为1（一级） |
| - | mplsAsNumber | Long | 可选 | MPLS AS号，仅agg角色可设置 |
| deviceSiteId(Integer) | - | - | 必选 | 站点ID，存储在自定义表中 |
| deviceSite | - | - | 必选 | 站点名称，存储在自定义表中 |
| deviceRole | - | - | 可选 | 设备角色(hub/spoke/agg)，存储在自定义表中 |
| isRR(Boolean) | - | - | 必选 | 是否RR设备，存储在自定义表中 |
| devicePlaneId(Integer) | - | - | 可选 | 平面ID，存储在自定义表中 |
| deviceIpv6 | - | - | 可选 | IPv6地址，存储在自定义表中 |
| deviceGroup | - | - | 可选 | 设备组，存储在自定义表中 |

## 设备厂商映射 (company)

| 工行参数值 | ADWAN平台值 | 说明 |
|-----------|-------------|------|
| H3C | 0 | H3C厂商 |
| HP | 1 | HP厂商 |
| UNIS | 5 | UNIS厂商 |
| 其他 | 65535 | Unknown厂商 |

## 设备角色处理方式

**存储策略：**
- `deviceRole` 字段完整保存在自定义数据表中，保留原始值(hub/spoke/agg)
- ADWAN平台的 `nodeRole` 字段固定设置为 2（PE角色）
- 这样既保留了原始角色信息，又确保平台兼容性

| deviceRole | 自定义表存储 | ADWAN nodeRole |
|------------|-------------|----------------|
| hub        | hub         | 2 (PE)         |
| spoke      | spoke       | 2 (PE)         |
| agg        | agg         | 2 (PE)         |
| 其他        | 原值         | 2 (PE)         |

## 设备类型 (nodeType)

| 平台值 | 说明 |
|--------|------|
| 1 | 真实设备（固定使用） |
| 2 | 虚拟设备 |

## 设备层级 (nodeLevel)

| 平台值 | 说明 |
|--------|------|
| 0 | 无 |
| 1 | 一级（默认使用） |
| 2 | 二级 |
| 3 | 三级 |

## 特殊说明

1. **必选字段**: nodeName、manageIp、netconfTemplateId、snmpTemplateId
2. **MPLS AS号**: 当前nodeRole固定为2（PE），不设置mplsAsNumber
3. **模板ID**: netconfTemplateId和snmpTemplateId固定为0
4. **IP地址格式**: 支持IPv4和IPv6，IPv4仅支持A、B、C三类地址
5. **MAC地址**: 不支持全0地址
6. **序列号**: 多个序列号用分号(;)分割

## 配置示例

```yaml
adwan:
  platform:
    baseUrl: http://***********:30000
    addDeviceUrl: /nfm/physicalNetwork/node/addNode
``` 