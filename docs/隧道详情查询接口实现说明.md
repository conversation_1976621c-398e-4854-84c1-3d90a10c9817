# 隧道详情查询接口实现说明

## 概述

本文档说明了《隧道详情》接口 `/srv6/getTeGroupDetails` 的实现细节。该接口根据工商总行的需求，查询隧道组的详细信息，包括正向和反向隧道的部署详情。

## 接口信息

- **接口地址**: `/srv6/getTeGroupDetails`
- **请求方法**: POST
- **功能描述**: 根据隧道组ID列表查询隧道详情信息，支持设备名称和部署状态过滤

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### Body参数
| 参数名称 | 参数描述 | 是否必填 | 类型 |
|---------|---------|---------|------|
| teGroupIds | 隧道组ID列表 | Y | List<Integer> |
| devName | 设备名称 | N | String |
| deployState | 隧道状态 | N | Integer |

#### deployState枚举值
- 1: 未部署
- 2: 修改未部署
- 3: 部署成功
- 4: 部署失败
- 5: 待删除

## 响应参数

### 成功响应
```json
{
  "requestId": "req_123456789",
  "result": 1,
  "failReason": null,
  "teGroupDetails": [
    {
      "id": 1001,
      "name": "tunnel-001",
      "teGroupId": 1001,
      "srcNodeId": 1001,
      "dstNodeId": 1002,
      "path": [
        {
          "memberId": 3001,
          "memberName": "Node-1001",
          "memberType": "NODE",
          "memberIndex": 1
        }
      ],
      "color": 100,
      "srcNodeName": "Device-001",
      "dstNodeName": "Device-002",
      "deployState": 3,
      "reversed": false,
      "reverseId": 1002,
      "deployMsg": null
    }
  ]
}
```

### 失败响应
```json
{
  "requestId": "req_123456789",
  "result": 0,
  "failReason": "查询失败原因",
  "teGroupDetails": null
}
```

## 实现逻辑

### 1. 数据查询流程
1. **查询定制库**: 根据teGroupIds和devName查询符合条件的隧道组
2. **设备名称过滤**: 利用JSON查询特性，在teGroupDcs字段中查找包含指定设备名称的记录
3. **查询业务列表**: 对每个隧道组，调用平台接口查询SRv6 Policy业务列表
   - 如果传入了deployState参数，转换为平台的deployStatus参数进行过滤
   - 如果某个隧道组未查询到业务数据，则从结果中移除该隧道组
4. **查询部署详情**: 对每个业务，调用平台接口查询部署详情
5. **生成隧道详情**: 根据部署详情生成正向和反向隧道信息

### 2. 关键实现点

#### 设备名称过滤
使用MySQL的JSON查询功能：
```sql
JSON_CONTAINS(te_group_dcs, JSON_QUOTE(?), '$.srcDeviceNames')
OR JSON_CONTAINS(te_group_dcs, JSON_QUOTE(?), '$.dstDeviceNames')
```

#### 正向和反向隧道生成
- **正向隧道**: 使用forwardPolicyId、forwardName、forwardSourceNeName等字段
  - `srcNodeId`: 取业务列表的`sourceNeId`，通过device_custom_info表转换为上游设备ID
  - `dstNodeId`: 取业务列表的`destinationNeId`，通过device_custom_info表转换为上游设备ID
- **反向隧道**: 使用reversePolicyId、reverseName、reverseSourceNeName等字段
  - `srcNodeId`: 取业务列表的`destinationNeId`，通过device_custom_info表转换为上游设备ID
  - `dstNodeId`: 取业务列表的`sourceNeId`，通过device_custom_info表转换为上游设备ID

#### 设备ID转换
- **平台节点ID → 上游设备ID**: 通过查询`device_custom_info`表，将平台的`nodeId`转换为上游系统的`deviceId`
- **转换逻辑**: `platformNodeId` → `device_custom_info.platform_node_id` → `device_custom_info.device_id`
- **异常处理**: 如果未找到对应的设备信息，返回null并记录警告日志

#### 部署状态映射

**查询时状态转换** (接口状态 → 平台状态):
| 接口状态 | 平台状态 | 说明 |
|---------|---------|------|
| 1 | TO_BE_DEPLOY | 未部署 |
| 3 | DEPLOY_SUCCESS | 部署成功 |
| 4 | DEPLOY_FAILED | 部署失败 |
| 5 | TO_BE_DELETE | 待删除 |

**响应时状态转换** (平台状态 → 接口状态):
| 平台状态 | 接口状态 | 说明 |
|---------|---------|------|
| TO_BE_DEPLOY | 1 | 未部署 |
| DEPLOY_SUCCESS | 3 | 部署成功 |
| DEPLOY_FAILED | 4 | 部署失败 |
| TO_BE_DELETE | 5 | 待删除 |
| 其他/null | 1 | 默认未部署 |

### 3. 路径信息提取
从候选路径信息中提取路径详情：
- 遍历候选路径列表 (forwardCandiPathInfo/reverseCandiPathInfo)
- 遍历分段路径列表 (sidPathList)
- 提取路径跳点信息 (detailList)
- 转换为接口要求的PathDetail格式

### 4. 数据有效性处理
- **业务数据验证**: 如果隧道组在平台上未查询到业务数据，则从结果中移除
- **避免空结果**: 确保返回的隧道组都有对应的业务和部署详情
- **日志记录**: 详细记录查询过程，包括有效隧道组数量和移除的隧道组

### 5. 性能优化
- **平台层过滤**: 将deployState参数转换为平台的deployStatus参数，在平台层面过滤数据
- **减少数据传输**: 避免查询不需要的业务数据，提高查询效率
- **避免重复过滤**: 在平台层过滤后，应用层不再需要重复过滤

## 新增文件

### DTO类
1. `GetTeGroupDetailsRequestDTO.java` - 请求参数DTO
2. `GetTeGroupDetailsResponseDTO.java` - 响应DTO
3. `GetSrv6PolicyTrailRequestDTO.java` - SRv6 Policy业务列表查询请求DTO
4. `GetSrv6PolicyTrailResponseDTO.java` - SRv6 Policy业务列表查询响应DTO
5. `GetSrv6PolicyTrailDeployDetailRequestDTO.java` - 部署详情查询请求DTO
6. `GetSrv6PolicyTrailDeployDetailResponseDTO.java` - 部署详情查询响应DTO

### 接口实现
1. `TeGroupController.getTeGroupDetails()` - 控制器方法
2. `TeGroupService.getTeGroupDetails()` - 服务接口方法
3. `TeGroupServiceImpl.getTeGroupDetails()` - 服务实现方法
4. `PlatformApiService.getSrv6PolicyTrail()` - 平台API接口方法
5. `PlatformApiService.getSrv6PolicyTrailDeployDetail()` - 平台API接口方法
6. `PlatformApiServiceImpl` - 平台API实现方法

## 测试

### 测试文件
- `test_get_te_group_details.http` - HTTP测试文件

### 测试场景
1. 基本查询 - 查询指定隧道组的详情
2. 设备名称过滤查询
3. 部署状态过滤查询
4. 综合条件查询
5. 各种部署状态的查询测试
6. 错误场景测试

## 配置说明

### 新增配置项
```properties
# SRv6 Policy业务列表查询接口
adwan.platform.getSrv6PolicyTrailUrl=/vas/srv6PolicyTrail/getSrv6PolicyTrail

# SRv6 Policy业务部署详情查询接口
adwan.platform.getSrv6PolicyTrailDeployDetailUrl=/vas/srv6PolicyTrail/getSrv6PolicyTrailDeployDetail
```

## 注意事项

1. **数据库JSON查询**: 需要确保MySQL版本支持JSON函数
2. **分页处理**: 业务列表查询使用较大的页面大小(1000)以获取所有数据
3. **异常处理**: 对平台接口调用失败进行了完善的异常处理
4. **测试模式**: 支持测试模式下的模拟数据返回
5. **性能考虑**: 对于大量隧道组的查询，可能需要考虑分批处理或异步处理

## 总结

该接口实现了工商总行要求的隧道详情查询功能，支持多种过滤条件，能够返回完整的正向和反向隧道信息。实现过程中充分考虑了错误处理、性能优化和测试支持。
