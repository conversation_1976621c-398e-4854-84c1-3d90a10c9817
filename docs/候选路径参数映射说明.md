# 候选路径参数映射说明

## 参数来源总览

修改隧道组时，候选路径的参数来源分为以下几类：

| 参数类型 | 来源 | 说明 |
|---------|------|------|
| **路径标识** | 原有配置 | 保持不变 |
| **隧道模板参数** | 隧道模板 | 从模板字段映射 |
| **业务模式参数** | 计算得出 | 根据balanceMode和pathSize计算 |
| **请求参数** | 请求覆盖 | 优先使用请求中的值 |

## 详细参数映射

### 1. 保持原有配置的参数

这些参数直接从查询到的原有候选路径配置中获取，不做任何修改：

```java
// 基于原有路径配置
constraint.setDataId(originalConstraint.getDataId());
constraint.setPathIndex(originalConstraint.getPathIndex());
constraint.setPreference(originalConstraint.getPreference());
constraint.setAffinityType(originalConstraint.getAffinityType());
constraint.setIncludeAffinityAll(originalConstraint.getIncludeAffinityAll());
constraint.setExcludeAffinity(originalConstraint.getExcludeAffinity());
```

**参数说明**：
- **dataId**：候选路径主键，用于更新操作
- **pathIndex**：候选路径索引（0或1）
- **preference**：候选路径优先级
- **affinityType**：亲和属性着色类型
- **includeAffinityAll**：亲和属性必选
- **excludeAffinity**：亲和属性排除

### 2. 从隧道模板取值的参数

#### preferColor（优选链路着色）

```java
// preferColor：候选路径1用模板里的upAllowLinkPriority，路径2不做配置
if (originalConstraint.getPathIndex() != null && originalConstraint.getPathIndex() == 0) {
    // 路径1使用隧道模板的upAllowLinkPriority
    constraint.setPreferColor(tunnelTemplate.getUpAllowLinkPriority());
} else {
    // 路径2不做配置，设为null
    constraint.setPreferColor(null);
}
```

**映射规则**：
- **候选路径1（pathIndex=0）**：使用隧道模板的`upAllowLinkPriority`字段
- **候选路径2（pathIndex=1）**：不做配置，设为null

#### includeAffinityAny（亲和属性包含）

```java
// includeAffinityAny：对应隧道模板downAllowLinkPriority字段
if (tunnelTemplate.getDownAllowLinkPriority() != null) {
    List<Integer> includeAffinityAny = new ArrayList<>();
    includeAffinityAny.add(tunnelTemplate.getDownAllowLinkPriority());
    constraint.setIncludeAffinityAny(includeAffinityAny);
} else {
    constraint.setIncludeAffinityAny(originalConstraint.getIncludeAffinityAny());
}
```

**映射规则**：
- 如果隧道模板的`downAllowLinkPriority`不为空，创建包含该值的列表
- 如果隧道模板的`downAllowLinkPriority`为空，保持原有配置

### 3. 根据业务模式计算的参数

#### initSidPathNum和maxSidPathNum（分段个数）

```java
// initSidPathNum和maxSidPathNum：负载模式取路径条数，主备默认1
if (request.getBalanceMode() == 2) {
    // 负载模式：取路径条数
    constraint.setInitSidPathNum(request.getPathSize());
    constraint.setMaxSidPathNum(request.getPathSize());
} else {
    // 主备模式：默认1
    constraint.setInitSidPathNum(1);
    constraint.setMaxSidPathNum(1);
}
```

**计算规则**：
- **负载模式（balanceMode=2）**：使用请求中的`pathSize`值
- **主备模式（balanceMode=1）**：固定设为1

### 4. 从请求参数取值的参数

#### hopLimit（最大跳数）

```java
// hopLimit从请求参数取值，如果没有则保持原有值
if (request.getHopLimit() != null) {
    constraint.setHopLimit(request.getHopLimit());
} else {
    constraint.setHopLimit(originalConstraint.getHopLimit());
}
```

**取值规则**：
- 优先使用请求中的`hopLimit`值
- 如果请求中没有提供，保持原有配置

## 参数映射示例

### 示例1：主备模式修改

**请求参数**：
```json
{
  "balanceMode": 1,
  "pathSize": 2,
  "hopLimit": 30
}
```

**隧道模板**：
```json
{
  "upAllowLinkPriority": 100,
  "downAllowLinkPriority": 200
}
```

**原有候选路径**：
```json
[
  {
    "dataId": 12345,
    "pathIndex": 0,
    "preference": 100,
    "hopLimit": 20
  },
  {
    "dataId": 12346,
    "pathIndex": 1,
    "preference": 90,
    "hopLimit": 20
  }
]
```

**生成结果**：
```json
[
  {
    "dataId": 12345,
    "pathIndex": 0,
    "preference": 100,
    "preferColor": 100,           // 从模板upAllowLinkPriority
    "includeAffinityAny": [200],  // 从模板downAllowLinkPriority
    "initSidPathNum": 1,          // 主备模式固定1
    "maxSidPathNum": 1,           // 主备模式固定1
    "hopLimit": 30                // 从请求参数
  },
  {
    "dataId": 12346,
    "pathIndex": 1,
    "preference": 90,
    "preferColor": null,          // 路径2不配置
    "includeAffinityAny": [200],  // 从模板downAllowLinkPriority
    "initSidPathNum": 1,          // 主备模式固定1
    "maxSidPathNum": 1,           // 主备模式固定1
    "hopLimit": 30                // 从请求参数
  }
]
```

### 示例2：负载模式修改

**请求参数**：
```json
{
  "balanceMode": 2,
  "pathSize": 3,
  "hopLimit": null
}
```

**生成结果**：
```json
[
  {
    "dataId": 12345,
    "pathIndex": 0,
    "preferColor": 100,           // 从模板upAllowLinkPriority
    "includeAffinityAny": [200],  // 从模板downAllowLinkPriority
    "initSidPathNum": 3,          // 负载模式取pathSize
    "maxSidPathNum": 3,           // 负载模式取pathSize
    "hopLimit": 20                // 保持原有值
  },
  {
    "dataId": 12346,
    "pathIndex": 1,
    "preferColor": null,          // 路径2不配置
    "includeAffinityAny": [200],  // 从模板downAllowLinkPriority
    "initSidPathNum": 3,          // 负载模式取pathSize
    "maxSidPathNum": 3,           // 负载模式取pathSize
    "hopLimit": 20                // 保持原有值
  }
]
```

## 总结

这种参数映射设计具有以下优势：

1. **配置连续性**：保持原有路径的核心配置不变
2. **模板驱动**：关键参数从隧道模板获取，保证一致性
3. **业务适配**：根据业务模式智能计算相关参数
4. **灵活覆盖**：支持请求参数覆盖特定字段
5. **逻辑清晰**：不同参数的来源和计算规则明确

通过这种设计，既保证了平台配置的稳定性，又实现了基于隧道模板的参数重新应用。
