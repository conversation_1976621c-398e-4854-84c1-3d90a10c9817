# 隧道模板管理接口说明

## 接口概述

工商总行定义隧道模板管理相关接口参数，定制开发根据总行下发的接口参数，通过定制库维护的方式对隧道模板进行增加、删除、修改和查询，以满足总行API标准需要。

### 接口列表
1. **新增隧道模板** - POST /srv6/addTeTunnelTemplate
2. **删除隧道模板** - POST /srv6/deleteTeTunnelTemplate  
3. **修改隧道模板** - POST /srv6/updateTeTunnelTemplate
4. **查询隧道模板列表** - GET /srv6/getAllTeTunnelTemplate

## 接口详情

### 1. 新增隧道模板接口

#### 接口信息
- **接口地址**: `/srv6/addTeTunnelTemplate`
- **请求方式**: `POST`
- **接口描述**: 根据参数新增隧道模板到定制库中

#### Header参数
| 参数名称 | 参数描述 | 是否必填 | 备注 |
|---------|---------|---------|------|
| X-Param-Length | 输入参数json的长度 | Y | |
| Content-Type | 默认值：application/json;charset=UTF-8 | Y | |
| X-Access-Token | 4.1获取到的token | Y | |

#### Body参数
| 参数名称 | 参数描述 | 类型 | 是否必填 | 备注 |
|---------|---------|------|---------|------|
| strategyId | 隧道策略ID | Integer | Y | 全局唯一 |
| strategyName | 策略名称 | String | Y | |
| slaId | SLA等级 | String | N | EF、AF4、AF3、AF2、AF1、BE |
| bandwidthPrecent | 保障带宽百分比 | BigDecimal | N | 与bandwidth二选一，范围：0-100 |
| bandwidth | 保障带宽 | Long | N | 与bandwidthPrecent二选一，单位：bps |
| upAllowLinkPriority | 正向链路等级 | Integer | Y | 优选链路等级 |
| downAllowLinkPriority | 反向链路等级 | Integer | Y | 可选链路等级 |
| packetLossRate | 丢包率 | BigDecimal | Y | 范围：0-1 |
| delayTime | 延迟时间 | Long | Y | 单位：毫秒 |
| networkJitter | 网络抖动 | Long | Y | 单位：毫秒 |

#### 响应格式

**成功响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

**失败响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 0,
  "failReason": "隧道策略ID已存在",
  "optionField": null
}
```

#### 响应字段说明
| 字段名称 | 字段描述 | 类型 |
|---------|---------|------|
| RequestId | 请求ID，用于溯源 | String |
| result | 状态：0-失败，1-成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 厂商自定义数据 | Object |

### 2. 删除隧道模板接口

#### 接口信息
- **接口地址**: `/srv6/deleteTeTunnelTemplate`
- **请求方式**: `POST`
- **接口描述**: 根据策略ID集合删除对应的隧道模板

#### Header参数
| 参数名称 | 参数描述 | 是否必填 | 备注 |
|---------|---------|---------|------|
| X-Param-Length | 输入参数json的长度 | Y | |
| Content-Type | 默认值：application/json;charset=UTF-8 | Y | |
| X-Access-Token | 4.1获取到的token | Y | |

#### Body参数
| 参数名称 | 参数描述 | 类型 | 是否必填 | 备注 |
|---------|---------|------|---------|------|
| strategyIds | 隧道策略ID集合 | List<Integer> | Y | 批量删除支持 |

#### 响应格式

**成功响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

**失败响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 0,
  "failReason": "未找到可删除的隧道模板",
  "optionField": null
}
```

#### 响应字段说明
| 字段名称 | 字段描述 | 类型 |
|---------|---------|------|
| RequestId | 请求ID，用于溯源 | String |
| result | 状态：0-失败，1-成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 厂商自定义数据 | Object |

### 3. 修改隧道模板接口

#### 接口信息
- **接口地址**: `/srv6/updateTeTunnelTemplate`
- **请求方式**: `POST`
- **接口描述**: 根据策略ID修改对应的隧道模板信息

#### Header参数
| 参数名称 | 参数描述 | 是否必填 | 备注 |
|---------|---------|---------|------|
| X-Param-Length | 输入参数json的长度 | Y | |
| Content-Type | 默认值：application/json;charset=UTF-8 | Y | |
| X-Access-Token | 4.1获取到的token | Y | |

#### Body参数
| 参数名称 | 参数描述 | 类型 | 是否必填 | 备注 |
|---------|---------|------|---------|------|
| strategyId | 隧道策略ID | Integer | Y | 要修改的策略ID |
| strategyName | 策略名称 | String | Y | |
| slaId | SLA等级 | String | N | EF、AF4、AF3、AF2、AF1、BE |
| bandwidthPrecent | 保障带宽百分比 | BigDecimal | N | 与bandwidth二选一，范围：0-100 |
| bandwidth | 保障带宽 | Long | N | 与bandwidthPrecent二选一，单位：bps |
| upAllowLinkPriority | 正向链路等级 | Integer | Y | 优选链路等级 |
| downAllowLinkPriority | 反向链路等级 | Integer | Y | 可选链路等级 |
| packetLossRate | 丢包率 | BigDecimal | Y | 范围：0-1 |
| delayTime | 延迟时间 | Long | Y | 单位：毫秒 |
| networkJitter | 网络抖动 | Long | Y | 单位：毫秒 |

#### 响应格式

**成功响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 1,
  "failReason": null,
  "optionField": null
}
```

**失败响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 0,
  "failReason": "隧道模板不存在",
  "optionField": null
}
```

#### 响应字段说明
| 字段名称 | 字段描述 | 类型 |
|---------|---------|------|
| RequestId | 请求ID，用于溯源 | String |
| result | 状态：0-失败，1-成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 厂商自定义数据 | Object |

## 业务逻辑

### 新增隧道模板数据验证
1. **必填字段验证**：strategyId、strategyName、upAllowLinkPriority、downAllowLinkPriority、packetLossRate、delayTime、networkJitter
2. **带宽字段验证**：bandwidthPrecent和bandwidth必须且只能填写其中一个
3. **范围验证**：
   - bandwidthPrecent：0-100
   - packetLossRate：0-1
   - 其他数值字段：非负数
4. **SLA等级验证**：如果填写，必须是EF、AF4、AF3、AF2、AF1、BE中的一个
5. **策略ID唯一性验证**：系统中不能存在相同的策略ID（排除已逻辑删除的记录）

### 删除隧道模板数据验证
1. **必填字段验证**：strategyIds不能为空
2. **存在性验证**：验证要删除的策略ID是否存在于系统中
3. **批量操作**：支持同时删除多个隧道模板

### 修改隧道模板数据验证
1. **必填字段验证**：strategyId、strategyName、upAllowLinkPriority、downAllowLinkPriority、packetLossRate、delayTime、networkJitter
2. **带宽字段验证**：bandwidthPrecent和bandwidth必须且只能填写其中一个
3. **范围验证**：
   - bandwidthPrecent：0-100
   - packetLossRate：0-1
   - 其他数值字段：非负数
4. **SLA等级验证**：如果填写，必须是EF、AF4、AF3、AF2、AF1、BE中的一个
5. **存在性验证**：验证要修改的策略ID是否存在于系统中

### 数据存储
- 所有数据保存到定制库中的`tunnel_template_custom_info`表
- 系统自动设置创建时间、更新时间和删除标识
- 支持逻辑删除机制

### 错误处理

#### 新增隧道模板错误处理
- 参数验证失败：返回具体的验证错误信息
- 策略ID重复：返回"隧道策略ID已存在"
- 系统异常：返回"新增隧道模板异常"

#### 删除隧道模板错误处理
- 参数验证失败：返回"隧道策略ID集合不能为空"
- 策略ID不存在：返回"未找到可删除的隧道模板"
- 系统异常：返回"删除隧道模板异常"

#### 修改隧道模板错误处理
- 参数验证失败：返回具体的验证错误信息
- 策略ID不存在：返回"隧道模板不存在"
- 系统异常：返回"修改隧道模板异常"

## 数据库表结构

### tunnel_template_custom_info 表
```sql
CREATE TABLE `tunnel_template_custom_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_id` int(11) NOT NULL COMMENT '隧道策略ID',
  `strategy_name` varchar(255) NOT NULL COMMENT '策略名称',
  `sla_id` varchar(64) DEFAULT NULL COMMENT 'SLA等级：EF、AF4、AF3、AF2、AF1、BE',
  `bandwidth_percent` decimal(5,2) DEFAULT NULL COMMENT '保障带宽百分比',
  `bandwidth` bigint(20) DEFAULT NULL COMMENT '保障带宽',
  `up_allow_link_priority` int(11) NOT NULL COMMENT '正向链路等级，优选链路等级',
  `down_allow_link_priority` int(11) NOT NULL COMMENT '反向链路等级，可选链路等级',
  `packet_loss_rate` decimal(8,4) NOT NULL COMMENT '丢包率',
  `delay_time` bigint(20) NOT NULL COMMENT '延迟时间',
  `network_jitter` bigint(20) NOT NULL COMMENT '网络抖动',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_id` (`strategy_id`, `is_deleted`) COMMENT '策略ID唯一索引（排除删除）',
  KEY `idx_strategy_name` (`strategy_name`) COMMENT '策略名称索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='隧道模板定制信息表';
```

## 使用示例

### 使用保障带宽百分比
```bash
curl -X POST "http://localhost:8080/srv6/addTeTunnelTemplate" \
-H "Content-Type: application/json;charset=UTF-8" \
-H "X-Access-Token: your_token_here" \
-H "X-Param-Length: 200" \
-d '{
  "strategyId": "strategy001",
  "strategyName": "高优先级隧道策略",
  "slaId": "EF",
  "bandwidthPrecent": 80.5,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 2,
  "packetLossRate": 0.001,
  "delayTime": 10,
  "networkJitter": 5
}'
```

### 使用保障带宽值
```bash
curl -X POST "http://localhost:8080/srv6/addTeTunnelTemplate" \
-H "Content-Type: application/json;charset=UTF-8" \
-H "X-Access-Token: your_token_here" \
-H "X-Param-Length: 200" \
-d '{
  "strategyId": "strategy002",
  "strategyName": "中等优先级隧道策略",
  "slaId": "AF4",
  "bandwidth": 1000000,
  "upAllowLinkPriority": 2,
  "downAllowLinkPriority": 3,
  "packetLossRate": 0.005,
  "delayTime": 20,
  "networkJitter": 10
}'
```

### 删除隧道模板示例

```bash
curl -X POST "http://localhost:8080/srv6/deleteTeTunnelTemplate" \
-H "Content-Type: application/json;charset=UTF-8" \
-H "X-Access-Token: your_token_here" \
-H "X-Param-Length: 50" \
-d '{
  "strategyIds": ["strategy001", "strategy002"]
}'
```

### 修改隧道模板示例

```bash
curl -X POST "http://localhost:8080/srv6/updateTeTunnelTemplate" \
-H "Content-Type: application/json;charset=UTF-8" \
-H "X-Access-Token: your_token_here" \
-H "X-Param-Length: 200" \
-d '{
  "strategyId": "strategy001",
  "strategyName": "修改后的高优先级隧道策略",
  "slaId": "AF4",
  "bandwidthPrecent": 90.0,
  "upAllowLinkPriority": 2,
  "downAllowLinkPriority": 3,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}'
```

## 技术实现

### 项目架构
- **Controller层**: `TeTunnelTemplateController` - 处理HTTP请求
- **Service层**: `TeTunnelTemplateService` - 业务逻辑处理
- **DAO层**: `TeTunnelTemplateCustomInfoMapper` - 数据访问
- **Entity层**: `TeTunnelTemplateCustomInfo` - 实体类
- **DTO层**: `AddTeTunnelTemplateRequestDTO` - 新增请求参数, `DeleteTeTunnelTemplateRequestDTO` - 删除请求参数, `UpdateTeTunnelTemplateRequestDTO` - 修改请求参数, `QueryTeTunnelTemplateResponseDTO` - 查询响应参数

### 关键特性
- 使用Spring Boot框架
- 集成MyBatis-Plus进行数据库操作
- 支持参数验证和业务校验
- 完整的异常处理机制
- 详细的日志记录
- 事务管理确保数据一致性

## 注意事项

1. **策略ID唯一性**：每个策略ID在系统中必须唯一
2. **带宽字段互斥**：bandwidthPrecent和bandwidth只能填写其中一个
3. **数值范围**：注意各数值字段的有效范围，避免提交无效数据
4. **SLA等级**：如果填写SLA等级，必须使用标准值
5. **Token认证**：确保请求头中包含有效的访问令牌

### 4. 查询隧道模板列表接口

#### 接口信息
- **接口地址**: `/srv6/getAllTeTunnelTemplate`
- **请求方式**: `GET`
- **接口描述**: 查询定制库中的所有隧道模板数据

#### Header参数
| 参数名称 | 参数描述 | 是否必填 | 备注 |
|---------|---------|---------|------|
| X-Param-Length | 输入参数json的长度 | Y | GET请求时为0 |
| Content-Type | 默认值：application/json;charset=UTF-8 | Y | |
| X-Access-Token | 4.1获取到的token | Y | |

#### Body参数
无

#### 响应格式

**成功响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 1,
  "failReason": null,
  "optionField": [
    {
      "strategyId": "strategy001",
      "strategyName": "高优先级隧道策略",
      "slaId": "EF",
      "bandwidthPrecent": "80.5",
      "bandwidth": null,
      "upAllowLinkPriority": "1",
      "downAllowLinkPriority": "2",
      "packetLossRate": "0.001",
      "delayTime": "10",
      "networkJitter": "5"
    },
    {
      "strategyId": "strategy002",
      "strategyName": "中等优先级隧道策略",
      "slaId": "AF4",
      "bandwidthPrecent": null,
      "bandwidth": 1000000,
      "upAllowLinkPriority": "2",
      "downAllowLinkPriority": "3",
      "packetLossRate": "0.005",
      "delayTime": "20",
      "networkJitter": "10"
    }
  ]
}
```

**失败响应示例**：
```json
{
  "RequestId": "req_20231201123456789",
  "result": 0,
  "failReason": "查询隧道模板列表异常：数据库连接失败",
  "optionField": null
}
```

#### 响应字段说明
| 字段名称 | 字段描述 | 类型 |
|---------|---------|------|
| RequestId | 请求ID，用于溯源 | String |
| result | 状态：0-失败，1-成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 隧道模板列表数据 | Array |

#### optionField 字段说明
| 字段名称 | 字段描述 | 类型 | 备注 |
|---------|---------|------|------|
| strategyId | 隧道策略ID | Integer | |
| strategyName | 策略名称 | String | |
| slaId | SLA等级 | String | EF、AF4、AF3、AF2、AF1、BE |
| bandwidthPrecent | 保障带宽百分比 | String | 数值转字符串，与bandwidth二选一 |
| bandwidth | 保障带宽 | Long | 单位：bps，与bandwidthPrecent二选一 |
| upAllowLinkPriority | 正向链路等级 | String | 数值转字符串 |
| downAllowLinkPriority | 反向链路等级 | String | 数值转字符串 |
| packetLossRate | 丢包率 | String | 数值转字符串 |
| delayTime | 延迟时间 | String | 数值转字符串，单位：毫秒 |
| networkJitter | 网络抖动 | String | 数值转字符串，单位：毫秒 |

### 查询隧道模板列表业务逻辑
1. **数据查询**：查询所有未逻辑删除的隧道模板记录
2. **排序规则**：按创建时间升序排列
3. **数据转换**：数值字段转换为字符串格式返回
4. **空数据处理**：如果没有数据，返回空数组

### 查询隧道模板列表错误处理
- 系统异常：返回"查询隧道模板列表异常"

### 查询隧道模板列表使用示例

```bash
curl -X GET "http://localhost:8080/srv6/getAllTeTunnelTemplate" \
-H "Content-Type: application/json;charset=UTF-8" \
-H "X-Access-Token: your_token_here" \
-H "X-Param-Length: 0"
``` 