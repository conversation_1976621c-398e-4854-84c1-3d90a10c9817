# 部署隧道组接口说明

## 接口概述

部署隧道组接口用于根据工商总行定义的隧道组ID集合部署隧道组，调用ADWAN平台的应用组规划及部署接口实现隧道组部署。

## 接口信息

- **接口协议**: POST
- **接口地址**: `/srv6/deployTeGroup`
- **Content-Type**: `application/json;charset=UTF-8`

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|----------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### Body参数
| 接口参数名 | 接口参数描述 | 是否必填 | 数据类型 | 默认值 |
|-----------|-------------|---------|---------|--------|
| teGroupIds | 隧道组ID集合，LIST | Y | List<Integer> | - |
| distribute | 是否下发配置，true计算（规划）路径并下发，false只作计算（规划），不下发 | N | Boolean | true |

### 请求示例

#### 规划并部署
```json
{
    "teGroupIds": [1001, 1002, 1003],
    "distribute": true
}
```

#### 只规划不部署
```json
{
    "teGroupIds": [1001, 1002, 1003],
    "distribute": false
}
```

#### 使用默认值（规划并部署）
```json
{
    "teGroupIds": [1001, 1002, 1003]
}
```

## 响应参数

### 响应字段说明
| 字段名称 | 字段描述 | 数据类型 |
|---------|----------|---------|
| requestId | 请求id，保存，用于溯源 | String |
| result | 状态：0:失败，1：成功 | Integer |
| failReason | 异常状态下，返回异常原因 | String |
| optionField | 涉及的属性 | Object |

### 成功响应示例
```json
{
    "requestId": "req_20240703_001",
    "result": 1
}
```

### 失败响应示例
```json
{
    "requestId": "req_20240703_002",
    "result": 0,
    "failReason": "隧道组1001不存在或已被删除; 隧道组1002规划失败"
}
```

## 业务流程

### 部署隧道组流程
1. **参数验证**：验证隧道组ID集合不能为空，设置distribute默认值
2. **逐个处理**：遍历隧道组ID集合，逐个执行部署操作
3. **查询隧道组**：根据隧道组ID查询本地数据库记录和平台应用组ID
4. **规划路径**：调用ADWAN平台接口规划应用组路径
5. **条件部署**：根据distribute参数决定是否调用部署接口
6. **结果汇总**：统计成功和失败的数量，返回处理结果

### 平台接口调用

#### 规划应用组路径
- **接口地址**: `PUT /vas/srv6PolicyGroup/startPlanSrv6PolicyGroup`
- **请求参数**: `{"groupId": 应用组ID}`
- **认证方式**: Cookie认证，使用X-Subject-Token

#### 部署应用组配置
- **接口地址**: `PUT /vas/srv6PolicyGroup/deploySrv6PolicyGroup`
- **请求参数**: `{"groupId": 应用组ID}`
- **认证方式**: Cookie认证，使用X-Subject-Token

### 重点逻辑

1. **distribute参数控制**：
   - `true`（默认）：先调用规划接口，规划成功后再调用部署接口
   - `false`：只执行规划接口，不执行部署接口

2. **错误信息返回**：
   - 如果规划失败，错误信息提示"规划失败"
   - 如果规划成功但部署失败，错误信息提示"部署失败"

3. **批量处理**：
   - 支持批量部署多个隧道组
   - 每个隧道组独立处理，单个失败不影响其他隧道组
   - 返回详细的成功和失败统计信息

### 错误处理
- **隧道组不存在**：返回"隧道组不存在或已被删除"
- **未关联平台应用组**：返回"隧道组未关联平台应用组"
- **规划失败**：返回"隧道组规划失败"
- **部署失败**：返回"隧道组部署失败"
- **部分成功**：返回"部分部署成功，失败原因：..."

## 注意事项

1. **前置条件**：隧道组必须已通过新增接口创建，并关联了平台应用组ID
2. **批量处理**：接口支持批量部署多个隧道组
3. **事务处理**：每个隧道组的部署操作独立处理，单个失败不影响其他隧道组
4. **幂等性**：重复部署同一个隧道组是安全的
5. **默认行为**：不传distribute参数时默认为true（规划并部署）
6. **日志记录**：详细记录每个步骤的执行情况，便于问题排查

## 测试模式

在测试模式下（`test.mode.mockAdwanPlatform=true`），平台接口调用会被模拟：
- 规划接口模拟处理时间200ms
- 部署接口模拟处理时间300ms
- 不会真实调用ADWAN平台接口

## 使用场景

1. **生产部署**：`distribute=true`，完整的规划和部署流程
2. **预演验证**：`distribute=false`，只验证规划是否成功，不实际部署
3. **批量操作**：一次性部署多个隧道组，提高操作效率
