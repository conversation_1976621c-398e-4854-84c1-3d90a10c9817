# 查询设备详情接口说明

## 接口概述

查询单个设备详细信息接口，整合多个数据源（ADWAN平台、网管接口、定制库）为工商总行提供完整的设备信息。

## 接口信息

- **接口协议**: GET
- **接口地址**: `/srv6/queryDeviceInfoDetail`
- **接口描述**: 根据设备ID查询设备详细信息，包括设备基本信息、硬件信息、性能数据、端口信息、告警信息等

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 | 示例值 |
|---------|---------|---------|--------|
| Content-Type | 内容类型 | Y | application/json;charset=UTF-8 |
| X-Access-Token | 访问令牌 | Y | eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... |

### URL参数
| 参数名称 | 参数描述 | 数据类型 | 是否必填 | 示例值 |
|---------|---------|---------|---------|--------|
| deviceId | 设备ID | Integer | Y | 12345 |

### 请求示例
```
GET /srv6/queryDeviceInfoDetail?deviceId=12345
Host: your-domain.com
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-access-token
```

## 响应参数

### 响应体结构
```json
{
    "requestId": "string",
    "deviceId": 12345,
    "deviceName": "string",
    "deviceManufacturer": "string",
    "deviceModel": "string",
    "deviceMac": "string",
    "deviceSn": "string",
    "deviceStatus": "string",
    "deviceIp": "string",
    "deviceSite": "string",
    "deviceRole": "string",
    "deviceHardwareVersion": "string",
    "deviceSoftwareVersion": "string",
    "firstOnlineTime": "2023-01-01T10:00:00",
    "lastOnlineTime": "2023-01-01T18:00:00",
    "isRR": true,
    "isMarkDevice": 1,
    "temperature": 36,
    "memUseRate": 75,
    "cpuUseRate": 80,
    "powerInfos": [
        {
            "powerId": 1,
            "powerStatus": 1
        }
    ],
    "fanInfos": [
        {
            "fanId": 1,
            "fanStatus": 1
        }
    ],
    "ports": [
        {
            "portName": "GigabitEthernet1/0/1",
            "portStatus": "on",
            "portBandWidth": "1000.00"
        }
    ],
    "alarmInfos": [
        {
            "alarmLevel": "严重",
            "alarmName": "设备温度过高",
            "alarmTime": "2023-01-01 12:00:00",
            "alarmDescription": "设备温度超过阈值"
        }
    ]
}
```

### 字段说明

#### 基本信息字段
| 字段名称 | 字段描述 | 数据类型 | 数据来源 |
|---------|---------|---------|---------|
| requestId | 请求ID，用于溯源 | String | 系统生成 |
| deviceId | 设备ID | Integer | 请求参数 |
| deviceName | 设备名称 | String | ADWAN平台：nodeName |
| deviceManufacturer | 设备厂商 | String | ADWAN平台：company，需翻译 |
| deviceModel | 设备型号 | String | ADWAN平台：nodeModel |
| deviceMac | 设备MAC地址 | String | 网管设备详情接口：mac |
| deviceSn | 设备序列号 | String | ADWAN平台：serialNumbers，取第一个 |
| deviceStatus | 设备状态 | String | ADWAN平台：nodeDisplay，需翻译 |
| deviceIp | 设备IP | String | ADWAN平台：manageIp |
| deviceSite | 设备站点 | String | 定制库：device_site |
| deviceRole | 设备角色 | String | ADWAN平台：nodeRole，需翻译 |
| deviceHardwareVersion | 设备硬件版本 | String | 网管资产接口：hardwareRev |
| deviceSoftwareVersion | 设备软件版本 | String | 网管设备详情接口：devVersion |
| firstOnlineTime | 首次上线时间 | LocalDateTime | 定制库：create_time |
| lastOnlineTime | 最后在线时间 | LocalDateTime | 根据设备状态和告警计算 |
| isRR | 是否为RR设备 | Boolean | 定制库：is_rr |
| isMarkDevice | 是否纳管 | Integer | ADWAN平台：isolationStatus，需转换 |

#### 性能信息字段
| 字段名称 | 字段描述 | 数据类型 | 数据来源 |
|---------|---------|---------|---------|
| temperature | 温度(℃) | Integer | 网管性能接口：taskId=49，多个取平均值并四舍五入 |
| memUseRate | 内存利用率(%) | Integer | 网管性能接口：taskId=4，四舍五入 |
| cpuUseRate | CPU利用率(%) | Integer | 网管性能接口：taskId=2，四舍五入 |

#### 电源信息数组字段
| 字段名称 | 字段描述 | 数据类型 | 数据来源 |
|---------|---------|---------|---------|
| powerId | 电源ID | Integer | 网管性能接口：instanceId |
| powerStatus | 电源状态（0异常/1正常） | Integer | 网管性能接口：status，需转换 |

#### 风扇信息数组字段
| 字段名称 | 字段描述 | 数据类型 | 数据来源 |
|---------|---------|---------|---------|
| fanId | 风扇ID | Integer | 网管性能接口：instanceId |
| fanStatus | 风扇状态（0异常/1正常） | Integer | 网管性能接口：status，需转换 |

#### 端口信息数组字段
| 字段名称 | 字段描述 | 数据类型 | 数据来源 |
|---------|---------|---------|---------|
| portName | 端口名称 | String | ADWAN平台接口信息：ifName |
| portStatus | 端口状态（on/off） | String | ADWAN平台接口信息：status，需转换 |
| portBandWidth | 带宽（Mbps） | String | ADWAN平台接口信息：bandwidth，需转换 |

#### 告警信息数组字段
| 字段名称 | 字段描述 | 数据类型 | 数据来源 |
|---------|---------|---------|---------|
| alarmLevel | 告警级别 | String | 网管告警接口：alarmLevel |
| alarmName | 告警名称 | String | 网管告警接口：alarmContent |
| alarmTime | 告警时间 | String | 网管告警接口：alarmTime，格式化为字符串 |
| alarmDescription | 告警描述 | String | 网管告警接口：alarmDesc |

## 数据源整合说明

### 1. ADWAN平台接口
- **设备查询接口**: `/nfm/physicalNetwork/node/getNodes`
  - 提供设备基本信息（名称、厂商、型号、序列号、状态、IP、角色、软件版本等）
- **接口信息查询**: `/nfm/physicalNetwork/node/getBatchInterfaceList`
  - 提供端口信息（端口名称、状态、带宽、描述等）

### 2. 网管接口（通过uclink调用）
- **资产接口**: `/asset/asset/assetList`
  - 提供硬件版本信息
- **设备详情接口**: `/resrs/v2/device/detail`
  - 提供设备MAC地址和软件版本
- **性能接口**: `/perfrs/perf/data/device/latest`
  - 提供CPU、内存、温度、电源、风扇等性能数据
- **告警接口**: `/alarmrs/fullFault`
  - 提供活动告警信息

### 3. 定制库
- **device_custom_info表**
  - 提供设备站点、是否RR设备、首次上线时间等定制信息

## 特殊处理逻辑

### 1. 模糊查询精准过滤
网管接口（设备详情、告警）使用IP模糊查询，需要在返回结果中精准匹配IP地址。

### 2. 温度平均值计算
当设备有多个温度传感器时，取所有温度值的平均值作为设备温度。

### 3. 最后在线时间计算
- 如果设备状态为"灰色，设备下线"且有告警信息，取最新告警时间作为最后在线时间
- 否则最后在线时间为空

### 4. 状态码翻译
- **设备厂商翻译**: 0→H3C, 1→HP, 5→UNIS, 其他→Unknown
- **设备状态翻译**: 1→绿色设备在线无告警, 2→蓝色设备在线有警告告警, 3→灰色设备下线, 4→橙色设备在线有次要告警, 5→红色设备在线有重要告警
- **设备角色翻译**: 1→P, 2→PE, 10→ASBR_PE, 其他→无

## 错误处理

### 1. 设备不存在
当deviceId在定制库中不存在或已被逻辑删除时，返回只包含requestId和deviceId的空响应。

### 2. 接口调用失败
单个数据源接口调用失败不影响其他数据源，确保接口的健壮性。相关字段会保持为空值。

### 3. 数据格式异常
对于数值类型字段（如CPU利用率、温度等），如果原始数据格式异常，会记录警告日志并跳过该字段。

## 测试模式支持

接口支持测试模式，可通过配置跳过Token验证：
```yaml
test-mode:
  skip-token-validation: true
```

## 接口调用示例

### 成功响应示例
```bash
curl -X GET "http://your-domain.com/srv6/queryDeviceInfoDetail?deviceId=12345" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your-access-token"
```

### 响应示例
```json
{
    "requestId": "REQ-20231201-001",
    "deviceId": 12345,
    "deviceName": "Core-Router-01",
    "deviceManufacturer": "H3C",
    "deviceModel": "S12500X-AF",
    "deviceMac": "00:1A:2B:3C:4D:5E",
    "deviceSn": "SN123456789",
    "deviceStatus": "绿色，设备在线无告警",
    "deviceIp": "************",
    "deviceSite": "数据中心A",
    "deviceRole": "PE",
    "deviceHardwareVersion": "REV.C",
    "deviceSoftwareVersion": "7.1.070",
    "firstOnlineTime": "2023-01-01T10:00:00",
    "lastOnlineTime": null,
    "isRR": false,
    "isMarkDevice": 1,
    "temperature": 36,
    "memUseRate": 75,
    "cpuUseRate": 20,
    "powerInfos": [
        {
            "powerId": 1,
            "powerStatus": 1
        }
    ],
    "fanInfos": [
        {
            "fanId": 1,
            "fanStatus": 1
        }
    ],
    "ports": [
        {
            "portName": "GigabitEthernet1/0/1",
            "portStatus": "on",
            "portBandWidth": "1000.00"
        }
    ],
    "alarmInfos": []
}
```

## 性能考虑

该接口需要调用多个外部接口，响应时间可能较长。建议：
1. 设置合理的超时时间
2. 考虑使用缓存优化频繁查询的数据
3. 对于非关键数据源的失败，采用降级处理 