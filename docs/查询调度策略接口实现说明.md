# 查询调度策略接口实现说明

## 接口概述

工商总行定义查询所有调度策略接口，定制开发根据总行下发的要求，查询定制库调度策略数据进行封装，同时查询平台部署历史，以满足总行API标准需要。

## 接口信息

- **接口协议**: GET
- **接口地址**: `/srv6/queryScheduleList`
- **功能描述**: 查询所有调度策略及其部署状态

## 请求参数

### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|---------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 4.1获取到的token | Y |

### 请求参数
无请求参数，查询所有调度策略。

## 响应参数

### 成功响应（状态码200）
```json
{
    "requestId": "请求ID，用于溯源",
    "result": 1,
    "total": "总量",
    "scheduleList": [
        {
            "appScheduleId": "调度策略ID",
            "appScheduleName": "策略名称",
            "appGroupName": "应用组名称",
            "drainageType": "引流类型 0:五元组、1:dscp、4:Vpn",
            "networkIds": ["隧道组ID数组"],
            "vpnId": "业务网络ID",
            "scheduleStatus": "策略状态（0未启用、1未生效、2部分生效、3全部生效、4生效中、9异常）",
            "optionField": [
                {
                    "siteId": "站点ID",
                    "siteName": "站点名称",
                    "deviceIp": "设备IP",
                    "deviceSn": "设备SN",
                    "result": "策略结果（1:成功,0:失败）",
                    "faultReason": "失败原因"
                }
            ]
        }
    ]
}
```

### 失败响应
```json
{
    "requestId": "请求ID",
    "result": 0,
    "failReason": "失败原因"
}
```

## 实现逻辑

### 1. 查询调度策略基本信息
- 从 `schedule_custom_info` 表查询所有未删除的调度策略
- 获取策略ID、名称、应用组名称、引流类型、隧道组ID、VPN ID等基本信息

### 2. 查询关联的流策略信息
- 从 `schedule_policy_info` 表查询每个调度策略关联的流策略
- 获取流策略ID、设备ID、设备IP等信息

### 3. 查询设备和站点信息
- 根据设备ID从 `device_custom_info` 表查询设备信息
- 获取站点ID、站点名称、平台节点ID等信息
- 调用平台接口 `/nfm/physicalNetwork/node/getNodes` 查询设备序列号

### 4. 查询流策略部署历史
- 调用平台接口 `/qostrs/qos/policy/deploy/devif` 查询每个流策略的部署历史
- 获取最新一条部署记录的结果和失败原因

### 5. 计算策略状态
根据所有关联流策略的部署结果计算整体策略状态：
- **未生效(1)**: 所有流策略都部署失败或无部署记录
- **部分生效(2)**: 部分流策略部署成功，部分失败
- **全部生效(3)**: 所有流策略都部署成功

## 核心文件

### 新增文件
1. `QueryScheduleListResponseDTO.java` - 响应DTO
2. `PlatformQosPolicyDeployHistoryRequestDTO.java` - 平台API请求DTO
3. `PlatformQosPolicyDeployHistoryResponseDTO.java` - 平台API响应DTO

### 修改文件
1. `ScheduleController.java` - 添加新的GET端点
2. `ScheduleService.java` - 添加方法签名
3. `ScheduleServiceImpl.java` - 实现业务逻辑
4. `PlatformApiService.java` - 添加平台API方法
5. `PlatformApiServiceImpl.java` - 实现平台API调用

### 配置文件
1. `application-dev.yml` - 添加部署历史查询URL配置
2. `application-prod.yml` - 添加生产环境URL配置

## 平台接口依赖

### 查询流策略部署历史
- **接口地址**: `GET /qostrs/qos/policy/deploy/devif`
- **功能**: 查询流策略在设备接口上的部署历史
- **参数**: policyId, desc, start, size等
- **返回**: 部署时间、结果、失败原因等信息

### 查询设备信息
- **接口地址**: `POST /nfm/physicalNetwork/node/getNodes`
- **功能**: 查询设备详细信息，包括序列号
- **参数**: dataId (平台节点ID)
- **返回**: 设备名称、序列号、管理IP等信息

## 测试说明

### 测试文件
- `test_query_schedule_list.http` - HTTP测试文件

### 测试场景
1. **正常查询**: 查询所有调度策略列表
2. **空数据**: 无调度策略时返回空列表
3. **权限验证**: 无Token时返回错误
4. **异常处理**: 系统异常时返回错误信息

## 部署说明

1. **编译检查**: 使用 `./gradlew compileJava` 检查编译
2. **配置更新**: 确保配置文件中的URL正确
3. **数据库依赖**: 依赖现有的调度策略相关表
4. **平台接口**: 确保平台部署历史查询接口可用

## 注意事项

1. **性能考虑**: 查询涉及多表关联和平台API调用，注意性能优化
2. **错误处理**: 平台API调用失败时有降级处理
3. **数据一致性**: 确保调度策略和流策略数据的一致性
4. **测试模式**: 支持测试模式，返回模拟数据

## 状态码说明

### 策略状态 (scheduleStatus)
- **0**: 未启用
- **1**: 未生效
- **2**: 部分生效
- **3**: 全部生效
- **4**: 生效中
- **9**: 异常

### 部署结果 (result)
- **1**: 成功
- **0**: 失败
