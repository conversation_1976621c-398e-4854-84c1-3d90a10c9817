# 设备管理接口总览

## 概述

工商总行SDWAN设备管理接口，提供设备的新增和删除功能。接口遵循总行API标准，通过定制开发封装ADWAN平台接口，实现设备生命周期管理。

## 接口列表

| 接口名称 | 接口方法 | 接口地址 | 功能描述 |
|---------|---------|---------|---------|
| 新增设备 | POST | `/srv6/addDevice` | 新增SDWAN设备到平台 |
| 删除设备 | DELETE | `/srv6/delDevice/{deviceId}` | 从平台删除指定设备 |
| 更新设备名称 | POST | `/srv6/updateDevice` | 更新指定设备的名称 |

## 架构设计

### 数据流向
```
工行接口 → 参数映射 → ADWAN平台 → 响应处理 → 工行标准响应
    ↓
定制数据库存储
```

### 核心组件
1. **DeviceController**: 接口控制层，处理HTTP请求
2. **DeviceService**: 业务逻辑层，处理设备管理逻辑
3. **PlatformApiService**: 平台接口层，调用ADWAN平台API
4. **DeviceCustomInfo**: 数据存储层，保存平台不支持的字段

### 数据存储策略
- **平台支持字段**: 直接传递给ADWAN平台
- **平台不支持字段**: 存储在`device_custom_info`表中
- **关联关系**: 通过`platform_node_id`关联平台设备

## 字段映射策略

### 新增设备字段映射
| 工行字段 | ADWAN字段 | 映射规则 |
|---------|-----------|---------|
| deviceManufacturer | company | H3C=0, HP=1, UNIS=5, 其他=65535 |
| deviceRole | - | 存储在定制库，平台nodeRole固定为2(PE) |
| deviceName | nodeName | 直接映射 |
| deviceIp | manageIp | 直接映射 |
| deviceSn | serialNumbers | 直接映射 |
| deviceModel | nodeModel | 直接映射 |

### 删除设备流程
1. 根据`deviceId`查询定制库
2. 获取`platform_node_id`
3. 调用平台删除接口
4. 清理定制库记录

### 更新设备名称流程
1. 根据`deviceId`查询定制库
2. 获取`platform_node_id`
3. 调用平台查询接口获取设备详细信息
4. 调用平台更新接口修改设备名称

## 测试模式

系统提供测试模式，方便开发和调试：

### 配置参数
```yaml
test:
  mode:
    enabled: true                    # 启用测试模式
    skipTokenValidation: true        # 跳过token验证
    mockAdwanPlatform: true         # 模拟ADWAN平台调用
    mockDeviceIdRange:
      min: *********                # 模拟设备ID范围
      max: 999999999
```

### 测试特性
- **Token验证跳过**: 可使用任意token值
- **平台调用模拟**: 返回随机生成的设备ID
- **快速验证**: 无需外部依赖即可测试接口逻辑

## 安全机制

### Token验证
- 所有接口都需要有效的`X-Access-Token`
- 通过UC2SessionFactory验证token有效性
- 测试模式下可配置跳过验证

### 数据验证
- 请求参数使用`@Valid`注解验证
- 设备ID唯一性检查
- 数据类型和格式验证

### 事务管理
- 使用`@Transactional`确保数据一致性
- 平台操作失败时自动回滚
- 异常处理和日志记录

## 日志记录

### ApiLogAspect切面
- 自动记录所有API调用
- 统一的requestId管理
- 请求参数和响应结果记录

### 日志级别
- **INFO**: 正常业务流程
- **WARN**: 业务警告（如设备不存在）
- **ERROR**: 系统异常和错误

## 配置管理

### 环境配置
- **开发环境**: 启用测试模式，便于调试
- **测试环境**: 可选择性启用测试模式
- **生产环境**: 必须关闭测试模式

### 平台配置
```yaml
adwan:
  platform:
    baseUrl: http://10.88.44.37:30000
    addDeviceUrl: /nfm/physicalNetwork/node/addNode
    deleteDeviceUrl: /nfm/physicalNetwork/node/deleteMaintainedNodes
```

## 错误处理

### 统一响应格式
```json
{
    "requestId": "请求ID",
    "result": "0/1",
    "failReason": "失败原因",
    "optionField": "操作结果描述"
}
```

### 常见错误码
- **设备ID已存在**: 新增设备时设备ID重复
- **设备不存在**: 删除设备时设备ID不存在
- **Token无效**: 访问token验证失败
- **平台调用失败**: ADWAN平台接口调用异常

## 性能考虑

### 数据库优化
- 设备ID字段建立索引
- 合理的连接池配置
- 事务范围最小化

### 接口性能
- HTTP连接超时设置（30秒）
- 异步日志记录
- 合理的线程池配置

## 扩展性

### 接口扩展
- 支持批量操作
- 设备查询接口
- 设备状态管理
- 更多设备属性更新

### 功能扩展
- 设备配置管理
- 设备监控集成
- 设备生命周期管理

## 部署说明

### 数据库准备
```sql
-- 执行建表脚本
source sql/device_custom_info.sql
```

### 应用配置
1. 配置数据库连接
2. 配置ADWAN平台地址
3. 配置测试模式参数
4. 配置UC认证信息

### 验证部署
1. 启动应用
2. 检查健康状态
3. 执行接口测试
4. 验证日志输出

## 文档索引

- [设备管理接口说明](./设备管理接口说明.md) - 详细接口文档
- [删除设备接口说明](./删除设备接口说明.md) - 删除接口专项文档
- [更新设备接口说明](./更新设备接口说明.md) - 更新接口专项文档
- [ADWAN平台字段映射说明](./ADWAN平台字段映射说明.md) - 字段映射详情
- [测试模式Postman示例](./测试模式Postman示例.md) - 测试用例和示例 