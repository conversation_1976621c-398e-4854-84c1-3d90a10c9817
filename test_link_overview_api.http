### 测试链路总览接口

# 测试链路总览查询（实时数据）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 15,
    "sortOrder": -1,
    "sortName": "linkName"
}

###

# 测试链路总览查询（按链路名称查询）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 15,
    "linkName": "************** To ************** Link1 IPv6"
}

###

# 测试链路总览查询（最近一小时数据）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "LAST_ONE_HOUR"
    },
    "pageNo": 1,
    "pageSize": 10
}

###

# 测试链路总览查询（自定义时间范围）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "SELF_DEFINE",
        "startTime": "2024-04-18 15:00:00 GMT+8",
        "endTime": "2024-04-18 17:00:00 GMT+8"
    },
    "pageNo": 1,
    "pageSize": 20
}

###

# 测试链路总览查询（带搜索条件）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 15,
    "searchDelay": {
        "operator": "LT",
        "value": "100",
        "searchName": "delay"
    },
    "searchBdPercent": {
        "operator": "GTE",
        "value": "5.0",
        "searchName": "bandwidthPercent"
    }
} 