# API请求日志定时清理功能说明

## 功能概述

为了防止API请求日志无限增长占用过多存储空间，系统新增了定时清理功能，可以定期删除旧的API请求日志记录。

## 功能特性

- **自动执行**: 使用Spring Boot的@Scheduled注解实现定时任务
- **可配置时间**: 支持通过配置文件自定义执行时间
- **可配置保留天数**: 支持通过配置文件自定义数据保留天数
- **安全清理**: 仅删除超过指定天数的历史数据，不影响近期数据
- **性能优化**: 支持分批删除，避免长时间锁表，提升数据库性能
- **智能限制**: 支持单次任务最大删除数量限制，防止过度删除
- **批次控制**: 支持配置批次大小和延迟时间，控制数据库压力
- **双模式支持**: 既支持传统一次性删除，也支持性能优化的分批删除
- **详细监控**: 完整的执行日志和性能统计，便于监控和排查问题

## 配置说明

### 配置文件位置
- 开发环境: `src/main/resources/application-dev.yml`
- 生产环境: `src/main/resources/application-prod.yml`

### 配置项详解

```yaml
# 数据清理配置
cleanup:
  api-log:
    # 定时清理API请求日志的cron表达式
    # 格式：秒 分 时 日 月 周
    # 默认：0 10 0 * * ? (每天0点10分执行)
    cron: 0 10 0 * * ?
    
    # 保留的日志天数（仅保留近N天的数据，不含执行当天）
    # 默认：3天
    keep-days: 3
    
    # 性能优化配置
    performance-optimization: true  # 是否启用性能优化模式（分批删除）
    batch-size: 1000               # 分批删除的批次大小
    max-delete-count: 50000        # 单次任务最大删除数量限制
    batch-delay-ms: 100            # 批次间延迟时间（毫秒）
```

### Cron表达式说明

| 表达式 | 说明 |
|--------|------|
| `0 10 0 * * ?` | 每天0点10分执行（默认配置） |
| `0 0 2 * * ?` | 每天凌晨2点执行 |
| `0 30 1 * * ?` | 每天凌晨1点30分执行 |
| `0 0 0 * * SUN` | 每周日凌晨0点执行 |
| `0 */30 * * * ?` | 每30分钟执行一次（测试用） |

## 技术实现

### 主要文件

1. **ScheduledCleanupTask.java** - 定时清理任务类（位于common/scheduled包）
2. **ApiRequestLogMapper.java** - 数据访问层，添加删除方法
3. **ApiRequestLogMapper.xml** - MyBatis SQL映射文件
4. **ApiRequestLogService.java** - 业务服务接口
5. **ApiRequestLogServiceImpl.java** - 业务服务实现类
6. **GszhApiApplication.java** - 启动类，添加@EnableScheduling注解

### 核心实现逻辑

#### 双模式支持
系统支持两种删除模式：

**1. 性能优化模式（推荐）**：
```java
@Scheduled(cron = "${cleanup.api-log.cron:0 10 0 * * ?}")
public void cleanupApiRequestLogs() {
    if (performanceOptimization) {
        // 分批删除，避免长时间锁表
        int deletedCount = apiRequestLogService.cleanupOldLogsOptimized(
                keepDays, batchSize, maxDeleteCount, batchDelayMs);
    } else {
        // 传统一次性删除
        int deletedCount = apiRequestLogService.cleanupOldLogs(keepDays);
    }
}
```

**2. 分批删除核心算法**：
```java
// 1. 先统计总数量
long totalCount = apiRequestLogMapper.countOldLogs(keepDays);

// 2. 检查是否超过最大删除限制
if (totalCount > maxDeleteCount) {
    totalCount = maxDeleteCount; // 限制单次删除数量
}

// 3. 分批执行删除
while (totalDeleted < totalCount) {
    int deleted = apiRequestLogMapper.deleteOldLogsBatch(keepDays, batchSize);
    totalDeleted += deleted;
    
    // 批次间延迟，减少数据库压力
    Thread.sleep(batchDelayMs);
}
```

### SQL清理逻辑

**统计查询**：
```sql
SELECT COUNT(*) FROM api_request_log 
WHERE create_time < DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
```

**分批删除**：
```sql
DELETE FROM api_request_log 
WHERE create_time < DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
LIMIT #{batchSize}
```

**传统删除**：
```sql
DELETE FROM api_request_log 
WHERE create_time < DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
```

## 使用说明

### 1. 启用定时任务

确保启动类包含`@EnableScheduling`注解：

```java
@EnableScheduling
@SpringBootApplication
public class GszhApiApplication {
    // ...
}
```

### 2. 修改配置

根据实际需求修改配置文件中的参数：

```yaml
cleanup:
  api-log:
    # 修改执行时间（例如改为每天凌晨2点执行）
    cron: 0 0 2 * * ?
    # 修改保留天数（例如保留7天）
    keep-days: 7
```

### 3. 验证功能

#### 查看日志输出
定时任务执行时会输出以下日志：
```
INFO  - 开始执行API请求日志定时清理任务，保留近3天的数据
INFO  - API请求日志清理完成，删除了xxx条记录，保留近3天的数据
INFO  - API请求日志定时清理任务执行完成，共删除xxx条记录
```

#### 临时测试
如需立即测试清理功能，可以：
1. 修改cron表达式为更频繁的执行（如每分钟）
2. 在数据库中手动插入历史日期的测试数据
3. 重启应用等待定时任务执行

## 注意事项

### 1. 数据安全
- 清理操作是不可逆的，请谨慎设置保留天数
- 建议在生产环境部署前先在测试环境验证配置

### 2. 性能考虑
- **启用性能优化模式**：建议在生产环境启用分批删除模式
- **合理设置批次大小**：根据数据库性能和业务负载调整batch-size
- **控制删除频率**：通过batch-delay-ms控制删除间隔，避免持续压力
- **设置删除上限**：通过max-delete-count防止单次删除过多数据
- **监控执行时间**：关注日志中的执行时间和删除数量

### 3. 监控建议
- 定期检查定时任务执行日志
- 监控数据库表大小变化
- 设置合适的数据保留策略

### 4. 故障排查
如果定时任务未执行，请检查：
1. 启动类是否包含`@EnableScheduling`注解
2. 配置文件中的cron表达式是否正确
3. 应用日志中是否有相关错误信息
4. 数据库连接是否正常

## 相关配置示例

### 开发环境配置示例
```yaml
cleanup:
  api-log:
    # 开发环境可以设置更频繁的清理用于测试
    cron: 0 */10 * * * ?  # 每10分钟执行一次
    keep-days: 1  # 仅保留1天数据
    # 开发环境性能优化配置
    performance-optimization: true
    batch-size: 500          # 较小的批次大小
    max-delete-count: 10000  # 较小的删除限制
    batch-delay-ms: 50       # 较短的延迟时间
```

### 生产环境配置示例
```yaml
cleanup:
  api-log:
    # 生产环境建议在凌晨执行
    cron: 0 10 0 * * ?  # 每天0点10分执行
    keep-days: 7  # 保留7天数据
    # 生产环境性能优化配置
    performance-optimization: true
    batch-size: 2000         # 较大的批次大小，提高效率
    max-delete-count: 100000 # 较大的删除限制
    batch-delay-ms: 200      # 适当的延迟时间，平衡性能和稳定性
```

### 高负载环境配置示例
```yaml
cleanup:
  api-log:
    cron: 0 10 0 * * ?
    keep-days: 3
    # 高负载环境更保守的配置
    performance-optimization: true
    batch-size: 1000         # 中等批次大小
    max-delete-count: 50000  # 适中的删除限制
    batch-delay-ms: 500      # 较长的延迟时间，减少数据库压力
```

## 更新记录

- 2024-XX-XX: 初始版本，实现基本的定时清理功能
- 支持可配置的执行时间和保留天数
- 完整的日志记录和异常处理 