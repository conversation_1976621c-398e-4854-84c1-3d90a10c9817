### 测试精准接口查询功能

# 1. 测试基础精准查询（按srcTpName查询）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 50,
    "srcDeviceName": ["**************"],
    "srcTpName": ["GigabitEthernet1/2/0"]
}

###

# 2. 测试多接口精准查询
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 100,
    "srcDeviceName": ["**************"],
    "srcTpName": ["GigabitEthernet1/2/0", "GigabitEthernet1/1/0", "GigabitEthernet1/3/0"]
}

###

# 3. 测试多设备精准查询
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 100,
    "srcDeviceName": ["**************", "**************"],
    "srcTpName": ["GigabitEthernet1/2/0"]
}

###

# 4. 测试精准查询与目标设备过滤结合
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 50,
    "srcDeviceName": ["**************"],
    "dstDeviceName": ["**************"],
    "srcTpName": ["GigabitEthernet1/2/0"],
    "dstTpName": ["GigabitEthernet1/2/0"]
}

###

# 5. 测试性能指标过滤的精准查询
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 50,
    "srcDeviceName": ["**************"],
    "srcTpName": ["GigabitEthernet1/2/0"],
    "searchBdPercent": {
        "operator": "GT",
        "value": "0",
        "searchName": "bandwidthPercent"
    }
}

###

# 6. 测试站点链路查询（验证精准查询集成效果）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-precise

###

# 7. 测试历史数据的精准查询
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "LAST_ONE_HOUR"
    },
    "pageNo": 1,
    "pageSize": 50,
    "srcDeviceName": ["**************"],
    "srcTpName": ["GigabitEthernet1/2/0"],
    "sortName": "delay",
    "sortOrder": 1
}

###

# 8. 测试批量接口查询（模拟大规模网络场景）
POST http://localhost:8080/oam/linkOverView/getLinkOverView
Content-Type: application/json;charset=UTF-8

{
    "historyTimeEntity": {
        "historyTimeInterval": "REALTIME"
    },
    "pageNo": 1,
    "pageSize": 200,
    "srcTpName": [
        "GigabitEthernet1/1/0",
        "GigabitEthernet1/2/0", 
        "GigabitEthernet1/3/0",
        "GigabitEthernet1/4/0",
        "TenGigabitEthernet1/0/1",
        "TenGigabitEthernet1/0/2"
    ]
} 