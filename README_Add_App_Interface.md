# 新增应用接口实现说明

## 概述

根据工商总行的需求，实现了新增应用接口，该接口通过SDWAN中QoS ACL相关接口进行封装，满足总行API标准需要。

## 接口信息

- **接口协议**: POST
- **接口地址**: `/srv6/addApp`
- **描述**: 新增应用，通过IMC平台的QoS ACL接口创建相应的ACL模板

## 主要功能

### 1. 基础功能
- 接收应用对象参数并写入定制库
- 参数验证（应用类型、协议类型、IP格式等）
- 防重复验证（应用ID唯一性检查）
- 应用组关联验证

### 2. ACL模板创建逻辑
- **基础规则**: 一个源设备、目标设备生成一个ACL模板
- **IPv4/IPv6分离**: 当传入参数包含IPv4和IPv6时，平台分别生成ACL模板
- **多目标处理**: 目标地址为list集合，为每个目标设备生成独立的ACL模板
- **规则映射**: 将应用参数映射为IMC平台ACL规则参数
- **ACL ID获取**: 新增ACL模板成功后，通过查询接口获取ACL ID
- **分页查询**: 支持分页查询ACL模板，按降序排序确保新增的ACL在前面

### 3. 数据存储
- 所有接口入参全部入库（`app_custom_info`表）
- 记录应用组与应用的关联关系
- 保存应用ID与ACL模板ID的关联关系（1:N）
- 支持软删除机制

## 技术实现

### 1. 新增组件

#### 数据层
- `AppCustomInfo.java` - 应用实体类
- `AppCustomInfoMapper.java` - 数据访问接口
- `AppCustomInfoMapper.xml` - MyBatis映射文件
- `app_custom_info.sql` - 数据库表创建脚本
- `AppAclRelation.java` - 应用ACL关系实体类
- `AppAclRelationMapper.java` - ACL关系数据访问接口
- `AppAclRelationMapper.xml` - ACL关系MyBatis映射文件
- `app_acl_relation.sql` - ACL关系表创建脚本

#### 业务层
- `AppService.java` - 应用服务接口
- `AppServiceImpl.java` - 应用服务实现类
- 添加了ACL模板创建相关的平台API调用

#### 控制层
- 新增`AppController.java`专门处理应用相关接口
- 在`AppController.java`中实现了`addApp`接口方法

#### 控制层
- `AppController.java` - 应用管理控制器

#### DTO类
- `AddAppRequestDTO.java` - 新增应用请求DTO
- `PlatformAclRequestDTO.java` - 平台ACL请求DTO
- `PlatformAclResponseDTO.java` - 平台ACL响应DTO

#### 平台API扩展
- 在`PlatformApiService.java`中添加了`addAclTemplate`、`queryAclTemplates`、`getAclIdByName`方法
- 在`PlatformApiServiceImpl.java`中实现了ACL模板创建、查询和ID获取逻辑
- 新增了`QueryAclTemplateRequestDTO`和`QueryAclTemplateResponseDTO`用于ACL模板查询
- 新增了`AclErrorResponseDTO`用于处理ACL操作错误响应

### 2. 配置更新
- 在`application-dev.yml`和`application-prod.yml`中添加了ACL接口配置
- 新增了`queryAclTemplatesUrl`配置项用于查询ACL模板
- 支持测试模式下的ACL模拟调用

## ACL模板创建流程

### 1. 新增ACL模板流程
```
开始新增ACL模板
  ↓
调用平台POST /qostrs/qos/acl接口
  ↓
判断响应状态码
  ↓
200 (成功)
  ↓
调用查询接口获取ACL ID
  ↓
分页查询ACL模板列表
  ↓
按降序排序查找匹配的ACL名称
  ↓
找到匹配项？
  ↓ (是)
返回ACL ID
  ↓ (否)
查询下一页
  ↓
返回成功响应

500 (错误)
  ↓
解析错误响应
  ↓
返回具体错误信息

其他状态码
  ↓
返回通用错误信息
```

### 2. ACL ID查询逻辑
- 按每页50条记录进行分页查询
- 使用降序排序，确保新增的ACL模板在前面
- 在每页中精确匹配ACL名称
- 如果当前页未找到且记录数小于页大小，说明已查询完毕
- 最多查询10000条记录，避免无限循环
- 支持测试模式下的模拟查询

### 3. 错误处理机制
- **200状态码**: 创建成功，但需要查询获取ACL ID
- **500状态码**: 解析错误响应，返回具体错误信息（如"ACL名称不合法"）
- **其他状态码**: 返回通用错误信息
- **查询失败**: 创建成功但无法获取ACL ID时的处理

## 接口参数映射

| 接口参数 | 类型 | 必填 | IMC平台映射 | 说明 |
|---------|------|------|-------------|------|
| appId | Integer | Y | 无 | 应用唯一标识 |
| appName | String | Y | aclName | ACL名称，系统会根据IP版本自动添加后缀（IPv4：_ipv4，IPv6：_ipv6） |
| appType | String | Y | 无 | 仅支持"五元组" |
| internetAppDSCP | String | N | 无 | 网络应用DSCP |
| agreementType | String | Y | matchList.protocol | 协议类型 |
| sourceIP | String | Y | matchList.sourceIpMask | 源IP地址及掩码 |
| sourcePort | String | Y | matchList.sourcePort | 源端口 |
| destinationIPList | List | Y | matchList.destIpMask & destPort | 目的IP及端口列表 |
| sourceIPv6 | String | N | matchList.sourceIpMask | IPv6源IP地址 |
| sourcePortIPv6 | String | N | matchList.sourcePort | IPv6源端口 |
| destinationIPv6List | List | N | matchList.destIpMask & destPort | IPv6目的IP及端口 |
| appGroupName | String | Y | idValue | 所属应用分组 |

## ACL命名规则

系统在创建ACL模板时，会根据IP版本为ACL名称添加相应的后缀：

- **IPv4 ACL模板**: `{appName}_ipv4`
- **IPv6 ACL模板**: `{appName}_ipv6`

### 示例说明

假设应用名称为 "acl22"，则：
- IPv4 ACL模板名称：`acl22_ipv4`
- IPv6 ACL模板名称：`acl22_ipv6`

如果一个应用同时包含IPv4和IPv6配置，系统会：
1. 创建一个名为 `acl22_ipv4` 的IPv4 ACL模板
2. 创建一个名为 `acl22_ipv6` 的IPv6 ACL模板

这样确保了不同IP版本的ACL模板具有唯一且易于识别的名称。

## 协议类型映射

| 协议名称 | 协议编号 |
|---------|---------|
| ICMP | 1 |
| IGMP | 2 |
| IPINIP | 4 |
| TCP | 6 |
| UDP | 17 |
| GRE | 47 |
| OSPF | 89 |
| IP | 256 |

## 数据库表结构

### 1. app_custom_info 表字段（主表）

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 主键ID |
| app_id | int | 应用ID |
| app_name | varchar(255) | 应用名称 |
| app_type | varchar(50) | 应用类型 |
| internet_app_dscp | varchar(50) | 网络应用DSCP |
| agreement_type | varchar(20) | 协议类型 |
| source_ip | varchar(255) | 源IP地址及掩码 |
| source_port | varchar(50) | 源端口 |
| destination_ip_list | text | 目的IP及端口列表（JSON格式） |
| source_ipv6 | varchar(255) | IPv6源IP地址及掩码 |
| source_port_ipv6 | varchar(50) | IPv6源端口 |
| destination_ipv6_list | text | IPv6目的IP及端口列表（JSON格式） |
| app_group_name | varchar(255) | 所属应用分组 |
| app_group_id | bigint | 关联的应用组ID |
| acl_id_list | text | 关联的ACL模板ID列表（JSON格式） |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_deleted | tinyint | 是否删除 |

### 2. app_acl_relation 表字段（关系表）

| 字段名 | 类型 | 说明 |
|-------|------|------|
| id | bigint | 主键ID |
| app_custom_id | bigint | 关联的应用定制信息ID（app_custom_info表主键） |
| app_id | int | 应用ID（便于直接查询） |
| acl_id | int | ACL模板ID |
| ip_version | tinyint | IP版本：4-IPv4，6-IPv6 |
| destination_ip | varchar(255) | 目标IP地址（拆分后的单个IP） |
| destination_port | varchar(50) | 目标端口（拆分后的单个端口） |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_deleted | tinyint | 是否删除 |

### 3. 数据关系说明

#### 场景1：仅IPv4应用
```
app_custom_info (1条)
└── destinationIPList: ["***********/32&443", "***********/32&8080"]
    
app_acl_relation (2条记录，共享aclId)
├── {acl_id: 12345, ip_version: 4, destination_ip: "***********/32", destination_port: "443"}
└── {acl_id: 12345, ip_version: 4, destination_ip: "***********/32", destination_port: "8080"}
```

#### 场景2：IPv4+IPv6应用
```
app_custom_info (1条)
├── destinationIPList: ["***********/32&443"]
└── destinationIPv6List: ["2001:db8::1/64&8080"]
    
app_acl_relation (2条记录，不同aclId)
├── {acl_id: 12345, ip_version: 4, destination_ip: "***********/32", destination_port: "443"}
└── {acl_id: 12346, ip_version: 6, destination_ip: "2001:db8::1/64", destination_port: "8080"}
```

## 测试说明

### 测试文件
- `test_add_app.http` - 包含多种测试场景的HTTP测试文件
- `test_acl_template.http` - 专门测试ACL模板创建和查询的HTTP测试文件

### 测试场景
1. 仅IPv4应用创建
2. IPv4+IPv6混合应用创建
3. UDP协议应用创建
4. 参数验证测试（无效应用类型、IP格式错误等）
5. 必填字段验证测试
6. 多目标IP应用创建
7. ACL名称不合法测试（测试500错误处理）
8. ACL模板查询和ID获取测试
9. 分页查询ACL模板测试

### 测试前准备
1. 确保数据库中存在对应的应用分组
2. 启动应用服务
3. 配置正确的Token（测试模式下可使用任意Token）

## 关键特性

### 1. 事务处理
- 使用`@Transactional`注解确保数据一致性
- ACL创建失败时会回滚数据库操作

### 2. 错误处理
- 完善的参数验证
- 详细的错误日志记录
- 用户友好的错误信息返回

### 3. 性能考虑
- 批量处理多个目的IP
- 异步ACL模板创建（可扩展）
- 数据库索引优化

### 4. 安全性
- Token验证
- SQL注入防护
- 参数格式验证

### 5. 可维护性
- 模块化设计
- 详细的日志记录
- 清晰的代码注释
- 统一的异常处理

## 部署说明

### 1. 数据库更新
```sql
-- 执行应用表创建脚本
source sql/app_custom_info.sql;

-- 执行ACL关系表创建脚本
source sql/app_acl_relation.sql;
```

### 2. 配置更新
- 确认`application-dev.yml`中的ACL接口配置正确
- 验证平台API连接配置

### 3. 重启服务
- 重新部署应用
- 验证接口可用性

## 注意事项

1. **平台接口依赖**: 接口功能依赖IMC平台的QoS ACL接口，需要确保平台API可用
   - 新增ACL：POST /qostrs/qos/acl（成功时仅返回200状态码，无数据）
   - 查询ACL：GET /qostrs/qos/acl（支持分页和多种过滤条件）
   - 删除ACL：DELETE /qostrs/qos/acl/all?aclId={aclId}
2. **应用组前置**: 新增应用前需要确保对应的应用组已存在
3. **ACL ID获取**: 新增ACL模板成功后，系统会自动查询获取ACL ID
4. **ACL名称规范**: ACL名称需要符合平台规范，否则会返回500错误
5. **分页查询**: 系统支持自动分页查询ACL模板，最多查询10000条记录
6. **测试模式**: 开发环境默认启用测试模式，会模拟平台API调用
7. **日志监控**: 平台ACL接口返回结果会完整打印，便于问题排查

## 后续扩展

1. **批量导入**: 可扩展批量新增应用功能
2. **应用查询**: 可添加应用查询、更新、删除接口
3. **ACL管理**: 可添加ACL模板的独立管理功能
4. **性能优化**: 可添加ACL创建的异步处理机制
5. **监控告警**: 可添加平台API调用失败的告警机制
6. **高级查询**: 基于ACL关系表实现复杂查询功能：
   - 按IP地址范围查询应用
   - 按端口范围查询应用  
   - ACL使用情况统计
   - 应用目标IP分布分析
7. **数据导出**: 支持应用及ACL关系数据的导出功能
 