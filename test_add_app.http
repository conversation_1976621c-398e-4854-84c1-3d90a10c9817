### 新增应用接口测试
###
### 说明：创建ACL模板时，系统会为ACL名称添加IP版本后缀
### - IPv4 ACL模板：{appName}_ipv4
### - IPv6 ACL模板：{appName}_ipv6
### 例如：appName为"Web服务应用"时，IPv4 ACL名称为"Web服务应用_ipv4"
###

### 1. 新增应用（仅IPv4）
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1001,
  "appName": "Web服务应用",
  "appType": "五元组",
  "internetAppDSCP": "EF",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "************/32&443",
    "************/32&8080"
  ],
  "appGroupName": "业务应用组"
}

### 2. 新增应用（IPv4 + IPv6）
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1002,
  "appName": "数据库应用",
  "appType": "五元组",
  "internetAppDSCP": "AF31",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "3306",
  "destinationIPList": [
    "************/32&3306"
  ],
  "sourceIPv6": "2001:db8::1/64",
  "sourcePortIPv6": "3306",
  "destinationIPv6List": [
    "2001:db8::20/64&3306"
  ],
  "appGroupName": "数据库应用组"
}

### 3. 新增应用（UDP协议）
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1003,
  "appName": "DNS服务",
  "appType": "五元组",
  "agreementType": "UDP",
  "sourceIP": "************/32",
  "sourcePort": "53",
  "destinationIPList": [
    "*******/32&53",
    "*******/32&53"
  ],
  "appGroupName": "网络服务组"
}

### 4. 测试错误场景 - 无效的应用类型
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1004,
  "appName": "测试应用",
  "appType": "七元组",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "destinationIPList": [
    "***********/32&80"
  ],
  "appGroupName": "测试组"
}

### 5. 测试错误场景 - 目的IP格式错误
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1005,
  "appName": "测试应用",
  "appType": "五元组",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "destinationIPList": [
    "***********:80"
  ],
  "appGroupName": "测试组"
}

### 6. 测试参数验证 - 缺少必填字段
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appName": "测试应用",
  "appType": "五元组",
  "agreementType": "TCP",
  "sourceIP": "***********/32",
  "sourcePort": "80",
  "destinationIPList": [
    "***********/32&80"
  ],
  "appGroupName": "测试组"
}

### 7. 测试多目标IP场景
POST http://localhost:28000/gszh-srv6-api/srv6/addApp
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

{
  "appId": 1006,
  "appName": "多目标应用",
  "appType": "五元组",
  "agreementType": "TCP",
  "sourceIP": "*************/32",
  "sourcePort": "80",
  "destinationIPList": [
    "***********/32&443",
    "***********/32&8080",
    "***********/32&9000"
  ],
  "sourceIPv6": "2001:db8::100/64",
  "sourcePortIPv6": "8080",
  "destinationIPv6List": [
    "2001:db8::1/64&443",
    "2001:db8::2/64&8080"
  ],
  "appGroupName": "多目标应用组"
}

### ============ 删除测试 ============

### 8. 删除应用（删除之前创建的应用）
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 9. 删除不存在的应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/9999
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345 