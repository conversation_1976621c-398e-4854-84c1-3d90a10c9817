# OAuth Token DES加密配置说明

## 概述

`DesUtil`工具类专门用于OAuth获取Token接口的DES加密，现在支持可配置的DES加密密钥，通过Spring配置文件进行管理，提高了安全性和灵活性。

## 配置方式

### 1. 配置文件设置

在`application.yml`或对应环境的配置文件中添加：

```yaml
oauth:
  token:
    des:
      # DES加密密钥，必须是8字节长度
      # 用于OAuth获取Token接口的参数加密
      key: your_key_
```

### 2. 环境配置示例

#### 开发环境 (application.yml)
```yaml
oauth:
  token:
    des:
      key: h3c_dzkf  # 默认开发密钥
```

#### 生产环境 (application-prod.yml)
```yaml
oauth:
  token:
    des:
      key: Prod@Key  # 生产环境密钥
```

## 使用场景

此DES加密专门用于：
- **OAuth获取Token接口**的参数加密
- **敏感认证信息**的传输保护
- **用户凭据**的安全处理

## 重要注意事项

### 1. 密钥长度要求
- **必须是8字节长度**
- 中文字符占用多个字节，请谨慎使用
- 建议使用ASCII字符

### 2. 密钥安全性
- **生产环境必须使用强密钥**
- 不要在代码中硬编码密钥
- 定期更换密钥
- 密钥应包含大小写字母、数字和特殊字符

### 3. 密钥示例
```
有效密钥（8字节）：
- "h3c_dzkf"
- "Prod@Key"
- "12345678"
- "Aa1@Bb2#"

无效密钥：
- "123"      (太短)
- "123456789" (太长)
- "中文密钥"   (中文字符长度不确定)
```

## 使用方法

### 1. 加密（OAuth Token接口参数）
```java
try {
    String encrypted = DesUtil.encrypt("用户名或密码");
    System.out.println("加密结果：" + encrypted);
} catch (Exception e) {
    e.printStackTrace();
}
```

### 2. 解密（OAuth Token接口参数）
```java
try {
    String decrypted = DesUtil.decrypt("加密后的Base64字符串");
    System.out.println("解密结果：" + decrypted);
} catch (Exception e) {
    e.printStackTrace();
}
```

### 3. 测试
```java
boolean success = DesUtil.test("测试数据");
if (success) {
    System.out.println("OAuth Token DES加密测试成功");
} else {
    System.out.println("OAuth Token DES加密测试失败");
}
```

## 架构改进

### 1. 配置类
- `DesConfig` - 管理OAuth Token DES加密配置
- 配置前缀：`oauth.token.des`
- 支持配置验证
- 提供默认值

### 2. 工具类改进
- `DesUtil` - 专门用于OAuth Token接口的DES加密
- 添加了详细的日志记录
- 增强了错误处理
- 启动时验证配置

### 3. 安全特性
- 密钥长度验证
- 配置初始化检查
- 详细的异常信息
- 日志记录（不记录敏感信息）

## 故障排除

### 1. 常见错误

#### 密钥长度错误
```
错误：DES密钥配置无效，密钥必须是8字节长度
解决：确保密钥正好是8个字节
```

#### 配置未初始化
```
警告：DES配置未初始化，使用默认密钥
解决：检查Spring配置是否正确加载
```

#### 解密失败
```
错误：解密失败：数据填充错误
解决：检查密钥是否正确，数据是否完整
```

### 2. 调试建议
- 启动时查看日志确认密钥配置
- 使用test()方法验证加密解密功能
- 检查配置文件格式是否正确

## 最佳实践

1. **密钥管理**
   - 生产环境使用环境变量或外部配置
   - 定期更换密钥
   - 不要在日志中记录密钥

2. **配置管理**
   - 不同环境使用不同密钥
   - 配置文件加密存储
   - 限制配置文件访问权限

3. **安全考虑**
   - 仅用于OAuth Token接口的参数加密
   - 考虑升级到AES加密
   - 使用更长的密钥长度
   - 添加盐值和初始化向量

## 配置路径说明

```yaml
oauth:              # OAuth相关配置
  token:            # Token相关配置
    des:            # DES加密配置
      key: xxxxxxxx # 8字节密钥
```

此配置结构清晰地表明了DES加密的具体用途：**OAuth Token接口的参数加密**。 