# 设备新增错误处理优化

## 概述

本次修改优化了设备新增功能的错误处理逻辑，当调用平台接口新增设备失败时，现在会返回平台接口的具体错误信息，而不是通用的"调用平台接口新增设备失败"消息。

## 修改内容

### 1. 简化设备新增逻辑

由于 `DeviceCustomInfo` 实体的 `isDeleted` 字段有 `@TableLogic` 注解，MyBatis-Plus 会自动处理逻辑删除，且数据库中不存在唯一索引，因此简化了设备新增逻辑：

- 移除了复杂的逻辑删除记录更新逻辑
- 移除了 `updateDeletedDeviceRecord` 方法
- 简化为直接插入新记录的方式

### 2. 新增 PlatformAddDeviceResult 类

**文件**: `src/main/java/com/h3c/dzkf/entity/platform/PlatformAddDeviceResult.java`

新增了一个结果封装类，用于包含设备新增操作的详细结果信息：

- `success`: 操作是否成功
- `nodeId`: 成功时返回的平台设备节点ID
- `errorMessage`: 失败时的错误消息
- `errorCode`: 失败时的错误代码
- `rawResponse`: 原始响应内容（用于调试）

提供了便捷的静态方法：
- `PlatformAddDeviceResult.success(nodeId)`: 创建成功结果
- `PlatformAddDeviceResult.failure(errorMessage, errorCode, rawResponse)`: 创建失败结果
- `PlatformAddDeviceResult.failure(errorMessage)`: 创建简化的失败结果

### 2. 修改 PlatformApiService 接口

**文件**: `src/main/java/com/h3c/dzkf/service/PlatformApiService.java`

修改了 `addDevice` 方法的返回类型：

```java
// 修改前
String addDevice(PlatformAddDeviceRequestDTO request);

// 修改后
PlatformAddDeviceResult addDevice(PlatformAddDeviceRequestDTO request);
```

### 3. 修改 PlatformApiServiceImpl 实现

**文件**: `src/main/java/com/h3c/dzkf/service/impl/PlatformApiServiceImpl.java`

优化了 `addDevice` 方法的错误处理逻辑：

1. **成功情况**: 返回 `PlatformAddDeviceResult.success(nodeId)`
2. **失败情况**: 直接使用 `PlatformAddResponseDTO.message` 字段作为错误信息：
   - 优先使用平台响应中的 `message` 字段
   - 如果没有错误信息，使用默认消息
   - 返回 `PlatformAddDeviceResult.failure(errorMessage)`
   - 不再返回完整的响应内容，保持错误信息简洁

### 4. 修改 DeviceServiceImpl 业务逻辑

**文件**: `src/main/java/com/h3c/dzkf/service/impl/DeviceServiceImpl.java`

简化了设备新增方法的逻辑：

1. **简化重复性检查**：由于 `@TableLogic` 注解，MyBatis-Plus 自动排除逻辑删除记录，无需手动处理
2. **简化数据库操作**：直接插入新记录，无需复杂的更新逻辑删除记录的操作
3. **优化错误处理**：使用平台接口返回的具体错误信息

```java
// 修改前的复杂逻辑
// 1. 检查未删除记录
// 2. 检查已删除记录
// 3. 根据是否存在已删除记录决定更新还是插入

// 修改后的简化逻辑
// 1. 检查设备ID是否存在（@TableLogic自动排除已删除记录）
// 2. 调用平台接口
// 3. 直接插入新记录

PlatformAddDeviceResult addResult = platformApiService.addDevice(platformRequest);
if (!addResult.isSuccess()) {
    String errorMessage = addResult.getErrorMessage();
    if (StrUtil.isBlank(errorMessage)) {
        errorMessage = "调用平台接口新增设备失败";
    }
    return ApiResponseDTO.fail(requestId, errorMessage);
}

String platformNodeId = addResult.getNodeId();
DeviceCustomInfo deviceCustomInfo = buildDeviceCustomInfo(request, platformNodeId);
deviceCustomInfoMapper.insert(deviceCustomInfo);
```

## 错误信息解析逻辑

平台接口的错误响应结构：

```json
{
  "successful": false,
  "message": "具体错误消息",
  "code": "错误代码"
}
```

修改后的代码简化了错误信息解析：
1. 直接使用 `PlatformAddResponseDTO.message` 字段作为错误信息
2. 如果没有错误信息，使用默认错误消息
3. 不再返回完整的响应内容，保持错误信息简洁明了

## 测试

添加了 `PlatformAddDeviceResultTest` 测试类来验证结果封装类的功能。

## 影响范围

此修改影响设备新增功能的逻辑和错误处理：

- **逻辑简化**: 移除了复杂的逻辑删除记录处理，代码更简洁易维护
- **性能提升**: 减少了数据库查询次数（从3次查询减少到1次）
- **错误处理优化**: 用户将看到更具体的错误信息，有助于问题诊断
- **成功情况**: 行为保持不变，仍然正确创建设备记录

## 示例

### 修改前
```json
{
  "requestId": "req_123",
  "result": 0,
  "failReason": "调用平台接口新增设备失败"
}
```

### 修改后
```json
{
  "requestId": "req_123", 
  "result": 0,
  "failReason": "设备IP地址已存在"
}
```

## 兼容性

此修改向后兼容，不会破坏现有的API接口契约。
