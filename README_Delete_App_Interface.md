# 删除应用接口实现说明

## 概述

根据工商总行需求，实现了删除应用接口，该接口通过删除SDWAN中QoS ACL模板的方式删除应用，满足总行API标准需要。

## 接口信息

- **接口协议**: DELETE
- **接口地址**: `/srv6/delApp/{appId}`
- **描述**: 删除应用，同时删除关联的ACL模板和相关数据

## 主要功能

### 1. 基础功能
- 根据应用ID删除应用信息
- 删除关联的ACL模板
- 删除ACL关系数据
- 支持软删除机制
- 完善的错误处理和日志记录

### 2. 核心业务逻辑
1. **应用存在性验证**: 检查应用是否存在
2. **ACL模板查询**: 查询应用关联的所有ACL模板，按IP版本分组
3. **平台ACL删除**: 按IPv6优先、IPv4次序删除ACL模板
4. **删除结果处理**: 
   - 如有任何ACL删除失败，接口返回失败并说明具体原因
   - 只删除平台删除成功的ACL关系记录
   - 只有全部ACL删除成功才删除应用记录
5. **数据更新**: 
   - 成功删除的ACL：软删除关系记录，从ACL ID列表中移除
   - 失败的ACL：保留关系记录和应用记录
6. **结果反馈**: 提供详细的删除结果和失败原因

### 3. 错误处理策略
- **严格错误处理**: 任何ACL删除失败都会导致接口返回失败
- **部分删除处理**: 成功删除的ACL会被清理，失败的ACL保留
- **事务保证**: 确保数据库操作的一致性
- **详细错误信息**: 返回具体的失败ACL ID和失败原因

## 技术实现

### 1. 新增组件

#### 响应DTO
- `DeleteAppResponseDTO.java` - 删除应用响应DTO，包含请求ID、结果状态和失败原因

#### 服务层扩展
- 在`AppService.java`中添加`deleteApp`方法
- 在`AppServiceImpl.java`中实现完整的删除逻辑
- 在`PlatformApiService.java`中添加`deleteAclTemplate`方法
- 在`PlatformApiServiceImpl.java`中实现ACL删除功能

#### 控制层扩展
- 在`AppController.java`中添加`deleteApp`接口方法

#### 配置更新
- 在`application-dev.yml`中添加删除ACL接口配置

### 2. 平台API集成

#### 删除ACL模板接口
- **平台接口**: DELETE `/qostrs/qos/acl/all?aclId={aclId}`
- **参数**: aclId (query parameter)
- **响应**: 根据平台返回判断删除是否成功

#### 测试模式支持
- 支持测试模式下的模拟ACL删除调用
- 完整的模拟响应机制

## 接口规格

### 请求参数

#### Header参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|----------|
| Content-Type | 默认值：application/json;charset=UTF-8 | Y |
| X-Access-Token | 获取到的token | Y |

#### 路径参数
| 参数名称 | 参数描述 | 是否必填 |
|---------|---------|----------|
| appId | 应用唯一标识ID | Y |

### 响应体

#### 成功响应 (200)
```json
{
  "requestId": "req_20240630_001",
  "result": 1,
  "failReason": "删除成功"
}
```

#### 失败响应 (200)
```json
{
  "requestId": "req_20240630_001", 
  "result": 0,
  "failReason": "应用不存在"
}
```

#### 部分删除失败响应 (200)
```json
{
  "requestId": "req_20240630_001", 
  "result": 0,
  "failReason": "部分ACL模板删除失败，失败ACL ID：[12345, 12346]，失败原因：IPv6 ACL模板(ID:12345)删除失败; IPv4 ACL模板(ID:12346)删除异常: 网络超时"
}
```



### 响应字段说明
| 字段名称 | 字段描述 |
|---------|---------|
| requestId | 请求ID，用于溯源 |
| result | 状态：0-失败，1-成功 |
| failReason | 异常状态下，返回异常原因 |

## 删除逻辑详解

### 1. 删除流程图
```
开始
  ↓
查询应用是否存在
  ↓
查询关联的ACL列表，按IP版本分组
  ↓
是否存在IPv6 ACL?
  ↓ (是)
按顺序删除IPv6 ACL模板
  ↓
记录删除结果
  ↓
是否存在IPv4 ACL?
  ↓ (是)
按顺序删除IPv4 ACL模板
  ↓
记录删除结果
  ↓
是否有ACL删除失败?
  ↓ (是)
更新成功删除的ACL记录
保留失败的ACL和应用记录
返回失败结果和原因
  ↓ (否)
软删除所有ACL关系数据
  ↓
软删除应用信息
  ↓
返回成功结果
  ↓
结束
```

### 2. 删除顺序策略
- **IPv6优先**: 先删除所有IPv6 ACL模板
- **IPv4次序**: 再删除所有IPv4 ACL模板
- **去重处理**: 同一ACL ID的多个关系记录只删除一次ACL模板
- **错误终止**: 任何ACL删除失败立即终止后续删除

### 3. 部分删除处理策略
- **成功的ACL**: 从`app_acl_relation`表软删除，从`acl_id_list`字段移除
- **失败的ACL**: 保留关系记录和应用记录，便于后续重试
- **数据一致性**: 确保数据库记录与平台状态一致

### 4. 软删除策略
- 应用信息：设置`is_deleted = 1`（仅当所有ACL删除成功）
- ACL关系：设置`is_deleted = 1`（仅对删除成功的ACL）
- 保留历史数据，便于审计和恢复

## 测试说明

### 测试文件
- `test_delete_app.http` - 专门的删除应用测试文件
- `test_add_app.http` - 添加了删除测试用例

### 测试场景
1. 删除存在的应用
2. 删除不存在的应用
3. 删除IPv4+IPv6混合应用
4. 删除多目标IP应用
5. 无效Token测试
6. 缺少Token测试
7. 无效应用ID测试

### 测试前准备
1. 确保有已创建的应用数据
2. 启动应用服务
3. 配置正确的Token（测试模式下可使用任意Token）

## 关键特性

### 1. 事务处理
- 使用`@Transactional`注解确保数据一致性
- ACL删除失败不影响接口返回结果

### 2. 错误处理
- 应用不存在时返回明确错误信息
- ACL删除失败立即返回错误，包含具体失败ACL ID和原因
- 部分删除成功时更新数据库，保持数据一致性
- 完善的异常捕获和处理

### 3. 性能考虑
- 批量删除ACL模板
- 软删除策略避免数据丢失
- 详细的操作日志记录

### 4. 安全性
- Token验证
- 参数验证
- 操作日志记录

### 5. 可维护性
- 清晰的代码结构
- 详细的日志记录
- 统一的异常处理
- 完善的测试覆盖

## 部署说明

### 1. 配置更新
- 确认`application-dev.yml`中的删除ACL接口配置正确
- 验证平台API连接配置

### 2. 重启服务
- 重新部署应用
- 验证接口可用性

## 注意事项

1. **软删除机制**: 删除操作不会物理删除数据，只是标记为已删除
2. **删除顺序**: 系统会先删除IPv6 ACL模板，再删除IPv4 ACL模板
3. **严格错误处理**: 任何ACL删除失败都会导致接口返回失败，不会删除应用记录
4. **部分删除**: 如果部分ACL删除成功，系统会更新数据库，移除成功删除的ACL记录
5. **重试机制**: 删除失败的应用可以重新调用删除接口，系统会处理剩余的ACL
6. **数据一致性**: 数据库记录始终与平台ACL状态保持一致
7. **平台接口依赖**: ACL删除依赖IMC平台接口，删除失败会返回错误
8. **测试模式**: 开发环境默认启用测试模式，会模拟平台API调用
6. **日志监控**: 平台ACL删除结果会完整打印，便于问题排查
7. **事务回滚**: 数据库操作失败会触发事务回滚

## 后续扩展

1. **批量删除**: 可扩展批量删除应用功能
2. **恢复机制**: 可添加软删除数据的恢复功能
3. **删除审计**: 可添加删除操作的审计日志
4. **异步删除**: 可改为异步删除ACL模板
5. **删除策略**: 可配置不同的删除策略（软删除vs硬删除）

## 错误码说明

| 场景 | result | failReason |
|------|--------|------------|
| 删除成功 | 1 | "删除成功" |
| 应用不存在 | 0 | "应用不存在" |
| 数据库删除失败 | 0 | "删除应用信息失败" |
| 系统异常 | 0 | "系统异常" |

**注意**: ACL删除失败不影响接口返回结果，只会在日志中记录。

## 交付内容

### 新增文件
- `src/main/java/com/h3c/dzkf/entity/dto/DeleteAppResponseDTO.java`: 删除响应DTO
- `test_delete_app.http`: 删除应用测试文件
- `README_Delete_App_Interface.md`: 详细说明文档

### 修改文件
- `src/main/java/com/h3c/dzkf/service/AppService.java`: 添加删除方法
- `src/main/java/com/h3c/dzkf/service/impl/AppServiceImpl.java`: 实现删除逻辑
- `src/main/java/com/h3c/dzkf/service/PlatformApiService.java`: 添加删除ACL方法
- `src/main/java/com/h3c/dzkf/service/impl/PlatformApiServiceImpl.java`: 实现ACL删除功能
- `src/main/java/com/h3c/dzkf/controller/AppController.java`: 添加删除接口
- `src/main/resources/application-dev.yml`: 添加删除ACL配置
- `test_add_app.http`: 添加删除测试用例

## 总结

删除应用接口的实现遵循了以下原则：
1. **业务完整性**: 完整的应用删除流程，包括ACL模板和关系数据
2. **数据安全性**: 软删除机制保护历史数据
3. **错误容错性**: 部分失败时的优雅处理
4. **操作可追溯**: 详细的日志记录便于问题排查
5. **接口一致性**: 与新增应用接口保持一致的设计模式

该接口为工商总行提供了完整、安全、可靠的应用删除功能，满足了总行API标准要求。 