### 修改调度策略接口测试

POST http://localhost:8080/srv6/modifySchedule
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token

{
  "appScheduleId": 1001,
  "appScheduleName": "修改后的调度策略",
  "appGroupName": "办公应用组",
  "drainageType": 0,
  "networkIds": [1001, 1002],
  "vpnId": 100
}

### 测试参数说明
# appScheduleId: 调度策略唯一标识ID (必填)
# appScheduleName: 策略名称 (必填)
# appGroupName: 应用组名称 (必填)
# drainageType: 引流类型 0:五元组、1:dscp、4:Vpn (可选)
# networkIds: 隧道组ID列表 (必填)
# vpnId: VPN ID (可选)

### 预期响应格式
# {
#   "requestId": "请求ID",
#   "result": 1,  // 0:失败，1：成功
#   "failReason": "异常状态下，返回异常原因"
# }
