### 删除隧道组接口测试

### 1. 删除单个隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deleteTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001]
}

### 2. 删除多个隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deleteTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001, 1002, 1003]
}

### 3. 删除不存在的隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deleteTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [9999]
}

### 4. 空隧道组ID集合（应该返回参数验证错误）
POST http://localhost:28000/gszh-srv6-api/srv6/deleteTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": []
}

### 5. 缺少隧道组ID集合参数（应该返回参数验证错误）
POST http://localhost:28000/gszh-srv6-api/srv6/deleteTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
}

### 6. 混合存在和不存在的隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deleteTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001, 9999, 1002]
}

### 测试说明
# 1. 确保服务已启动：./gradlew bootRun
# 2. 测试模式已启用，会模拟平台接口调用
# 3. 需要先通过新增隧道组接口创建测试数据
# 4. 删除操作会同时删除平台应用组和回收ServiceClass资源
# 5. 支持批量删除，部分成功部分失败会返回详细错误信息
