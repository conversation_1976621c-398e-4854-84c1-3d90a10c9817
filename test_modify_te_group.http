### 修改隧道组接口测试

### 1. 获取Token
POST http://localhost:8080/srv6/getToken
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

> {%
client.global.set("token", response.body.token);
%}

### 2. 修改隧道组
POST http://localhost:8080/srv6/modifyTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{token}}

{
  "id": 1001,
  "name": "ModifiedGroup01",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 150,
  "pathSize": 3,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [1001, 1002],
      "srcDeviceNames": ["Device-1001", "Device-1002"],
      "dstDeviceIds": [1003, 1004],
      "dstDeviceNames": ["Device-1003", "Device-1004"]
    }
  ],
  "hopLimit": 15
}

### 3. 修改隧道组 - 按业务网络配置
POST http://localhost:8080/srv6/modifyTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{token}}

{
  "id": 1002,
  "name": "ModifiedGroup02",
  "isLoose": false,
  "balanceMode": 2,
  "balanceProportion": "2:3",
  "scheduleStrategyId": 2,
  "color": 200,
  "pathSize": 2,
  "planeRoutingType": 2,
  "isVirtualNet": true,
  "virtualNetId": "vnet002",
  "hopLimit": 20
}

### 4. 修改隧道组 - 错误测试（隧道组不存在）
POST http://localhost:8080/srv6/modifyTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{token}}

{
  "id": 9999,
  "name": "NonExistentGroup",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 100,
  "pathSize": 2,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [1001],
      "srcDeviceNames": ["Device-1001"],
      "dstDeviceIds": [1003],
      "dstDeviceNames": ["Device-1003"]
    }
  ]
}

### 5. 修改隧道组 - 参数验证错误（按设备配置但未提供设备信息）
POST http://localhost:8080/srv6/modifyTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{token}}

{
  "id": 1001,
  "name": "InvalidGroup",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 100,
  "pathSize": 2,
  "planeRoutingType": 1,
  "isVirtualNet": false
}

### 6. 修改隧道组 - 参数验证错误（按业务网络配置但未提供业务网络ID）
POST http://localhost:8080/srv6/modifyTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{token}}

{
  "id": 1001,
  "name": "InvalidGroup2",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 100,
  "pathSize": 2,
  "planeRoutingType": 1,
  "isVirtualNet": true
}
