package com.h3c.dzkf.controller;

import com.alibaba.fastjson2.JSONObject;
import com.h3c.dzkf.uc2linker.framework.plat.model.RsToken;
import com.h3c.dzkf.uc2linker.framework.sessionfactory.UC2SessionFactory;
import com.h3c.dzkf.util.DesUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/srv6/oauth")
@Api(tags = "获取TOKEN接口")
public class TokenController {

    @Autowired
    private UC2SessionFactory uc2SessionFactory;

    @PostMapping("/token")
    @ApiOperation("获取Token")
    public ResponseEntity<?> getToken(@RequestBody String base64Body) {
        try {
            // 1. Base64解密
            log.info("接收到获取Token请求，Base64编码内容：{}", base64Body);
            String json = new String(Base64.getDecoder().decode(base64Body), StandardCharsets.UTF_8);
            log.info("解密后的JSON内容：{}", json);

            // 2. 解析json
            JSONObject obj = JSONObject.parseObject(json);
            String username = obj.getString("username");
            String desPassword = obj.getString("password");
            String timestamp = obj.getString("timestamp");

            // 3. DES解密
            String password = DesUtil.decrypt(desPassword);
            log.info("解密后的密码：{}", password);

            // 4. 调用uc2SessionFactory获取token
            RsToken rsToken = new RsToken();
            rsToken.setUserName(username);
            rsToken.setPassWord(password);
            RsToken tokenResult = uc2SessionFactory.getPlatComponent().getTokenFunction().generateToken(rsToken);
            if (tokenResult == null) {
                Map<String, Object> resp = buildFailResp("账号或密码错误");
                log.warn("Token获取失败，响应内容：{}", resp);
                return ResponseEntity.ok(resp);
            }

            // 返回token数据
            Map<String, Object> resp = buildSuccessResp(
                    tokenResult.getToken(),
                    "success",
                    tokenResult.getCreateTime(),
                    tokenResult.getEffectiveTime()
            );
            return ResponseEntity.ok(resp);
        } catch (Exception e) {
            Map<String, Object> resp = buildFailResp("参数或解密异常");
            log.error("Token获取异常，响应内容：{}", resp, e);
            return ResponseEntity.ok(resp);
        }
    }

    /**
     * 构造成功响应体
     */
    private static Map<String, Object> buildSuccessResp(String token, String msg, String createTime, Object effectiveTime) {
        Map<String, Object> resp = new HashMap<>();
        resp.put("X_Access_Token", token);
        resp.put("msg", msg);
        resp.put("createTime", createTime);
        resp.put("effectiveTime", effectiveTime);
        return resp;
    }

    /**
     * 构造失败响应体
     */
    private static Map<String, Object> buildFailResp(String msg) {
        return buildSuccessResp("", msg, "", 0);
    }
} 