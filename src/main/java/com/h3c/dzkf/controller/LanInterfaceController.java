package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.LanInterfaceService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * LAN口管理控制器
 * 所有接口都会通过拦截器自动进行Token验证
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "LAN口管理")
@Validated
public class LanInterfaceController {

    @Autowired
    private LanInterfaceService lanInterfaceService;

    /**
     * 新增LAN口接口
     */
    @PostMapping("/addLanInterface")
    @ApiOperation("新增LAN口")
    public ResponseEntity<AddLanInterfaceResponseDTO> addLanInterface(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddLanInterfaceRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增LAN口接口请求，请求ID：{}，设备ID：{}，接口名称：{}，Token：{}",
                requestId, request.getDeviceId(), request.getInterfaceName(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            AddLanInterfaceResponseDTO response = lanInterfaceService.addLanInterface(request, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除LAN口接口
     */
    @DeleteMapping("/deleteLanInterface/{lanId}")
    @ApiOperation("删除LAN口")
    public ResponseEntity<DeleteLanInterfaceResponseDTO> deleteLanInterface(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @PathVariable Long lanId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到删除LAN口接口请求，请求ID：{}，LAN口ID：{}，Token：{}",
                requestId, lanId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            DeleteLanInterfaceResponseDTO response = lanInterfaceService.deleteLanInterface(lanId, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改LAN口接口
     */
    @PutMapping("/updateLanInterface")
    @ApiOperation("修改LAN口")
    public ResponseEntity<ApiResponseDTO> updateLanInterface(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody UpdateLanInterfaceRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到修改LAN口接口请求，请求ID：{}，LAN口ID：{}，设备ID：{}，接口名称：{}，Token：{}",
                requestId, request.getLanId(), request.getDeviceId(), request.getInterfaceName(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            lanInterfaceService.updateLanInterface(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询LAN口接口
     */
    @GetMapping("/queryLanInterface")
    @ApiOperation("查询LAN口")
    public ResponseEntity<QueryLanInterfaceResponseDTO> queryLanInterface(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询LAN口接口请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryLanInterfaceResponseDTO response = lanInterfaceService.queryLanInterface(requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
} 