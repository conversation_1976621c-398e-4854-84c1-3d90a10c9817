package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.SiteService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;


/**
 * 站点管理控制器
 * 所有接口都会通过拦截器自动进行Token验证
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "站点管理接口")
@Validated
public class SiteController {

    @Autowired
    private SiteService siteService;

    /**
     * 新增站点
     */
    @PostMapping("/addSite")
    @ApiOperation("新增站点")
    public ResponseEntity<ApiResponseDTO> addSite(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddSiteRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增站点请求，请求ID：{}，站点ID：{}，Token：{}", requestId, request.getSiteId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            siteService.addSite(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除站点
     */
    @DeleteMapping("/deleteSite/{siteId}")
    @ApiOperation("删除站点")
    public ResponseEntity<ApiResponseDTO> deleteSite(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @PathVariable Integer siteId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到删除站点请求，请求ID：{}，站点ID：{}，Token：{}", requestId, siteId, accessToken);

        try {
            // 参数验证
            if (siteId == null) {
                log.warn("参数验证失败：站点ID不能为空，请求ID：{}", requestId);
                return ResponseEntity.ok(ApiResponseDTO.fail(requestId, "站点ID不能为空"));
            }

            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            siteService.deleteSite(siteId, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改站点
     */
    @PutMapping("/updateSite")
    @ApiOperation("修改站点")
    public ResponseEntity<ApiResponseDTO> updateSite(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody UpdateSiteRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到修改站点请求，请求ID：{}，站点ID：{}，Token：{}", requestId, request.getSiteId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            siteService.updateSite(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 批量导入站点模板
     */
    @PostMapping("/import_site_template")
    @ApiOperation("批量导入站点模板")
    public ResponseEntity<BatchImportSiteResponseDTO> importSiteTemplate(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("file") MultipartFile file) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到批量导入站点模板请求，请求ID：{}，文件名：{}，Token：{}",
                requestId, file.getOriginalFilename(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            BatchImportSiteResponseDTO response = siteService.importSiteTemplate(file, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询站点信息列表
     */
    @GetMapping("/querySiteInfoList")
    @ApiOperation("查询站点信息列表")
    public ResponseEntity<QuerySiteInfoListResponseDTO> querySiteInfoList(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询站点信息列表请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QuerySiteInfoListResponseDTO response = siteService.querySiteInfoList(requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
} 