package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.AppService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

/**
 * 应用管理控制器
 * 负责处理应用相关的API请求
 */
@RestController
@RequestMapping("/srv6")
@Api(tags = "应用管理")
@Slf4j
public class AppController {

    @Autowired
    private AppService appService;

    /**
     * 新增应用
     */
    @PostMapping("/addApp")
    @ApiOperation("新增应用")
    public ResponseEntity<ApiResponseDTO> addApp(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddAppRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增应用请求，请求ID：{}，应用ID：{}，Token：{}",
            requestId, request.getAppId(), accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            appService.addApp(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId, null));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改应用
     */
    @PutMapping("/modifyApp")
    @ApiOperation("修改应用")
    public ResponseEntity<ApiResponseDTO> modifyApp(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody ModifyAppRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到修改应用请求，请求ID：{}，应用ID：{}，Token：{}",
            requestId, request.getAppId(), accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            appService.modifyApp(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId, null));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除应用
     */
    @DeleteMapping("/delApp/{appId}")
    @ApiOperation("删除应用")
    public ResponseEntity<DeleteAppResponseDTO> deleteApp(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @PathVariable Integer appId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到删除应用请求，请求ID：{}，应用ID：{}，Token：{}",
            requestId, appId, accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            appService.deleteApp(appId, requestId);

            return ResponseEntity.ok(DeleteAppResponseDTO.success(requestId));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询应用列表
     */
    @GetMapping("/queryAppInfoList")
    @ApiOperation("查询应用列表")
    public ResponseEntity<QueryAppInfoListResponseDTO> queryAppInfoList(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询应用列表请求，请求ID：{}，Token：{}", requestId, accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            QueryAppInfoListResponseDTO response = appService.queryAppInfoList(requestId);

            return ResponseEntity.ok(response);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 根据应用ID查询单个应用
     */
    @GetMapping("/queryAppInfoListByAppId")
    @ApiOperation("根据应用ID查询单个应用")
    public ResponseEntity<QueryAppInfoByAppIdResponseDTO> queryAppInfoByAppId(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("appId") Integer appId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询单个应用请求，请求ID：{}，应用ID：{}，Token：{}", requestId, appId, accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            QueryAppInfoByAppIdResponseDTO response = appService.queryAppInfoByAppId(appId, requestId);

            return ResponseEntity.ok(response);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 批量导入应用模板
     */
    @PostMapping("/import_app_template")
    @ApiOperation("批量导入应用模板")
    public ResponseEntity<BatchImportAppResponseDTO> importAppTemplate(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("file") MultipartFile file) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到批量导入应用模板请求，请求ID：{}，文件名：{}，Token：{}",
                requestId, file.getOriginalFilename(), accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            BatchImportAppResponseDTO response = appService.importAppTemplate(file, requestId);

            return ResponseEntity.ok(response);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
}