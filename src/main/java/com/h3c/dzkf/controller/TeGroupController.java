package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.TeGroupService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 隧道组管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "隧道组管理接口")
@Validated
public class TeGroupController {

    @Autowired
    private TeGroupService teGroupService;

    /**
     * 新增隧道组
     *
     * @param accessToken X-Access-Token
     * @param request 新增隧道组请求参数
     * @return 响应结果
     */
    @PostMapping("/addTeGroup")
    @ApiOperation(value = "新增隧道组", notes = "根据工商总行定义的参数新增隧道组，对应ADWAN平台的SRv6 Policy应用组")
    public ResponseEntity<ApiResponseDTO> addTeGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestBody @Validated AddTeGroupRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到新增隧道组请求，隧道组ID：{}，请求ID：{}，Token：{}", request.getTeGroupDto().getId(), requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teGroupService.addTeGroup(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除隧道组
     *
     * @param accessToken X-Access-Token
     * @param request 删除隧道组请求参数
     * @return 响应结果
     */
    @PostMapping("/deleteTeGroup")
    @ApiOperation(value = "删除隧道组", notes = "根据工商总行定义的参数删除隧道组，同时删除ADWAN平台的SRv6 Policy应用组")
    public ResponseEntity<ApiResponseDTO> deleteTeGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestBody @Validated DeleteTeGroupRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到删除隧道组请求，隧道组ID集合：{}，请求ID：{}，Token：{}", request.getTeGroupIds(), requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teGroupService.deleteTeGroup(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 部署隧道组
     *
     * @param accessToken X-Access-Token
     * @param request 部署隧道组请求参数
     * @return 响应结果
     */
    @PostMapping("/deployTeGroup")
    @ApiOperation(value = "部署隧道组", notes = "根据工商总行定义的参数部署隧道组，调用ADWAN平台的应用组规划及部署接口")
    public ResponseEntity<ApiResponseDTO> deployTeGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestBody @Validated DeployTeGroupRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到部署隧道组请求，隧道组ID集合：{}，是否下发配置：{}，请求ID：{}，Token：{}",
                request.getTeGroupIds(), request.getDistribute(), requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teGroupService.deployTeGroup(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改隧道组
     *
     * @param accessToken X-Access-Token
     * @param request 修改隧道组请求参数
     * @return 响应结果
     */
    @PostMapping("/modifyTeGroup")
    @ApiOperation(value = "修改隧道组", notes = "根据工商总行定义的参数修改隧道组，对应ADWAN平台的SRv6 Policy应用组")
    public ResponseEntity<ApiResponseDTO> modifyTeGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestBody @Validated ModifyTeGroupRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到修改隧道组请求，隧道组ID：{}，请求ID：{}，Token：{}", request.getTeGroupDto().getId(), requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teGroupService.modifyTeGroup(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询隧道组
     *
     * @param accessToken X-Access-Token
     * @return 响应结果
     */
    @PostMapping("/getTeGroup")
    @ApiOperation(value = "查询隧道组", notes = "查询所有隧道组信息，包含部署状态")
    public ResponseEntity<GetTeGroupResponseDTO> getTeGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到查询隧道组请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            GetTeGroupResponseDTO response = teGroupService.getTeGroup(requestId);

            return ResponseEntity.ok(response);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询隧道详情
     */
    @PostMapping("/getTeGroupDetails")
    @ApiOperation(value = "查询隧道详情", notes = "根据隧道组ID列表查询隧道详情信息，包含正向和反向隧道")
    public ResponseEntity<GetTeGroupDetailsResponseDTO> getTeGroupDetails(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestBody @Validated GetTeGroupDetailsRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到查询隧道详情请求，隧道组ID列表：{}，设备名称：{}，部署状态：{}，请求ID：{}",
                request.getTeGroupIds(), request.getDevName(), request.getDeployState(), requestId);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            GetTeGroupDetailsResponseDTO response = teGroupService.getTeGroupDetails(request, requestId);

            return ResponseEntity.ok(response);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
}