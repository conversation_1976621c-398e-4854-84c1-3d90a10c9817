package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.DeviceService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;


/**
 * 设备管理控制器
 * 所有接口都会通过拦截器自动进行Token验证
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "设备管理接口")
@Validated
public class DeviceController {

    @Autowired
    private DeviceService deviceService;

    /**
     * 新增设备
     */
    @PostMapping("/addDevice")
    @ApiOperation("新增设备")
    public ResponseEntity<ApiResponseDTO> addDevice(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddDeviceRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增设备请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            deviceService.addDevice(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除设备
     */
    @DeleteMapping("/delDevice/{deviceId}")
    @ApiOperation("删除设备")
    public ResponseEntity<ApiResponseDTO> deleteDevice(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @PathVariable Long deviceId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到删除设备请求，请求ID：{}，设备ID：{}，Token：{}", requestId, deviceId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            deviceService.deleteDevice(deviceId, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 更新设备名称
     */
    @PutMapping("/updateDevice")
    @ApiOperation("更新设备名称")
    public ResponseEntity<ApiResponseDTO> updateDevice(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody UpdateDeviceRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到更新设备名称请求，请求ID：{}，设备ID：{}，Token：{}", requestId, request.getDeviceId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            deviceService.updateDevice(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询站点下所有设备信息
     */
    @GetMapping("/queryDeviceInfoListBySiteId")
    @ApiOperation("查询站点下所有设备信息")
    public ResponseEntity<QueryDeviceInfoListBySiteIdResponseDTO> queryDeviceInfoListBySiteId(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("siteId") Integer siteId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询站点下设备信息请求，请求ID：{}，站点ID：{}，Token：{}", requestId, siteId, accessToken);

        try {
            // 参数验证
            if (siteId == null) {
                throw new IllegalArgumentException("站点ID不能为空");
            }

            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryDeviceInfoListBySiteIdResponseDTO response = deviceService.queryDeviceInfoListBySiteId(siteId, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询单个设备信息
     */
    @GetMapping("/queryDeviceInfoDetail")
    @ApiOperation("查询单个设备信息")
    public ResponseEntity<QueryDeviceInfoDetailResponseDTO> queryDeviceInfoDetail(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("deviceId") Long deviceId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询单个设备信息请求，请求ID：{}，设备ID：{}，Token：{}", requestId, deviceId, accessToken);

        try {
            // 参数验证
            if (deviceId == null) {
                throw new IllegalArgumentException("设备ID不能为空");
            }

            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryDeviceInfoDetailResponseDTO response = deviceService.queryDeviceInfoDetail(deviceId, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 批量导入设备模板
     */
    @PostMapping("/import_device_template")
    @ApiOperation("批量导入设备模板")
    public ResponseEntity<BatchImportDeviceResponseDTO> importDeviceTemplate(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("file") MultipartFile file) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到批量导入设备模板请求，请求ID：{}，文件名：{}，Token：{}",
                requestId, file.getOriginalFilename(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            BatchImportDeviceResponseDTO response = deviceService.importDeviceTemplate(file, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询设备型号列表
     */
    @GetMapping("/queryDeviceModel")
    @ApiOperation("查询设备型号列表")
    public ResponseEntity<QueryDeviceModelResponseDTO> queryDeviceModel(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询设备型号列表请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryDeviceModelResponseDTO response = deviceService.queryDeviceModel(requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询设备状态信息
     */
    @GetMapping("/queryDeviceStatus")
    @ApiOperation("查询设备状态信息")
    public ResponseEntity<QueryDeviceStatusResponseDTO> queryDeviceStatus(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @RequestParam("siteId") Integer siteId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询设备状态信息请求，请求ID：{}，站点ID：{}，Token：{}", requestId, siteId, accessToken);

        try {
            // 参数验证
            if (siteId == null) {
                throw new IllegalArgumentException("站点ID不能为空");
            }

            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryDeviceStatusResponseDTO response = deviceService.queryDeviceStatus(siteId, requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
}