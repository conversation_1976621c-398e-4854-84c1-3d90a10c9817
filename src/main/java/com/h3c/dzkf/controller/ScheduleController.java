package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.ScheduleService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 调度策略管理控制器
 * 负责处理调度策略相关的API请求
 */
@RestController
@RequestMapping("/srv6")
@Api(tags = "调度策略管理")
@Slf4j
public class ScheduleController {

    @Autowired
    private ScheduleService scheduleService;

    /**
     * 新增调度策略
     */
    @PostMapping("/addSchedule")
    @ApiOperation("新增调度策略")
    public ResponseEntity<ApiResponseDTO> addSchedule(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddScheduleRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增调度策略请求，请求ID：{}，调度策略ID：{}，Token：{}",
                requestId, request.getAppScheduleId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            scheduleService.addSchedule(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改调度策略
     */
    @PutMapping("/modifySchedule")
    @ApiOperation("修改调度策略")
    public ResponseEntity<ApiResponseDTO> modifySchedule(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody ModifyScheduleRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到修改调度策略请求，请求ID：{}，调度策略ID：{}，Token：{}",
                requestId, request.getAppScheduleId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            scheduleService.modifySchedule(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除调度策略
     */
    @DeleteMapping("/delSchedule/{appScheduleId}")
    @ApiOperation("删除调度策略")
    public ResponseEntity<ApiResponseDTO> deleteSchedule(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @PathVariable("appScheduleId") Long appScheduleId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到删除调度策略请求，请求ID：{}，调度策略ID：{}，Token：{}",
                requestId, appScheduleId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 构建请求对象
            DeleteScheduleRequestDTO request = new DeleteScheduleRequestDTO();
            request.setAppScheduleId(appScheduleId);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            scheduleService.deleteSchedule(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询调度策略列表
     */
    @GetMapping("/queryScheduleList")
    @ApiOperation("查询调度策略列表")
    public ResponseEntity<QueryScheduleListResponseDTO> queryScheduleList(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询调度策略列表请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryScheduleListResponseDTO response = scheduleService.queryScheduleList(requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
}
