package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.TeTunnelTemplateService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 隧道模板管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "隧道模板管理接口")
public class TeTunnelTemplateController {

    @Autowired
    private TeTunnelTemplateService teTunnelTemplateService;

    /**
     * 新增隧道模板
     *
     * @param accessToken X-Access-Token
     * @param request 隧道模板请求参数
     * @return 响应结果
     */
    @PostMapping("/addTeTunnelTemplate")
    @ApiOperation(value = "新增隧道模板", notes = "根据参数新增隧道模板到定制库中")
    public ApiResponseDTO addTeTunnelTemplate(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Validated @RequestBody AddTeTunnelTemplateRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到新增隧道模板请求，请求ID：{}，策略ID：{}，Token：{}", requestId, request.getStrategy().getStrategyId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teTunnelTemplateService.addTeTunnelTemplate(request, requestId);

            return ApiResponseDTO.success(requestId);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除隧道模板
     *
     * @param accessToken X-Access-Token
     * @param request 删除隧道模板请求参数
     * @return 响应结果
     */
    @PostMapping("/deleteTeTunnelTemplate")
    @ApiOperation(value = "删除隧道模板", notes = "根据策略ID集合删除对应的隧道模板")
    public ApiResponseDTO deleteTeTunnelTemplate(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Validated @RequestBody DeleteTeTunnelTemplateRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到删除隧道模板请求，请求ID：{}，策略ID集合：{}，Token：{}", requestId, request.getStrategyIds(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teTunnelTemplateService.deleteTeTunnelTemplate(request, requestId);

            return ApiResponseDTO.success(requestId);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改隧道模板
     *
     * @param accessToken X-Access-Token
     * @param request 修改隧道模板请求参数
     * @return 响应结果
     */
    @PostMapping("/updateTeTunnelTemplate")
    @ApiOperation(value = "修改隧道模板", notes = "根据策略ID修改对应的隧道模板信息")
    public ApiResponseDTO updateTeTunnelTemplate(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Validated @RequestBody UpdateTeTunnelTemplateRequestDTO request) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到修改隧道模板请求，请求ID：{}，策略ID：{}，Token：{}", requestId, request.getStrategy().getStrategyId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            teTunnelTemplateService.updateTeTunnelTemplate(request, requestId);

            return ApiResponseDTO.success(requestId);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询隧道模板列表
     *
     * @param accessToken X-Access-Token
     * @return 响应结果
     */
    @PostMapping("/getAllTeTunnelTemplate")
    @ApiOperation(value = "查询隧道模板列表", notes = "查询定制库中的所有隧道模板数据")
    public QueryTeTunnelTemplateResponseDTO queryTeTunnelTemplateList(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        log.info("收到查询隧道模板列表请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            return teTunnelTemplateService.queryTeTunnelTemplateList(requestId);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

} 