package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.AddAppGroupRequestDTO;
import com.h3c.dzkf.entity.dto.ApiResponseDTO;
import com.h3c.dzkf.entity.dto.ModifyAppGroupRequestDTO;
import com.h3c.dzkf.entity.dto.QueryAppGroupListResponseDTO;
import com.h3c.dzkf.service.AppGroupService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 应用组管理控制器
 * 所有接口都会通过拦截器自动进行Token验证
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "应用组管理接口")
@Validated
public class AppGroupController {

    @Autowired
    private AppGroupService appGroupService;

    /**
     * 新增应用组
     */
    @PostMapping("/addAppGroup")
    @ApiOperation("新增应用组")
    public ResponseEntity<ApiResponseDTO> addAppGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddAppGroupRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增应用组请求，请求ID：{}，应用组ID：{}，Token：{}",
            requestId, request.getAppGroupId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            appGroupService.addAppGroup(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 删除应用组
     */
    @DeleteMapping("/delAppGroup/{appGroupId}")
    @ApiOperation("删除应用组")
    public ResponseEntity<ApiResponseDTO> deleteAppGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @PathVariable Integer appGroupId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到删除应用组请求，请求ID：{}，应用组ID：{}，Token：{}",
            requestId, appGroupId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            appGroupService.deleteAppGroup(appGroupId, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 修改应用组
     */
    @PutMapping("/modifyAppGroup")
    @ApiOperation("修改应用组")
    public ResponseEntity<ApiResponseDTO> modifyAppGroup(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody ModifyAppGroupRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到修改应用组请求，请求ID：{}，应用组ID：{}，Token：{}",
            requestId, request.getAppGroupId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            appGroupService.modifyAppGroup(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询应用组列表
     */
    @GetMapping("/queryAppGroupList")
    @ApiOperation("查询应用组列表")
    public ResponseEntity<QueryAppGroupListResponseDTO> queryAppGroupList(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询应用组列表请求，请求ID：{}，Token：{}", requestId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            QueryAppGroupListResponseDTO response = appGroupService.queryAppGroupList(requestId);

            return ResponseEntity.ok(response);

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
} 