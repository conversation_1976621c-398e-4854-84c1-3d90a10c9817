package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.QueryApiRequestLogResponseDTO;
import com.h3c.dzkf.service.ApiRequestLogService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * API请求日志控制器
 * 负责处理API请求日志相关的API请求
 */
@RestController
@RequestMapping("/srv6")
@Api(tags = "API请求日志管理")
@Slf4j
public class ApiRequestLogController {

    @Autowired
    private ApiRequestLogService apiRequestLogService;

    /**
     * 根据请求ID查询API请求日志
     */
    @GetMapping("/queryApiRequestLog/{requestId}")
    @ApiOperation("根据请求ID查询API请求日志")
    public ResponseEntity<QueryApiRequestLogResponseDTO> queryApiRequestLog(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @ApiParam(value = "请求ID", required = true, example = "req_123456")
            @PathVariable @NotBlank(message = "请求ID不能为空") String requestId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String currentRequestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询API请求日志请求，当前请求ID：{}，查询的请求ID：{}，Token：{}",
                currentRequestId, requestId, accessToken);

        // 将token设置到上下文中，供下游服务使用
        TokenContext.setToken(accessToken);

        try {
            // 调用服务层处理业务逻辑，异常会传播到GlobalExceptionHandler
            QueryApiRequestLogResponseDTO response = apiRequestLogService.queryApiRequestLogByRequestId(requestId, currentRequestId);

            return ResponseEntity.ok(response);
        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
}
