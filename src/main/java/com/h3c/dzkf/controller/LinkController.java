package com.h3c.dzkf.controller;

import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.LinkService;
import com.h3c.dzkf.util.RequestIdUtil;
import com.h3c.dzkf.util.TokenContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 链路管理控制器
 * 所有接口都会通过拦截器自动进行Token验证
 */
@Slf4j
@RestController
@RequestMapping("/srv6")
@Api(tags = "链路管理接口")
@Validated
public class LinkController {

    @Autowired
    private LinkService linkService;

    /**
     * 新增链路
     */
    @PostMapping("/addLink")
    @ApiOperation("新增链路")
    public ResponseEntity<AddLinkResponseDTO> addLink(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody AddLinkRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到新增链路请求，请求ID：{}，链路名称：{}，Token：{}", requestId, request.getLinkName(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            Long linkId = linkService.addLink(request, requestId);

            return ResponseEntity.ok(AddLinkResponseDTO.success(requestId, linkId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 更新链路
     */
    @PutMapping("/updateLink")
    @ApiOperation("更新链路")
    public ResponseEntity<ApiResponseDTO> updateLink(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @Valid @RequestBody UpdateLinkRequestDTO request) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到更新链路请求，请求ID：{}，链路ID：{}，Token：{}", requestId, request.getLinkId(), accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            linkService.updateLink(request, requestId);

            return ResponseEntity.ok(ApiResponseDTO.success(requestId));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }

    /**
     * 查询站点下所有链路信息
     */
    @GetMapping("/queryLinkInfoListBySiteId")
    @ApiOperation("查询站点下所有链路信息")
    public ResponseEntity<QueryLinkInfoListBySiteIdResponseDTO> queryLinkInfoListBySiteId(
            @RequestHeader(value = "X-Access-Token", required = true) String accessToken,
            @ApiParam(value = "站点ID", required = true)
            @RequestParam("siteId") @NotNull(message = "站点ID不能为空") Integer siteId) {

        // 从切面中获取requestId，如果获取不到则生成新的（兜底方案）
        String requestId = RequestIdUtil.getCurrentRequestId();

        log.info("接收到查询站点下链路信息请求，请求ID：{}，站点ID：{}，Token：{}", requestId, siteId, accessToken);

        try {
            // 将token设置到上下文中，供下游服务使用
            TokenContext.setToken(accessToken);

            // 调用服务层处理业务逻辑，异常由全局异常处理器处理
            java.util.List<QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO> linkInfoList = linkService.queryLinkInfoListBySiteId(siteId, requestId);

            return ResponseEntity.ok(QueryLinkInfoListBySiteIdResponseDTO.success(requestId, linkInfoList));

        } finally {
            // 清除token上下文，防止内存泄漏
            TokenContext.clearToken();
        }
    }
}