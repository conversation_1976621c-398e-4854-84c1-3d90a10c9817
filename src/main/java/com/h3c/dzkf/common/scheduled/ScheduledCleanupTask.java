package com.h3c.dzkf.common.scheduled;

import com.h3c.dzkf.service.ApiRequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时清理任务
 * 负责定时清理系统中的历史数据
 */
@Slf4j
@Component
public class ScheduledCleanupTask {

    @Autowired
    private ApiRequestLogService apiRequestLogService;

    /**
     * 保留的日志天数，默认3天
     */
    @Value("${cleanup.api-log.keep-days:3}")
    private int keepDays;

    /**
     * 是否启用性能优化模式，默认启用
     */
    @Value("${cleanup.api-log.performance-optimization:true}")
    private boolean performanceOptimization;

    /**
     * 分批删除的批次大小，默认1000条
     */
    @Value("${cleanup.api-log.batch-size:1000}")
    private int batchSize;

    /**
     * 单次任务最大删除数量限制，默认50000条
     */
    @Value("${cleanup.api-log.max-delete-count:50000}")
    private long maxDeleteCount;

    /**
     * 批次间延迟时间（毫秒），默认100ms
     */
    @Value("${cleanup.api-log.batch-delay-ms:100}")
    private long batchDelayMs;

    /**
     * 定时清理API请求日志
     * 默认每天0点10分执行，可通过配置文件修改
     */
    @Scheduled(cron = "${cleanup.api-log.cron:0 10 0 * * ?}")
    public void cleanupApiRequestLogs() {
        log.info("开始执行API请求日志定时清理任务，保留近{}天的数据，性能优化模式：{}", 
                keepDays, performanceOptimization ? "启用" : "禁用");
        
        try {
            int deletedCount;
            
            if (performanceOptimization) {
                // 使用性能优化的分批删除
                deletedCount = apiRequestLogService.cleanupOldLogsOptimized(
                        keepDays, batchSize, maxDeleteCount, batchDelayMs);
                        
                log.info("API请求日志定时清理任务执行完成（分批模式），共删除{}条记录", deletedCount);
            } else {
                // 使用传统的一次性删除
                deletedCount = apiRequestLogService.cleanupOldLogs(keepDays);
                log.info("API请求日志定时清理任务执行完成（传统模式），共删除{}条记录", deletedCount);
            }
            
            // 如果删除的记录数接近最大限制，给出提示
            if (performanceOptimization && deletedCount >= maxDeleteCount * 0.9) {
                log.warn("本次删除的记录数({})接近最大限制({})，可能还有更多历史数据需要在下次执行时清理", 
                        deletedCount, maxDeleteCount);
            }
            
        } catch (Exception e) {
            log.error("API请求日志定时清理任务执行失败", e);
        }
    }
} 