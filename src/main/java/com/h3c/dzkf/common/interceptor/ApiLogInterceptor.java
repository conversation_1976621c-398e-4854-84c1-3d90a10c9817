package com.h3c.dzkf.common.interceptor;

import cn.hutool.core.util.StrUtil;
import com.h3c.dzkf.entity.ApiRequestLog;
import com.h3c.dzkf.service.ApiRequestLogService;
import com.h3c.dzkf.util.RequestIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

/**
 * API日志记录拦截器
 * 
 * 优先级：最高（order = 0）
 * 职责：记录所有API请求和响应日志
 * 
 * 优势：
 * 1. 能记录所有请求，包括Token验证失败的
 * 2. 统一的日志记录逻辑
 * 3. 可以获取完整的响应内容
 * 4. 执行顺序可控
 */
@Slf4j
@Component
public class ApiLogInterceptor implements HandlerInterceptor {

    @Autowired
    private ApiRequestLogService apiRequestLogService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 生成requestId
        String requestId = UUID.randomUUID().toString().replace("-", "");
        RequestIdUtil.setCurrentRequestId(requestId);
        
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        request.setAttribute("startTime", startTime);
        request.setAttribute("requestId", requestId);
        
        log.debug("开始处理请求 - 请求ID: {}, URI: {}, 方法: {}", 
                requestId, request.getRequestURI(), request.getMethod());
        
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 这里可以做一些后处理，但主要的日志记录在afterCompletion中
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            String requestId = (String) request.getAttribute("requestId");
            Long startTime = (Long) request.getAttribute("startTime");
            
            if (startTime == null) {
                startTime = System.currentTimeMillis();
            }
            
            long duration = System.currentTimeMillis() - startTime;
            
            // 获取请求体
            String requestBody = getRequestBody(request);
            
            // 获取响应体
            String responseBody = getResponseBody(response);
            
            // 创建日志记录
            ApiRequestLog logEntity = new ApiRequestLog();
            logEntity.setRequestId(requestId);
            logEntity.setRequestUri(request.getRequestURI());
            logEntity.setRequestMethod(request.getMethod());
            logEntity.setClientIp(getRealClientIp(request));
            logEntity.setRequestBody(requestBody);
            logEntity.setResponseBody(responseBody);
            logEntity.setRequestTime(new Date(startTime));
            logEntity.setDurationMs((int) duration);
            logEntity.setCreateTime(new Date());

            // 判断请求是否成功（根据响应状态码或响应内容）
            boolean isSuccess = isRequestSuccess(response, responseBody);
            String logType = isSuccess ? "API成功调用" : "API失败调用";
            
            // 打印请求和响应到日志
            log.info("{} - 请求ID: {}, URI: {}, 方法: {}, 客户端IP: {}, 状态码: {}, 请求体: {}, 响应体: {}, 耗时: {}ms",
                    logType,
                    requestId,
                    request.getRequestURI(),
                    request.getMethod(),
                    getRealClientIp(request),
                    response.getStatus(),
                    requestBody,
                    responseBody,
                    duration
            );

            // 保存日志
            apiRequestLogService.saveLog(logEntity);
            
        } catch (Exception e) {
            log.error("记录API日志异常", e);
        } finally {
            // 清理ThreadLocal
            RequestIdUtil.clearCurrentRequestId();
        }
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        String requestBody = "";
        if ("POST".equalsIgnoreCase(request.getMethod()) || "PUT".equalsIgnoreCase(request.getMethod())) {
            if (request instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                byte[] buf = wrapper.getContentAsByteArray();
                if (buf.length > 0) {
                    try {
                        requestBody = new String(buf, StandardCharsets.UTF_8);
                    } catch (Exception e) {
                        log.warn("获取请求体失败：{}", e.getMessage());
                    }
                }
            }
        }
        return requestBody;
    }

    /**
     * 获取响应体内容
     */
    private String getResponseBody(HttpServletResponse response) {
        String responseBody = "";
        if (response instanceof ContentCachingResponseWrapper) {
            ContentCachingResponseWrapper wrapper = (ContentCachingResponseWrapper) response;
            byte[] buf = wrapper.getContentAsByteArray();
            if (buf.length > 0) {
                try {
                    responseBody = new String(buf, StandardCharsets.UTF_8);
                } catch (Exception e) {
                    log.warn("获取响应体失败：{}", e.getMessage());
                }
            }
        }
        return responseBody;
    }

    /**
     * 判断请求是否成功
     */
    private boolean isRequestSuccess(HttpServletResponse response, String responseBody) {
        // 根据HTTP状态码判断
        if (response.getStatus() >= 400) {
            return false;
        }
        
        // 根据响应体中的result字段判断（如果是JSON格式）
        if (StrUtil.isNotBlank(responseBody) && responseBody.contains("\"result\"")) {
            try {
                // 简单的字符串匹配，也可以用JSON解析
                return responseBody.contains("\"result\":1") || responseBody.contains("\"result\": 1");
            } catch (Exception e) {
                // 解析失败时根据状态码判断
                return response.getStatus() < 400;
            }
        }
        
        return true;
    }

    /**
     * 获取真实客户端IP地址
     */
    private String getRealClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多级代理的情况下，第一个IP为客户端真实IP
            int index = ip.indexOf(",");
            if (index != -1) {
                return ip.substring(0, index).trim();
            } else {
                return ip.trim();
            }
        }

        ip = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getRemoteAddr();
        // 如果是本地IPv6地址，转换为IPv4
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "127.0.0.1";
        }

        return ip;
    }
} 