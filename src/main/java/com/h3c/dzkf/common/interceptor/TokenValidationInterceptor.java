package com.h3c.dzkf.common.interceptor;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.h3c.dzkf.entity.dto.ApiResponseDTO;
import com.h3c.dzkf.entity.result.TokenValidationResult;
import com.h3c.dzkf.service.TokenValidationService;
import com.h3c.dzkf.util.RequestIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * Token验证拦截器
 * <p>
 * 优先级：次高（order = 1）
 * 职责：专注于Token验证，不处理日志记录
 * <p>
 * 注意：日志记录由ApiLogInterceptor统一处理
 */
@Slf4j
@Component
public class TokenValidationInterceptor implements HandlerInterceptor {

    @Autowired
    private TokenValidationService tokenValidationService;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    private ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 不需要Token验证的接口路径（相对路径，不包含context-path）
     * 只有这些接口不需要验证Token
     */
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
            "/srv6/oauth/token",     // 获取Token接口
            "/swagger-ui",           // Swagger UI
            "/v2/api-docs",         // API文档
            "/swagger-resources",    // Swagger资源
            "/webjars",             // Web静态资源
            "/favicon.ico",         // 网站图标
            "/actuator"             // Spring Boot监控端点（如果有的话）
    );

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String requestMethod = request.getMethod();

        log.debug("Token验证拦截器 - 请求：{} {}", requestMethod, requestURI);

        // 检查是否需要跳过Token验证
        if (shouldSkipValidation(requestURI)) {
            log.debug("跳过Token验证：{}", requestURI);
            return true;
        }

        // 获取requestId（由ApiLogInterceptor设置）
        String requestId = RequestIdUtil.getCurrentRequestId();

        // 获取Token
        String token = getTokenFromRequest(request);

        log.debug("开始Token验证，请求ID：{}，URI：{}，Token：{}", requestId, requestURI, token);

        // 验证Token
        TokenValidationResult validationResult = tokenValidationService.validateTokenWithResult(token);

        if (!validationResult.isValid()) {
            // Token验证失败，返回错误响应（日志记录由ApiLogInterceptor处理）
            handleTokenValidationFailure(response, requestId, validationResult.getErrorMessage(), requestURI);
            return false;
        }

        log.debug("Token验证成功，请求ID：{}", requestId);
        return true;
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        // 优先从X-Access-Token头获取
        String token = request.getHeader("X-Access-Token");

        if (StrUtil.isBlank(token)) {
            // 尝试从Authorization头获取
            token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
        }

        return token;
    }

    /**
     * 检查是否应该跳过Token验证
     * 支持带有context-path的请求URI精确匹配
     */
    private boolean shouldSkipValidation(String requestURI) {
        return EXCLUDE_PATHS.stream().anyMatch(excludePath -> {
            // 直接匹配相对路径
            if (requestURI.equals(excludePath) || requestURI.startsWith(excludePath + "/")) {
                return true;
            }
            
            // 如果配置了context-path，则匹配完整路径
            if (StrUtil.isNotBlank(contextPath)) {
                String fullPath = contextPath + excludePath;
                return requestURI.equals(fullPath) || requestURI.startsWith(fullPath + "/");
            }
            
            return false;
        });
    }

    /**
     * 处理Token验证失败的情况
     */
    private void handleTokenValidationFailure(HttpServletResponse response, String requestId,
                                              String errorMessage, String requestURI) throws Exception {
        log.warn("Token验证失败，请求ID：{}，URI：{}，错误信息：{}", requestId, requestURI, errorMessage);

        // 设置响应状态和内容类型
        response.setStatus(HttpServletResponse.SC_OK);
        response.setContentType("application/json;charset=UTF-8");

        // 创建错误响应
        ApiResponseDTO errorResponse = ApiResponseDTO.fail(requestId, errorMessage);
        String jsonResponse = objectMapper.writeValueAsString(errorResponse);

        // 写入响应
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
} 