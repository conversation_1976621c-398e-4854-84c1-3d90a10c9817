package com.h3c.dzkf.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Token DES加密配置类
 * 用于OAuth Token接口的DES加密配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "oauth.token.des")
public class DesConfig {
    
    /**
     * Token DES加密密钥，必须是8字节
     * 用于OAuth获取Token接口的参数加密
     * 默认值：h3c_dzkf
     */
    private String key = "h3c_dzkf";
    
    /**
     * 验证密钥长度是否有效
     * @return 是否有效
     */
    public boolean isValidKey() {
        return key != null && key.getBytes().length == 8;
    }
} 