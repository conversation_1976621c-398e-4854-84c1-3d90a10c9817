package com.h3c.dzkf.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 测试模式配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "test.mode")
public class TestModeConfig {

    /**
     * 是否启用测试模式
     */
    private boolean enabled = false;

    /**
     * 是否跳过token验证
     */
    private boolean skipTokenValidation = false;

    /**
     * 是否模拟ADWAN平台调用
     */
    private boolean mockAdwanPlatform = false;

    /**
     * 模拟返回的设备ID范围
     */
    private MockDeviceIdRange mockDeviceIdRange = new MockDeviceIdRange();

    @Data
    public static class MockDeviceIdRange {
        /**
         * 最小设备ID
         */
        private long min = 100000000L;

        /**
         * 最大设备ID
         */
        private long max = 999999999L;
    }
} 