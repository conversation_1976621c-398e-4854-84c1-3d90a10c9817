package com.h3c.dzkf.common.config;

import com.h3c.dzkf.common.interceptor.ApiLogInterceptor;
import com.h3c.dzkf.common.interceptor.TokenValidationInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.Filter;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Web MVC 配置
 * 包含Swagger资源处理、请求体缓存过滤器、响应体缓存过滤器和拦截器配置
 */
@Slf4j
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private ApiLogInterceptor apiLogInterceptor;

    @Autowired
    private TokenValidationInterceptor tokenValidationInterceptor;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("注册API拦截器");
        
        // 1. 日志拦截器 - 最高优先级，记录所有请求
        registry.addInterceptor(apiLogInterceptor)
                .addPathPatterns("/srv6/**")
                .order(0);  // 最高优先级
        
        // 2. Token验证拦截器 - 次优先级，进行Token验证
        registry.addInterceptor(tokenValidationInterceptor)
                .addPathPatterns("/srv6/**")
                .order(1);  // 次优先级
        
        log.info("API拦截器注册完成 - 日志拦截器(order=0), Token验证拦截器(order=1)");
    }

    // 注册RequestBodyCacheFilter
    @Bean
    public FilterRegistrationBean<Filter> requestBodyCacheFilter() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter((request, response, chain) -> {
            ServletRequest requestWrapper = request;
            ServletResponse responseWrapper = response;
            
            if (request instanceof HttpServletRequest) {
                requestWrapper = new ContentCachingRequestWrapper((HttpServletRequest) request);
            }
            
            if (response instanceof HttpServletResponse) {
                responseWrapper = new ContentCachingResponseWrapper((HttpServletResponse) response);
            }
            
            chain.doFilter(requestWrapper, responseWrapper);
            
            // 确保响应内容被写入到客户端
            if (responseWrapper instanceof ContentCachingResponseWrapper) {
                ((ContentCachingResponseWrapper) responseWrapper).copyBodyToResponse();
            }
        });
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(1);
        return registrationBean;
    }

} 