package com.h3c.dzkf.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 设备API配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "device.api")
public class DeviceApiConfig {
    
    /**
     * 基础URL
     */
    private String baseUrl = "http://10.88.45.240:30000";
    
    /**
     * 资源查询接口路径
     */
    private String resourceQueryUrl = "/ucpresource/v2/resources";
    
    /**
     * 设备详情查询接口路径
     */
    private String deviceDetailUrl = "/resrs/device";
    
    /**
     * 性能数据查询接口路径
     */
    private String performanceDataUrl = "/perfrs/data/queryTaskPerfData";
} 