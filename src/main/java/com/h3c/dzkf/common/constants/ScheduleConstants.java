package com.h3c.dzkf.common.constants;

/**
 * 调度策略相关常量
 */
public class ScheduleConstants {

    /**
     * 引流类型常量
     */
    public static class DrainageType {
        /**
         * 五元组
         */
        public static final int FIVE_TUPLE = 0;
        /**
         * DSCP
         */
        public static final int DSCP = 1;
        /**
         * VPN
         */
        public static final int VPN = 4;
    }

    /**
     * SLA ID映射常量
     */
    public static class SlaMapping {
        /**
         * EF对应的数值
         */
        public static final int EF_VALUE = 46;
        /**
         * AF4对应的数值
         */
        public static final int AF4_VALUE = 34;
        /**
         * AF3对应的数值
         */
        public static final int AF3_VALUE = 26;
        /**
         * AF2对应的数值
         */
        public static final int AF2_VALUE = 18;
        /**
         * AF1对应的数值
         */
        public static final int AF1_VALUE = 10;
        /**
         * BE对应的数值
         */
        public static final int BE_VALUE = 0;
    }

    /**
     * Remark类型常量
     */
    public static class RemarkType {
        /**
         * 使用隧道模板的sla_id转换值
         */
        public static final int SLA_ID_REMARK = 1;
        /**
         * 使用隧道组的positive_service_class
         */
        public static final int SERVICE_CLASS_REMARK = 16;
        /**
         * 使用隧道模板的sla_id转换值（第二个）
         */
        public static final int SLA_ID_REMARK_2 = 17;
    }

    /**
     * 流分类相关常量
     */
    public static class Classifier {
        /**
         * 逻辑关系，固定为1
         */
        public static final int LOGIC_AND = 1;
        /**
         * 选择类型
         */
        public static final int SELECT_TYPE_DEFAULT = 2;
        /**
         * 匹配关系
         */
        public static final int MATCH_RELATION_DEFAULT = 1;
        /**
         * 整数值1默认值
         */
        public static final int INT_VALUE1_DEFAULT = 0;
        /**
         * 整数值2默认值
         */
        public static final int INT_VALUE2_DEFAULT = 1;
    }

    /**
     * 策略部署相关常量
     */
    public static class PolicyDeploy {
        /**
         * 方向默认值
         */
        public static final int DIRECTION_DEFAULT = 0;
        /**
         * 预序默认值
         */
        public static final int PREORDER_DEFAULT = -1;
        /**
         * 共享模式默认值
         */
        public static final boolean SHARE_MODE_DEFAULT = false;
    }

    /**
     * 命名规则常量
     */
    public static class NamingRule {
        /**
         * 流行为名称后缀
         */
        public static final String BEHAVIOR_NAME_SUFFIX = "_behavior";
        /**
         * 流分类名称后缀
         */
        public static final String CLASSIFIER_NAME_SUFFIX = "_classifier";
        /**
         * 流策略名称前缀
         */
        public static final String POLICY_NAME_PREFIX = "policy_";
        /**
         * ACL模板名称后缀
         */
        public static final String ACL_TEMPLATE_SUFFIX = "_ipv4";
    }
}
