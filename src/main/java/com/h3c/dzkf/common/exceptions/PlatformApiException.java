package com.h3c.dzkf.common.exceptions;

/**
 * 平台接口调用异常
 * <p>
 * 当调用ADWAN平台接口失败时抛出此异常，包含平台返回的具体错误信息
 */
public class PlatformApiException extends RuntimeException {

    /**
     * 平台返回的错误代码
     */
    private final String errorCode;

    /**
     * 平台返回的原始响应内容
     */
    private final String rawResponse;

    /**
     * HTTP状态码
     */
    private final Integer httpStatus;

    /**
     * 构造函数 - 仅包含错误消息
     *
     * @param message 错误消息
     */
    public PlatformApiException(String message) {
        super(message);
        this.errorCode = null;
        this.rawResponse = null;
        this.httpStatus = null;
    }

    /**
     * 构造函数 - 包含错误消息和错误代码
     *
     * @param message   错误消息
     * @param errorCode 错误代码
     */
    public PlatformApiException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
        this.rawResponse = null;
        this.httpStatus = null;
    }

    /**
     * 构造函数 - 包含错误消息、错误代码和原始响应
     *
     * @param message     错误消息
     * @param errorCode   错误代码
     * @param rawResponse 原始响应内容
     */
    public PlatformApiException(String message, String errorCode, String rawResponse) {
        super(message);
        this.errorCode = errorCode;
        this.rawResponse = rawResponse;
        this.httpStatus = null;
    }

    /**
     * 构造函数 - 包含错误消息、错误代码、原始响应和HTTP状态码
     *
     * @param message     错误消息
     * @param errorCode   错误代码
     * @param rawResponse 原始响应内容
     * @param httpStatus  HTTP状态码
     */
    public PlatformApiException(String message, String errorCode, String rawResponse, Integer httpStatus) {
        super(message);
        this.errorCode = errorCode;
        this.rawResponse = rawResponse;
        this.httpStatus = httpStatus;
    }

    /**
     * 构造函数 - 包含错误消息和原因异常
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public PlatformApiException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.rawResponse = null;
        this.httpStatus = null;
    }

    /**
     * 构造函数 - 包含错误消息、错误代码和原因异常
     *
     * @param message   错误消息
     * @param errorCode 错误代码
     * @param cause     原因异常
     */
    public PlatformApiException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.rawResponse = null;
        this.httpStatus = null;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取原始响应内容
     *
     * @return 原始响应内容
     */
    public String getRawResponse() {
        return rawResponse;
    }

    /**
     * 获取HTTP状态码
     *
     * @return HTTP状态码
     */
    public Integer getHttpStatus() {
        return httpStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("PlatformApiException{");
        sb.append("message='").append(getMessage()).append('\'');
        if (errorCode != null) {
            sb.append(", errorCode='").append(errorCode).append('\'');
        }
        if (httpStatus != null) {
            sb.append(", httpStatus=").append(httpStatus);
        }
        sb.append('}');
        return sb.toString();
    }
}
