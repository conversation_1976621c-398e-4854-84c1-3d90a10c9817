package com.h3c.dzkf.common.exceptions;

/**
 * 通用Service层异常
 * <p>
 * 用于Service层业务逻辑处理失败时抛出的异常。
 * 该异常继承自RuntimeException，能够正确触发Spring事务回滚。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0
 */
public class ServiceException extends RuntimeException {

    /**
     * 错误代码
     */
    private final String errorCode;

    /**
     * 业务上下文信息（如业务ID、操作类型等）
     */
    private final String businessContext;

    /**
     * 构造函数 - 仅包含错误消息
     *
     * @param message 错误消息
     */
    public ServiceException(String message) {
        super(message);
        this.errorCode = null;
        this.businessContext = null;
    }

    /**
     * 构造函数 - 包含错误代码和错误消息
     *
     * @param errorCode 错误代码
     * @param message   错误消息
     */
    public ServiceException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.businessContext = null;
    }

    /**
     * 构造函数 - 包含错误代码、错误消息和业务上下文
     *
     * @param errorCode       错误代码
     * @param message         错误消息
     * @param businessContext 业务上下文信息
     */
    public ServiceException(String errorCode, String message, String businessContext) {
        super(message);
        this.errorCode = errorCode;
        this.businessContext = businessContext;
    }

    /**
     * 构造函数 - 包含错误消息和原因异常
     *
     * @param message 错误消息
     * @param cause   原因异常
     */
    public ServiceException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = null;
        this.businessContext = null;
    }

    /**
     * 构造函数 - 包含错误代码、错误消息和原因异常
     *
     * @param errorCode 错误代码
     * @param message   错误消息
     * @param cause     原因异常
     */
    public ServiceException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.businessContext = null;
    }

    /**
     * 构造函数 - 包含错误代码、错误消息、业务上下文和原因异常
     *
     * @param errorCode       错误代码
     * @param message         错误消息
     * @param businessContext 业务上下文信息
     * @param cause           原因异常
     */
    public ServiceException(String errorCode, String message, String businessContext, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.businessContext = businessContext;
    }

    /**
     * 获取错误代码
     *
     * @return 错误代码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取业务上下文信息
     *
     * @return 业务上下文信息
     */
    public String getBusinessContext() {
        return businessContext;
    }

    /**
     * 从平台API异常转换为Service异常
     *
     * @param platformException 平台API异常
     * @param errorCode         错误代码
     * @return ServiceException
     */
    public static ServiceException fromPlatformException(PlatformApiException platformException, String errorCode) {
        return new ServiceException(errorCode, platformException.getMessage(), platformException);
    }

    /**
     * 从平台API异常转换为Service异常（包含业务上下文）
     *
     * @param platformException 平台API异常
     * @param errorCode         错误代码
     * @param businessContext   业务上下文
     * @return ServiceException
     */
    public static ServiceException fromPlatformException(PlatformApiException platformException, String errorCode, String businessContext) {
        return new ServiceException(errorCode, platformException.getMessage(), businessContext, platformException);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ServiceException{");
        sb.append("message='").append(getMessage()).append('\'');
        if (errorCode != null) {
            sb.append(", errorCode='").append(errorCode).append('\'');
        }
        if (businessContext != null) {
            sb.append(", businessContext='").append(businessContext).append('\'');
        }
        sb.append('}');
        return sb.toString();
    }
}
