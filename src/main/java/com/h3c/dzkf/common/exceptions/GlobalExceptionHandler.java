package com.h3c.dzkf.common.exceptions;

import com.h3c.dzkf.entity.dto.ApiResponseDTO;
import com.h3c.dzkf.util.RequestIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 统一处理各种异常，返回标准的API响应格式
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理 @Valid 注解的参数验证异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponseDTO> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        
        // 获取第一个验证错误信息
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String errorMessage = "参数验证失败";
        
        if (!fieldErrors.isEmpty()) {
            FieldError firstError = fieldErrors.get(0);
            errorMessage = firstError.getDefaultMessage();
            
            // 如果有多个错误，记录到日志中
            if (fieldErrors.size() > 1) {
                String allErrors = fieldErrors.stream()
                        .map(error -> error.getField() + ": " + error.getDefaultMessage())
                        .collect(Collectors.joining("; "));
                log.warn("参数验证失败，请求ID：{}，所有错误：{}", requestId, allErrors);
            }
        }
        
        log.warn("参数验证异常，请求ID：{}，错误信息：{}", requestId, errorMessage);
        
        ApiResponseDTO response = ApiResponseDTO.fail(requestId, errorMessage);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理 @Validated 注解的参数验证异常（@RequestParam、@PathVariable）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponseDTO> handleConstraintViolationException(ConstraintViolationException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = "参数验证失败";
        
        if (!violations.isEmpty()) {
            ConstraintViolation<?> firstViolation = violations.iterator().next();
            errorMessage = firstViolation.getMessage();
        }
        
        log.warn("参数约束验证异常，请求ID：{}，错误信息：{}", requestId, errorMessage);
        
        ApiResponseDTO response = ApiResponseDTO.fail(requestId, errorMessage);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理表单绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponseDTO> handleBindException(BindException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String errorMessage = "参数绑定失败";
        
        if (!fieldErrors.isEmpty()) {
            FieldError firstError = fieldErrors.get(0);
            errorMessage = firstError.getDefaultMessage();
        }
        
        log.warn("参数绑定异常，请求ID：{}，错误信息：{}", requestId, errorMessage);

        ApiResponseDTO response = ApiResponseDTO.fail(requestId, errorMessage);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理Service层业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public ResponseEntity<ApiResponseDTO> handleServiceException(ServiceException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        String errorMessage = e.getMessage() != null ? e.getMessage() : "业务处理失败";

        // 记录详细的错误信息到日志
        if (e.getBusinessContext() != null) {
            log.error("Service层业务异常，请求ID：{}，错误代码：{}，业务上下文：{}，错误信息：{}",
                    requestId, e.getErrorCode(), e.getBusinessContext(), errorMessage, e);
        } else {
            log.error("Service层业务异常，请求ID：{}，错误代码：{}，错误信息：{}",
                    requestId, e.getErrorCode(), errorMessage, e);
        }

        ApiResponseDTO response = ApiResponseDTO.fail(requestId, errorMessage);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理平台接口调用异常
     */
    @ExceptionHandler(PlatformApiException.class)
    public ResponseEntity<ApiResponseDTO> handlePlatformApiException(PlatformApiException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        String errorMessage = e.getMessage() != null ? e.getMessage() : "平台接口调用失败";

        // 记录详细的错误信息到日志
        if (e.getErrorCode() != null || e.getHttpStatus() != null) {
            log.error("平台接口调用异常，请求ID：{}，错误信息：{}，错误代码：{}，HTTP状态：{}",
                    requestId, errorMessage, e.getErrorCode(), e.getHttpStatus(), e);
        } else {
            log.error("平台接口调用异常，请求ID：{}，错误信息：{}", requestId, errorMessage, e);
        }

        ApiResponseDTO response = ApiResponseDTO.fail(requestId, errorMessage);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponseDTO> handleIllegalArgumentException(IllegalArgumentException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        String errorMessage = e.getMessage() != null ? e.getMessage() : "非法参数";

        log.warn("非法参数异常，请求ID：{}，错误信息：{}", requestId, errorMessage);

        ApiResponseDTO response = ApiResponseDTO.fail(requestId, errorMessage);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponseDTO> handleRuntimeException(RuntimeException e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        
        log.error("运行时异常，请求ID：{}", requestId, e);
        
        ApiResponseDTO response = ApiResponseDTO.fail(requestId, "系统异常");
        return ResponseEntity.ok(response);
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponseDTO> handleException(Exception e) {
        String requestId = RequestIdUtil.getCurrentRequestId();
        
        log.error("未捕获异常，请求ID：{}", requestId, e);
        
        ApiResponseDTO response = ApiResponseDTO.fail(requestId, "系统异常");
        return ResponseEntity.ok(response);
    }
} 