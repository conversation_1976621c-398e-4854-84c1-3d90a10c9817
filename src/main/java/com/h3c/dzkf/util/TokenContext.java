package com.h3c.dzkf.util;

/**
 * Token上下文工具类
 * 使用ThreadLocal在同一请求线程中传递认证token
 * 
 * 支持的认证方式：
 * - ADWAN平台：Cookie认证 (X-Subject-Token)
 * - 其他平台：可扩展支持Header认证
 */
public class TokenContext {
    
    private static final ThreadLocal<String> TOKEN_CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置当前线程的Token
     * 
     * @param token 认证token（通常从HTTP请求的X-Access-Token头部获取）
     */
    public static void setToken(String token) {
        TOKEN_CONTEXT.set(token);
    }
    
    /**
     * 获取当前线程的Token
     * 用于在PlatformApiService中添加到Cookie认证头
     * 
     * @return 认证token，如果没有设置则返回null
     */
    public static String getToken() {
        return TOKEN_CONTEXT.get();
    }
    
    /**
     * 清除当前线程的Token
     * 防止内存泄漏，通常在请求结束时调用
     * 必须在Controller的finally块中调用
     */
    public static void clearToken() {
        TOKEN_CONTEXT.remove();
    }
} 