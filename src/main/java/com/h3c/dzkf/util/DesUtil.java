package com.h3c.dzkf.util;

import com.h3c.dzkf.common.config.DesConfig;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.security.Security;
import java.util.Base64;

@Slf4j
@Component
public class DesUtil {
    
    private static final String ALGORITHM = "DES";
    private static final String TRANSFORMATION = "DES/ECB/PKCS7Padding";

    @Autowired
    private DesConfig desConfig;

    // 静态实例，用于静态方法调用
    private static DesUtil instance;

    static {
        // add BC provider
        Security.addProvider(new BouncyCastleProvider());
    }

    @PostConstruct
    public void init() {
        instance = this;
        // 验证密钥配置
        if (!desConfig.isValidKey()) {
            log.error("DES密钥配置无效，密钥必须是8字节长度，当前密钥：{}", desConfig.getKey());
            throw new IllegalArgumentException("DES密钥配置无效，密钥必须是8字节长度");
        }
        log.info("DES加密工具初始化完成，密钥长度：{} 字节", desConfig.getKey().getBytes().length);
    }

    /**
     * 获取配置的密钥
     */
    private static String getKey() {
        if (instance == null || instance.desConfig == null) {
            log.warn("DES配置未初始化，使用默认密钥");
            return "h3c_dzkf";
        }
        return instance.desConfig.getKey();
    }

    /**
     * DES加密
     *
     * @param data 待加密的字符串
     * @return 加密后的Base64字符串
     * @throws Exception 加密异常
     */
    public static String encrypt(String data) throws Exception {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("待加密数据不能为空");
        }

        String key = getKey();
        DESKeySpec desKeySpec = new DESKeySpec(key.getBytes());
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
        SecretKey secretKey = keyFactory.generateSecret(desKeySpec);

        Cipher cipher = Cipher.getInstance(TRANSFORMATION, "BC");
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encrypted = cipher.doFinal(data.getBytes("UTF-8"));

        return Base64.getEncoder().encodeToString(encrypted);
    }

    /**
     * DES解密
     *
     * @param data 待解密的Base64字符串
     * @return 解密后的字符串
     * @throws Exception 解密异常
     */
    public static String decrypt(String data) throws Exception {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("待解密数据不能为空");
        }

        try {
            // 验证Base64格式
            byte[] bytes = Base64.getDecoder().decode(data);

            // 检查数据长度是否为8的倍数（DES块大小）
            if (bytes.length % 8 != 0) {
                throw new IllegalArgumentException("加密数据长度无效，不是8字节的倍数");
            }

            String key = getKey();
            DESKeySpec desKeySpec = new DESKeySpec(key.getBytes());
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            SecretKey secretKey = keyFactory.generateSecret(desKeySpec);

            Cipher cipher = Cipher.getInstance(TRANSFORMATION, "BC");
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decrypted = cipher.doFinal(bytes);

            return new String(decrypted, "UTF-8");

        } catch (IllegalArgumentException e) {
            if (e.getMessage().contains("Illegal base64 character")) {
                throw new Exception("输入数据不是有效的Base64格式", e);
            }
            throw e;
        } catch (javax.crypto.BadPaddingException e) {
            throw new Exception("解密失败：数据填充错误，请检查密钥是否正确或数据是否完整", e);
        } catch (Exception e) {
            throw new Exception("解密过程中发生错误：" + e.getMessage(), e);
        }
    }

    /**
     * 验证加密解密是否正常工作
     *
     * @param testData 测试数据
     * @return 测试是否成功
     */
    public static boolean test(String testData) {
        try {
            String encrypted = encrypt(testData);
            String decrypted = decrypt(encrypted);
            boolean success = testData.equals(decrypted);
            if (success) {
                log.debug("DES加密测试成功，测试数据：{}", testData);
            } else {
                log.error("DES加密测试失败，原始数据：{}，解密数据：{}", testData, decrypted);
            }
            return success;
        } catch (Exception e) {
            log.error("DES加密测试异常：{}", e.getMessage(), e);
            return false;
        }
    }
}