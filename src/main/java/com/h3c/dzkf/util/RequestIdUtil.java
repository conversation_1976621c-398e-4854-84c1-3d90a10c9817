package com.h3c.dzkf.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * RequestId工具类
 * 用于统一管理请求ID，确保切面和Controller使用同一个requestId
 */
public class RequestIdUtil {

    private static final String REQUEST_ID_ATTRIBUTE = "requestId";

    /**
     * 获取当前请求的requestId
     * 优先从request属性中获取，如果没有则生成新的
     *
     * @return requestId
     */
    public static String getCurrentRequestId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String requestId = (String) request.getAttribute(REQUEST_ID_ATTRIBUTE);
                if (StrUtil.isNotBlank(requestId)) {
                    return requestId;
                }
            }
        } catch (Exception e) {
            // 忽略异常，使用生成的requestId
        }

        // 如果获取不到，生成新的requestId
        return IdUtil.simpleUUID();
    }

    /**
     * 设置当前请求的requestId
     *
     * @param requestId 请求ID
     */
    public static void setCurrentRequestId(String requestId) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                request.setAttribute(REQUEST_ID_ATTRIBUTE, requestId);
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }

    /**
     * 清理当前请求的requestId
     * 注意：由于使用的是request属性而不是ThreadLocal，
     * 这个方法主要是为了API一致性，实际上request属性会在请求结束时自动清理
     */
    public static void clearCurrentRequestId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                request.removeAttribute(REQUEST_ID_ATTRIBUTE);
            }
        } catch (Exception e) {
            // 忽略异常
        }
    }
} 