package com.h3c.dzkf.util;

import cn.hutool.core.util.StrUtil;
import com.h3c.dzkf.entity.dto.BatchImportAppDataDTO;
import com.h3c.dzkf.entity.dto.BatchImportDeviceRequestDTO;
import com.h3c.dzkf.entity.dto.BatchImportSiteRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel处理工具类
 */
@Slf4j
public class ExcelUtil {
    
    /**
     * 解析设备模板Excel文件
     * 
     * @param file Excel文件
     * @return 设备列表
     * @throws IOException IO异常
     */
    public static List<BatchImportDeviceRequestDTO> parseDeviceTemplateExcel(MultipartFile file) throws IOException {
        List<BatchImportDeviceRequestDTO> deviceList = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new IllegalArgumentException("Excel文件格式错误，找不到工作表");
            }
            
            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件格式错误，找不到表头");
            }
            
            validateHeader(headerRow);
            
            // 从第二行开始读取数据（跳过表头）
            int rowCount = sheet.getLastRowNum();
            for (int i = 1; i <= rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }
                
                try {
                    BatchImportDeviceRequestDTO device = parseRowToDevice(row, i + 1);
                    if (device != null) {
                        deviceList.add(device);
                    }
                } catch (Exception e) {
                    log.warn("解析第{}行数据失败: {}", i + 1, e.getMessage());
                    // 创建一个包含错误信息的设备对象，用于后续错误统计
                    BatchImportDeviceRequestDTO errorDevice = new BatchImportDeviceRequestDTO();
                    errorDevice.setRowIndex(i + 1);
                    errorDevice.setDeviceName("第" + (i + 1) + "行数据解析失败");
                    deviceList.add(errorDevice);
                }
            }
        }
        
        return deviceList;
    }

    /**
     * 解析站点模板Excel文件
     * 
     * @param file Excel文件
     * @return 站点列表
     * @throws IOException IO异常
     */
    public static List<BatchImportSiteRequestDTO> parseSiteTemplateExcel(MultipartFile file) throws IOException {
        List<BatchImportSiteRequestDTO> siteList = new ArrayList<>();
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new IllegalArgumentException("Excel文件格式错误，找不到工作表");
            }
            
            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件格式错误，找不到表头");
            }
            
            validateSiteHeader(headerRow);
            
            // 从第二行开始读取数据（跳过表头）
            int rowCount = sheet.getLastRowNum();
            for (int i = 1; i <= rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }
                
                try {
                    BatchImportSiteRequestDTO site = parseRowToSite(row, i + 1);
                    if (site != null) {
                        siteList.add(site);
                    }
                } catch (Exception e) {
                    log.warn("解析第{}行数据失败: {}", i + 1, e.getMessage());
                    // 创建一个包含错误信息的站点对象，用于后续错误统计
                    BatchImportSiteRequestDTO errorSite = new BatchImportSiteRequestDTO();
                    errorSite.setRowIndex(i + 1);
                    errorSite.setSiteName("第" + (i + 1) + "行数据解析失败");
                    siteList.add(errorSite);
                }
            }
        }
        
        return siteList;
    }

    /**
     * 验证站点表头格式
     */
    private static void validateSiteHeader(Row headerRow) {
        String[] expectedHeaders = {
            "站点ID", "站点名称", "所属站点", "站点位置", "站点类型"
        };
        
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(cellValue)) {
                throw new IllegalArgumentException(
                    String.format("表头格式错误，第%d列应为'%s'，实际为'%s'", 
                        i + 1, expectedHeaders[i], cellValue));
            }
        }
    }

    /**
     * 解析行数据为站点对象
     */
    private static BatchImportSiteRequestDTO parseRowToSite(Row row, int rowNumber) {
        BatchImportSiteRequestDTO site = new BatchImportSiteRequestDTO();
        site.setRowIndex(rowNumber);
        
        try {
            // 站点ID (必填)
            String siteIdStr = getCellStringValue(row.getCell(0));
            if (StrUtil.isNotBlank(siteIdStr)) {
                site.setSiteId(Integer.parseInt(siteIdStr.trim()));
            }
            
            // 站点名称 (必填)
            site.setSiteName(getCellStringValue(row.getCell(1)));
            
            // 所属站点 (可选)
            site.setParentSite(getCellStringValue(row.getCell(2)));
            
            // 站点位置 (必填)
            site.setLocation(getCellStringValue(row.getCell(3)));
            
            // 站点类型 (必填)
            site.setSiteType(getCellStringValue(row.getCell(4)));
            
        } catch (Exception e) {
            throw new RuntimeException("第" + rowNumber + "行数据格式错误: " + e.getMessage());
        }
        
        return site;
    }
    
    /**
     * 验证表头格式
     */
    private static void validateHeader(Row headerRow) {
        String[] expectedHeaders = {
            "设备ID", "设备名称", "IP地址", "SN号", "设备型号", 
            "设备角色", "所属站点", "是否RR设备", "厂商名称",
                "是否纳管", "IPv6地址", "设备平面"
        };
        
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(cellValue)) {
                throw new IllegalArgumentException(
                    String.format("表头格式错误，第%d列应为'%s'，实际为'%s'", 
                        i + 1, expectedHeaders[i], cellValue));
            }
        }
    }
    
    /**
     * 解析行数据为设备对象
     */
    private static BatchImportDeviceRequestDTO parseRowToDevice(Row row, int rowNumber) {
        BatchImportDeviceRequestDTO device = new BatchImportDeviceRequestDTO();
        device.setRowIndex(rowNumber);
        
        try {
            // 设备ID (必填)
            String deviceIdStr = getCellStringValue(row.getCell(0));
            if (StrUtil.isNotBlank(deviceIdStr)) {
                device.setDeviceId(Long.parseLong(deviceIdStr.trim()));
            }
            
            // 设备名称 (必填)
            device.setDeviceName(getCellStringValue(row.getCell(1)));
            
            // IP地址 (必填)
            device.setDeviceIp(getCellStringValue(row.getCell(2)));
            
            // SN号
            device.setDeviceSn(getCellStringValue(row.getCell(3)));
            
            // 设备型号 (必填)
            device.setDeviceModel(getCellStringValue(row.getCell(4)));
            
            // 设备角色 (必填)
            device.setDeviceRole(getCellStringValue(row.getCell(5)));
            
            // 所属站点 (必填)
            device.setDeviceSite(getCellStringValue(row.getCell(6)));
            
            // 是否RR设备 (必填)
            device.setIsRR(getCellStringValue(row.getCell(7)));
            
            // 厂商名称 (必填)
            device.setDeviceManufacturer(getCellStringValue(row.getCell(8)));
            
            // 是否纳管
            device.setIsMarkDevice(getCellStringValue(row.getCell(9)));
            
            // IPv6地址
            device.setDeviceIpv6(getCellStringValue(row.getCell(10)));

            // 设备分组（已废弃）
            // device.setDeviceGroup(getCellStringValue(row.getCell(11)));

            // 设备平面
            device.setDevicePlane(getCellStringValue(row.getCell(11)));
            
        } catch (Exception e) {
            throw new RuntimeException("第" + rowNumber + "行数据格式错误: " + e.getMessage());
        }
        
        return device;
    }
    
    /**
     * 获取单元格字符串值
     */
    private static String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字格式，去掉小数点后的0
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    /**
     * 判断行是否为空
     */
    private static boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && StrUtil.isNotBlank(getCellStringValue(cell))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析应用模板Excel文件
     *
     * @param file Excel文件
     * @return 应用列表
     * @throws IOException IO异常
     */
    public static List<BatchImportAppDataDTO> parseAppTemplateExcel(MultipartFile file) throws IOException {
        List<BatchImportAppDataDTO> appList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new IllegalArgumentException("Excel文件格式错误，找不到工作表");
            }

            // 验证表头
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件格式错误，找不到表头");
            }

            validateAppHeader(headerRow);

            // 从第二行开始读取数据（跳过表头）
            int rowCount = sheet.getLastRowNum();
            for (int i = 1; i <= rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }

                try {
                    BatchImportAppDataDTO app = parseRowToApp(row, i + 1);
                    appList.add(app);
                } catch (Exception e) {
                    log.warn("解析第{}行数据失败: {}", i + 1, e.getMessage());
                    // 创建一个包含错误信息的应用对象，用于后续错误统计
                    BatchImportAppDataDTO errorApp = new BatchImportAppDataDTO();
                    errorApp.setRowIndex(i + 1);
                    errorApp.setAppName("第" + (i + 1) + "行数据解析失败");
                    appList.add(errorApp);
                }
            }
        }

        return appList;
    }

    /**
     * 验证应用表头格式
     */
    private static void validateAppHeader(Row headerRow) {
        String[] expectedHeaders = {
                "应用名称*", "应用分组*", "类型*", "协议", "APPID", "DSCP值", "源IP", "源端口",
                "目的IP1", "目的端口1", "目的IP2", "目的端口2", "目的IP3", "目的端口3",
                "目的IP4", "目的端口4", "目的IP5", "目的端口5", "目的IP6", "目的端口6",
                "目的IP7", "目的端口7", "目的IP8", "目的端口8", "目的IP9", "目的端口9",
                "目的IP10", "目的端口10", "源IPV6", "源IPV6端口", "目的IPV6-1", "目的端口IPV6-1",
                "目的IPV6-2", "目的端口IPV6-2", "目的IPV6-3", "目的端口IPV6-3", "目的IPV6-4", "目的端口IPV6-4",
                "目的IPV6-5", "目的端口IPV6-5", "目的IPV6-6", "目的端口IPV6-6", "目的IPV6-7", "目的端口IPV6-7",
                "目的IPV6-8", "目的端口IPV6-8", "目的IPV6-9", "目的端口IPV6-9", "目的IPV6-10", "目的端口IPV6-10",
                "应用ID"
        };

        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(cellValue)) {
                throw new IllegalArgumentException(
                        String.format("表头格式错误，第%d列应为'%s'，实际为'%s'",
                                i + 1, expectedHeaders[i], cellValue));
            }
        }
    }

    /**
     * 解析行数据为应用对象
     */
    private static BatchImportAppDataDTO parseRowToApp(Row row, int rowNumber) {
        BatchImportAppDataDTO app = new BatchImportAppDataDTO();
        app.setRowIndex(rowNumber);

        try {
            // 应用名称 (必填)
            app.setAppName(getCellStringValue(row.getCell(0)));

            // 应用分组 (必填)
            app.setAppGroupName(getCellStringValue(row.getCell(1)));

            // 类型 (必填)
            app.setAppType(getCellStringValue(row.getCell(2)));

            // 协议 (选填) - 需要进行数字到字符串的转换
            String protocolValue = getCellStringValue(row.getCell(3));
            app.setAgreementType(convertProtocolValue(protocolValue));

            // APPID (忽略不处理)
            app.setAppIdFromExcel(getCellStringValue(row.getCell(4)));

            // DSCP值 (选填)
            app.setInternetAppDSCP(getCellStringValue(row.getCell(5)));

            // 源IP (选填)
            app.setSourceIP(getCellStringValue(row.getCell(6)));

            // 源端口 (选填)
            app.setSourcePort(getCellStringValue(row.getCell(7)));

            // 目的IP1-10和目的端口1-10
            app.setDestinationIP1(getCellStringValue(row.getCell(8)));
            app.setDestinationPort1(getCellStringValue(row.getCell(9)));
            app.setDestinationIP2(getCellStringValue(row.getCell(10)));
            app.setDestinationPort2(getCellStringValue(row.getCell(11)));
            app.setDestinationIP3(getCellStringValue(row.getCell(12)));
            app.setDestinationPort3(getCellStringValue(row.getCell(13)));
            app.setDestinationIP4(getCellStringValue(row.getCell(14)));
            app.setDestinationPort4(getCellStringValue(row.getCell(15)));
            app.setDestinationIP5(getCellStringValue(row.getCell(16)));
            app.setDestinationPort5(getCellStringValue(row.getCell(17)));
            app.setDestinationIP6(getCellStringValue(row.getCell(18)));
            app.setDestinationPort6(getCellStringValue(row.getCell(19)));
            app.setDestinationIP7(getCellStringValue(row.getCell(20)));
            app.setDestinationPort7(getCellStringValue(row.getCell(21)));
            app.setDestinationIP8(getCellStringValue(row.getCell(22)));
            app.setDestinationPort8(getCellStringValue(row.getCell(23)));
            app.setDestinationIP9(getCellStringValue(row.getCell(24)));
            app.setDestinationPort9(getCellStringValue(row.getCell(25)));
            app.setDestinationIP10(getCellStringValue(row.getCell(26)));
            app.setDestinationPort10(getCellStringValue(row.getCell(27)));

            // 源IPV6 (选填)
            app.setSourceIPv6(getCellStringValue(row.getCell(28)));

            // 源IPV6端口 (选填)
            app.setSourcePortIPv6(getCellStringValue(row.getCell(29)));

            // 目的IPV6-1至目的IPV6-10和目的端口IPV6-1至目的端口IPV6-10
            app.setDestinationIPv6_1(getCellStringValue(row.getCell(30)));
            app.setDestinationPortIPv6_1(getCellStringValue(row.getCell(31)));
            app.setDestinationIPv6_2(getCellStringValue(row.getCell(32)));
            app.setDestinationPortIPv6_2(getCellStringValue(row.getCell(33)));
            app.setDestinationIPv6_3(getCellStringValue(row.getCell(34)));
            app.setDestinationPortIPv6_3(getCellStringValue(row.getCell(35)));
            app.setDestinationIPv6_4(getCellStringValue(row.getCell(36)));
            app.setDestinationPortIPv6_4(getCellStringValue(row.getCell(37)));
            app.setDestinationIPv6_5(getCellStringValue(row.getCell(38)));
            app.setDestinationPortIPv6_5(getCellStringValue(row.getCell(39)));
            app.setDestinationIPv6_6(getCellStringValue(row.getCell(40)));
            app.setDestinationPortIPv6_6(getCellStringValue(row.getCell(41)));
            app.setDestinationIPv6_7(getCellStringValue(row.getCell(42)));
            app.setDestinationPortIPv6_7(getCellStringValue(row.getCell(43)));
            app.setDestinationIPv6_8(getCellStringValue(row.getCell(44)));
            app.setDestinationPortIPv6_8(getCellStringValue(row.getCell(45)));
            app.setDestinationIPv6_9(getCellStringValue(row.getCell(46)));
            app.setDestinationPortIPv6_9(getCellStringValue(row.getCell(47)));
            app.setDestinationIPv6_10(getCellStringValue(row.getCell(48)));
            app.setDestinationPortIPv6_10(getCellStringValue(row.getCell(49)));

            // 应用ID (选填)
            String appIdStr = getCellStringValue(row.getCell(50));
            if (StrUtil.isNotBlank(appIdStr)) {
                app.setAppId(Integer.parseInt(appIdStr.trim()));
            }

        } catch (Exception e) {
            throw new RuntimeException("第" + rowNumber + "行数据格式错误: " + e.getMessage());
        }

        return app;
    }

    /**
     * 转换协议值（数字转字符串）
     *
     * @param protocolValue 协议值（可能是数字或字符串）
     * @return 转换后的协议字符串
     */
    private static String convertProtocolValue(String protocolValue) {
        if (StrUtil.isBlank(protocolValue)) {
            return protocolValue;
        }

        // 去除空格并转换为小写进行比较
        String trimmedValue = protocolValue.trim();

        // 如果已经是字符串格式，直接返回（转换为大写）
        if (trimmedValue.equalsIgnoreCase("ICMP") ||
                trimmedValue.equalsIgnoreCase("TCP") ||
                trimmedValue.equalsIgnoreCase("UDP") ||
                trimmedValue.equalsIgnoreCase("IP")) {
            return trimmedValue.toUpperCase();
        }

        // 尝试解析为数字进行转换
        try {
            int protocolNumber = Integer.parseInt(trimmedValue);
            switch (protocolNumber) {
                case 1:
                    return "ICMP";
                case 6:
                    return "TCP";
                case 17:
                    return "UDP";
                case 0:
                case -999:
                    return "IP";
                default:
                    log.warn("未知的协议数字值: {}, 转换为默认值", protocolNumber);
                    return "IP"; // 其他值默认转换为IP
            }
        } catch (NumberFormatException e) {
            // 不是数字，可能是其他字符串，记录警告并返回原值
            log.warn("无法解析的协议值: {}, 保持原值", protocolValue);
            return protocolValue;
        }
    }
}