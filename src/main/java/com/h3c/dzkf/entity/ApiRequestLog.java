package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("api_request_log")
public class ApiRequestLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String requestId;
    private String requestUri;
    private String requestMethod;
    private String requestBody;
    private String responseBody;
    private Date requestTime;
    private Integer durationMs;
    private String clientIp;
    private Date createTime;
}