package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 隧道组定制信息实体类
 */
@Data
@TableName("tunnel_group_custom_info")
public class TeGroupCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 隧道组ID（工商总行定义）
     */
    private Long teGroupId;

    /**
     * 隧道组名称
     */
    private String name;

    /**
     * 平台应用组ID（与平台创建的应用组关联）
     */
    private String platformGroupId;

    /**
     * 选路方式（true:智能选路, false:自定义）
     */
    private Boolean isLoose;

    /**
     * 路径模式 1-主备模式 2-负载模式
     */
    private Integer balanceMode;

    /**
     * 负载模式下比例 例如1:2或1:2:3
     */
    private String balanceProportion;

    /**
     * 隧道模板ID
     */
    private Long scheduleStrategyId;

    /**
     * 隧道组的color值
     */
    private Integer color;

    /**
     * 路径条数
     */
    private Integer pathSize;

    /**
     * 平面选路类型（1-同平面, 2-跨平面）【选路方式为智能选路时填写】
     */
    private Integer planeRoutingType;

    /**
     * 配置方式，是否按业务网络（true:按业务网络, false:按设备）
     */
    private Boolean isVirtualNet;

    /**
     * 业务网络ID 【配置方式为按业务网络时填写】
     */
    private String virtualNetId;

    /**
     * 最大跳数，取值范围1~**********
     */
    private Integer hopLimit;

    /**
     * 正向Policy的ServiceClass
     */
    private Integer positiveServiceClass;

    /**
     * 反向Policy的ServiceClass
     */
    private Integer negativeServiceClass;

    /**
     * 隧道组设备配置信息（JSON格式）
     * 【配置方式为按设备时填写，存储teGroupDcs数组的JSON字符串】
     */
    private String teGroupDcs;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    private Integer isDeleted;
} 