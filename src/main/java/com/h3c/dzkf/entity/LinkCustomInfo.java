package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 链路自定义信息实体类（仅存储工行定制字段）
 */
@Data
@TableName("link_custom_info")
public class LinkCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 平台生成的链路ID（用于关联平台链路）
     */
    private String platformLinkId;

    /**
     * 站点ID（以源设备的站点为准，用于站点级链路查询）
     */
    private Integer siteId;

    /**
     * 链路类型（工行定制字段）
     */
    private String linkType;

    /**
     * 链路等级（工行定制字段）
     */
    private String linkLevel;

    /**
     * 链路状态（工行定制字段）
     */
    private String linkStatus;

    /**
     * 链路名称（工行定制字段，同时作为平台linkName的备份）
     */
    private String linkName;

    /**
     * 源设备ID（工行控制器统一设定）
     */
    private Integer sourceDeviceId;

    /**
     * 源设备名称（工行定制字段）
     */
    private String sourceDeviceName;

    /**
     * 源设备端口（工行定制字段）
     */
    private String sourceDevicePort;

    /**
     * 源设备接口ID（从平台设备接口信息接口获取的dataId）
     */
    private Long sourceInterfaceId;

    /**
     * 目标设备ID（工行控制器统一设定）
     */
    private Integer targetDeviceId;

    /**
     * 目标设备名称（工行定制字段）
     */
    private String targetDeviceName;

    /**
     * 目标设备端口（工行定制字段）
     */
    private String targetDevicePort;

    /**
     * 目标设备接口ID（从平台设备接口信息接口获取的dataId）
     */
    private Long targetInterfaceId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
}