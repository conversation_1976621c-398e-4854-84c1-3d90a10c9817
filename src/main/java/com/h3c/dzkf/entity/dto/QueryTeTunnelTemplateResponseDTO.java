package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 查询隧道模板响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryTeTunnelTemplateResponseDTO extends ApiResponseDTO {

    @ApiModelProperty(value = "隧道模板列表")
    private List<TeTunnelTemplateInfo> data;

    /**
     * 隧道模板信息
     */
    @Data
    public static class TeTunnelTemplateInfo {
        /**
         * 隧道策略ID
         */
        @ApiModelProperty(value = "隧道策略ID", example = "1")
        private Long strategyId;

        /**
         * 策略名称
         */
        @ApiModelProperty(value = "策略名称", example = "高优先级隧道策略")
        private String strategyName;

        /**
         * SLA等级
         */
        @ApiModelProperty(value = "SLA等级", example = "EF", notes = "服务等级协议")
        private String slaId;

        /**
         * 保障带宽
         */
        @ApiModelProperty(value = "保障带宽", example = "1000", notes = "保障带宽，单位为Mbps")
        private Long bandwidth;

        /**
         * 正向链路等级
         */
        @ApiModelProperty(value = "正向链路等级", example = "高", notes = "优选链路等级，可选值：高、中、低")
        private String upAllowLinkPriority;

        /**
         * 反向链路等级
         */
        @ApiModelProperty(value = "反向链路等级", example = "[\"高\", \"中\", \"低\"]", notes = "可选链路等级列表，可选值：高、中、低")
        private List<String> downAllowLinkPriority;

        /**
         * 丢包率
         */
        @ApiModelProperty(value = "丢包率", example = "0.01", notes = "允许的最大丢包率")
        private String packetLossRate;

        /**
         * 延迟时间
         */
        @ApiModelProperty(value = "延迟时间", example = "100", notes = "最大允许延迟时间，单位为毫秒")
        private String delayTime;

        /**
         * 网络抖动
         */
        @ApiModelProperty(value = "网络抖动", example = "10", notes = "最大允许网络抖动，单位为毫秒")
        private String networkJitter;
    }

    /**
     * 创建成功响应
     *
     * @param requestId 请求ID
     * @param templateList 隧道模板列表
     * @return 响应对象
     */
    public static QueryTeTunnelTemplateResponseDTO success(String requestId, List<TeTunnelTemplateInfo> templateList) {
        QueryTeTunnelTemplateResponseDTO response = new QueryTeTunnelTemplateResponseDTO();
        response.setRequestId(requestId);
        response.setResult(1);
        response.setFailReason(null);
        response.setData(templateList);
        return response;
    }

    /**
     * 创建失败响应
     *
     * @param requestId 请求ID
     * @param failReason 失败原因
     * @return 响应对象
     */
    public static QueryTeTunnelTemplateResponseDTO fail(String requestId, String failReason) {
        QueryTeTunnelTemplateResponseDTO response = new QueryTeTunnelTemplateResponseDTO();
        response.setRequestId(requestId);
        response.setResult(0);
        response.setFailReason(failReason);
        response.setData(new ArrayList<>());
        return response;
    }

} 