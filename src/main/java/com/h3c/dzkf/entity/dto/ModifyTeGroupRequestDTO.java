package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 修改隧道组请求DTO
 */
@Data
public class ModifyTeGroupRequestDTO {

    /**
     * 隧道组DTO
     */
    @ApiModelProperty(value = "隧道组DTO", required = true)
    @NotNull(message = "隧道组DTO不能为空")
    @Valid
    private TeGroupDto teGroupDto;

    /**
     * 隧道组DTO内部类
     */
    @Data
    public static class TeGroupDto {
        /**
         * 隧道组ID
         */
        @ApiModelProperty(value = "隧道组ID", required = true, example = "1001")
        @NotNull(message = "隧道组ID不能为空")
        private Long id;

        /**
         * 隧道组名称【支持英文、数字、下划线、短横线，最大长度15个字符】
         */
        @ApiModelProperty(value = "隧道组名称", required = true, example = "MyTeGroup", notes = "支持英文、数字、下划线、短横线，最大长度15个字符")
        @NotBlank(message = "隧道组名称不能为空")
        @Size(max = 15, message = "隧道组名称最大长度为15个字符")
        @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "隧道组名称只能包含英文、数字、下划线、短横线")
        private String name;

        /**
         * 选路方式（true:智能选路, false:自定义）
         */
        @ApiModelProperty(value = "选路方式", required = false, example = "true", notes = "true:智能选路, false:自定义")
        private Boolean isLoose;

        /**
         * 路径模式 1-主备模式 2-负载模式
         */
        @ApiModelProperty(value = "路径模式", required = true, example = "1", notes = "1-主备模式 2-负载模式")
        @NotNull(message = "路径模式不能为空")
        private Integer balanceMode;

        /**
         * 负载模式下比例 例如1:2或1:2:3
         */
        @ApiModelProperty(value = "负载模式下比例", required = true, example = "1:2", notes = "例如1:2或1:2:3")
//        @NotBlank(message = "负载模式下比例不能为空")
        private String balanceProportion;

        /**
         * 隧道模板ID
         */
        @ApiModelProperty(value = "隧道模板ID", required = true, example = "1")
        @NotNull(message = "隧道模板ID不能为空")
        private Long scheduleStrategyId;

        /**
         * 隧道组的color值
         */
        @ApiModelProperty(value = "隧道组的color值", required = true, example = "100")
        @NotNull(message = "隧道组的color值不能为空")
        private Integer color;

        /**
         * 路径条数
         */
        @ApiModelProperty(value = "路径条数", required = true, example = "2")
        @NotNull(message = "路径条数不能为空")
        private Integer pathSize;

        /**
         * 平面选路类型（1-同平面, 2-跨平面）【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "平面选路类型", required = true, example = "1", notes = "1-同平面, 2-跨平面，选路方式为智能选路时填写")
        @NotNull(message = "平面选路类型不能为空")
        private Integer planeRoutingType;

        /**
         * 配置方式，是否按业务网络（true:按业务网络, false:按设备）
         */
        @ApiModelProperty(value = "配置方式", required = true, example = "false", notes = "true:按业务网络, false:按设备")
        @NotNull(message = "配置方式不能为空")
        private Boolean isVirtualNet;

        /**
         * 业务网络ID 【配置方式为按业务网络时填写】
         */
        @ApiModelProperty(value = "业务网络ID", required = false, example = "1001", notes = "配置方式为按业务网络时填写")
        private Integer virtualNetId;

        /**
         * 隧道组源目的信息 【配置方式为按设备时填写】
         */
        @ApiModelProperty(value = "隧道组源目的信息", required = false, notes = "配置方式为按设备时填写")
        @Valid
        private List<TeGroupDcInfo> teGroupDcs;

        /**
         * 必选设备ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "必选设备ID集合", required = false, example = "[1001, 1002]", notes = "选路方式为智能选路时填写")
        private List<Long> mandatoryNodeIds;

        /**
         * 排除设备ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "排除设备ID集合", required = false, example = "[1003, 1004]", notes = "选路方式为智能选路时填写")
        private List<Long> excludeNodeIds;

        /**
         * 必经链路ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "必经链路ID集合", required = false, example = "[2001, 2002]", notes = "选路方式为智能选路时填写")
        private List<Long> mandatoryLinkIds;

        /**
         * 排除链路ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "排除链路ID集合", required = false, example = "[2003, 2004]", notes = "选路方式为智能选路时填写")
        private List<Long> excludeLinkIds;

        /**
         * 指定路径 【选路方式为自定义选路时填写】(废弃，不处理)
         */
        @ApiModelProperty(value = "指定路径", required = false, example = "[]", notes = "废弃，不处理")
        private List<Long> teTunnelManualList;

        /**
         * 最大跳数，取值范围1~2147483647
         */
        @ApiModelProperty(value = "最大跳数", required = false, example = "10", notes = "取值范围1~2147483647")
        private Integer hopLimit;
    }

    /**
     * 隧道组源目的信息
     */
    @Data
    public static class TeGroupDcInfo {
        /**
         * ID
         */
        @ApiModelProperty(value = "ID", required = false, example = "1")
        private Integer id;

        /**
         * 源设备ID列表 - Long类型
         */
        @ApiModelProperty(value = "源设备ID列表", required = true, example = "[1001, 1002]")
        @NotNull(message = "源设备ID列表不能为空")
        @Size(min = 1, message = "源设备ID列表不能为空")
        private List<Long> srcDeviceIds;

        /**
         * 源设备名称列表
         */
        @ApiModelProperty(value = "源设备名称列表", required = true, example = "[\"Device1\", \"Device2\"]")
        @NotNull(message = "源设备名称列表不能为空")
        @Size(min = 1, message = "源设备名称列表不能为空")
        private List<String> srcDeviceNames;

        /**
         * 目的设备ID列表 - Long类型
         */
        @ApiModelProperty(value = "目的设备ID列表", required = true, example = "[1003, 1004]")
        @NotNull(message = "目的设备ID列表不能为空")
        @Size(min = 1, message = "目的设备ID列表不能为空")
        private List<Long> dstDeviceIds;

        /**
         * 目的设备名称列表
         */
        @ApiModelProperty(value = "目的设备名称列表", required = true, example = "[\"Device3\", \"Device4\"]")
        @NotNull(message = "目的设备名称列表不能为空")
        @Size(min = 1, message = "目的设备名称列表不能为空")
        private List<String> dstDeviceNames;
    }
}
