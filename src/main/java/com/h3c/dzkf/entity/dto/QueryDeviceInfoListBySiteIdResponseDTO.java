package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询站点下所有设备信息响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询站点下所有设备信息响应")
public class QueryDeviceInfoListBySiteIdResponseDTO extends ApiResponseDTO {

    /**
     * 设备总数
     */
    @ApiModelProperty(value = "设备总数")
    private Integer total;

    /**
     * 设备信息列表
     */
    @ApiModelProperty(value = "设备信息列表")
    private List<DeviceInfo> deviceInfoList;

    /**
     * 设备信息
     */
    @Data
    @ApiModel("设备信息")
    public static class DeviceInfo {
        /**
         * 设备ID
         */
        @ApiModelProperty(value = "设备ID")
        private Long deviceId;

        /**
         * 站点ID
         */
        @ApiModelProperty(value = "站点ID")
        private Integer siteId;

        /**
         * 设备名称
         */
        @ApiModelProperty(value = "设备名称")
        private String deviceName;

        /**
         * 设备厂商
         */
        @ApiModelProperty(value = "设备厂商")
        private String deviceManufacturer;

        /**
         * 设备型号
         */
        @ApiModelProperty(value = "设备型号")
        private String deviceModel;

        /**
         * 设备MAC地址
         */
        @ApiModelProperty(value = "设备MAC地址")
        private String deviceMac;

        /**
         * 设备序列号
         */
        @ApiModelProperty(value = "设备序列号")
        private String deviceSn;

        /**
         * 设备状态
         */
        @ApiModelProperty(value = "设备状态")
        private String deviceStatus;

        /**
         * 设备IP
         */
        @ApiModelProperty(value = "设备IP")
        private String deviceIp;

        /**
         * 设备站点
         */
        @ApiModelProperty(value = "设备站点")
        private String deviceSite;

        /**
         * 设备角色
         */
        @ApiModelProperty(value = "设备角色")
        private String deviceRole;

        /**
         * 是否为RR设备
         */
        @ApiModelProperty(value = "是否为RR设备")
        private Boolean isRR;

        /**
         * 是否纳管设备：0：未纳管设备，1：已纳管设备
         */
        @ApiModelProperty(value = "是否纳管设备")
        private Integer isMarkDevice;

        /**
         * 扩展字段
         */
        @ApiModelProperty(value = "扩展字段")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Object optionField;
    }

    /**
     * 构造成功响应
     */
    public static QueryDeviceInfoListBySiteIdResponseDTO success(String requestId, List<DeviceInfo> deviceInfoList) {
        QueryDeviceInfoListBySiteIdResponseDTO response = new QueryDeviceInfoListBySiteIdResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setTotal(deviceInfoList != null ? deviceInfoList.size() : 0);
        response.setDeviceInfoList(deviceInfoList);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryDeviceInfoListBySiteIdResponseDTO fail(String requestId, String failReason) {
        QueryDeviceInfoListBySiteIdResponseDTO response = new QueryDeviceInfoListBySiteIdResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 