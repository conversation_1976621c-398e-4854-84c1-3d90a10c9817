package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改应用请求DTO
 */
@Data
@ApiModel("修改应用请求参数")
public class ModifyAppRequestDTO {

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", required = true, example = "1001")
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true, example = "测试应用")
    @NotBlank(message = "应用名称不能为空")
    private String appName;

    /**
     * 应用类型，仅支持'五元组'
     */
    @ApiModelProperty(value = "应用类型，仅支持'五元组'", required = true, example = "五元组")
    @NotBlank(message = "应用类型不能为空")
    private String appType;

    /**
     * 网络应用DSCP
     */
    @ApiModelProperty(value = "网络应用DSCP", required = false, example = "EF")
    private String internetAppDSCP;

    /**
     * 协议类型（如TCP/UDP）
     */
    @ApiModelProperty(value = "协议类型（如TCP/UDP）", required = true, example = "TCP")
    @NotBlank(message = "协议类型不能为空")
    private String agreementType;

    /**
     * IPv4源IP地址及掩码（格式：*******/32）
     * 注意：sourceIP、sourcePort、destinationIPList 与 IPv6参数是二选一的关系
     */
    @ApiModelProperty(value = "源IP地址及掩码（格式：*******/32）", required = false, example = "***********/32")
    private String sourceIP;

    /**
     * IPv4源端口(如80）
     * 注意：sourceIP、sourcePort、destinationIPList 与 IPv6参数是二选一的关系
     */
    @ApiModelProperty(value = "源端口(如80）", required = false, example = "80")
    private String sourcePort;

    /**
     * IPv4目的IP及端口列表（格式：ip&port，如['***********/32&443']）
     * 注意：sourceIP、sourcePort、destinationIPList 与 IPv6参数是二选一的关系
     */
    @ApiModelProperty(value = "目的IP及端口列表（格式：ip&port，如['***********/32&443']）", required = false)
    private List<String> destinationIPList;

    /**
     * IPv6源IP地址及掩码
     */
    @ApiModelProperty(value = "IPv6源IP地址及掩码", required = false, example = "2001:db8::1/64")
    private String sourceIPv6;

    /**
     * IPv6源端口
     */
    @ApiModelProperty(value = "IPv6源端口", required = false, example = "80")
    private String sourcePortIPv6;

    /**
     * IPv6目的IP及端口列表（格式：ip&port，如['1111:2222::ffff/32&8080']）
     */
    @ApiModelProperty(value = "IPv6目的IP及端口列表（格式：ip&port）", required = false)
    private List<String> destinationIPv6List;

    /**
     * 所属应用分组
     */
    @ApiModelProperty(value = "所属应用分组", required = true, example = "业务应用组")
    @NotBlank(message = "所属应用分组不能为空")
    private String appGroupName;
} 