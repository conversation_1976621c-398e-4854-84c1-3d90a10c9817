package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询调度策略列表响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class QueryScheduleListResponseDTO extends ApiResponseDTO {

    /**
     * 总量
     */
    private Integer total;

    /**
     * 调度策略数组
     */
    private List<ScheduleInfo> scheduleList;

    /**
     * 调度策略信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ScheduleInfo {
        /**
         * 调度策略ID
         */
        private Long appScheduleId;

        /**
         * 策略名称
         */
        private String appScheduleName;

        /**
         * 应用组名称
         */
        private String appGroupName;

        /**
         * 引流类型 0:五元组、1:dscp、4:Vpn
         */
        private Integer drainageType;

        /**
         * 隧道组ID
         */
        private List<Long> networkIds;

        /**
         * 业务网络ID
         */
        private Integer vpnId;

        /**
         * 策略状态（0未启用、1未生效、2部分生效、3全部生效、4生效中、9异常）
         */
        private Integer scheduleStatus;

        /**
         * 扩展信息列表
         */
        private List<OptionField> optionField;
    }

    /**
     * 扩展信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OptionField {
        /**
         * 站点ID
         */
        private Integer siteId;

        /**
         * 站点名称
         */
        private String siteName;

        /**
         * 设备IP
         */
        private String deviceIp;

        /**
         * 设备SN
         */
        private String deviceSn;

        /**
         * 策略结果（1:成功,0:失败）
         */
        private Integer result;

        /**
         * 失败原因
         */
        private String faultReason;
    }

    /**
     * 成功响应构造方法
     */
    public static QueryScheduleListResponseDTO success(String requestId, Integer total, List<ScheduleInfo> scheduleList) {
        QueryScheduleListResponseDTO response = new QueryScheduleListResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setTotal(total);
        response.setScheduleList(scheduleList);
        return response;
    }

    /**
     * 失败响应构造方法
     */
    public static QueryScheduleListResponseDTO fail(String requestId, String failReason) {
        QueryScheduleListResponseDTO response = new QueryScheduleListResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
}
