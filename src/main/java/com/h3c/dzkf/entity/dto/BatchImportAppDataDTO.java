package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量导入应用数据DTO（Excel解析后的单行数据）
 */
@Data
@ApiModel("批量导入应用数据")
public class BatchImportAppDataDTO {

    /**
     * 行索引（用于错误定位）
     */
    @ApiModelProperty(value = "行索引", hidden = true)
    private Integer rowIndex;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称", required = true, example = "业务一网F5-Real-Servers1")
    private String appName;

    /**
     * 应用分组
     */
    @ApiModelProperty(value = "应用分组", required = true, example = "业务应用组")
    private String appGroupName;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型", required = true, example = "五元组")
    private String appType;

    /**
     * 协议
     */
    @ApiModelProperty(value = "协议", required = false, example = "TCP")
    private String agreementType;

    /**
     * APPID（忽略不处理）
     */
    @ApiModelProperty(value = "APPID", required = false, example = "1001")
    private String appIdFromExcel;

    /**
     * DSCP值
     */
    @ApiModelProperty(value = "DSCP值", required = false, example = "EF")
    private String internetAppDSCP;

    /**
     * 源IP
     */
    @ApiModelProperty(value = "源IP", required = false, example = "***********/32")
    private String sourceIP;

    /**
     * 源端口
     */
    @ApiModelProperty(value = "源端口", required = false, example = "80")
    private String sourcePort;

    /**
     * 目的IP1
     */
    @ApiModelProperty(value = "目的IP1", required = false, example = "***********00")
    private String destinationIP1;

    /**
     * 目的端口1
     */
    @ApiModelProperty(value = "目的端口1", required = false, example = "443")
    private String destinationPort1;

    /**
     * 目的IP2
     */
    @ApiModelProperty(value = "目的IP2", required = false, example = "*************")
    private String destinationIP2;

    /**
     * 目的端口2
     */
    @ApiModelProperty(value = "目的端口2", required = false, example = "8080")
    private String destinationPort2;

    /**
     * 目的IP3
     */
    @ApiModelProperty(value = "目的IP3", required = false)
    private String destinationIP3;

    /**
     * 目的端口3
     */
    @ApiModelProperty(value = "目的端口3", required = false)
    private String destinationPort3;

    /**
     * 目的IP4
     */
    @ApiModelProperty(value = "目的IP4", required = false)
    private String destinationIP4;

    /**
     * 目的端口4
     */
    @ApiModelProperty(value = "目的端口4", required = false)
    private String destinationPort4;

    /**
     * 目的IP5
     */
    @ApiModelProperty(value = "目的IP5", required = false)
    private String destinationIP5;

    /**
     * 目的端口5
     */
    @ApiModelProperty(value = "目的端口5", required = false)
    private String destinationPort5;

    /**
     * 目的IP6
     */
    @ApiModelProperty(value = "目的IP6", required = false)
    private String destinationIP6;

    /**
     * 目的端口6
     */
    @ApiModelProperty(value = "目的端口6", required = false)
    private String destinationPort6;

    /**
     * 目的IP7
     */
    @ApiModelProperty(value = "目的IP7", required = false)
    private String destinationIP7;

    /**
     * 目的端口7
     */
    @ApiModelProperty(value = "目的端口7", required = false)
    private String destinationPort7;

    /**
     * 目的IP8
     */
    @ApiModelProperty(value = "目的IP8", required = false)
    private String destinationIP8;

    /**
     * 目的端口8
     */
    @ApiModelProperty(value = "目的端口8", required = false)
    private String destinationPort8;

    /**
     * 目的IP9
     */
    @ApiModelProperty(value = "目的IP9", required = false)
    private String destinationIP9;

    /**
     * 目的端口9
     */
    @ApiModelProperty(value = "目的端口9", required = false)
    private String destinationPort9;

    /**
     * 目的IP10
     */
    @ApiModelProperty(value = "目的IP10", required = false)
    private String destinationIP10;

    /**
     * 目的端口10
     */
    @ApiModelProperty(value = "目的端口10", required = false)
    private String destinationPort10;

    /**
     * 源IPV6
     */
    @ApiModelProperty(value = "源IPV6", required = false, example = "2001:db8::1/64")
    private String sourceIPv6;

    /**
     * 源IPV6端口
     */
    @ApiModelProperty(value = "源IPV6端口", required = false, example = "8080")
    private String sourcePortIPv6;

    /**
     * 目的IPV6-1
     */
    @ApiModelProperty(value = "目的IPV6-1", required = false)
    private String destinationIPv6_1;

    /**
     * 目的端口IPV6-1
     */
    @ApiModelProperty(value = "目的端口IPV6-1", required = false)
    private String destinationPortIPv6_1;

    /**
     * 目的IPV6-2
     */
    @ApiModelProperty(value = "目的IPV6-2", required = false)
    private String destinationIPv6_2;

    /**
     * 目的端口IPV6-2
     */
    @ApiModelProperty(value = "目的端口IPV6-2", required = false)
    private String destinationPortIPv6_2;

    /**
     * 目的IPV6-3
     */
    @ApiModelProperty(value = "目的IPV6-3", required = false)
    private String destinationIPv6_3;

    /**
     * 目的端口IPV6-3
     */
    @ApiModelProperty(value = "目的端口IPV6-3", required = false)
    private String destinationPortIPv6_3;

    /**
     * 目的IPV6-4
     */
    @ApiModelProperty(value = "目的IPV6-4", required = false)
    private String destinationIPv6_4;

    /**
     * 目的端口IPV6-4
     */
    @ApiModelProperty(value = "目的端口IPV6-4", required = false)
    private String destinationPortIPv6_4;

    /**
     * 目的IPV6-5
     */
    @ApiModelProperty(value = "目的IPV6-5", required = false)
    private String destinationIPv6_5;

    /**
     * 目的端口IPV6-5
     */
    @ApiModelProperty(value = "目的端口IPV6-5", required = false)
    private String destinationPortIPv6_5;

    /**
     * 目的IPV6-6
     */
    @ApiModelProperty(value = "目的IPV6-6", required = false)
    private String destinationIPv6_6;

    /**
     * 目的端口IPV6-6
     */
    @ApiModelProperty(value = "目的端口IPV6-6", required = false)
    private String destinationPortIPv6_6;

    /**
     * 目的IPV6-7
     */
    @ApiModelProperty(value = "目的IPV6-7", required = false)
    private String destinationIPv6_7;

    /**
     * 目的端口IPV6-7
     */
    @ApiModelProperty(value = "目的端口IPV6-7", required = false)
    private String destinationPortIPv6_7;

    /**
     * 目的IPV6-8
     */
    @ApiModelProperty(value = "目的IPV6-8", required = false)
    private String destinationIPv6_8;

    /**
     * 目的端口IPV6-8
     */
    @ApiModelProperty(value = "目的端口IPV6-8", required = false)
    private String destinationPortIPv6_8;

    /**
     * 目的IPV6-9
     */
    @ApiModelProperty(value = "目的IPV6-9", required = false)
    private String destinationIPv6_9;

    /**
     * 目的端口IPV6-9
     */
    @ApiModelProperty(value = "目的端口IPV6-9", required = false)
    private String destinationPortIPv6_9;

    /**
     * 目的IPV6-10
     */
    @ApiModelProperty(value = "目的IPV6-10", required = false)
    private String destinationIPv6_10;

    /**
     * 目的端口IPV6-10
     */
    @ApiModelProperty(value = "目的端口IPV6-10", required = false)
    private String destinationPortIPv6_10;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", required = false, example = "1001")
    private Integer appId;
}
