package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 删除调度策略请求DTO
 */
@Data
@ApiModel(description = "删除调度策略请求参数")
public class DeleteScheduleRequestDTO {

    /**
     * 调度策略唯一标识ID
     */
    @NotNull(message = "调度策略ID不能为空")
    @ApiModelProperty(value = "调度策略唯一标识ID", required = true, example = "1001")
    private Long appScheduleId;
}
