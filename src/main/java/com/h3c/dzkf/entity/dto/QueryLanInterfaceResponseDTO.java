package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询LAN口接口响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "查询LAN口接口响应参数")
public class QueryLanInterfaceResponseDTO extends ApiResponseDTO {

    @ApiModelProperty(value = "LAN接口集合")
    private List<LanInterfaceInfo> lanList;

    /**
     * LAN接口信息
     */
    @Data
    @ApiModel(description = "LAN接口信息")
    public static class LanInterfaceInfo {
        
        @ApiModelProperty(value = "LAN接口ID", example = "1001")
        private Long lanId;
        
        @ApiModelProperty(value = "设备ID", example = "123456")
        private Long deviceId;
        
        @ApiModelProperty(value = "接口名称", example = "eth0/0/1")
        private String interfaceName;
    }

    /**
     * 成功响应
     */
    public static QueryLanInterfaceResponseDTO success(String requestId, List<LanInterfaceInfo> lanList) {
        QueryLanInterfaceResponseDTO response = new QueryLanInterfaceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(1);
        response.setFailReason(null);
        response.setOptionField(null);
        response.setLanList(lanList);
        return response;
    }

    /**
     * 失败响应
     */
    public static QueryLanInterfaceResponseDTO fail(String requestId, String failReason) {
        QueryLanInterfaceResponseDTO response = new QueryLanInterfaceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(0);
        response.setFailReason(failReason);
        response.setOptionField(null);
        response.setLanList(null);
        return response;
    }
} 