package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 批量导入应用响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("批量导入应用响应")
public class BatchImportAppResponseDTO extends ApiResponseDTO {

    @Data
    @ApiModel("自定义结果字段")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OptionField {
        /**
         * 应用总数
         */
        @ApiModelProperty(value = "应用总数", example = "10")
        private Integer totalNum;

        /**
         * 成功总数
         */
        @ApiModelProperty(value = "成功导入数量", example = "8")
        private Integer succNum;

        /**
         * 失败总数
         */
        @ApiModelProperty(value = "失败导入数量", example = "2")
        private Integer failNum;

        /**
         * 应用添加结果，数组，用于显示每一个应用的执行结果
         */
        @ApiModelProperty(value = "应用导入结果列表")
        private List<AddAppResult> addAppResults;
    }

    @Data
    @ApiModel("应用导入结果")
    public static class AddAppResult {
        /**
         * 应用名称
         */
        @ApiModelProperty(value = "应用名称", example = "业务一网F5-Real-Servers1")
        private String appName;

        /**
         * 同步状态：0:失败，1：成功
         */
        @ApiModelProperty(value = "导入状态", allowableValues = "0,1", example = "1", notes = "0:失败，1：成功")
        private Integer status;

        /**
         * 失败原因
         */
        @ApiModelProperty(value = "失败原因", required = false, example = "应用名称已存在")
        private String errorMsg;
    }

    /**
     * 创建成功响应
     */
    public static BatchImportAppResponseDTO success(String requestId, OptionField optionField) {
        BatchImportAppResponseDTO response = success(requestId);
        response.setOptionField(optionField);
        return response;
    }

    /**
     * 创建成功响应（无optionField）
     */
    public static BatchImportAppResponseDTO success(String requestId) {
        BatchImportAppResponseDTO response = new BatchImportAppResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static BatchImportAppResponseDTO fail(String requestId, String failReason) {
        BatchImportAppResponseDTO response = new BatchImportAppResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
}