package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 删除站点请求DTO
 */
@Data
@ApiModel("删除站点请求参数")
public class DeleteSiteRequestDTO {

    /**
     * 站点ID，工行控制器统一设定唯一标识
     */
    @ApiModelProperty(value = "站点ID，工行控制器统一设定唯一标识", required = true)
    @NotNull(message = "站点ID不能为空")
    private Integer siteId;
} 