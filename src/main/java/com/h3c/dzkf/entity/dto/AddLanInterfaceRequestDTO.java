package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增LAN口接口请求DTO
 */
@Data
@ApiModel(description = "新增LAN口接口请求参数")
public class AddLanInterfaceRequestDTO {

    @ApiModelProperty(value = "设备ID", required = true, example = "123456")
    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    @ApiModelProperty(value = "接口名称", required = true, example = "eth0/0/1")
    @NotBlank(message = "接口名称不能为空")
    private String interfaceName;
} 