package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新增LAN口接口响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "新增LAN口接口响应")
public class AddLanInterfaceResponseDTO extends ApiResponseDTO {

    @ApiModelProperty(value = "LAN口ID", example = "1")
    @JsonProperty("lanId")
    private Long lanId;

    /**
     * 创建成功响应
     */
    public static AddLanInterfaceResponseDTO success(String requestId, Long lanId) {
        AddLanInterfaceResponseDTO response = new AddLanInterfaceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setLanId(lanId);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static AddLanInterfaceResponseDTO fail(String requestId, String failReason) {
        AddLanInterfaceResponseDTO response = new AddLanInterfaceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 