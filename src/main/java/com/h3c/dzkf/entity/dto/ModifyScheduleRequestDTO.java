package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改调度策略请求DTO
 */
@Data
@ApiModel(description = "修改调度策略请求参数")
public class ModifyScheduleRequestDTO {

    /**
     * 调度策略唯一标识ID
     */
    @ApiModelProperty(value = "调度策略唯一标识ID", required = true, example = "1001")
    @NotNull(message = "调度策略唯一标识ID不能为空")
    private Long appScheduleId;

    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称", required = true, example = "测试调度策略")
    @NotBlank(message = "策略名称不能为空")
    private String appScheduleName;

    /**
     * 应用组名称
     */
    @ApiModelProperty(value = "应用组名称", required = true, example = "办公应用组")
    @NotBlank(message = "应用组名称不能为空")
    private String appGroupName;

    /**
     * 引流类型 0:五元组、1:dscp、4:Vpn
     */
    @ApiModelProperty(value = "引流类型", allowableValues = "0,1,4", example = "0", notes = "0:五元组、1:dscp、4:Vpn")
    private Integer drainageType;

    /**
     * 隧道组ID列表
     */
    @ApiModelProperty(value = "隧道组ID列表", required = true, example = "[1001, 1002]")
    @NotNull(message = "隧道组ID列表不能为空")
    private List<Long> networkIds;

    /**
     * VPN ID
     */
    @ApiModelProperty(value = "VPN ID", example = "100")
    private Integer vpnId;
}
