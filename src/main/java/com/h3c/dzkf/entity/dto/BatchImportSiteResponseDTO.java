package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 批量导入站点响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("批量导入站点响应")
public class BatchImportSiteResponseDTO extends ApiResponseDTO {

    /**
     * 自定义结果字段
     */
    @Data
    @ApiModel("自定义结果字段")
    public static class OptionField {
        /**
         * 站点总数
         */
        @ApiModelProperty(value = "站点总数")
        private Integer totalNum;

        /**
         * 成功总数
         */
        @ApiModelProperty(value = "成功总数")
        private Integer succNum;

        /**
         * 失败总数
         */
        @ApiModelProperty(value = "失败总数")
        private Integer failNum;

        /**
         * 站点添加结果
         */
        @ApiModelProperty(value = "站点添加结果")
        private List<AddSiteResult> addSiteResults;
    }

    /**
     * 站点添加结果
     */
    @Data
    @ApiModel("站点添加结果")
    public static class AddSiteResult {
        /**
         * 站点名称
         */
        @ApiModelProperty(value = "站点名称")
        private String siteName;

        /**
         * 同步状态：0-失败，1-成功
         */
        @ApiModelProperty(value = "同步状态：0-失败，1-成功")
        private Integer status;

        /**
         * 失败原因
         */
        @ApiModelProperty(value = "失败原因")
        private String errorMsg;
    }

    /**
     * 构造成功响应
     */
    public static BatchImportSiteResponseDTO success(String requestId, OptionField optionField) {
        BatchImportSiteResponseDTO response = success(requestId);
        response.setOptionField(optionField);
        return response;
    }

    /**
     * 构造成功响应（无optionField）
     */
    public static BatchImportSiteResponseDTO success(String requestId) {
        BatchImportSiteResponseDTO response = new BatchImportSiteResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static BatchImportSiteResponseDTO fail(String requestId, String failReason) {
        BatchImportSiteResponseDTO response = new BatchImportSiteResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 