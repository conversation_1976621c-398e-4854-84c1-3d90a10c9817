package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 查询隧道详情请求DTO
 */
@Data
public class GetTeGroupDetailsRequestDTO {

    /**
     * 隧道组ID列表
     */
    @ApiModelProperty(value = "隧道组ID列表", required = true, example = "[1001, 1002, 1003]")
    @NotEmpty(message = "隧道组ID列表不能为空")
    private List<Long> teGroupIds;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", required = false, example = "Device-001")
    private String devName;

    /**
     * 隧道状态，为空的时候查所有
     * 枚举值：1-未部署，2-修改未部署，3-部署成功，4-部署失败，5-待删除
     */
    @ApiModelProperty(value = "隧道状态", required = false, example = "3",
            notes = "为空的时候查所有，枚举值：1-未部署，2-修改未部署，3-部署成功，4-部署失败，5-待删除")
    private Integer deployState;
}
