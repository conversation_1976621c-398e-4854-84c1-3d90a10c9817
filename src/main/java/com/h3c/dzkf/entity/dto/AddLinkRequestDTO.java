package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增链路请求DTO
 */
@Data
@ApiModel("新增链路请求")
public class AddLinkRequestDTO {

    @ApiModelProperty(value = "链路状态", required = false)
    private String linkStatus;

    @ApiModelProperty(value = "链路名称", required = true)
    @NotBlank(message = "链路名称不能为空")
    private String linkName;

    @ApiModelProperty(value = "链路类型", required = true)
    @NotBlank(message = "链路类型不能为空")
    private String linkType;

    @ApiModelProperty(value = "链路等级", required = true)
    @NotBlank(message = "链路等级不能为空")
    private String linkLevel;

    @ApiModelProperty(value = "线路带宽（格式：1000Mbps）", required = true)
    @NotBlank(message = "线路带宽不能为空")
    private String linkBandWidth;

    @ApiModelProperty(value = "源设备ID", required = true)
    @NotNull(message = "源设备ID不能为空")
    private Integer sourceDeviceId;

    @ApiModelProperty(value = "源设备名称", required = true)
    @NotBlank(message = "源设备名称不能为空")
    private String sourceDeviceName;

    @ApiModelProperty(value = "源设备管理IP", required = true)
    @NotBlank(message = "源设备管理IP不能为空")
    private String sourceDeviceIp;

    @ApiModelProperty(value = "源设备端口", required = true)
    @NotBlank(message = "源设备端口不能为空")
    private String sourceDevicePort;

    @ApiModelProperty(value = "目标设备ID", required = true)
    @NotNull(message = "目标设备ID不能为空")
    private Integer targetDeviceId;

    @ApiModelProperty(value = "目标设备名称", required = true)
    @NotBlank(message = "目标设备名称不能为空")
    private String targetDeviceName;

    @ApiModelProperty(value = "目标设备管理IP", required = true)
    @NotBlank(message = "目标设备管理IP不能为空")
    private String targetDeviceIp;

    @ApiModelProperty(value = "目标设备端口", required = true)
    @NotBlank(message = "目标设备端口不能为空")
    private String targetDevicePort;
} 