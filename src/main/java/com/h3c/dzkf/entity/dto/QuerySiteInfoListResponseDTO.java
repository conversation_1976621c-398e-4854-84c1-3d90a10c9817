package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询站点信息列表响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询站点信息列表响应")
public class QuerySiteInfoListResponseDTO extends ApiResponseDTO {

    /**
     * 站点数组
     */
    @ApiModelProperty(value = "站点数组")
    private List<SiteInfo> siteList;

    /**
     * 站点信息
     */
    @Data
    @ApiModel("站点信息")
    public static class SiteInfo {
        /**
         * 站点ID
         */
        @ApiModelProperty(value = "站点ID")
        private Integer siteId;

        /**
         * 站点名称
         */
        @ApiModelProperty(value = "站点名称")
        private String siteName;

        /**
         * 上级站点ID
         */
        @ApiModelProperty(value = "上级站点ID")
        private Integer parentSite;

        /**
         * 位置
         */
        @ApiModelProperty(value = "位置")
        private String location;

        /**
         * 站点类型
         */
        @ApiModelProperty(value = "站点类型")
        private String siteType;
    }

    /**
     * 构造成功响应
     */
    public static QuerySiteInfoListResponseDTO success(String requestId, List<SiteInfo> siteList) {
        QuerySiteInfoListResponseDTO response = new QuerySiteInfoListResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setSiteList(siteList);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QuerySiteInfoListResponseDTO fail(String requestId, String failReason) {
        QuerySiteInfoListResponseDTO response = new QuerySiteInfoListResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 