package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量导入设备模板请求DTO
 */
@Data
public class BatchImportDeviceRequestDTO {
    
    /**
     * 设备ID，工行控制器统一设定唯一标识
     */
    @ApiModelProperty(value = "设备ID，工行控制器统一设定唯一标识", example = "1001")
    private Long deviceId;
    
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", example = "核心交换机01")
    private String deviceName;
    
    /**
     * 管理IP地址
     */
    @ApiModelProperty(value = "管理IP地址", example = "*************")
    private String deviceIp;
    
    /**
     * 设备序列号
     */
    @ApiModelProperty(value = "设备序列号", required = false, example = "SN123456789")
    private String deviceSn;
    
    /**
     * 设备型号
     */
    @ApiModelProperty(value = "设备型号", example = "S6850-32QC-EI")
    private String deviceModel;
    
    /**
     * 设备角色
     */
    @ApiModelProperty(value = "设备角色", allowableValues = "hub,spoke,agg", example = "hub")
    private String deviceRole;
    
    /**
     * 所属站点
     */
    @ApiModelProperty(value = "所属站点名称", example = "总行机房")
    private String deviceSite;
    
    /**
     * 是否RR设备
     */
    @ApiModelProperty(value = "是否RR设备", allowableValues = "true,false", example = "true")
    private String isRR;
    
    /**
     * 厂商名称
     */
    @ApiModelProperty(value = "厂商名称", example = "H3C")
    private String deviceManufacturer;
    
    /**
     * 是否纳管
     */
    @ApiModelProperty(value = "是否纳管", allowableValues = "0,1", example = "1", notes = "0:不纳管，1:纳管")
    private String isMarkDevice;
    
    /**
     * IPv6地址
     */
    @ApiModelProperty(value = "IPv6地址", required = false, example = "2001:db8::1")
    private String deviceIpv6;
    
    /**
     * 设备平面
     */
    @ApiModelProperty(value = "设备平面", required = false, example = "设备平面")
    private String devicePlane;
    
    /**
     * 行号（用于错误提示）
     */
    @ApiModelProperty(value = "行号（用于错误提示）", example = "2")
    private Integer rowIndex;
} 