package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增设备请求DTO
 */
@Data
public class AddDeviceRequestDTO {
    
    /**
     * 设备ID，工行控制器统一设定唯一标识
     */
    @ApiModelProperty(value = "设备ID，工行控制器统一设定唯一标识", required = true, example = "1001")
    @NotNull(message = "设备ID不能为空")
    private Long deviceId;
    
    /**
     * 厂商名称
     */
    @ApiModelProperty(value = "厂商名称", required = true, example = "H3C")
    @NotBlank(message = "厂商名称不能为空")
    private String deviceManufacturer;
    
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", required = true, example = "核心交换机01")
    @NotBlank(message = "设备名称不能为空")
    private String deviceName;
    
    /**
     * 管理IP地址
     */
    @ApiModelProperty(value = "管理IP地址", required = true, example = "*************")
    @NotBlank(message = "管理IP地址不能为空")
    private String deviceIp;
    
    /**
     * 设备序列号
     */
    @ApiModelProperty(value = "设备序列号", required = false, example = "SN123456789")
    private String deviceSn;
    
    /**
     * 设备型号
     */
    @ApiModelProperty(value = "设备型号", required = true, example = "S6850-32QC-EI")
    //@NotBlank(message = "设备型号不能为空")
    private String deviceModel;
    
    /**
     * 设备角色（hub/spoke/agg）
     */
    @ApiModelProperty(value = "设备角色", required = true, allowableValues = "hub,spoke,agg", example = "hub")
    @NotBlank(message = "设备角色不能为空")
    private String deviceRole;
    
    /**
     * 设备所属站点ID
     */
    @ApiModelProperty(value = "设备所属站点ID", required = true, example = "100")
    @NotNull(message = "设备所属站点ID不能为空")
    private Integer deviceSiteId;
    
    /**
     * 所属站点名称
     */
    @ApiModelProperty(value = "所属站点名称", required = true, example = "总行机房")
    @NotBlank(message = "所属站点名称不能为空")
    private String deviceSite;
    
    /**
     * 是否为RR设备
     */
    @ApiModelProperty(value = "是否为RR设备", required = true, example = "true")
    @NotNull(message = "是否为RR设备不能为空")
    private Boolean isRR;
    
    /**
     * 所属平面ID
     */
    @ApiModelProperty(value = "所属平面ID", required = false, example = "1")
    private Integer devicePlaneId;
    
    /**
     * 是否纳管（0:不纳管，1:纳管）
     */
    @ApiModelProperty(value = "是否纳管", required = false, allowableValues = "0,1", example = "1", notes = "0:不纳管，1:纳管")
    private Integer isMarkDevice;
    
    /**
     * IPv6地址
     */
    @ApiModelProperty(value = "IPv6地址", required = false, example = "2001:db8::1")
    private String deviceIpv6;
    
    /**
     * 设备组(可以为空)
     */
    @ApiModelProperty(value = "设备组", required = false, example = "核心设备组")
    private String deviceGroup;
}