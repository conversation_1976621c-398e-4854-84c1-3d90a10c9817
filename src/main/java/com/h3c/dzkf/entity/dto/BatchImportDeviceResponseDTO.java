package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 批量导入设备响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BatchImportDeviceResponseDTO extends ApiResponseDTO {
    
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OptionField {
        /**
         * 设备总数
         */
        @ApiModelProperty(value = "设备总数", example = "10")
        private Integer totalNum;
        
        /**
         * 成功总数
         */
        @ApiModelProperty(value = "成功导入数量", example = "8")
        private Integer succNum;
        
        /**
         * 失败总数
         */
        @ApiModelProperty(value = "失败导入数量", example = "2")
        private Integer failNum;
        
        /**
         * 设备添加结果，数组，用于显示每一个设备的执行结果
         */
        @ApiModelProperty(value = "设备导入结果列表")
        private List<AddDeviceResult> addDeviceResults;
    }
    
    @Data
    public static class AddDeviceResult {
        /**
         * 设备名称
         */
        @ApiModelProperty(value = "设备名称", example = "核心交换机01")
        private String deviceName;
        
        /**
         * 同步状态：0:失败，1：成功
         */
        @ApiModelProperty(value = "导入状态", allowableValues = "0,1", example = "1", notes = "0:失败，1：成功")
        private Integer status;
        
        /**
         * 失败原因
         */
        @ApiModelProperty(value = "失败原因", required = false, example = "设备ID已存在")
        private String errorMsg;
    }

    /**
     * 创建成功响应
     */
    public static BatchImportDeviceResponseDTO success(String requestId, OptionField optionField) {
        BatchImportDeviceResponseDTO response = success(requestId);
        response.setOptionField(optionField);
        return response;
    }

    /**
     * 创建成功响应（无optionField）
     */
    public static BatchImportDeviceResponseDTO success(String requestId) {
        BatchImportDeviceResponseDTO response = new BatchImportDeviceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }
    
    /**
     * 创建失败响应
     */
    public static BatchImportDeviceResponseDTO fail(String requestId, String failReason) {
        BatchImportDeviceResponseDTO response = new BatchImportDeviceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 