package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询隧道组响应DTO
 */
@Data
public class GetTeGroupResponseDTO {

    /**
     * 请求id，保存，用于溯源
     */
    @ApiModelProperty(value = "请求ID", example = "req_123456789")
    private String requestId;

    /**
     * 状态：0:失败，1：成功
     */
    @ApiModelProperty(value = "状态", example = "1", notes = "0:失败，1：成功")
    private Integer result;

    /**
     * 异常状态下，返回异常原因
     */
    @ApiModelProperty(value = "异常原因", example = "查询失败")
    private String failReason;

    /**
     * teGroup集合
     */
    @ApiModelProperty(value = "隧道组集合")
    private List<TeGroupInfo> data;

    /**
     * 创建成功响应
     */
    public static GetTeGroupResponseDTO success(String requestId, List<TeGroupInfo> teGroups) {
        GetTeGroupResponseDTO response = new GetTeGroupResponseDTO();
        response.setRequestId(requestId);
        response.setResult(1);
        response.setData(teGroups);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static GetTeGroupResponseDTO fail(String requestId, String failReason) {
        GetTeGroupResponseDTO response = new GetTeGroupResponseDTO();
        response.setRequestId(requestId);
        response.setResult(0);
        response.setFailReason(failReason);
        return response;
    }

    /**
     * 隧道组信息
     */
    @Data
    public static class TeGroupInfo {
        /**
         * 隧道组ID
         */
        @ApiModelProperty(value = "隧道组ID", example = "1001")
        private Long id;

        /**
         * 隧道组名称【支持英文、数字、下划线、短横线，最大长度15个字符】
         */
        @ApiModelProperty(value = "隧道组名称", example = "MyTeGroup")
        private String name;

        /**
         * 选路方式（true:智能选路, false:自定义）
         */
        @ApiModelProperty(value = "选路方式", example = "true", notes = "true:智能选路, false:自定义")
        private Boolean isLoose;

        /**
         * 路径模式 1-主备模式 2-负载模式
         */
        @ApiModelProperty(value = "路径模式", example = "1", notes = "1-主备模式 2-负载模式")
        private Integer balanceMode;

        /**
         * 负载模式下比例 例如1:2或1:2:3
         */
        @ApiModelProperty(value = "负载模式下比例", example = "1:2")
        private String balanceProportion;

        /**
         * 隧道模板ID
         */
        @ApiModelProperty(value = "隧道模板ID", example = "1")
        private Long scheduleStrategyId;

        /**
         * 隧道组的color值
         */
        @ApiModelProperty(value = "隧道组的color值", example = "100")
        private Integer color;

        /**
         * 路径条数
         */
        @ApiModelProperty(value = "路径条数", example = "2")
        private Integer pathSize;

        /**
         * 平面选路类型（1-同平面, 2-跨平面）【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "平面选路类型", example = "1", notes = "1-同平面, 2-跨平面")
        private Integer planeRoutingType;

        /**
         * 配置方式，是否按业务网络（true:按业务网络, false:按设备）
         */
        @ApiModelProperty(value = "配置方式", example = "false", notes = "true:按业务网络, false:按设备")
        private Boolean isVirtualNet;

        /**
         * 业务网络ID 【配置方式为按业务网络时填写】
         */
        @ApiModelProperty(value = "业务网络ID", example = "1001")
        private Integer virtualNetId;

        /**
         * 隧道组源目的信息 【配置方式为按设备时填写】
         */
        @ApiModelProperty(value = "隧道组源目的信息")
        private List<TeGroupDc> teGroupDcs;

        /**
         * 必选设备ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "必选设备ID集合")
        private List<Long> mandatoryNodeIds;

        /**
         * 排除设备ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "排除设备ID集合")
        private List<Long> excludeNodeIds;

        /**
         * 必经链路ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "必经链路ID集合")
        private List<Long> mandatoryLinkIds;

        /**
         * 排除链路ID集合 【选路方式为智能选路时填写】
         */
        @ApiModelProperty(value = "排除链路ID集合")
        private List<Long> excludeLinkIds;

        /**
         * 指定路径 【选路方式为自定义选路时填写】(废弃，不处理)
         */
        @ApiModelProperty(value = "指定路径", notes = "废弃，不处理")
        private List<Long> teTunnelManualList;

        /**
         * 最大跳数，取值范围1~2147483647
         */
        @ApiModelProperty(value = "最大跳数", example = "10")
        private Integer hopLimit;

        /**
         * 部署状态1-未部署，2-修改未部署（控制器无该状态，都返回未部署），3-部署成功，4-部署失败（有一个policy失败，就整体失败）
         */
        @ApiModelProperty(value = "部署状态", example = "1", notes = "1-未部署，2-修改未部署，3-部署成功，4-部署失败")
        private Integer deployState;
    }

    /**
     * 隧道组源目的信息
     */
    @Data
    public static class TeGroupDc {
        /**
         * 源设备ID列表
         */
        @ApiModelProperty(value = "源设备ID列表")
        private List<Long> srcDeviceIds;

        /**
         * 源设备名称列表
         */
        @ApiModelProperty(value = "源设备名称列表")
        private List<String> srcDeviceNames;

        /**
         * 目的设备ID列表
         */
        @ApiModelProperty(value = "目的设备ID列表")
        private List<Long> dstDeviceIds;

        /**
         * 目的设备名称列表
         */
        @ApiModelProperty(value = "目的设备名称列表")
        private List<String> dstDeviceNames;
    }
}
