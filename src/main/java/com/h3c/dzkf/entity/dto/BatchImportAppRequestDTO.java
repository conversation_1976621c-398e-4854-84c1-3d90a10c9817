package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * 批量导入应用请求DTO
 */
@Data
@ApiModel("批量导入应用请求")
public class BatchImportAppRequestDTO {

    /**
     * 应用批量文件
     */
    @NotNull(message = "应用批量文件不能为空")
    @ApiModelProperty(value = "应用批量文件", required = true)
    private MultipartFile file;
}