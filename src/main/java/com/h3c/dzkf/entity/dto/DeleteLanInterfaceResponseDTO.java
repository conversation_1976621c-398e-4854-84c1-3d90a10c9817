package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 删除LAN口接口响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "删除LAN口接口响应")
public class DeleteLanInterfaceResponseDTO extends ApiResponseDTO {

    /**
     * 创建成功响应
     */
    public static DeleteLanInterfaceResponseDTO success(String requestId) {
        DeleteLanInterfaceResponseDTO response = new DeleteLanInterfaceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static DeleteLanInterfaceResponseDTO fail(String requestId, String failReason) {
        DeleteLanInterfaceResponseDTO response = new DeleteLanInterfaceResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 