package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 部署隧道组请求DTO
 */
@Data
public class DeployTeGroupRequestDTO {

    /**
     * 隧道组ID集合
     */
    @ApiModelProperty(value = "隧道组ID集合", required = true, example = "[1001, 1002, 1003]", notes = "要部署的隧道组ID列表")
    @NotEmpty(message = "隧道组ID集合不能为空")
    private List<Long> teGroupIds;

    /**
     * 是否下发配置
     * true: 计算（规划）路径并下发
     * false: 只作计算（规划），不下发
     * 默认为true
     */
    @ApiModelProperty(value = "是否下发配置", required = false, example = "true", 
        notes = "true: 计算（规划）路径并下发; false: 只作计算（规划），不下发; 默认为true")
    private Boolean distribute = true;

}
