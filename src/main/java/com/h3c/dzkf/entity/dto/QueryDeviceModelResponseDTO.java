package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询设备型号响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询设备型号响应")
public class QueryDeviceModelResponseDTO extends ApiResponseDTO {

    /**
     * 设备型号列表
     */
    @ApiModelProperty(value = "设备型号列表")
    private List<DeviceModel> deviceModelList;

    /**
     * 设备型号信息
     */
    @Data
    @ApiModel("设备型号信息")
    public static class DeviceModel {
        /**
         * 设备型号
         */
        @ApiModelProperty(value = "设备型号")
        private String deviceModel;

        /**
         * 厂商扩展字段
         */
        @ApiModelProperty(value = "厂商扩展字段")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Object optionField;
    }

    /**
     * 构造成功响应
     */
    public static QueryDeviceModelResponseDTO success(String requestId, List<DeviceModel> deviceModelList) {
        QueryDeviceModelResponseDTO response = new QueryDeviceModelResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setDeviceModelList(deviceModelList);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryDeviceModelResponseDTO fail(String requestId, String failReason) {
        QueryDeviceModelResponseDTO response = new QueryDeviceModelResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 