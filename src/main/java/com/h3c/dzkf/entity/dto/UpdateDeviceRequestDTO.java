package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新设备请求DTO
 */
@Data
@ApiModel("更新设备请求参数")
public class UpdateDeviceRequestDTO {

    /**
     * 设备ID，工行控制器统一设定唯一标识
     */
    @ApiModelProperty(value = "设备ID", required = true, example = "1001")
    @NotNull(message = "设备ID不能为空")
    private Long deviceId;

    /**
     * 新设备名称
     */
    @ApiModelProperty(value = "新设备名称", required = true, example = "更新后的设备名称")
    @NotBlank(message = "设备名称不能为空")
    private String deviceName;

    /**
     * 是否纳管状态(0/1)，平台无该字段，仅保留兼容性
     */
    @ApiModelProperty(value = "是否纳管状态", required = true, example = "1", notes = "0:不纳管，1:纳管")
    @NotNull(message = "纳管状态不能为空")
    private Integer isMarkDevice;
} 