package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 工行更新链路请求DTO
 */
@Data
public class UpdateLinkRequestDTO {

    /**
     * 链路Id，由SDWAN平台在新建时返回（必填）
     */
    @ApiModelProperty(value = "链路ID，由SDWAN平台在新建时返回", required = true, example = "1001")
    @NotNull(message = "链路ID不能为空")
    private Long linkId;

    /**
     * 链路名称（可选）
     */
    @ApiModelProperty(value = "链路名称", required = false, example = "总行-分行链路")
    private String linkName;

    /**
     * 链路类型（可选）
     */
    @ApiModelProperty(value = "链路类型", required = false, example = "专线")
    private String linkType;

    /**
     * 线路带宽，格式：1000Mbps，支持bps/Kbps/Mbps/Gbps（可选）
     */
    @ApiModelProperty(value = "线路带宽", required = false, example = "1000Mbps", notes = "支持bps/Kbps/Mbps/Gbps格式")
    private String linkBandWidth;
} 