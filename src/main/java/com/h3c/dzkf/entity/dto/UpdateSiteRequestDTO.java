package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改站点请求DTO
 */
@Data
@ApiModel("修改站点请求参数")
public class UpdateSiteRequestDTO {

    /**
     * 站点ID，工行控制器统一设定唯一标识
     */
    @ApiModelProperty(value = "站点ID，工行控制器统一设定唯一标识", required = true)
    @NotNull(message = "站点ID不能为空")
    private Integer siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty(value = "站点名称", required = true)
    @NotBlank(message = "站点名称不能为空")
    private String siteName;

    /**
     * 父级站点ID，可为空
     */
    @ApiModelProperty(value = "父级站点ID，可为空", required = false)
    private Integer parentSite;

    /**
     * 位置信息
     */
    @ApiModelProperty(value = "位置信息", required = true)
    @NotBlank(message = "位置信息不能为空")
    private String location;

    /**
     * 站点类型
     */
    @ApiModelProperty(value = "站点类型", required = true)
//    @NotBlank(message = "站点类型不能为空")
    private String siteType;
} 