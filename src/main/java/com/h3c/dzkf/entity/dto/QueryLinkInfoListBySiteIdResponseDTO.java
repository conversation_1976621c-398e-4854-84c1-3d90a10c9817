package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询站点下链路信息列表响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryLinkInfoListBySiteIdResponseDTO extends ApiResponseDTO {

    /**
     * 总数
     */
    @ApiModelProperty(value = "链路总数", example = "10")
    private Integer total;

    /**
     * 链路集合，数组
     */
    @ApiModelProperty(value = "链路信息列表")
    private List<LinkInfoDTO> linkInfoList;

    /**
     * 链路信息DTO
     */
    @Data
    public static class LinkInfoDTO {
        /**
         * 链路id
         */
        @ApiModelProperty(value = "链路ID", example = "1001")
        private String linkId;

        /**
         * 站点id
         */
        @ApiModelProperty(value = "站点ID", example = "100")
        private Integer siteId;

        /**
         * 链路状态，翻译成中文
         */
        @ApiModelProperty(value = "链路状态", example = "正常")
        private String linkStatus;

        /**
         * 链路名称
         */
        @ApiModelProperty(value = "链路名称", example = "总行-分行链路")
        private String linkName;

        /**
         * 空（预留字段）
         */
        @ApiModelProperty(value = "运营商名称（预留字段）", required = false)
        private String carrierName;

        /**
         * 源设备id
         */
        @ApiModelProperty(value = "源设备ID", example = "1001")
        private Integer sourceDeviceId;

        /**
         * 源设备名称
         */
        @ApiModelProperty(value = "源设备名称", example = "核心交换机01")
        private String sourceDeviceName;

        /**
         * 源设备IP
         */
        @ApiModelProperty(value = "源设备IP", example = "*************")
        private String sourceDeviceIp;

        /**
         * 源设备端口
         */
        @ApiModelProperty(value = "源设备端口", example = "GE1/0/1")
        private String sourceDevicePort;

        /**
         * 目标设备id
         */
        @ApiModelProperty(value = "目标设备ID", example = "1002")
        private Integer targetDeviceId;

        /**
         * 目标设备名称
         */
        @ApiModelProperty(value = "目标设备名称", example = "分支交换机01")
        private String targetDeviceName;

        /**
         * 目的设备IP
         */
        @ApiModelProperty(value = "目标设备IP", example = "*************")
        private String targetDeviceIp;

        /**
         * 目标设备端口
         */
        @ApiModelProperty(value = "目标设备端口", example = "GE1/0/2")
        private String targetDevicePort;

        /**
         * 链路类型
         */
        @ApiModelProperty(value = "链路类型", example = "专线")
        private String linkType;

        /**
         * 链路优先级
         */
        @ApiModelProperty(value = "链路优先级", example = "高")
        private String linkLevel;

        /**
         * 链路带宽
         */
        @ApiModelProperty(value = "链路带宽(Mbps)", example = "1000.0")
        private Double linkBandWidth;

        /**
         * 上行带宽（空）
         */
        @ApiModelProperty(value = "上行带宽（预留字段）", required = false)
        private Double upBandWidth;

        /**
         * 下行带宽（空）
         */
        @ApiModelProperty(value = "下行带宽（预留字段）", required = false)
        private Double downBandWidth;

        /**
         * 接收速率（反向链路的带宽字段）
         */
        @ApiModelProperty(value = "接收速率(Mbps)", example = "950.5")
        private Double receiveRate;

        /**
         * 发送速率（链路带宽字段）
         */
        @ApiModelProperty(value = "发送速率(Mbps)", example = "980.3")
        private Double sendRate;

        /**
         * 接收带宽使用百分比
         */
        @ApiModelProperty(value = "接收带宽使用百分比", example = "75.5")
        private Double receiveBwUsedPercent;

        /**
         * 发送带宽使用百分比
         */
        @ApiModelProperty(value = "发送带宽使用百分比", example = "82.3")
        private Double sendBwUsedPercent;

        /**
         * 接收延迟
         */
        @ApiModelProperty(value = "接收延迟(ms)", example = "5.2")
        private Double receiveDelay;

        /**
         * 发送延迟
         */
        @ApiModelProperty(value = "发送延迟(ms)", example = "4.8")
        private Double sendDelay;

        /**
         * 接收抖动
         */
        @ApiModelProperty(value = "接收抖动(ms)", example = "1.2")
        private Double receiveJitter;

        /**
         * 发送抖动
         */
        @ApiModelProperty(value = "发送抖动(ms)", example = "0.9")
        private Double sendJitter;

        /**
         * 接收丢包率
         */
        @ApiModelProperty(value = "接收丢包率(%)", example = "0.01")
        private Double receiveLoss;

        /**
         * 发送丢包率
         */
        @ApiModelProperty(value = "发送丢包率(%)", example = "0.02")
        private Double sendLoss;

        /**
         * 是否加密（空，预留字段）
         */
        @ApiModelProperty(value = "是否加密（预留字段）", allowableValues = "0,1", required = false, notes = "0:未加密，1:已加密")
        private Integer isEncrypt;

        /**
         * 厂商自定义字段
         */
        @ApiModelProperty(value = "厂商自定义字段", required = false)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Object optionField;
    }

    /**
     * 构造成功响应
     */
    public static QueryLinkInfoListBySiteIdResponseDTO success(String requestId, List<LinkInfoDTO> linkInfoList) {
        QueryLinkInfoListBySiteIdResponseDTO response = new QueryLinkInfoListBySiteIdResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setTotal(linkInfoList != null ? linkInfoList.size() : 0);
        response.setLinkInfoList(linkInfoList);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryLinkInfoListBySiteIdResponseDTO fail(String requestId, String failReason) {
        QueryLinkInfoListBySiteIdResponseDTO response = new QueryLinkInfoListBySiteIdResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 