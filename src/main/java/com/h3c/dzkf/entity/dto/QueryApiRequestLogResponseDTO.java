package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询API请求日志响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "查询API请求日志响应")
public class QueryApiRequestLogResponseDTO extends ApiResponseDTO {

    /**
     * 日志ID
     */
    @ApiModelProperty(value = "日志ID", example = "1")
    private Long id;

    /**
     * 请求ID
     */
    @ApiModelProperty(value = "请求ID", example = "req_123456")
    private String requestId;

    /**
     * 请求URI
     */
    @ApiModelProperty(value = "请求URI", example = "/srv6/addDevice")
    private String requestUri;

    /**
     * 请求方法
     */
    @ApiModelProperty(value = "请求方法", example = "POST")
    private String requestMethod;

    /**
     * 请求体
     */
    @ApiModelProperty(value = "请求体")
    private String requestBody;

    /**
     * 响应体
     */
    @ApiModelProperty(value = "响应体")
    private String responseBody;

    /**
     * 请求时间
     */
    @ApiModelProperty(value = "请求时间", example = "2024-01-01 10:00:00")
    private String requestTime;

    /**
     * 请求耗时（毫秒）
     */
    @ApiModelProperty(value = "请求耗时（毫秒）", example = "1500")
    private Integer durationMs;

    /**
     * 客户端IP
     */
    @ApiModelProperty(value = "客户端IP", example = "*************")
    private String clientIp;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 10:00:00")
    private String createTime;

    /**
     * 构造成功响应
     */
    public static QueryApiRequestLogResponseDTO success(String requestId) {
        QueryApiRequestLogResponseDTO response = new QueryApiRequestLogResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryApiRequestLogResponseDTO fail(String requestId, String failReason) {
        QueryApiRequestLogResponseDTO response = new QueryApiRequestLogResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
}
