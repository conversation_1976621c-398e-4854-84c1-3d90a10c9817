package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 删除隧道组请求DTO
 */
@Data
public class DeleteTeGroupRequestDTO {

    /**
     * 隧道组ID集合
     */
    @ApiModelProperty(value = "隧道组ID集合", required = true, example = "[1001, 1002, 1003]", notes = "要删除的隧道组ID列表")
    @NotEmpty(message = "隧道组ID集合不能为空")
    private List<Long> teGroupIds;

}
