package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 查询隧道详情响应DTO
 */
@Data
public class GetTeGroupDetailsResponseDTO {

    /**
     * 请求id，保存，用于溯源
     */
    @ApiModelProperty(value = "请求ID", example = "req_123456789")
    private String requestId;

    /**
     * 状态：0:失败，1：成功
     */
    @ApiModelProperty(value = "状态", example = "1", notes = "0:失败，1：成功")
    private Integer result;

    /**
     * 异常状态下，返回异常原因
     */
    @ApiModelProperty(value = "异常原因", example = "查询失败")
    private String failReason;

    /**
     * teGroupDetail集合
     */
    @ApiModelProperty(value = "隧道详情集合")
    private List<TeGroupDetail> data;

    /**
     * 创建成功响应
     */
    public static GetTeGroupDetailsResponseDTO success(String requestId, List<TeGroupDetail> teGroupDetails) {
        GetTeGroupDetailsResponseDTO response = new GetTeGroupDetailsResponseDTO();
        response.setRequestId(requestId);
        response.setResult(1);
        response.setData(teGroupDetails);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static GetTeGroupDetailsResponseDTO fail(String requestId, String failReason) {
        GetTeGroupDetailsResponseDTO response = new GetTeGroupDetailsResponseDTO();
        response.setRequestId(requestId);
        response.setResult(0);
        response.setFailReason(failReason);
        return response;
    }

    /**
     * 隧道详情信息
     */
    @Data
    public static class TeGroupDetail {
        /**
         * 隧道ID
         */
        @ApiModelProperty(value = "隧道ID", example = "1001")
        private Long id;

        /**
         * 隧道名称
         */
        @ApiModelProperty(value = "隧道名称", example = "tunnel-001")
        private String name;

        /**
         * 隧道组ID
         */
        @ApiModelProperty(value = "隧道组ID", example = "1001")
        private Long teGroupId;

        /**
         * 源设备ID
         */
        @ApiModelProperty(value = "源设备ID", example = "1001")
        private Long srcNodeId;

        /**
         * 目的设备ID
         */
        @ApiModelProperty(value = "目的设备ID", example = "1002")
        private Long dstNodeId;

        /**
         * 路径
         */
        @ApiModelProperty(value = "路径")
        private List<PathDetail> path;

        /**
         * 隧道组的color值
         */
        @ApiModelProperty(value = "隧道组的color值", example = "100")
        private Integer color;

        /**
         * 源设备名称
         */
        @ApiModelProperty(value = "源设备名称", example = "Device-001")
        private String srcNodeName;

        /**
         * 目的设备名称
         */
        @ApiModelProperty(value = "目的设备名称", example = "Device-002")
        private String dstNodeName;

        /**
         * 隧道部署状态：1-未部署，2-修改未部署，3-部署成功，4-部署失败，5-待删除
         */
        @ApiModelProperty(value = "隧道部署状态", example = "3",
                notes = "1-未部署，2-修改未部署，3-部署成功，4-部署失败，5-待删除")
        private Integer deployState;

        /**
         * 是否为反向隧道
         */
        @ApiModelProperty(value = "是否为反向隧道", example = "false")
        private Boolean reversed;

        /**
         * 反向隧道ID
         */
        @ApiModelProperty(value = "反向隧道ID", example = "1002")
        private Long reverseId;

        /**
         * 部署信息
         */
        @ApiModelProperty(value = "部署信息", example = "部署成功")
        private String deployMsg;
    }

    /**
     * 路径详情
     */
    @Data
    public static class PathDetail {
        /**
         * 路径成员ID
         */
        @ApiModelProperty(value = "路径成员ID", example = "1001")
        private Long memberId;

        /**
         * 路径成员名称
         */
        @ApiModelProperty(value = "路径成员名称", example = "Node-001")
        private String memberName;

        /**
         * 路径成员类型
         */
        @ApiModelProperty(value = "路径成员类型", example = "NODE",
                notes = "NODE-节点, LINK-链路, ANYCAST-ANYCAST节点, BSID-指定policy, SFC-指定服务链节点, DT4-DT4")
        private String memberType;

        /**
         * 顺序
         */
        @ApiModelProperty(value = "顺序", example = "1")
        private Integer memberIndex;
    }
}
