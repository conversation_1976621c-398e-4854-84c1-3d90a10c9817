package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新增链路响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("新增链路响应")
public class AddLinkResponseDTO extends ApiResponseDTO {

    @ApiModelProperty("平台生成的linkid,如果失败，返回0")
    private Long linkId;

    /**
     * 构造成功响应
     */
    public static AddLinkResponseDTO success(String requestId, Long linkId, Object optionField) {
        AddLinkResponseDTO response = success(requestId, linkId);
        response.setOptionField(optionField);
        return response;
    }

    /**
     * 构造成功响应（无optionField）
     */
    public static AddLinkResponseDTO success(String requestId, Long linkId) {
        AddLinkResponseDTO response = new AddLinkResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setLinkId(linkId);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static AddLinkResponseDTO fail(String requestId, String failReason) {
        AddLinkResponseDTO response = new AddLinkResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        response.setLinkId(0L);
        return response;
    }
} 