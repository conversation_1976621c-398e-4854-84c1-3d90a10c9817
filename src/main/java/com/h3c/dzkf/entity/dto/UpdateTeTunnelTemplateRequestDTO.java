package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改隧道模板请求DTO
 */
@Data
public class UpdateTeTunnelTemplateRequestDTO {

    /**
     * 隧道策略信息
     */
    @ApiModelProperty(value = "隧道策略信息", required = true)
    @NotNull(message = "隧道策略信息不能为空")
    @Valid
    private Strategy strategy;

    /**
     * 隧道策略内部类
     */
    @Data
    public static class Strategy {
        /**
         * 隧道策略ID
         */
        @ApiModelProperty(value = "隧道策略ID", required = true, example = "1")
        @NotNull(message = "隧道策略ID不能为空")
        private Long strategyId;

        /**
         * 策略名称
         */
        @ApiModelProperty(value = "策略名称", required = true, example = "高优先级隧道策略")
        @NotBlank(message = "策略名称不能为空")
        private String strategyName;

        /**
         * SLA等级：EF、AF4、AF3、AF2、AF1、BE
         */
        @ApiModelProperty(value = "SLA等级", required = false, allowableValues = "EF,AF4,AF3,AF2,AF1,BE", example = "EF", notes = "服务等级协议，可选值：EF、AF4、AF3、AF2、AF1、BE")
        private String slaId;

        /**
         * 保障带宽
         */
        @ApiModelProperty(value = "保障带宽", required = false, example = "1000", notes = "单位为Mbps")
        private Long bandwidth;

        /**
         * 正向链路等级，优选链路等级
         */
        @ApiModelProperty(value = "正向链路等级", required = true, example = "高", notes = "优选链路等级，可选值：高、中、低")
        @NotBlank(message = "正向链路等级不能为空")
        private String upAllowLinkPriority;

        /**
         * 反向链路等级，可选链路等级
         */
        @ApiModelProperty(value = "反向链路等级", required = true, example = "[\"高\", \"中\", \"低\"]", notes = "可选链路等级列表，可选值：高、中、低")
        @NotNull(message = "反向链路等级不能为空")
        private List<String> downAllowLinkPriorityList;

        /**
         * 丢包率
         */
        @ApiModelProperty(value = "丢包率", required = true, example = "0.01", notes = "允许的最大丢包率")
        @NotBlank(message = "丢包率不能为空")
        private String packetLossRate;

        /**
         * 延迟时间
         */
        @ApiModelProperty(value = "延迟时间", required = true, example = "100", notes = "最大允许延迟时间，单位为毫秒")
        @NotBlank(message = "延迟时间不能为空")
        private String delayTime;

        /**
         * 网络抖动
         */
        @ApiModelProperty(value = "网络抖动", required = true, example = "10", notes = "最大允许网络抖动，单位为毫秒")
        @NotBlank(message = "网络抖动不能为空")
        private String networkJitter;
    }

}