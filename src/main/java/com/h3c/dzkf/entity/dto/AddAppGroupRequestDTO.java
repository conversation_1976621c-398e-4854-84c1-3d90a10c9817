package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增应用组请求DTO
 */
@Data
@ApiModel("新增应用组请求参数")
public class AddAppGroupRequestDTO {

    /**
     * 应用组唯一标识ID（工行控制器设定）
     */
    @ApiModelProperty(value = "应用组唯一标识ID（工行控制器设定）", required = true)
    @NotNull(message = "应用组ID不能为空")
    private Integer appGroupId;

    /**
     * 应用组名称
     */
    @ApiModelProperty(value = "应用组名称", required = true)
    @NotBlank(message = "应用组名称不能为空")
    private String appGroupName;
} 