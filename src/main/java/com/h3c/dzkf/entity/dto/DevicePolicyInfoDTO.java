package com.h3c.dzkf.entity.dto;

import lombok.Data;

/**
 * 设备策略信息DTO
 * 包含设备ID、平台设备ID、设备IP和策略ID的完整信息
 */
@Data
public class DevicePolicyInfoDTO {

    /**
     * 定制表设备ID（用于查询lan_custom_info等定制表）
     */
    private Integer deviceId;

    /**
     * 平台节点ID（用于调用平台接口）
     */
    private String platformNodeId;

    /**
     * 设备IP地址（用于流策略命名）
     */
    private String deviceIp;

    /**
     * 流策略ID
     */
    private Integer policyId;

    /**
     * 构造函数
     */
    public DevicePolicyInfoDTO() {
    }

    /**
     * 构造函数
     */
    public DevicePolicyInfoDTO(Integer deviceId, String platformNodeId, String deviceIp, Integer policyId) {
        this.deviceId = deviceId;
        this.platformNodeId = platformNodeId;
        this.deviceIp = deviceIp;
        this.policyId = policyId;
    }
}
