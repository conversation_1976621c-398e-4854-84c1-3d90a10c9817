package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询应用组列表响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询应用组列表响应")
public class QueryAppGroupListResponseDTO extends ApiResponseDTO {

    /**
     * 总量
     */
    @ApiModelProperty(value = "总量")
    private Integer total;

    /**
     * 应用组数组
     */
    @ApiModelProperty(value = "应用组数组")
    private List<AppGroupInfo> appGroupList;

    /**
     * 应用组信息
     */
    @Data
    @ApiModel("应用组信息")
    public static class AppGroupInfo {
        /**
         * 应用组ID
         */
        @ApiModelProperty(value = "应用组ID")
        private Integer appGroupId;

        /**
         * 应用组名称
         */
        @ApiModelProperty(value = "应用组名称")
        private String appGroupName;
    }

    /**
     * 构造成功响应
     */
    public static QueryAppGroupListResponseDTO success(String requestId, List<AppGroupInfo> appGroupList) {
        QueryAppGroupListResponseDTO response = new QueryAppGroupListResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setTotal(appGroupList != null ? appGroupList.size() : 0);
        response.setAppGroupList(appGroupList);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryAppGroupListResponseDTO fail(String requestId, String failReason) {
        QueryAppGroupListResponseDTO response = new QueryAppGroupListResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        response.setTotal(0);
        return response;
    }
} 