package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统一API响应DTO
 */
@Data
public class ApiResponseDTO {
    
    /**
     * 成功状态常量
     */
    public static final int SUCCESS = 1;
    
    /**
     * 失败状态常量
     */
    public static final int FAIL = 0;
    
    /**
     * 请求id，保存，用于溯源
     */
    @ApiModelProperty(value = "请求ID，用于溯源", example = "req_123456")
    private String requestId;
    
    /**
     * 状态：0:失败，1：成功
     */
    @ApiModelProperty(value = "响应状态", allowableValues = "0,1", example = "1", notes = "0:失败，1：成功")
    private Integer result;
    
    /**
     * 异常状态下，返回异常原因
     */
    @ApiModelProperty(value = "失败原因", required = false, example = "参数验证失败")
    private String failReason;
    
    /**
     * 涉及的属性
     */
    @ApiModelProperty(value = "自定义字段", required = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object optionField;
    
    /**
     * 构造成功响应
     */
    public static ApiResponseDTO success(String requestId, Object optionField) {
        ApiResponseDTO response = success(requestId);
        response.setOptionField(optionField);
        return response;
    }

    /**
     * 构造成功响应（无optionField）
     */
    public static ApiResponseDTO success(String requestId) {
        ApiResponseDTO response = new ApiResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }
    
    /**
     * 构造失败响应
     */
    public static ApiResponseDTO fail(String requestId, String failReason) {
        ApiResponseDTO response = new ApiResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 