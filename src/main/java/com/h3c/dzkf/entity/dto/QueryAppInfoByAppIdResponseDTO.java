package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询单个应用响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询单个应用响应")
public class QueryAppInfoByAppIdResponseDTO extends ApiResponseDTO {

    /**
     * 应用对象（单个，不是数组）
     */
    @ApiModelProperty(value = "应用对象")
    private AppInfo app;

    /**
     * 应用信息
     */
    @Data
    @ApiModel("应用信息")
    public static class AppInfo {
        /**
         * 应用ID
         */
        @ApiModelProperty(value = "应用ID", required = true)
        private Integer appId;

        /**
         * 应用名称
         */
        @ApiModelProperty(value = "应用名称", required = true)
        private String appName;

        /**
         * 应用类型（五元组/DSCP）
         */
        @ApiModelProperty(value = "应用类型（五元组/DSCP）", required = true)
        private String appType;

        /**
         * 网络应用DSCP（非必须）
         */
        @ApiModelProperty(value = "网络应用DSCP（非必须）")
        private String internetAppDSCP;

        /**
         * 协议类型
         */
        @ApiModelProperty(value = "协议类型", required = true)
        private String agreementType;

        /**
         * 源IP及掩码
         */
        @ApiModelProperty(value = "源IP及掩码", required = true)
        private String sourceIP;

        /**
         * 源端口
         */
        @ApiModelProperty(value = "源端口", required = true)
        private String sourcePort;

        /**
         * 目的IP及端口列表
         */
        @ApiModelProperty(value = "目的IP及端口列表", required = true)
        private List<String> destinationIPList;

        /**
         * IPv6源IP及掩码
         */
        @ApiModelProperty(value = "IPv6源IP及掩码")
        private String sourceIPv6;

        /**
         * IPv6源端口
         */
        @ApiModelProperty(value = "IPv6源端口")
        private String sourcePortIPv6;

        /**
         * IPv6目的IP及端口列表
         */
        @ApiModelProperty(value = "IPv6目的IP及端口列表")
        private List<String> destinationIPv6List;

        /**
         * 所属应用分组
         */
        @ApiModelProperty(value = "所属应用分组", required = true)
        private String appGroupName;

        /**
         * 厂商自定义扩展字段
         */
        @ApiModelProperty(value = "厂商自定义扩展字段")
        private Object optionField;
    }

    /**
     * 构造成功响应
     */
    public static QueryAppInfoByAppIdResponseDTO success(String requestId, AppInfo app) {
        QueryAppInfoByAppIdResponseDTO response = new QueryAppInfoByAppIdResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setApp(app);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryAppInfoByAppIdResponseDTO fail(String requestId, String failReason) {
        QueryAppInfoByAppIdResponseDTO response = new QueryAppInfoByAppIdResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 