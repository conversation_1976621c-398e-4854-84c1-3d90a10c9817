package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 查询设备状态信息响应DTO
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryDeviceStatusResponseDTO extends ApiResponseDTO {
    
    /**
     * 设备总数
     */
    @ApiModelProperty(value = "设备总数", example = "100")
    private Integer total;
    
    /**
     * 在线设备数量
     */
    @ApiModelProperty(value = "在线设备数量", example = "95")
    private Integer onLine;
    
    /**
     * 离线设备数量
     */
    @ApiModelProperty(value = "离线设备数量", example = "5")
    private Integer offLine;

    /**
     * 构造成功响应
     */
    public static QueryDeviceStatusResponseDTO success(String requestId, Integer total, Integer onLine, Integer offLine) {
        QueryDeviceStatusResponseDTO response = new QueryDeviceStatusResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        response.setTotal(total);
        response.setOnLine(onLine);
        response.setOffLine(offLine);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryDeviceStatusResponseDTO fail(String requestId, String failReason) {
        QueryDeviceStatusResponseDTO response = new QueryDeviceStatusResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 