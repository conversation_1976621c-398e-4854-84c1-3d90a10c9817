package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 批量导入站点请求DTO
 */
@Data
@ApiModel("批量导入站点请求参数")
public class BatchImportSiteRequestDTO {

    /**
     * Excel行号（用于错误提示）
     */
    @ApiModelProperty(value = "Excel行号", hidden = true)
    private Integer rowIndex;

    /**
     * 站点ID，工行控制器统一设定唯一标识
     */
    @ApiModelProperty(value = "站点ID，工行控制器统一设定唯一标识", required = true)
    private Integer siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty(value = "站点名称", required = true)
    private String siteName;

    /**
     * 所属站点（父级站点ID或名称）
     */
    @ApiModelProperty(value = "所属站点", required = false)
    private String parentSite;

    /**
     * 站点位置
     */
    @ApiModelProperty(value = "站点位置", required = true)
    private String location;

    /**
     * 站点类型
     */
    @ApiModelProperty(value = "站点类型", required = true)
    private String siteType;
} 