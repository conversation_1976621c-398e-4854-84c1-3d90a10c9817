package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 删除隧道模板请求DTO
 */
@Data
public class DeleteTeTunnelTemplateRequestDTO {

    /**
     * 隧道策略ID集合
     */
    @ApiModelProperty(value = "隧道策略ID集合", required = true, example = "[1, 2]", notes = "要删除的隧道策略ID列表")
    @NotEmpty(message = "隧道策略ID集合不能为空")
    private List<Long> strategyIds;

} 