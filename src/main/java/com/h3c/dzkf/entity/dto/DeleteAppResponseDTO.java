package com.h3c.dzkf.entity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除应用响应DTO
 */
@Data
@ApiModel("删除应用响应")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeleteAppResponseDTO {

    /**
     * 请求ID，用于溯源
     */
    @ApiModelProperty(value = "请求ID，用于溯源", example = "req_20240630_001")
    private String requestId;

    /**
     * 状态：0-失败，1-成功
     */
    @ApiModelProperty(value = "状态：0-失败，1-成功", example = "1")
    private Integer result;

    /**
     * 异常状态下，返回异常原因
     */
    @ApiModelProperty(value = "异常状态下，返回异常原因", example = "删除成功")
    private String failReason;

    /**
     * 成功响应
     */
    public static DeleteAppResponseDTO success(String requestId) {
        DeleteAppResponseDTO response = new DeleteAppResponseDTO();
        response.setRequestId(requestId);
        response.setResult(1);
        response.setFailReason("删除成功");
        return response;
    }

    /**
     * 失败响应
     */
    public static DeleteAppResponseDTO fail(String requestId, String failReason) {
        DeleteAppResponseDTO response = new DeleteAppResponseDTO();
        response.setRequestId(requestId);
        response.setResult(0);
        response.setFailReason(failReason);
        return response;
    }
} 