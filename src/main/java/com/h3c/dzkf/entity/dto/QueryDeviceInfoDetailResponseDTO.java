package com.h3c.dzkf.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 查询设备详细信息响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryDeviceInfoDetailResponseDTO extends ApiResponseDTO {

    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID", example = "1001")
    private Long deviceId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称", example = "核心交换机01")
    private String deviceName;

    /**
     * 设备厂商
     */
    @ApiModelProperty(value = "设备厂商", example = "H3C")
    private String deviceManufacturer;

    /**
     * 设备型号
     */
    @ApiModelProperty(value = "设备型号", example = "S6850-32QC-EI")
    private String deviceModel;

    /**
     * 设备MAC地址
     */
    @ApiModelProperty(value = "设备MAC地址", example = "00:11:22:33:44:55")
    private String deviceMac;

    /**
     * 设备序列号
     */
    @ApiModelProperty(value = "设备序列号", example = "SN123456789")
    private String deviceSn;

    /**
     * 设备状态
     */
    @ApiModelProperty(value = "设备状态", example = "在线")
    private String deviceStatus;

    /**
     * 设备IP
     */
    @ApiModelProperty(value = "设备IP", example = "*************")
    private String deviceIp;

    /**
     * 设备站点
     */
    @ApiModelProperty(value = "设备站点", example = "总行机房")
    private String deviceSite;

    /**
     * 设备角色
     */
    @ApiModelProperty(value = "设备角色", allowableValues = "hub,spoke,agg", example = "hub")
    private String deviceRole;

    /**
     * 设备硬件版本
     */
    @ApiModelProperty(value = "设备硬件版本", example = "V1.0")
    private String deviceHardwareVersion;

    /**
     * 设备软件版本
     */
    @ApiModelProperty(value = "设备软件版本", example = "V7.1.070")
    private String deviceSoftwareVersion;

    /**
     * 首次上线时间
     */
    @ApiModelProperty(value = "首次上线时间", example = "2024-01-01 10:00:00")
    private String firstOnlineTime;

    /**
     * 最后在线时间
     */
    @ApiModelProperty(value = "最后在线时间", example = "2024-01-15 14:30:00")
    private String lastOnlineTime;

    /**
     * 是否为RR设备
     */
    @ApiModelProperty(value = "是否为RR设备", example = "true")
    private Boolean isRR;

    /**
     * 是否纳管
     */
    @ApiModelProperty(value = "是否纳管", allowableValues = "0,1", example = "1", notes = "0:不纳管，1:纳管")
    private Integer isMarkDevice;

    /**
     * 温度
     */
    @ApiModelProperty(value = "设备温度(℃)", example = "45")
    private Integer temperature;

    /**
     * 内存利用率(%)
     */
    @ApiModelProperty(value = "内存利用率(%)", example = "75")
    private Integer memUseRate;

    /**
     * CPU利用率(%)
     */
    @ApiModelProperty(value = "CPU利用率(%)", example = "65")
    private Integer cpuUseRate;

    /**
     * 电源信息列表
     */
    @ApiModelProperty(value = "电源信息列表")
    private List<PowerInfo> powerInfos;

    /**
     * 风扇信息列表
     */
    @ApiModelProperty(value = "风扇信息列表")
    private List<FanInfo> fanInfos;

    /**
     * 端口信息列表
     */
    @ApiModelProperty(value = "端口信息列表")
    private List<PortInfo> ports;

    /**
     * 告警信息列表
     */
    @ApiModelProperty(value = "告警信息列表")
    private List<AlarmInfo> alarmInfos;

    /**
     * 电源信息内嵌类
     */
    @Data
    public static class PowerInfo {
        /**
         * 电源ID
         */
        @ApiModelProperty(value = "电源ID", example = "1")
        private Integer powerId;

        /**
         * 电源状态（0异常/1正常）
         */
        @ApiModelProperty(value = "电源状态", allowableValues = "0,1", example = "1", notes = "0:异常，1:正常")
        private Integer powerStatus;
    }

    /**
     * 风扇信息内嵌类
     */
    @Data
    public static class FanInfo {
        /**
         * 风扇ID
         */
        @ApiModelProperty(value = "风扇ID", example = "1")
        private Integer fanId;

        /**
         * 风扇状态（0异常/1正常）
         */
        @ApiModelProperty(value = "风扇状态", allowableValues = "0,1", example = "1", notes = "0:异常，1:正常")
        private Integer fanStatus;
    }

    /**
     * 端口信息内嵌类
     */
    @Data
    public static class PortInfo {
        /**
         * 端口名称
         */
        @ApiModelProperty(value = "端口名称", example = "GE1/0/1")
        private String portName;

        /**
         * 端口状态
         */
        @ApiModelProperty(value = "端口状态", allowableValues = "on,off", example = "on")
        private String portStatus;

        /**
         * 端口带宽
         */
        @ApiModelProperty(value = "端口带宽(Mbps)", example = "1000")
        private String portBandWidth;
    }

    /**
     * 告警信息内嵌类
     */
    @Data
    public static class AlarmInfo {
        /**
         * 告警级别
         */
        @ApiModelProperty(value = "告警级别", allowableValues = "严重,重要,次要,警告", example = "重要")
        private String alarmLevel;

        /**
         * 告警名称
         */
        @ApiModelProperty(value = "告警名称", example = "端口状态异常")
        private String alarmName;

        /**
         * 告警时间
         */
        @ApiModelProperty(value = "告警时间", example = "2024-01-15 14:30:00")
        private String alarmTime;

        /**
         * 告警描述
         */
        @ApiModelProperty(value = "告警描述", example = "端口GE1/0/1状态异常，请检查连接")
        private String alarmDescription;
    }

    /**
     * 构造成功响应
     */
    public static QueryDeviceInfoDetailResponseDTO success(String requestId) {
        QueryDeviceInfoDetailResponseDTO response = new QueryDeviceInfoDetailResponseDTO();
        response.setRequestId(requestId);
        response.setResult(SUCCESS);
        return response;
    }

    /**
     * 构造失败响应
     */
    public static QueryDeviceInfoDetailResponseDTO fail(String requestId, String failReason) {
        QueryDeviceInfoDetailResponseDTO response = new QueryDeviceInfoDetailResponseDTO();
        response.setRequestId(requestId);
        response.setResult(FAIL);
        response.setFailReason(failReason);
        return response;
    }
} 