package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 设备自定义信息实体类
 */
@Data
@TableName("device_custom_info")
public class DeviceCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID，工行控制器统一设定唯一标识
     */
    private Long deviceId;

    /**
     * 平台设备节点ID，关联平台设备
     */
    private String platformNodeId;

    /**
     * 设备所属站点ID
     */
    private Integer deviceSiteId;

    /**
     * 所属站点名称
     */
    private String deviceSite;

    /**
     * 设备角色（hub/spoke/agg）
     */
    private String deviceRole;

    /**
     * 是否为RR设备：false-否，true-是
     */
    private Boolean isRr;

    /**
     * 所属平面ID
     */
    private Integer devicePlaneId;

    /**
     * IPv6地址
     */
    private String deviceIpv6;

    /**
     * 设备组
     */
    private String deviceGroup;

    /**
     * 创建时间（首次上线时间）
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
} 