package com.h3c.dzkf.entity.result;

/**
 * Token验证结果封装类
 */
public class TokenValidationResult {
    private boolean valid;
    private String errorMessage;

    private TokenValidationResult(boolean valid, String errorMessage) {
        this.valid = valid;
        this.errorMessage = errorMessage;
    }

    public static TokenValidationResult success() {
        return new TokenValidationResult(true, null);
    }

    public static TokenValidationResult fail(String errorMessage) {
        return new TokenValidationResult(false, errorMessage);
    }

    public boolean isValid() {
        return valid;
    }

    public String getErrorMessage() {
        return errorMessage;
    }
} 