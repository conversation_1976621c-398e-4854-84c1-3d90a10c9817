package com.h3c.dzkf.entity.uclink;

import lombok.Data;

import java.util.List;

/**
 * 资源查询响应DTO
 */
@Data
public class ResourceQueryResponseDTO {
    
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 中文消息
     */
    private String messageZh;
    
    /**
     * 英文消息
     */
    private String messageEn;
    
    /**
     * 数据
     */
    private DataInfo data;
    
    @Data
    public static class DataInfo {
        /**
         * 起始位置
         */
        private Integer start;
        
        /**
         * 页面大小
         */
        private Integer size;
        
        /**
         * 总数
         */
        private Integer total;
        
        /**
         * 资源列表
         */
        private List<ResourceItem> data;
    }
    
    @Data
    public static class ResourceItem {
        /**
         * 资源ID
         */
        private Long id;
        
        /**
         * 资源名称
         */
        private String name;
        
        /**
         * IP地址
         */
        private String ip;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 图片URL
         */
        private String imgUrl;
        
        /**
         * 应用类型
         */
        private String appType;
        
        /**
         * 应用类型字符串
         */
        private String appTypeStr;
        
        /**
         * 二级分类
         */
        private String twoCategory;
        
        /**
         * 二级分类字符串
         */
        private String twoCategoryStr;
        
        /**
         * 操作系统CPU利用率
         */
        private String osCpuUtil;
        
        /**
         * 操作系统内存利用率
         */
        private String osMemUtil;
        
        /**
         * 一级分类
         */
        private String oneCategory;
        
        /**
         * 一级分类字符串
         */
        private String oneCategoryStr;
        
        /**
         * 状态
         */
        private Integer state;
        
        /**
         * 状态CP
         */
        private Integer stateCp;
        
        /**
         * 挂起状态
         */
        private Integer suspensionState;
        
        /**
         * 可用性
         */
        private Integer available;
        
        /**
         * 连通性
         */
        private Integer connectivity;
        
        /**
         * 健康度
         */
        private Integer health;
        
        /**
         * APM状态
         */
        private Integer apmStatus;
        
        /**
         * 配置轮询状态
         */
        private Integer configPollStatus;
        
        /**
         * 序列号
         */
        private String sn;
        
        /**
         * 厂商
         */
        private String vendor;
        
        /**
         * 系统OID
         */
        private String sysOid;
        
        /**
         * 型号
         */
        private String model;
        
        /**
         * 系列
         */
        private String series;
        
        /**
         * 是否堆叠
         */
        private Boolean isStack;
        
        /**
         * 更新时间
         */
        private Long updateTime;
        
        /**
         * 序列号
         */
        private String serialNumber;
        
        /**
         * 主机名
         */
        private String hostName;
    }
} 