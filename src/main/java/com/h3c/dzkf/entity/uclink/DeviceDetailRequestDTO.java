package com.h3c.dzkf.entity.uclink;

import lombok.Data;

/**
 * 网管设备详情接口请求DTO
 */
@Data
public class DeviceDetailRequestDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 15;
    
    /**
     * 设备IP
     */
    private String devIp;
    
    /**
     * 是否查询状态
     */
    private Boolean isQueryStatus = false;
    
    /**
     * 是否查询接口数量
     */
    private Boolean isQueryIfCount = false;
    
    /**
     * 是否查询扩展信息
     */
    private Boolean isQueryExt = false;
    
    public DeviceDetailRequestDTO(String devIp) {
        this.devIp = devIp;
    }
} 