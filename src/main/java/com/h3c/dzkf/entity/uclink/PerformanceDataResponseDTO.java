package com.h3c.dzkf.entity.uclink;

import lombok.Data;

import java.util.List;

/**
 * 性能数据响应DTO
 */
@Data
public class PerformanceDataResponseDTO {
    
    /**
     * 错误代码
     */
    private Integer errorCode;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 是否失败
     */
    private Boolean fail;
    
    /**
     * 数据
     */
    private List<PerfData> data;
    
    @Data
    public static class PerfData {
        /**
         * 任务ID
         */
        private Integer taskId;
        
        /**
         * 实例ID
         */
        private String instanceId;
        
        /**
         * 实例名称
         */
        private String instanceName;
        
        /**
         * 设备ID
         */
        private Long devId;
        
        /**
         * 实例数据
         */
        private String instanceData;
        
        /**
         * 实例数据单位
         */
        private String instanceDataUnit;
        
        /**
         * 采集时间
         */
        private Long collectTime;
    }
} 