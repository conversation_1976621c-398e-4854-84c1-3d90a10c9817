package com.h3c.dzkf.entity.uclink;

import lombok.Data;

/**
 * 新的设备详情响应DTO
 */
@Data
public class DeviceDetailNewResponseDTO {
    
    /**
     * 设备ID
     */
    private Long deviceId;
    
    /**
     * 设备IP
     */
    private String deviceIp;
    
    /**
     * 子网掩码
     */
    private String mask;
    
    /**
     * MAC地址
     */
    private String mac;
    
    /**
     * 设备名称
     */
    private String deviceName;
    
    /**
     * 序列号
     */
    private String serialNumber;
    
    /**
     * 是否堆叠
     */
    private Boolean isStack;
    
    /**
     * 最后轮询时间
     */
    private String latestPollTime;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 类型名称
     */
    private String typeName;
    
    /**
     * 挂起状态
     */
    private Integer suspensionState;
    
    /**
     * 状态轮询时间
     */
    private Integer statePollTime;
    
    /**
     * 配置轮询时间
     */
    private Integer configPollTime;
    
    /**
     * 系统名称
     */
    private String sysName;
    
    /**
     * 系统联系人
     */
    private String sysContact;
    
    /**
     * 位置
     */
    private String location;
    
    /**
     * 系统OID
     */
    private String sysOid;
    
    /**
     * 系统描述
     */
    private String sysDescription;
    
    /**
     * 系统版本
     */
    private String sysVersion;
    
    /**
     * 系统运行时间
     */
    private Long sysUptime;
    
    /**
     * 系统运行时间字符串
     */
    private String sysUptimeStr;
    
    /**
     * 区域ID
     */
    private Long regionId;
    
    /**
     * 区域IP
     */
    private String regionIp;
    
    /**
     * Ping状态
     */
    private Integer pingState;
    
    /**
     * 设备描述
     */
    private String devDescription;
} 