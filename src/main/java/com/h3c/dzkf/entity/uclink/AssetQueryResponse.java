package com.h3c.dzkf.entity.uclink;

import lombok.Data;

import java.util.List;

/**
 * 网管资产查询响应实体
 */
@Data
public class AssetQueryResponse {

    /**
     * 总记录数
     */
    private Integer rowCount;

    /**
     * 页面大小
     */
    private Integer size;

    /**
     * 起始位置
     */
    private Integer start;

    /**
     * 资产列表
     */
    private List<AssetItem> assets;

    /**
     * 消息
     */
    private String message;

    /**
     * 资产项实体
     */
    @Data
    public static class AssetItem {

        /**
         * 索引
         */
        private String index;

        /**
         * 描述
         */
        private String descr;

        /**
         * OID
         */
        private String oid;

        /**
         * 包含在
         */
        private String containedIn;

        /**
         * 类型
         */
        private String type;

        /**
         * 父级相对位置
         */
        private String parentRelPos;

        /**
         * 名称
         */
        private String name;

        /**
         * 硬件版本
         */
        private String hardwareRev;

        /**
         * 固件版本
         */
        private String firmwareRev;

        /**
         * 软件版本
         */
        private String softwareRev;

        /**
         * 序列号
         */
        private String serialNum;

        /**
         * 制造商名称
         */
        private String mfgName;

        /**
         * 型号名称
         */
        private String modelName;

        /**
         * 别名
         */
        private String alias;

        /**
         * 资产ID
         */
        private String assetID;

        /**
         * 是否为FRU
         */
        private String entPhysicalIsFRU;

        /**
         * 制造日期
         */
        private String mfgDate;

        /**
         * 物理URIs
         */
        private String entPhysicalUris;

        /**
         * 维护到期时间
         */
        private String maintenanceDueTime;

        /**
         * 部门
         */
        private String department;

        /**
         * 位置
         */
        private String location;

        /**
         * 维护人
         */
        private String maintainer;

        /**
         * 连接信息
         */
        private String connectInformation;

        /**
         * 产品号
         */
        private String productNumber;

        /**
         * 资产号
         */
        private String assetNumber;

        /**
         * 说明
         */
        private String explanation;

        /**
         * 使用时间
         */
        private String inUseTime;

        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 标签
         */
        private String label;

        /**
         * IP地址
         */
        private String ip;

        /**
         * 系统名称
         */
        private String sysName;

        /**
         * 设备类别名称
         */
        private String deviceCategoryName;

        /**
         * 板卡序列号
         */
        private String boardSerial;

        /**
         * 设备BOM
         */
        private String deviceBom;

        /**
         * 物理构建信息
         */
        private String physicalBuildInfo;

        /**
         * 物理标志
         */
        private String physicalFlag;
    }
} 