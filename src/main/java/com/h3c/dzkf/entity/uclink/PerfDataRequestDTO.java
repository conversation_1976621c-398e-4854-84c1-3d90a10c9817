package com.h3c.dzkf.entity.uclink;

import lombok.Data;

/**
 * 网管性能接口请求DTO
 */
@Data
public class PerfDataRequestDTO {

    /**
     * 任务ID列表，逗号分隔
     * cpu利用率：2，内存利用率：4，设备温度：49，电源：908，风扇：902
     */
    private String taskIds;

    /**
     * 时间类型
     */
    private Integer timeType = 1;

    /**
     * 设备ID列表，逗号分隔
     */
    private String devIds;

    public PerfDataRequestDTO(String taskIds, String devIds) {
        this.taskIds = taskIds;
        this.devIds = devIds;
    }
} 