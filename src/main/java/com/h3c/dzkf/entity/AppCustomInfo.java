package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 应用定制信息实体类
 */
@Data
@TableName("app_custom_info")
public class AppCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 应用类型，仅支持五元组
     */
    private String appType;

    /**
     * 网络应用DSCP
     */
    @TableField("internet_app_dscp")
    private String internetAppDSCP;

    /**
     * 协议类型（如TCP/UDP）
     */
    private String agreementType;

    /**
     * 源IP地址及掩码（格式：*******/32）
     */
    @TableField("source_ip")
    private String sourceIP;

    /**
     * 源端口(如80）
     */
    private String sourcePort;

    /**
     * 目的IP及端口列表（格式：ip&port，如["***********/32&443"]）存储JSON格式
     */
    @TableField("destination_ip_list")
    private String destinationIPList;

    /**
     * IPv6源IP地址及掩码
     */
    @TableField("source_ipv6")
    private String sourceIPv6;

    /**
     * IPv6源端口（非必须）
     */
    @TableField("source_port_ipv6")
    private String sourcePortIPv6;

    /**
     * IPv6目的IP及端口列表（非必须）存储JSON格式
     */
    @TableField("destination_ipv6_list")
    private String destinationIPv6List;

    /**
     * 所属应用分组
     */
    private String appGroupName;

    /**
     * 关联的应用组ID（app_group_custom_info表主键）
     */
    private Long appGroupId;

    /**
     * 关联的ACL模板ID列表，JSON格式存储，因为一个应用可能对应多个ACL模板
     */
    private String aclIdList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
} 