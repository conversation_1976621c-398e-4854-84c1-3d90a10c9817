package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 隧道模板定制信息实体类
 */
@Data
@TableName("tunnel_template_custom_info")
public class TeTunnelTemplateCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 隧道策略ID
     */
    private Long strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

    /**
     * SLA等级：EF、AF4、AF3、AF2、AF1、BE
     */
    private String slaId;

    /**
     * 保障带宽
     */
    private Long bandwidth;

    /**
     * 正向链路等级，优选链路等级
     */
    private String upAllowLinkPriority;

    /**
     * 反向链路等级，可选链路等级（JSON格式存储）
     */
    private String downAllowLinkPriority;

    /**
     * 丢包率
     */
    private String packetLossRate;

    /**
     * 延迟时间
     */
    private String delayTime;

    /**
     * 网络抖动
     */
    private String networkJitter;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    private Integer isDeleted;

} 