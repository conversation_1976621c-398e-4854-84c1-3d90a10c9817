package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * LAN口定制信息实体类
 */
@Data
@TableName("lan_custom_info")
public class LanCustomInfo {

    /**
     * LAN口ID，主键，自增
     */
    @TableId(type = IdType.AUTO)
    private Long lanId;

    /**
     * 设备ID，关联device_custom_info表的device_id
     */
    private Long deviceId;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 