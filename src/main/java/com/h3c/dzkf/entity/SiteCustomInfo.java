package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 站点信息实体类
 */
@Data
@TableName("site_custom_info")
public class SiteCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 站点ID，工行控制器统一设定唯一标识
     */
    private Integer siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 父级站点ID，可为空
     */
    private Integer parentSiteId;

    /**
     * 位置信息
     */
    private String location;

    /**
     * 站点类型
     */
    private String siteType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
} 