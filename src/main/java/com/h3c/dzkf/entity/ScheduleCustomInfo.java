package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 调度策略定制信息实体类
 */
@Data
@TableName("schedule_custom_info")
public class ScheduleCustomInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 调度策略唯一标识ID（工行控制器设定）
     */
    private Long appScheduleId;

    /**
     * 策略名称
     */
    private String appScheduleName;

    /**
     * 应用组名称
     */
    private String appGroupName;

    /**
     * 引流类型 0:五元组、1:dscp、4:Vpn
     */
    private Integer drainageType;

    /**
     * 隧道组ID列表（JSON格式存储）
     */
    private String networkIds;

    /**
     * VPN ID
     */
    private Integer vpnId;

    /**
     * 流分类ID
     */
    private Integer classifierId;

    /**
     * 流分类名称
     */
    private String classifierName;

    /**
     * 流行为ID
     */
    private Integer behaviorId;

    /**
     * 流行为名称
     */
    private String behaviorName;

    /**
     * 流策略信息（JSON格式存储，包含设备IP和对应的策略ID）
     */
    private String policyInfo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
}
