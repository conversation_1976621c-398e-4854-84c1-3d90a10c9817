package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * ServiceClass池管理实体类
 */
@Data
@TableName("service_class_pool")
public class ServiceClassPool {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * ServiceClass值，范围1-127
     */
    private Integer serviceClass;

    /**
     * 是否被使用：0-空闲，1-使用中
     */
    private Integer isUsed;

    /**
     * 使用该ServiceClass的隧道组ID
     */
    private Long teGroupId;

    /**
     * Policy方向：POSITIVE-正向，NEGATIVE-反向，BOTH-正反向共用
     */
    private String policyDirection;

    /**
     * 分配时间
     */
    private Date allocatedTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 