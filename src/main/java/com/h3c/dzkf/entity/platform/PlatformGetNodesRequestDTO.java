package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * ADWAN平台查询设备请求DTO
 */
@Data
public class PlatformGetNodesRequestDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 15;

    /**
     * 设备数据ID（单个查询）
     */
    private Long dataId;

    /**
     * 设备数据ID列表（批量查询）
     */
    private List<Long> dataIds;

    /**
     * 构造函数 - 单个设备查询
     *
     * @param dataId 设备数据ID
     */
    public PlatformGetNodesRequestDTO(Long dataId) {
        this.dataId = dataId;
    }

    /**
     * 构造函数 - 批量设备查询
     *
     * @param dataIds 设备数据ID列表
     */
    public PlatformGetNodesRequestDTO(List<Long> dataIds) {
        this.dataIds = dataIds;
        // 批量查询时增大页面大小，确保能返回所有数据
        this.pageSize = Math.max(dataIds.size(), 100);
    }

    /**
     * 默认构造函数
     */
    public PlatformGetNodesRequestDTO() {
    }
} 