package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询ACL模板详情请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformAclDetailRequestDTO {

    /**
     * ACL模板ID
     */
    private Integer aclId;

    /**
     * 构造函数
     */
    public PlatformAclDetailRequestDTO() {
    }

    /**
     * 构造函数 - 按ACL ID查询
     */
    public PlatformAclDetailRequestDTO(Integer aclId) {
        this.aclId = aclId;
    }
}
