package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询流行为详情请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosBehaviorDetailRequestDTO {

    /**
     * 流行为ID
     */
    private Integer behaviorId;

    /**
     * 构造函数
     */
    public PlatformQosBehaviorDetailRequestDTO() {
    }

    /**
     * 构造函数 - 按行为ID查询
     */
    public PlatformQosBehaviorDetailRequestDTO(Integer behaviorId) {
        this.behaviorId = behaviorId;
    }
}
