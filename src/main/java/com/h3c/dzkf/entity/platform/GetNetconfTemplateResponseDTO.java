package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 查询NETCONF模板响应DTO
 */
@Data
public class GetNetconfTemplateResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 分页结果VO
     */
    @Data
    public static class PageVO {
        /**
         * 当前页码
         */
        private Long pageNum;

        /**
         * 分页大小
         */
        private Long pageSize;

        /**
         * 总数据条数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 数据列表
         */
        private List<GetNetconfTmpVO> records;
    }

    /**
     * NETCONF模板VO
     */
    @Data
    public static class GetNetconfTmpVO {
        /**
         * Netconf模板ID
         */
        private Long dataId;

        /**
         * Netconf模板名称
         */
        private String name;

        /**
         * Netconf用户名
         */
        private String netconfUserName;

        /**
         * Netconf密码
         */
        private String netconfPassword;

        /**
         * Netconf SSH端口号
         */
        private Integer netconfSshPort;

        /**
         * Netconf HTTPS端口号
         */
        private Integer netconfHttpsPort;

        /**
         * Netconf模板描述信息
         */
        private String describe;
    }
} 