package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * ADWAN平台接口信息查询响应DTO
 */
@Data
public class PlatformInterfaceListResponseDTO {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应代码
     */
    private String code;
    
    /**
     * 查询结果
     */
    private List<NodeInterfaceData> result;
    
    /**
     * 是否成功
     */
    private Boolean successful;
    
    /**
     * 节点接口数据内嵌类
     */
    @Data
    public static class NodeInterfaceData {
        /**
         * 节点数据ID
         */
        private Long dataId;
        
        /**
         * 管理IP
         */
        private String manageIp;
        
        /**
         * 资源ID
         */
        private Long resourceId;
        
        /**
         * 接口列表
         */
        private List<InterfaceData> interfaceList;
    }
    
    /**
     * 接口数据内嵌类
     */
    @Data
    public static class InterfaceData {
        /**
         * 接口数据ID
         */
        private Long dataId;
        
        /**
         * 节点ID
         */
        private Long nodeId;
        
        /**
         * 接口类型
         */
        private Integer ifType;
        
        /**
         * 接口索引
         */
        private Integer ifIndex;
        
        /**
         * MAC地址
         */
        private String mac;
        
        /**
         * 接口名称
         */
        private String ifName;
        
        /**
         * 简称
         */
        private String abbreviatedName;
        
        /**
         * 接口编号
         */
        private String ifNumber;
        
        /**
         * 带宽(kbps)
         */
        private Long bandwidth;
        
        /**
         * 是否激活
         */
        private Integer activated;
        
        /**
         * 接口描述
         */
        private String description;
        
        /**
         * 接口模式
         */
        private Integer ifMode;
        
        /**
         * 接口状态
         */
        private Integer status;
        
        /**
         * 网络名称
         */
        private String networkName;
        
        /**
         * 网络类型
         */
        private String networkType;
        
        /**
         * 地址列表
         */
        private List<AddressData> addresses;
        
        /**
         * 子速率启用
         */
        private Boolean subRateEnable;
        
        /**
         * 板卡ID
         */
        private Long boardId;
        
        /**
         * 用户添加
         */
        private Boolean userAdd;
        
        /**
         * 管理状态
         */
        private Integer adminStatus;
    }
    
    /**
     * 地址数据内嵌类
     */
    @Data
    public static class AddressData {
        /**
         * 接口ID
         */
        private Long ifId;
        
        /**
         * 地址
         */
        private String address;
        
        /**
         * 掩码长度
         */
        private Integer maskLen;
        
        /**
         * 地址来源
         */
        private Integer addressOrigin;
        
        /**
         * IP类型（1:IPv4, 2:IPv6）
         */
        private Integer ipType;
    }
} 