package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 平台更新链路响应DTO (Result)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformUpdateLinkResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 返回结果
     */
    private UpdateLinkVo result;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 更新链路结果对象
     */
    @Data
    public static class UpdateLinkVo {
        
        /**
         * 链路ted信息下发是否成功
         */
        @JsonProperty("isSuccess")
        private Boolean isSuccess;

        /**
         * 编辑失败的链路输出原因信息
         */
        private String message;
    }
} 