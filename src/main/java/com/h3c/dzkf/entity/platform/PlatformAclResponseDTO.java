package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台ACL模板响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformAclResponseDTO {

    /**
     * 请求是否成功
     */
    private Boolean successful;

    /**
     * 返回码
     */
    private String returnCode;

    /**
     * 返回消息
     */
    private String returnMessage;

    /**
     * 结果对象
     */
    private AclResult result;

    /**
     * ACL创建结果
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AclResult {
        /**
         * ACL模板ID
         */
        private Integer aclId;

        /**
         * ACL名称
         */
        private String aclName;

        /**
         * 其他可能的返回字段
         */
        private Object data;
    }
} 