package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台链路总览查询响应DTO (Result)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformLinkOverViewResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private GetLinkOverViewVo result;

    /**
     * 链路总览信息结果类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GetLinkOverViewVo {
        /**
         * 总数据量
         */
        private Long totalSize;

        /**
         * 返回第一页的页下标
         */
        private Long pageNum;

        /**
         * 当前查询总页数
         */
        private Long totalPage;

        /**
         * 链路信息集合
         */
        private List<LinkOverViewInfo> linkOverViewInfoList;
    }

    /**
     * 链路总览信息类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class LinkOverViewInfo {
        /**
         * 链路ID
         */
        private String linkId;

        /**
         * 链路名称
         */
        private String linkName;

        /**
         * ISIS协议的路由协议实例ID
         */
        private Integer isisInstanceId;

        /**
         * 链路质量告警级别
         */
        private Integer linkQualityAlarmLevel;

        /**
         * 链路状态
         */
        private Integer linkStatus;

        /**
         * 源设备名称
         */
        private String srcNodeName;

        /**
         * 源设备接口名称
         */
        private String srcTpName;

        /**
         * 源设备接口地址
         */
        private String srcAddress;

        /**
         * 目的设备名称
         */
        private String destNodeName;

        /**
         * 目的设备接口名称
         */
        private String destTpName;

        /**
         * 目的设备接口地址
         */
        private String destAddress;

        /**
         * 丢包率
         */
        private Double packetLossRate;

        /**
         * 质量丢包采集时间
         */
        private String netQualityPacketLossTimeStamp;

        /**
         * 时延
         */
        private Double delay;

        /**
         * 抖动
         */
        private Long jitter;

        /**
         * 质量延迟抖动采集时间
         */
        private String netQualityJitterDelayTimeStamp;

        /**
         * 带宽
         */
        private Long bandwidth;

        /**
         * 带宽利用率
         */
        private Double bandwidthPercent;

        /**
         * 带宽采集时间
         */
        private String bandwidthTimeStamp;
    }
} 