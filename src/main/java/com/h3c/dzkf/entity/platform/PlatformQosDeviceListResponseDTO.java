package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询QoS设备列表响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosDeviceListResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 设备列表
         */
        private List<DeviceInfo> list;

        /**
         * 总行数
         */
        private Integer rowCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 起始位置
         */
        private Integer start;
    }

    /**
     * 设备信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DeviceInfo {
        /**
         * 设备ID
         */
        private Integer devId;

        /**
         * 设备UUID
         */
        private String devuuId;

        /**
         * 设备类型
         */
        private Integer devType;

        /**
         * 设备名称
         */
        private String devName;

        /**
         * 设备来源
         */
        private Integer devSource;

        /**
         * 设备IP
         */
        private String devIp;

        /**
         * 同步时间
         */
        private String syncTime;

        /**
         * 同步结果
         */
        private Integer synResult;

        /**
         * BGP编号
         */
        private String bgpNumber;

        /**
         * 设备型号
         */
        private String devModel;

        /**
         * 入方向策略ID
         */
        private Integer policyIdIn;

        /**
         * 出方向策略ID
         */
        private Integer policyIdOut;

        /**
         * 入方向策略名称
         */
        private String policyNameIn;

        /**
         * 出方向策略名称
         */
        private String policyNameOut;

        /**
         * RM UID
         */
        private String rmUID;

        /**
         * 同步原因
         */
        private String syncReason;

        /**
         * 租户ID
         */
        private String tenantId;
    }
}
