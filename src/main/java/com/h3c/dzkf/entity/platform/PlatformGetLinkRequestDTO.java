package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询链路请求DTO (GetLinkDto)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformGetLinkRequestDTO {

    /**
     * 链路ID（可选）
     */
    private Long linkId;

    /**
     * 链路网络类型(ipv4|ipv6)（可选）
     */
    private String linkNetworkType;

    /**
     * 源设备ID集合（可选）
     */
    private List<Long> srcNodeIdList;

    /**
     * 源tpId集合（可选）
     */
    private List<Long> srcTpIdList;

    /**
     * 目的设备Id集合（可选）
     */
    private List<Long> destNodeIdList;

    /**
     * 目的tpId集合（可选）
     */
    private List<Long> destTpIdList;

    /**
     * 链路名称（必选）
     */
    private String linkName;

    /**
     * 是否返回质量下发状态（可选）
     */
    private Boolean withNqaDistributeStatus;

    /**
     * 质量探测下发状态（可选）
     * 0 - 未配置, 1 - 下发中, 2 - 失败, 3 - 成功
     */
    private Integer nqaDistributeStatus;

    /**
     * 链路是否软删除（可选）
     */
    private Integer isDeleted;

    /**
     * 页码（可选）
     */
    private Long pageNum;

    /**
     * 单页条码数（可选）
     */
    private Long pageSize;

    /**
     * 排序字段名称（可选）
     */
    private String sortName;

    /**
     * 排序顺序（可选）
     * 1 - 升序, -1 - 降序
     */
    private Integer sortOrder;
} 