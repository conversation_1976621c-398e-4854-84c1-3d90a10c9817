package com.h3c.dzkf.entity.platform;

import lombok.Data;
import java.util.List;

/**
 * 查询ACL模板响应DTO
 */
@Data
public class QueryAclTemplateResponseDTO {
    
    /**
     * 输出结果
     */
    private Output output;
    
    /**
     * 输出结果内层结构
     */
    @Data
    public static class Output {
        /**
         * ACL模板列表
         */
        private List<AclTemplate> list;
        
        /**
         * 总记录数
         */
        private Integer rowCount;
        
        /**
         * 结果集记录数
         */
        private Integer size;
        
        /**
         * 查询初始索引
         */
        private Integer start;
    }
    
    /**
     * ACL模板信息
     */
    @Data
    public static class AclTemplate {
        
        /**
         * ACL模板id
         */
        private Integer aclId;
        
        /**
         * ACL名称
         */
        private String aclName;
        
        /**
         * ACL模板描述
         */
        private String description;
        
        /**
         * ACL类型，1：IPv4ACL 2：IPv6ACL
         */
        private Integer groupType;
        
        /**
         * 标识类型，1：名称标志 2：数字标识
         */
        private Integer idType;
        
        /**
         * ACL标识值
         */
        private String idValue;
        
        /**
         * 设备操作结果 0：未部署 1：部署成功 2：部署失败 3：部分成功 4：正在部署
         */
        private Integer devResult;
        
        /**
         * 接口操作结果 0：未部署 1：部署成功 2：部署失败 3：部分成功 4：正在部署
         */
        private Integer ifResult;
        
        /**
         * 匹配次序，1：配置 2：自动
         */
        private Integer matchOrder;
        
        /**
         * 步长，默认为5
         */
        private Integer step;
        
        /**
         * 匹配规则列表
         */
        private Object matchList;
    }

} 