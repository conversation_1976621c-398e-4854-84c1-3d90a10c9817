package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询设备接口请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformGetInterfacesRequestDTO {

    /**
     * 设备ID，nodeId和nodeIp二选一
     */
    private Long nodeId;

    /**
     * 设备管理IP地址，nodeId和nodeIp二选一
     */
    private String nodeIp;

    /**
     * 接口ID (可选)
     */
    private Long ifId;

    /**
     * 接口名称，模糊匹配，不区分大小写 (可选)
     */
    private String ifName;

    /**
     * 设备接口索引 (可选)
     */
    private Long ifIndex;

    /**
     * 接口接入的网络名称 (可选)
     */
    private String networkName;

    /**
     * 分页页码 (可选)
     */
    private Long pageNum;

    /**
     * 分页大小 (可选)
     */
    private Long pageSize;

    /**
     * 排序字段名称 (可选)
     * status：按接口状态排序
     * if_name：接口名称排序
     */
    private String sortName;

    /**
     * 排序顺序 (可选)
     * 1：升序
     * -1：降序
     */
    private Integer sortOrder;
} 