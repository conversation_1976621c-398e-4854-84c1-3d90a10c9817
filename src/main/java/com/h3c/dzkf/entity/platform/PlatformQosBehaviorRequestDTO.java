package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台新增流行为请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosBehaviorRequestDTO {

    /**
     * 流行为名称
     */
    private String behaviorName;

    /**
     * 标记列表
     */
    private List<RemarkInfo> remarkList;

    /**
     * 标记信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RemarkInfo {
        /**
         * 标记类型
         */
        private Integer remarkType;

        /**
         * 标记值
         */
        private Integer remarkValue;
    }
}
