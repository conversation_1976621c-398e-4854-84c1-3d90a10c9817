package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台硬件版本查询请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformInventoryInfoRequestDTO {

    /**
     * 设备ID（根据指定的设备ID查询设备的网络硬件信息）
     * 非必填
     */
    private String deviceIds;

    /**
     * 实体类型ID（根据指定的实体类型ID查询具体类型的网络硬件信息）
     * 非必填
     * 1：其它；2：未知；3：机框（单机设备）；4：背板；5：容器；6：电源；7：风扇；8：传感器；9：板卡（模块）；10：端口；11：堆叠；12：CPU
     * 多个设备实体类型以英文逗号","分隔
     */
    private String physicalClasses;

    /**
     * 请求者来源（组件名或者标识调用者信息的字符串）
     * 必填
     */
    private String requestFrom;

    /**
     * 分页查询的起始位置，默认从0开始。传-1时为查询全部
     * 必填，默认值为0
     */
    private Long start;

    /**
     * 分页查询的每页查询数量，最大值1000。传-1时为查询全部
     * 必填，默认值为200
     */
    private Long size;

    /**
     * 构造函数 - 根据设备ID查询
     *
     * @param deviceId 设备ID
     */
    public PlatformInventoryInfoRequestDTO(String deviceId) {
        this.deviceIds = deviceId;
        this.requestFrom = "FCAPS-Res_itom-res-rs";
        this.start = 0L;
        this.size = 1L;
    }

    /**
     * 默认构造函数
     */
    public PlatformInventoryInfoRequestDTO() {
        this.requestFrom = "FCAPS-Res_itom-res-rs";
        this.start = 0L;
        this.size = 200L;
    }
}
