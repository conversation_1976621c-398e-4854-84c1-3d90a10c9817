package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 平台API响应DTO
 */
@Data
public class PlatformAddResponseDTO {
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应代码
     */
    private String code;
    
    /**
     * 响应结果
     */
    private Result result;
    
    /**
     * 是否成功
     */
    private Boolean successful;
    
    @Data
    public static class Result {
        /**
         * 平台生成的设备ID
         */
        private Long dataId;
    }
} 