package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询流分类列表请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosClassifierListRequestDTO {

    /**
     * 流分类名称
     */
    private String classifierName;

    /**
     * 流分类描述
     */
    private String description;

    /**
     * 设备操作结果
     */
    private Integer devResultList;

    /**
     * 是否降序
     */
    private Boolean desc;

    /**
     * 每页起始位置
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 是否查询rule
     */
    private Boolean isWithRule;

    /**
     * 构造函数
     */
    public PlatformQosClassifierListRequestDTO() {
        this.desc = true;
        this.start = 0;
        this.size = 15;
    }

    /**
     * 构造函数 - 按名称查询
     */
    public PlatformQosClassifierListRequestDTO(String classifierName) {
        this();
        this.classifierName = classifierName;
    }
}
