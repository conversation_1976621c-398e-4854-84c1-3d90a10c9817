package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台新增流分类请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosClassifierRequestDTO {

    /**
     * 流分类名称
     */
    private String classifierName;

    /**
     * 流分类描述
     */
    private String description;

    /**
     * 逻辑关系，固定为1
     */
    private Integer logic;

    /**
     * 匹配规则列表
     */
    private List<MatchRule> matchList;

    /**
     * 匹配规则内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MatchRule {
        /**
         * 字符串值1
         */
        private String strValue1;

        /**
         * 整数值1
         */
        private Integer intValue1;

        /**
         * 整数值2
         */
        private Integer intValue2;

        /**
         * 索引号
         */
        private Integer indexNum;

        /**
         * 选择类型
         */
        private Integer selectType;

        /**
         * 匹配关系
         */
        private Integer matchRelation;
    }
}
