package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询流行为详情响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosBehaviorDetailResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 流行为ID
         */
        private Integer behaviorId;

        /**
         * 流行为名称
         */
        private String behaviorName;

        /**
         * 描述
         */
        private String description;

        /**
         * CAR配置
         */
        private CarConfig car;

        /**
         * GTS配置
         */
        private GtsConfig gts;

        /**
         * 队列配置
         */
        private QueueConfig queue;

        /**
         * 过滤器配置
         */
        private FilterConfig filter;

        /**
         * 账户配置
         */
        private AccountConfig account;

        /**
         * 重定向配置
         */
        private RedirectConfig redirect;

        /**
         * 镜像配置
         */
        private MirrorConfig mirror;

        /**
         * 策略配置
         */
        private PolicyConfig policy;

        /**
         * 标记列表
         */
        private List<RemarkInfo> remarkList;

        /**
         * 设备操作结果
         */
        private Integer devResult;
    }

    /**
     * CAR配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CarConfig {
        private Integer behaviorId;
        private Integer bandwidthUnit;
        private Integer carCir;
        private Integer carCbs;
        private Integer carEbs;
        private Integer carPir;
        private Integer carGreenAction;
        private Integer carGreenActionValue;
        private Integer carYellowAction;
        private Integer carYellowActionValue;
        private Integer carRedAction;
        private Integer carRedActionValue;
    }

    /**
     * GTS配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GtsConfig {
        private Integer behaviorId;
        private Integer bandwidthUnit;
        private Integer gtsCir;
        private Integer gtsCbs;
        private Integer gtsEbs;
        private Integer gtsPir;
        private Integer gtsQueueLength;
    }

    /**
     * 队列配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class QueueConfig {
        private Integer behaviorId;
        private Integer queueType;
        private Integer queueBandWidthUnit;
        private Integer queueBandWidthValue;
        private Integer queueCbs;
        private Integer queuePir;
        private Integer queuePct;
        private Integer remainPct;
        private Integer queueLength;
        private Integer queueCbsRatio;
        private Integer weight;
    }

    /**
     * 过滤器配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FilterConfig {
        private Integer behaviorId;
        private Integer filterAction;
    }

    /**
     * 账户配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AccountConfig {
        private Integer behaviorId;
        private Integer byptes;
        private Integer packets;
    }

    /**
     * 重定向配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RedirectConfig {
        private Integer behaviorId;
        private Integer redirectType;
        private String interfaceName;
        private Integer vlanId;
        private String vsiName;
        private String vrfName;
        private Integer ipAddressType;
        private String ipAddress1;
        private String ipAddress2;
        private String endPointAddress;
        private Integer color;
        private String publicSid;
        private String privateSid;
        private String accessVpn;
    }

    /**
     * 镜像配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MirrorConfig {
        private Integer behaviorId;
        private Integer mirrorType;
        private String interfaceName;
        private Integer vlanId;
    }

    /**
     * 策略配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PolicyConfig {
        private Integer behaviorId;
        private String policyName;
    }

    /**
     * 标记信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RemarkInfo {
        private Integer behaviorId;
        private Integer remarkType;
        private Integer remarkValue;
        private Integer remarkColor;
        private String remarkAddress;
        private String apnInstance;
    }
}
