package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * ADWAN平台更新设备请求DTO
 */
@Data
public class PlatformUpdateNodeRequestDTO {
    
    /**
     * 设备数据ID
     */
    private Long dataId;
    
    /**
     * 设备名称
     */
    private String nodeName;
    
    /**
     * 管理IP
     */
    private String manageIp;
    
    /**
     * 构造函数
     * @param dataId 设备数据ID
     * @param nodeName 设备名称
     * @param manageIp 管理IP
     */
    public PlatformUpdateNodeRequestDTO(Long dataId, String nodeName, String manageIp) {
        this.dataId = dataId;
        this.nodeName = nodeName;
        this.manageIp = manageIp;
    }
    
    /**
     * 默认构造函数
     */
    public PlatformUpdateNodeRequestDTO() {
    }
} 