package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 维护设备请求DTO
 */
@Data
public class MaintainNodeDTO {
    
    /**
     * 设备ID集合（必选）
     */
    private Long[] nodeIds;
    
    /**
     * 构造函数 - 单个设备维护
     * @param nodeId 设备节点ID
     */
    public MaintainNodeDTO(Long nodeId) {
        this.nodeIds = new Long[]{nodeId};
    }
    
    /**
     * 构造函数 - 批量设备维护
     * @param nodeIds 设备节点ID数组
     */
    public MaintainNodeDTO(Long[] nodeIds) {
        this.nodeIds = nodeIds;
    }
    
    /**
     * 默认构造函数
     */
    public MaintainNodeDTO() {
    }
} 