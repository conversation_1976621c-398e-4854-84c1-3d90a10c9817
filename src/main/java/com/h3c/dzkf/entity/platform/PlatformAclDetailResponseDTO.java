package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询ACL模板详情响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformAclDetailResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * ACL模板ID
         */
        private Integer aclId;

        /**
         * ACL模板名称
         */
        private String aclName;

        /**
         * 描述
         */
        private String description;

        /**
         * 组类型
         */
        private Integer groupType;

        /**
         * ID类型
         */
        private Integer idType;

        /**
         * ID值
         */
        private String idValue;

        /**
         * 设备操作结果
         */
        private Integer devResult;

        /**
         * 接口操作结果
         */
        private Integer ifResult;

        /**
         * 匹配顺序
         */
        private Integer matchOrder;

        /**
         * 步长
         */
        private Integer step;

        /**
         * 匹配列表
         */
        private List<Object> matchList;
    }
}
