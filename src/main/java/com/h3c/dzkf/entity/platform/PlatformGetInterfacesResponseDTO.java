package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询设备接口响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformGetInterfacesResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 分页对象
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PageVO {
        /**
         * 分页页码
         */
        private Long pageNum;

        /**
         * 每页展示的行数
         */
        private Long pageSize;

        /**
         * 总数据条数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 记录列表
         */
        private List<InterfaceVO> records;
    }

    /**
     * 接口信息对象
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class InterfaceVO {
        /**
         * 接口ID
         */
        private Long dataId;

        /**
         * 接口所属的设备ID
         */
        private Long nodeId;

        /**
         * 接口类型: 1-物理接口, 2-虚拟接口
         */
        private Integer ifType;

        /**
         * 接口索引
         */
        private Long ifIndex;

        /**
         * 接口MAC地址
         */
        private String mac;

        /**
         * 接口名称全称
         */
        private String ifName;

        /**
         * 接口名称缩写
         */
        private String abbreviatedName;

        /**
         * 接口号
         */
        private String ifNumber;

        /**
         * 接口带宽，kbps
         */
        private Long bandwidth;

        /**
         * 激活状态: 0-未激活, 1-已激活
         */
        private Integer activated;

        /**
         * 接口描述信息
         */
        private String description;

        /**
         * 接口工作模式: 0-未知, 2-二层交换模式, 3-三层路由模式
         */
        private Integer ifMode;

        /**
         * 接口状态: 1-UP, 2-DOWN
         */
        private Integer status;

        /**
         * 接口接入的网络名称
         */
        private String networkName;

        /**
         * 接口接入的网络类型: 201-LAN网络, 202-WAN网络
         */
        private Integer networkType;

        /**
         * 接口地址IP地址列表
         */
        private List<InterfaceAddressVO> addresses;

        /**
         * 使能子接口速率统计功能: 0-关闭, 1-开启
         */
        private Integer subRateEnable;

        /**
         * 接口所属的板卡ID
         */
        private Long boardId;

        /**
         * 是否用户手动添加: 0-控制器自动读取, 1-用户手动添加
         */
        private Integer userAdd;

        /**
         * 接口的admin状态: 1-up, 2-down
         */
        private Integer adminStatus;
    }

    /**
     * 接口地址对象
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class InterfaceAddressVO {
        /**
         * 接口ID
         */
        private Long ifId;

        /**
         * 接口IP地址，IPv4或IPv6类型
         */
        private String address;

        /**
         * 掩码长度
         */
        private Integer maskLen;

        /**
         * IP地址的配置方式
         */
        private Integer addressOrigin;

        /**
         * IP类型: 1-IPv4, 2-IPv6
         */
        private Integer ipType;
    }
} 