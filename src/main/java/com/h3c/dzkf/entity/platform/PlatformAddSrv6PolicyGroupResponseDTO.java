package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 平台新增SRv6 Policy应用组响应DTO
 */
@Data
public class PlatformAddSrv6PolicyGroupResponseDTO {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 响应数据（成功时通常为null）
     */
    private Object result;
    
    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }
} 