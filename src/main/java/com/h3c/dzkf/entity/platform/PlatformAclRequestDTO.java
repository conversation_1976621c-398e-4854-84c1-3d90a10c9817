package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台ACL模板请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformAclRequestDTO {

    /**
     * ACL模板id，PUT /acl、PUT /acl/all 接口该字段必填
     */
    private Integer aclId;

    /**
     * ACL名称
     */
    private String aclName;

    /**
     * ACL模板描述
     */
    private String description;

    /**
     * 设备操作结果 0：未部署 1：部署成功 2：部署失败 3：部分成功 4：正在部署
     */
    private Integer devResult;

    /**
     * ACL类型，1：IPv4ACL 2：IPv6ACL
     */
    private Integer groupType;

    /**
     * 标识类型，1：名称标志 2：数字标识
     */
    private Integer idType;

    /**
     * ACL标识值
     */
    private String idValue;

    /**
     * 接口操作结果 0：未部署 1：部署成功 2：部署失败 3：部分成功 4：正在部署
     */
    private Integer ifResult;

    /**
     * 匹配规则列表
     */
    private List<AclMatchRule> matchList;

    /**
     * 匹配次序，1：配置 2：自动
     */
    private Integer matchOrder;

    /**
     * 步长，默认为5
     */
    private Integer step;

    /**
     * ACL匹配规则
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AclMatchRule {
        /**
         * 动作 1：Deny、2：Permit
         */
        private Integer action;

        /**
         * 目的IP地址及掩码
         */
        private String destIpMask;

        /**
         * 当targeType值为5及介于时，此值为起始端口
         */
        private String destPort;

        /**
         * 1：小于，2：等于，3：大于，4：不等于，5：介于
         */
        private Integer destType;

        /**
         * 有效范围[0-63]
         */
        private Integer dscpvalue;

        /**
         * 目的对象组
         */
        private String dstObjectGroup;

        /**
         * 目的对象组ID
         */
        private Integer dstObjectGroupId;

        /**
         * 目的新对象组ID
         */
        private String dstObjectGroupNewId;

        /**
         * ICMPCode(0-255)
         */
        private Integer icmpCode;

        /**
         * ICMPType(0-255)
         */
        private Integer icmpType;

        /**
         * 1:子网掩码;2:通配符掩码
         */
        private Integer maskType;

        /**
         * 1：ICMP、2：IGMP、4：IPINIP、6：TCP、17：UDP、47：GRE、89：OSPF、256：IP协议；以及其他自定义协议取值0-255
         */
        private Integer protocol;

        /**
         * 规则号
         */
        private Integer ruleId;

        /**
         * 源IP地址
         */
        private String sourceIpMask;

        /**
         * 当targeType值为5及介于时，此值为起始端口
         */
        private String sourcePort;

        /**
         * 1：小于，2：等于，3：大于，4：不等于，5：介于
         */
        private Integer sourceType;

        /**
         * 源对象组
         */
        private String srcObjectGroup;

        /**
         * 源对象组ID
         */
        private Integer srcObjectGroupId;

        /**
         * 源新对象组ID
         */
        private String srcObjectGroupNewId;

        /**
         * 时间范围ID
         */
        private Integer trId;

        /**
         * 时间范围模板名称
         */
        private String trName;

        /**
         * VPN实例名称
         */
        private String vpnInstance;
    }
} 