package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 查询NETCONF模板请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetNetconfTemplateRequestDTO {

    /**
     * 模板ID列表 (可选)
     */
    private List<Long> netconfIds;

    /**
     * 模板名称 (可选) - 模糊匹配，不区分大小写
     */
    private String name;

    /**
     * 分页页码 (可选)
     */
    private Long pageNum;

    /**
     * 分页大小 (可选)
     */
    private Long pageSize;

    /**
     * 排序字段名称 (可选)
     * name：按模板名称排序
     */
    private String sortName;

    /**
     * 排序顺序 (可选)
     * 1：升序
     * -1：降序
     */
    private Integer sortOrder;

    /**
     * 构造函数 - 分页查询NETCONF模板以找到GLOBAL模板
     */
    public GetNetconfTemplateRequestDTO() {
        this.pageNum = 1L;
        this.pageSize = 15L;  // 使用默认分页大小
    }
} 