package com.h3c.dzkf.entity.platform;

import lombok.Data;
import java.util.List;

/**
 * 查询应用组信息响应DTO
 */
@Data
public class GetSrv6PolicyGroupResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }

    /**
     * 分页数据结构
     */
    @Data
    public static class PageVO {
        /**
         * 页码
         */
        private Long pageNum;

        /**
         * 每页展示的行数
         */
        private Long pageSize;

        /**
         * 总记录数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 记录列表
         */
        private List<VasSrv6PolicyGroupVO> records;
    }

    /**
     * 应用组信息
     */
    @Data
    public static class VasSrv6PolicyGroupVO {
        /**
         * 应用组ID
         */
        private Long dataId;

        /**
         * 应用组状态，可选值如下：
         * 0 - 正常状态
         * 1 - 正在删除
         */
        private Integer flowGroupStatus;

        /**
         * 应用组名称，0~255个字符
         */
        private String groupName;

        /**
         * 应用组描述，0~255个字符
         */
        private String description;

        /**
         * 组网模型：
         * 0 - Any-to-Any
         * 1 - Hub-Spoke
         */
        private Integer networkingModel;

        /**
         * 候选路径数：
         * 1 - 单路径
         * 2 - 主备路径
         */
        private Integer candipathNum;

        /**
         * 业务方向：
         * 1 - 单向
         * 2 - 双向
         */
        private Integer direction;

        /**
         * 使能共路
         */
        private Boolean coRouted;

        /**
         * Srv6 Policy部署方式（南向协议）
         * 1 - Netconf
         * 2 - BGP SR Policy
         */
        private Integer policyDeployMode;

        /**
         * 优选策略
         */
        private String calcLimit;

        /**
         * SRv6Policy业务总数
         */
        private Long totalNum;

        /**
         * 规划成功SRv6Policy业务总数
         */
        private Long planSuccess;

        /**
         * 规划失败SRv6Policy业务总数
         */
        private Long planFail;

        /**
         * 部署成功SRv6Policy业务总数
         */
        private Long deploySuccess;

        /**
         * 部署失败SRv6Policy业务总数
         */
        private Long deployFail;

        /**
         * 规划状态
         */
        private String planStatus;

        /**
         * 部署状态
         */
        private String deployStatus;
    }
} 