package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 平台删除操作结果
 */
@Data
public class PlatformDeleteResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * HTTP状态码
     */
    private int statusCode;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 响应体
     */
    private String responseBody;

    /**
     * 构造成功结果
     */
    public static PlatformDeleteResult success() {
        PlatformDeleteResult result = new PlatformDeleteResult();
        result.setSuccess(true);
        result.setStatusCode(200);
        return result;
    }

    /**
     * 构造失败结果
     */
    public static PlatformDeleteResult failure(int statusCode, String errorCode, String errorMessage, String responseBody) {
        PlatformDeleteResult result = new PlatformDeleteResult();
        result.setSuccess(false);
        result.setStatusCode(statusCode);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        result.setResponseBody(responseBody);
        return result;
    }

    /**
     * 构造失败结果（简化版）
     */
    public static PlatformDeleteResult failure(int statusCode, String responseBody) {
        PlatformDeleteResult result = new PlatformDeleteResult();
        result.setSuccess(false);
        result.setStatusCode(statusCode);
        result.setResponseBody(responseBody);
        return result;
    }
}
