package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台修改流策略请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyUpdateRequestDTO {

    /**
     * 流策略ID
     */
    private Integer policyId;

    /**
     * 流策略名称
     */
    private String policyName;

    /**
     * 流策略描述
     */
    private String description;

    /**
     * 分类行为对列表
     */
    private List<PlatformQosPolicyRequestDTO.CbPair> cbpairList;
}
