package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台部署流策略到设备接口请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyDeployRequestDTO {

    /**
     * 模板ID（流策略ID）
     */
    private Integer templateId;

    /**
     * 模板名称（流策略名称）
     */
    private String templateName;

    /**
     * 部署列表
     */
    private List<DeployInfo> list;

    /**
     * 部署信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DeployInfo {
        /**
         * 设备接口UUID
         */
        private String ifUuid;

        /**
         * 接口名称
         */
        private String interfaceName;

        /**
         * 接口描述
         */
        private String interfaceDescription;

        /**
         * 方向
         */
        private Integer direction;

        /**
         * 预序
         */
        private Integer preorder;

        /**
         * 共享模式
         */
        private Boolean shareMode;

        /**
         * 设备ID
         */
        private String devId;

        /**
         * 设备名称
         */
        private String devName;

        /**
         * 设备UUID
         */
        private String devUuid;
    }
}
