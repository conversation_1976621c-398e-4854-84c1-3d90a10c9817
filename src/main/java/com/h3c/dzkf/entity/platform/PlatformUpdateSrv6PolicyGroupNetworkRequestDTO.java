package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 平台更新SRv6 Policy应用组作用域请求DTO
 */
@Data
public class PlatformUpdateSrv6PolicyGroupNetworkRequestDTO {

    /**
     * 修改的时候需要，新增不需要
     */
    private Long dataId;

    /**
     * 应用组ID
     */
    private Long groupId;

    /**
     * 网络列表
     */
    private List<NetworkInfo> networkList;

    /**
     * 组网模型，固定3
     */
    private Integer networkingModel = 3;

    /**
     * 网络信息
     */
    @Data
    public static class NetworkInfo {
        /**
         * 节点类型，固定3
         */
        private Integer nodeType = 3;

        /**
         * 修改的时候需要，新增不需要
         */
        private String dataId;

        /**
         * 源节点列表 - Long类型
         */
        private List<Long> srcNodes;

        /**
         * 目的节点列表 - Long类型
         */
        private List<Long> dstNodes;
    }
} 