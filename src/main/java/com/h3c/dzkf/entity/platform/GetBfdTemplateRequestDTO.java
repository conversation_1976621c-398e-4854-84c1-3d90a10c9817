package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 查询BFD模板请求DTO
 */
@Data
public class GetBfdTemplateRequestDTO {

    /**
     * 主键
     */
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 页码
     */
    private Long pageNum;

    /**
     * 每页展示的行数
     */
    private Long pageSize;

    /**
     * 排序字段名称
     */
    private String sortName;

    /**
     * 排序顺序，可选值如下：
     * 1 - 升序
     * -1 - 降序
     */
    private Integer sortOrder;
} 