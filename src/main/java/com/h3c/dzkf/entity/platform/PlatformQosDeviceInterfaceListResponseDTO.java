package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询设备接口列表响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosDeviceInterfaceListResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 接口列表
         */
        private List<InterfaceInfo> list;

        /**
         * 总行数
         */
        private Integer rowCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 起始位置
         */
        private Integer start;
    }

    /**
     * 接口信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class InterfaceInfo {
        /**
         * 设备ID
         */
        private Integer devId;

        /**
         * 设备名称
         */
        private String devName;

        /**
         * 接口状态
         */
        private Integer ifStatus;

        /**
         * 接口名称
         */
        private String interfaceName;

        /**
         * 接口描述
         */
        private String interfaceDescription;

        /**
         * 接口型号
         */
        private String ifModel;

        /**
         * 带宽
         */
        private String bandWidth;

        /**
         * 物理编号
         */
        private String phyNum;

        /**
         * 接口IP地址
         */
        private String ifIpAddress;

        /**
         * 接口UUID ID
         */
        private String ifuuidId;

        /**
         * 设备UUID
         */
        private String devUuid;

        /**
         * 入方向模板名称
         */
        private String inTemplateName;

        /**
         * 出方向模板名称
         */
        private String outTemplateName;

        /**
         * 入方向灵活模板名称
         */
        private String inFlexTemplateName;

        /**
         * 出方向灵活模板名称
         */
        private String outFlexTemplateName;

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 无模板名称
         */
        private String noneTemplateName;

        /**
         * 接口配置ID
         */
        private String interfaceConfigId;

        /**
         * RM UID
         */
        private String rmUID;

        /**
         * 管理IP
         */
        private String manageIp;

        /**
         * 网络管理IP
         */
        private String networkMngIp;
    }
}
