package com.h3c.dzkf.entity.platform;

import lombok.Data;
import java.util.List;

/**
 * 查询BFD模板响应DTO
 */
@Data
public class GetBfdTemplateResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }

    /**
     * 分页数据结构
     */
    @Data
    public static class PageVO {
        /**
         * 页码
         */
        private Long pageNum;

        /**
         * 每页展示的行数
         */
        private Long pageSize;

        /**
         * 总记录数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 记录列表
         */
        private List<GetBfdTemplateVO> records;
    }

    /**
     * BFD模板信息
     */
    @Data
    public static class GetBfdTemplateVO {
        /**
         * 主键
         */
        private Long dataId;

        /**
         * 模板名称，1~63个字符
         */
        private String templateName;

        /**
         * 检测倍数，取值范围3~200
         */
        private Integer detectMultiplier;

        /**
         * 最小传输间隔，单位毫秒，取值范围3~10000
         */
        private Integer minTransmitInterval;

        /**
         * 最小接收间隔，单位毫秒，取值范围3~10000
         */
        private Integer minReceiveInterval;

        /**
         * 最小ECHO接收间隔，单位毫秒，取值范围0或3~10000
         */
        private Integer minEchoReceiveInterval;
    }
} 