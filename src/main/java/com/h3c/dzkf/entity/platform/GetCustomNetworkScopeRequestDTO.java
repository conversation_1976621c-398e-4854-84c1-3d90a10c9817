package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 查询作用域请求DTO
 */
@Data
public class GetCustomNetworkScopeRequestDTO {

    /**
     * 应用组ID
     */
    private Long groupId;

    /**
     * 页码
     */
    private Long pageNum;

    /**
     * 每页展示的行数
     */
    private Long pageSize;

    /**
     * 构造函数 - 设置默认分页参数
     */
    public GetCustomNetworkScopeRequestDTO() {
        this.pageNum = 1L;
        this.pageSize = -1L; // -1表示查询所有
    }

    /**
     * 构造函数 - 指定应用组ID
     */
    public GetCustomNetworkScopeRequestDTO(Long groupId) {
        this();
        this.groupId = groupId;
    }
}
