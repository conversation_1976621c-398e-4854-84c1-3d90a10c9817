package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询设备接口列表请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosDeviceInterfaceListRequestDTO {

    /**
     * 设备ID
     */
    private Integer devId;

    /**
     * 设备UUID
     */
    private String devuuId;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 是否降序
     */
    private Boolean desc;

    /**
     * 每页起始位置
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 构造函数
     */
    public PlatformQosDeviceInterfaceListRequestDTO() {
        this.desc = true;
        this.start = 0;
        this.size = 15;
    }

    /**
     * 构造函数 - 按设备UUID查询
     */
    public PlatformQosDeviceInterfaceListRequestDTO(String devuuId) {
        this();
        this.devuuId = devuuId;
    }

    /**
     * 构造函数 - 按设备UUID和接口名称查询
     */
    public PlatformQosDeviceInterfaceListRequestDTO(String devuuId, String interfaceName) {
        this();
        this.devuuId = devuuId;
        this.interfaceName = interfaceName;
    }
}
