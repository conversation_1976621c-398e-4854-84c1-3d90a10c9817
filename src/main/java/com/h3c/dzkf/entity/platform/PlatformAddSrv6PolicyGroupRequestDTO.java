package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 平台新增SRv6 Policy应用组请求DTO
 */
@Data
public class PlatformAddSrv6PolicyGroupRequestDTO {

    /**
     * 应用组名称，0~255个字符
     */
    private String groupName;

    /**
     * 应用组描述，0~255个字符
     */
    private String description;

    /**
     * 组网模型，可选值如下：
     * 0 - Any-to-Any
     * 1 - Hub-Spoke
     */
    private Integer networkingModel;

    /**
     * 候选路径数，可选值如下：
     * 1 - 单向(UNIDIRECTIONAL)
     * 2 - 双向(BIDIRECTIONAL)
     */
    private Integer candipathNum;

    /**
     * 业务方向，可选值如下：
     * 1 - 单向(SINGLE)
     * 2 - 多向(MULTIPLE)
     */
    private Integer direction;

    /**
     * 使能共路，默认false
     */
    private Boolean coRouted;

    /**
     * 部署方式
     */
    private String policyDeployMode;

    /**
     * 优选策略
     */
    private String calcLimit;

    /**
     * 当前用户token
     */
    private String token;

    /**
     * Policy参数信息
     */
    private List<AddSrv6PolicyGroupParamsDTO> srv6PolicyGroupParamsList;

    /**
     * Policy参数信息
     */
    @Data
    public static class AddSrv6PolicyGroupParamsDTO {
        private Long colorId;
        private Integer serviceClass;
        private Boolean positive;
        private Integer bandwidth;
        private Integer priority;
        private Long maxPacketLossRate;
        private Long maxJitter;
        private Long minDelay;
        private Long maxDelay;
        private Boolean softLocking;
        private Integer mtu;
        private Boolean statisticEnable;
        private Boolean hotStandbyEnable;
        private String bypassEnable;
        private Boolean gsrv6Enable;
        private Boolean currentBandwidthEnable;
        private Boolean bfdEnable;
        private String bfdType;
        private String bfdReverseType;
        private Long mainTemplateId;
        private Long backupTemplateId;
        private Integer trigPathEnable;
        private List<Srv6PolicyGroupCandiConstraintPathEntity> candiPathConstraints;
    }

    /**
     * 候选路径约束实体
     */
    @Data
    public static class Srv6PolicyGroupCandiConstraintPathEntity {
        private Long dataId;
        private Boolean positive;
        private Integer pathIndex;
        private Integer preference;
        private Integer affinityType;
        private Integer preferColor;
        private List<Integer> includeAffinityAny;
        private List<Integer> includeAffinityAll;
        private List<Integer> excludeAffinity;
        private Integer hopLimit;
        private Integer initSidPathNum;
        private Integer maxSidPathNum;
    }
} 