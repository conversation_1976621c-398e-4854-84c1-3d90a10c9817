package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 查询SNMP模板响应DTO
 */
@Data
public class GetSnmpTemplateResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 分页结果VO
     */
    @Data
    public static class PageVO {
        /**
         * 当前页码
         */
        private Long pageNum;

        /**
         * 分页大小
         */
        private Long pageSize;

        /**
         * 总数据条数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 数据列表
         */
        private List<GetSnmpTmpVO> records;
    }

    /**
     * SNMP模板VO
     */
    @Data
    public static class GetSnmpTmpVO {
        /**
         * SNMP模板ID
         */
        private Long dataId;

        /**
         * SNMP模板名称
         */
        private String name;

        /**
         * NETCONF协议类型
         */
        private String type;

        /**
         * 模板描述信息
         */
        private String describe;

        /**
         * 只读团体字
         */
        private String readOnly;

        /**
         * 读写团体字
         */
        private String readWrite;

        /**
         * SNMP 端口号，范围0~65535
         */
        private Integer port;

        /**
         * SNMPv3用户名，1~32个字符，区分大小写
         */
        private String userName;

        /**
         * SNMPv3认证密码，8~64个字符
         */
        private String authPassword;

        /**
         * SNMPv3加密密码，8~64个字符
         */
        private String encryptPassword;

        /**
         * SNMPv3上下文名称，0~64个字符，不能包含中文
         */
        private String contextName;
    }
} 