package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台新增链路响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformAddLinkResponseDTO {

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应码
     */
    private String code;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 响应结果
     */
    private PlatformAddLinkResult result;

    /**
     * 平台新增链路结果内部类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PlatformAddLinkResult {
        /**
         * 链路ID
         */
        private Long linkId;
    }
} 