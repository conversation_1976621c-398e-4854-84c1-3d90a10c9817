package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 查询应用组详情响应DTO
 */
@Data
public class GetSrv6PolicyGroupDetailResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private VasSrv6PolicyGroupDetailVO result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }

    /**
     * 应用组详情信息
     */
    @Data
    public static class VasSrv6PolicyGroupDetailVO {
        /**
         * 应用组ID
         */
        private Long dataId;

        /**
         * 应用组名称，0~255个字符
         */
        private String groupName;

        /**
         * 应用组描述，0~255个字符
         */
        private String description;

        /**
         * 组网模型：
         * 0 - Any-to-Any
         * 1 - Hub-Spoke
         */
        private Integer networkingModel;

        /**
         * 候选路径数：
         * 1 - 单路径
         * 2 - 主备路径
         */
        private Integer candipathNum;

        /**
         * 业务方向：
         * 1 - 单向
         * 2 - 双向
         */
        private Integer direction;

        /**
         * 使能共路
         */
        private Boolean coRouted;

        /**
         * Srv6 Policy部署方式（南向协议）
         * 1 - Netconf
         * 2 - BGP SR Policy
         */
        private Integer policyDeployMode;

        /**
         * 优选策略
         */
        private String calcLimit;

        private Boolean cbts;

        private Integer scheduleType;

        /**
         * 应用组策略信息
         */
        private List<VasSrv6PolicyGroupParamsVO> srv6PolicyGroupParamsList;
    }

    /**
     * 应用组策略参数信息
     */
    @Data
    public static class VasSrv6PolicyGroupParamsVO {
        /**
         * 主键
         */
        private Long dataId;

        /**
         * Color ID
         */
        private Long colorId;

        /**
         * Service Class
         */
        private Integer serviceClass;

        /**
         * 隧道的方向，true正向、false反向
         */
        private Boolean positive;

        /**
         * 带宽，单位kbps，范围10~2000000000之间的整数，默认10
         */
        private Integer bandwidth;

        /**
         * 优先级，取值范围0~7，值越大优先级越小
         */
        private Integer priority;

        /**
         * 最大丢包率，取值范围0~10000
         */
        private Long maxPacketLossRate;

        /**
         * 最大抖动，单位微秒，取值范围0~10000000
         */
        private Long maxJitter;

        /**
         * 最小时延，单位微秒，取值范围0~60000000
         */
        private Long minDelay;

        /**
         * 最大时延，单位微秒，取值范围0~60000000
         */
        private Long maxDelay;

        /**
         * MTU (Maximum Transmission Unit，最大传送单元)
         */
        private Integer mtu;

        /**
         * 使能流量统计
         */
        private Boolean statisticEnable;

        /**
         * 热备份
         */
        private Boolean hotStandbyEnable;

        /**
         * Bypass
         */
        private String bypassEnable;

        /**
         * gSrv6开关
         */
        private Boolean gsrv6Enable;

        private String gsrv6CompressMode;

        /**
         * 使能流量开关
         */
        private Boolean trafficAnalysisEnable;

        /**
         * 实时带宽选路
         */
        private Boolean currentBandwidthEnable;

        /**
         * 软锁定
         */
        private Boolean softLocking;

        /**
         * BFD检测
         */
        private Boolean bfdEnable;

        /**
         * BFD类型
         */
        private String bfdType;

        /**
         * BFD回程模式
         */
        private String bfdReverseType;

        /**
         * 主路径BFD模板
         */
        private Long mainTemplateId;

        /**
         * 主路径BFD模板名称
         */
        private String mainTemplateName;

        /**
         * 备路径BFD模板
         */
        private Long backupTemplateId;

        /**
         * 备路径BFD模板名称
         */
        private String backupTemplateName;

        /**
         * BFD联动路径切换
         */
        private Integer trigPathEnable;

        /**
         * 候选路径约束
         */
        private List<VasSrv6PolicyGroupCandidatePathVO> candiPathConstraints;
    }

    /**
     * 候选路径约束信息
     */
    @Data
    public static class VasSrv6PolicyGroupCandidatePathVO {
        /**
         * 候选路径主键
         */
        private Long dataId;

        private Boolean positive;

        /**
         * 候选路径索引，可选值如下：
         * 0 - 候选路径1
         * 1 - 候选路径2
         */
        private Integer pathIndex;

        /**
         * 候选路径优先级
         */
        private Integer preference;

        /**
         * 亲和属性着色类型，可选值如下：
         * 0 - TE亲和属性
         * 1 - Flex-Algo亲和属性
         */
        private Integer affinityType;

        /**
         * 优选链路着色
         */
        private Integer preferColor;

        /**
         * 亲和属性包含，链路着色bit位集合
         */
        private List<Integer> includeAffinityAny;

        /**
         * 亲和属性必选，链路着色bit位集合
         */
        private List<Integer> includeAffinityAll;

        /**
         * 亲和属性排除，链路着色bit位集合
         */
        private List<Integer> excludeAffinity;

        /**
         * 最大跳数，取值范围1~2147483647
         */
        private Integer hopLimit;

        private Integer sidPathMode;

        /**
         * 初始分段个数，取值范围1~128
         */
        private Integer initSidPathNum;

        /**
         * 最大分段个数，取值范围1~128
         */
        private Integer maxSidPathNum;
    }
}
