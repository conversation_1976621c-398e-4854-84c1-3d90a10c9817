package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询链路响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformGetLinkResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PageVO {
        /**
         * 页码
         */
        private Long pageNum;

        /**
         * 每页条数
         */
        private Long pageSize;

        /**
         * 总条数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 链路列表
         */
        private List<GetLinkVo> records;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GetLinkVo {
        /**
         * 链路ID
         */
        private Long linkId;

        /**
         * 链路名称
         */
        private String linkName;

        /**
         * 用户是否设置链路名称
         */
        private Integer linkNameUserSet;

        /**
         * 链路网络类型
         */
        private String linkNetworkType;

        /**
         * 源设备ID
         */
        private Long srcNodeId;

        /**
         * 源设备名称
         */
        private String srcNodeName;

        /**
         * 源接口ID
         */
        private Long srcTpId;

        /**
         * 源接口Index
         */
        private Long srcTpIndex;

        /**
         * 源接口名称
         */
        private String srcTpName;

        /**
         * 源接口IP
         */
        private String srcAddressStr;

        /**
         * 目的设备ID
         */
        private Long destNodeId;

        /**
         * 目的设备名称
         */
        private String destNodeName;

        /**
         * 目的接口ID
         */
        private Long destTpId;

        /**
         * 目的接口Index
         */
        private Long destTpIndex;

        /**
         * 目的接口名称
         */
        private String destTpName;

        /**
         * 目的接口IP
         */
        private String destAddressStr;

        /**
         * 链路带宽
         */
        private Long bandwidth;

        /**
         * 链路可分配带宽
         */
        private Long resvBandwidth;

        /**
         * 可分配带宽阈值
         */
        private Integer threshold;

        /**
         * 链路代价
         */
        private Long metric;

        /**
         * 链路类型
         */
        private Integer linkType;

        /**
         * 链路类型 int
         */
        private Integer linkTypeInt;

        /**
         * 链路状态 (0 - 离线, 1 - 在线)
         */
        private Integer linkStatus;

        /**
         * 链路可用度
         */
        private Integer available;

        /**
         * 可用度设置 (1 - 手动设置, 2 - 自动感知)
         */
        private Integer availableState;

        /**
         * 链路着色
         */
        private Long attributeFlags;

        /**
         * 链路是否软删除
         */
        private Integer isDeleted;

        /**
         * 链路是否在维护
         */
        private Integer maintainStatus;

        /**
         * 链路告警等级 (0 - 正常, 1 - 紧急告警, 2 - 重要告警, 3 - 次要告警, 4 - 警告告警, 5 - 未知)
         */
        private Integer linkQualityAlarmLevel;

        /**
         * 下一跳IP地址
         */
        private String nextHopAddress;

        /**
         * isis协议的路由协议实例ID
         */
        private Integer isisInstanceId;

        /**
         * 共享风险链路组Id
         */
        private String srlgIdList;

        /**
         * NQA/TWAMP下发状态 (0 - 未配置, 1 - 下发中, 2 - 失败, 3 - 成功)
         */
        private Integer nqaDistributeStatus;

        /**
         * 是不是NQA/TWAMP业务中的正向链路
         */
        private Boolean isForwardLink;

        /**
         * NQA/TWAMP下发失败的错误信息（简要）
         */
        private String nqaDistributeBriefMsg;

        /**
         * NQA/TWAMP下发失败的错误信息（详细）
         */
        private String nqaDistributeDetailedMsg;

        /**
         * 链路属性同步
         */
        private String linkAttrMode;

        /**
         * 链路bgp上报ID
         */
        private String bgpLinkId;

        /**
         * ipMtu值
         */
        private Integer ipMtu;

        /**
         * mtu值
         */
        private Integer mtu;
    }
} 