package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询QoS设备列表请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosDeviceListRequestDTO {

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备IP
     */
    private String devIp;

    /**
     * 是否降序
     */
    private Boolean desc;

    /**
     * 每页起始位置
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 优先队列ID
     */
    private Integer pqId;

    /**
     * 设备状态
     */
    private Integer devType;

    /**
     * 构造函数
     */
    public PlatformQosDeviceListRequestDTO() {
        this.desc = true;
        this.start = 0;
        this.size = 15;
    }

    /**
     * 构造函数 - 按IP查询
     */
    public PlatformQosDeviceListRequestDTO(String devIp) {
        this();
        this.devIp = devIp;
    }
}
