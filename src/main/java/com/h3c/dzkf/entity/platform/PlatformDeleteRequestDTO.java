package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * ADWAN平台删除设备请求DTO
 */
@Data
public class PlatformDeleteRequestDTO {
    
    /**
     * 要删除的设备节点ID列表
     */
    private List<Long> nodeIds;
    
    /**
     * 构造函数 - 单个设备删除
     * @param nodeId 设备节点ID
     */
    public PlatformDeleteRequestDTO(Long nodeId) {
        this.nodeIds = Collections.singletonList(nodeId);
    }
    
    /**
     * 构造函数 - 批量设备删除
     * @param nodeIds 设备节点ID列表
     */
    public PlatformDeleteRequestDTO(List<Long> nodeIds) {
        this.nodeIds = nodeIds;
    }
    
    /**
     * 默认构造函数
     */
    public PlatformDeleteRequestDTO() {
    }
} 