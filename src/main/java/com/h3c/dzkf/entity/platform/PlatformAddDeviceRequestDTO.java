package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * ADWAN平台设备请求DTO
 */
@Data
public class PlatformAddDeviceRequestDTO {
    
    /**
     * 设备名称，1~255个字符（必选）
     */
    private String nodeName;
    
    /**
     * 设备管理IP地址，支持IPv4和IPv6格式（必选）
     */
    private String manageIp;
    
    /**
     * 设备管理IP所在接口的MAC地址，不支持全0地址（可选）
     */
    private String manageMac;
    
    /**
     * 设备类型，默认真实设备（可选）
     * 1：真实设备
     * 2：虚拟设备
     */
    private Integer nodeType;
    
    /**
     * 设备厂商信息，默认Unknown（可选）
     * 0：H3C
     * 1：HP
     * 5：UNIS
     * 65535: Unknown
     */
    private Integer company;
    
    /**
     * 设备角色信息，默认无（可选）
     * 1：P
     * 2：PE
     * 10：ASBR_PE
     * 65535：无
     */
    private Integer nodeRole;
    
    /**
     * 设备序列号，1~31个字符，多个以;分割（可选）
     */
    private String serialNumbers;
    
    /**
     * 设备OID信息（可选）
     */
    private String sysObjectOid;
    
    /**
     * 设备版本号（可选）
     */
    private String softVersion;
    
    /**
     * 设备型号（可选）
     */
    private String nodeModel;
    
    /**
     * NETCONF模板ID（必选）
     */
    private Long netconfTemplateId;
    
    /**
     * SNMP模板ID（必选）
     */
    private Long snmpTemplateId;
    
    /**
     * 设备层级，默认无（可选）
     * 0：无
     * 1：一级
     * 2：二级
     * 3：三级
     */
    private Integer nodeLevel;
    
    /**
     * mpls as号，取值范围1~4294967295，ASBR_PE角色的设备才能设置AS号（可选）
     */
    private Long mplsAsNumber;
} 