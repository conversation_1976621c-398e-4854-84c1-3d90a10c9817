package com.h3c.dzkf.entity.platform;

import lombok.Data;
import java.util.List;

/**
 * 查询作用域响应DTO
 */
@Data
public class GetCustomNetworkScopeResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }

    /**
     * 分页数据结构
     */
    @Data
    public static class PageVO {
        /**
         * 页码
         */
        private Long pageNum;

        /**
         * 每页展示的行数
         */
        private Long pageSize;

        /**
         * 总记录数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 记录列表
         */
        private List<NetworkScopeVO> records;
    }

    /**
     * 网络作用域信息
     */
    @Data
    public static class NetworkScopeVO {
        /**
         * 作用域ID
         */
        private Long dataId;

        /**
         * 应用组ID
         */
        private Long groupId;

        /**
         * 节点类型
         */
        private Integer nodeType;

        /**
         * 源节点信息
         */
        private List<NetworkScopeSrcNodeVO> networkScopeSrcNodesVO;

        /**
         * 目的节点信息
         */
        private List<NetworkScopeDstNodeVO> networkScopeDstNodesVO;
    }

    /**
     * 源节点信息
     */
    @Data
    public static class NetworkScopeSrcNodeVO {
        /**
         * 源节点ID
         */
        private Long networkScopeSrcNode;

        /**
         * 源节点名称
         */
        private String srcName;

        /**
         * 源节点管理IP
         */
        private String srcManageIp;
    }

    /**
     * 目的节点信息
     */
    @Data
    public static class NetworkScopeDstNodeVO {
        /**
         * 目的节点ID
         */
        private Long networkScopeDstNode;

        /**
         * 目的节点名称
         */
        private String dstName;

        /**
         * 目的节点管理IP
         */
        private String dstManageIp;
    }
}
