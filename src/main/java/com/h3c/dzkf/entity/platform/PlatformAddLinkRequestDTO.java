package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台新增链路请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformAddLinkRequestDTO {

    /**
     * 链路着色 (可选)
     */
    private Long attributeFlags;

    /**
     * 可用度:范围0-100 (可选)
     */
    private Integer available;

    /**
     * 可用度设置: 1-手动设置, 2-自动感知 (可选)
     */
    private Integer availableState;

    /**
     * 目的地址 (可选)
     */
    private String destAddress;

    /**
     * 目的设备ID (必选)
     */
    private Long destNodeId;

    /**
     * 目的接口ID (必选)
     */
    private Long destTpId;

    /**
     * 链路名称 (可选)
     */
    private String linkName;

    /**
     * 用户是否设置链路名称: 0-自动生成, 1-手动设置 (必选)
     */
    private Integer linkNameUserSet;

    /**
     * 链路类型: 100-真实链路, 200-虚拟链路 (必选)
     */
    private Integer linkTypeInt;

    /**
     * 源地址 (可选)
     */
    private String srcAddress;

    /**
     * 源设备ID (必选)
     */
    private Long srcNodeId;

    /**
     * 源接口ID (可选)
     */
    private Long srcTpId;

    /**
     * 链路代价 (必选)
     */
    private Long metric;

    /**
     * 带宽
     */
    private Long bandwidth;

    /**
     * 可分配带宽 (必选)
     */
    private Long resvBandwidth;

    /**
     * 可分配带宽阈值 (可选)
     */
    private Integer threshold;

    /**
     * 链路属性同步: syn-同步, asyn-非同步 (必选)
     */
    private String linkAttrMode;

    /**
     * 下一跳IP地址 (可选)
     */
    private String nextHopAddress;

    /**
     * 共享风险链路组ID (可选)
     */
    private String srlgIdList;

    /**
     * 路由协议实例ID (可选)
     */
    private Integer isisInstanceId;

    /**
     * IP MTU值 (可选)
     */
    private Integer ipMtu;

    /**
     * MTU值 (可选)
     */
    private Integer mtu;
} 