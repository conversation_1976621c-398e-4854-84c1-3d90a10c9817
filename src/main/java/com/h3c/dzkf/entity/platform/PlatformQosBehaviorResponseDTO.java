package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台新增流行为响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosBehaviorResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 流行为ID
         */
        private Integer behaviorId;

        /**
         * 流行为名称
         */
        private String behaviorName;

        /**
         * 描述
         */
        private String description;

        /**
         * QoS账户
         */
        private Object qosAccount;

        /**
         * QoS CAR
         */
        private Object qosCar;

        /**
         * QoS过滤器
         */
        private Object qosFilter;

        /**
         * QoS GTS
         */
        private Object qosGts;

        /**
         * QoS镜像
         */
        private Object qosMirror;

        /**
         * QoS队列
         */
        private Object qosQueue;

        /**
         * QoS重定向
         */
        private Object qosRedirect;

        /**
         * QoS策略
         */
        private Object qosPolicy;

        /**
         * QoS标记
         */
        private Object qosRemarks;

        /**
         * 租户ID
         */
        private String tenantId;

        /**
         * 设备操作结果
         */
        private Integer devResult;

        /**
         * 是否正在部署
         */
        private Boolean deploying;
    }
}
