package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 查询应用组信息请求DTO
 */
@Data
public class GetSrv6PolicyGroupRequestDTO {

    /**
     * 应用组ID
     */
    private Long flowGroupId;

    /**
     * 应用组名称，0~255个字符
     */
    private String groupName;

    /**
     * 页码
     */
    private Long pageNum;

    /**
     * 每页展示的行数
     */
    private Long pageSize;

    /**
     * 排序字段名称
     */
    private String sortName;

    /**
     * 排序顺序，可选值如下：
     * 1 - 升序
     * -1 - 降序
     */
    private Integer sortOrder;
} 