package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询流策略部署历史请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyDeployHistoryRequestDTO {

    /**
     * 流策略的标识
     */
    private Integer policyId;

    /**
     * 流策略名称
     */
    private String policyName;

    /**
     * 是否降序
     */
    private Boolean desc;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 每页起始位置
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 接口名称
     */
    private String interfaceName;

    /**
     * 接口描述
     */
    private String interfaceDescription;

    /**
     * 部署关键字
     */
    private String deployKey;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 接口方向 0：入方向 1：出方向
     */
    private Integer direction;

    /**
     * 操作状态 1：部署成功，2：部署失败，3：部署中，4：拆除成功，5：拆除失败，6：正在拆除
     */
    private Integer statusList;

    /**
     * 开始时间 格式：yy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 格式：yy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * 部署对象
     */
    private Integer target;
}
