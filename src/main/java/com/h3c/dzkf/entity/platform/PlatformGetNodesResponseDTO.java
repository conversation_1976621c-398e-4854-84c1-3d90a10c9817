package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ADWAN平台查询设备响应DTO
 */
@Data
public class PlatformGetNodesResponseDTO {

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应代码
     */
    private String code;

    /**
     * 查询结果
     */
    private GetNodesResult result;

    /**
     * 是否成功
     */
    private Boolean successful;

    /**
     * 查询结果内部类
     */
    @Data
    public static class GetNodesResult {
        /**
         * 页码
         */
        private Integer pageNum;

        /**
         * 页大小
         */
        private Integer pageSize;

        /**
         * 总条数
         */
        private Integer totalItem;

        /**
         * 总页数
         */
        private Integer totalPage;

        /**
         * 设备记录列表
         */
        private List<NodeRecord> records;
    }

    /**
     * 设备记录内部类
     */
    @Data
    public static class NodeRecord {
        /**
         * 设备数据ID
         */
        private Long dataId;

        /**
         * 资源ID
         */
        private Long resourceId;

        /**
         * 本地设备ID
         */
        private String localDevId;

        /**
         * 设备名称
         */
        private String nodeName;

        /**
         * 设备名称是否为用户所设置
         * 0：否, 1：是
         */
        private Integer userSetName;

        /**
         * 设备小版本号
         */
        private String miniVersion;

        /**
         * 设备是否已经上过线
         * 0：添加设备后还没上过线, 1：添加设备后已经上过线
         */
        private Integer onlineBefore;

        /**
         * 管理IP
         */
        private String manageIp;

        /**
         * 管理IP列表
         */
        private List<String> manageIpList;

        /**
         * 管理MAC
         */
        private String manageMac;

        /**
         * 分类
         * 0：未知, 1：路由器, 2：交换机
         */
        private Integer category;

        /**
         * 设备层级
         * 0：无, 1：一级, 2：二级, 3：三级
         */
        private Integer nodeLevel;

        /**
         * 设备型号
         */
        private String nodeModel;

        /**
         * 厂商
         * 0：H3C, 1：HP, 5：UNIS, 65535: Unknown
         */
        private Integer company;

        /**
         * 设备角色
         * 1：P, 2：PE, 10：ASBR_PE, 65535：无
         */
        private Integer nodeRole;

        /**
         * 设备节点颜色
         * 1：绿色，设备在线无告警
         * 2：蓝色，设备在线有警告告警
         * 3：灰色，设备下线
         * 4：橙色，设备在线有次要告警
         * 5：红色，设备在线有重要告警
         */
        private Integer nodeDisplay;

        /**
         * 序列号列表
         */
        private List<String> serialNumbers;

        /**
         * 系统对象OID
         */
        private String sysObjectOid;

        /**
         * 软件版本
         */
        private String softVersion;

        /**
         * 城市名称
         */
        private String cityName;

        /**
         * 描述
         */
        private String description;

        /**
         * MPLS AS号
         */
        private Long mplsAsNumber;

        /**
         * NETCONF模板ID
         */
        private Long netconfTemplateId;

        /**
         * NETCONF模板名称
         */
        private String netconfTemplateName;

        /**
         * SNMP模板ID
         */
        private Long snmpTemplateId;

        /**
         * SNMP模板名称
         */
        private String snmpTemplateName;

        /**
         * 设备管理IP来源
         * 0：HAND，手动添加
         * 1：BGP-LS，BGP邻居自动发现
         */
        private List<Integer> nodeSource;

        /**
         * 设备来源
         * 0：HAND，手动添加
         * 1：BGP-LS，BGP邻居自动发现
         */
        private Integer comeFrom;

        /**
         * 设备下线原因
         * 1：Netconf通信失败
         * 2：没有可用的节点License
         * 3：序列号与管理IP不匹配
         * 6：设备无版本号
         * 7：设备厂商未知
         * 8：设备IP地址或MAC地址或序列号冲突
         * 9：配置恢复中
         * 65535：未知
         */
        private Integer inactiveReason;

        /**
         * NETCONF状态
         * 0：false，控制器与设备netconf通信失败
         * 1：true，控制器与设备netconf通信成功
         */
        private Integer netconfStatus;

        /**
         * 许可证状态
         * 0：false，申请网元license失败
         * 1：true，申请网元license成功
         */
        private Integer licenseStatus;

        /**
         * 设备状态
         * 0：未纳管设备，BGP-LS自动发现后尚未添加到控制器设备列表的设备状态
         * 1：已纳管设备，控制器正常管理的设备
         */
        private Integer isolationStatus;

        /**
         * 维护状态
         * 0：设备处于非维护状态
         * 1：设备处于维护状态
         * 4：设备处于维护删除状态
         */
        private Integer maintainStatus;

        /**
         * BGP对等状态
         * 0：不在线, 1：在线
         */
        private Integer bgpPeerStatus;

        /**
         * 区域
         */
        private String zone;

        /**
         * 连接状态
         * 0：3条连接都不在线
         * 1：仅订阅连接在线
         * 2：仅查询连接在线
         * 3：订阅和查询连接在线
         * 4：仅配置下发连接在线
         * 5：订阅连接和配置下发连接在线
         * 6：查询和配置下发连接在线
         * 7：3条连接都在线
         */
        private Integer connState;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
} 