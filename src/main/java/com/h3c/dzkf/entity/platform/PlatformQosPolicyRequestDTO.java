package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台新增流策略请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyRequestDTO {

    /**
     * 流策略名称
     */
    private String policyName;

    /**
     * 流策略描述
     */
    private String description;

    /**
     * 分类行为对列表
     */
    private List<CbPair> cbpairList;

    /**
     * 分类行为对内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CbPair {
        /**
         * 流分类ID
         */
        private Integer classifierId;

        /**
         * 流分类名称
         */
        private String classifierName;

        /**
         * 流行为ID
         */
        private Integer behaviorId;

        /**
         * 流行为名称
         */
        private String behaviorName;

        /**
         * 记录Id
         */
        private Integer pairId;
    }
}
