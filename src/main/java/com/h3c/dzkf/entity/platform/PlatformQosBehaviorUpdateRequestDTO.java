package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台修改流行为请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosBehaviorUpdateRequestDTO {

    /**
     * 流行为ID
     */
    private Integer behaviorId;

    /**
     * 流行为名称
     */
    private String behaviorName;

    /**
     * 流行为描述
     */
    private String description;

    /**
     * CAR配置
     */
    private CarConfig car;

    /**
     * GTS配置
     */
    private GtsConfig gts;

    /**
     * 队列配置
     */
    private QueueConfig queue;

    /**
     * 策略配置
     */
    private PolicyConfig policy;

    /**
     * 过滤器配置
     */
    private FilterConfig filter;

    /**
     * 账户配置
     */
    private AccountConfig account;

    /**
     * 重定向配置
     */
    private RedirectConfig redirect;

    /**
     * 镜像配置
     */
    private MirrorConfig mirror;

    /**
     * 标记列表
     */
    private List<RemarkInfo> remarkList;

    /**
     * CAR配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CarConfig {
        // CAR相关配置字段
    }

    /**
     * GTS配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GtsConfig {
        // GTS相关配置字段
    }

    /**
     * 队列配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class QueueConfig {
        private Integer behaviorId;
        private Integer queueType;
        private Integer queueBandWidthUnit;
        private Integer queueBandWidthValue;
        private Integer queueCbs;
        private Integer queuePir;
        private Integer queuePct;
        private Integer remainPct;
        private Integer queueCbsRatio;
        private Integer queueLength;
        private Integer weight;
    }

    /**
     * 策略配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PolicyConfig {
        private Integer behaviorId;
        private String policyName;
    }

    /**
     * 过滤器配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FilterConfig {
        // 过滤器相关配置字段
    }

    /**
     * 账户配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class AccountConfig {
        private Integer behaviorId;
        private Integer byptes;
        private Integer packets;
    }

    /**
     * 重定向配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RedirectConfig {
        private Integer behaviorId;
        private Integer redirectType;
        private String interfaceName;
        private Integer vlanId;
        private String vsiName;
        private String vrfName;
        private String ipAddress1;
        private String ipAddress2;
        private String endPointAddress;
        private Integer color;
        private String publicSid;
        private String privateSid;
        private String accessVpn;
    }

    /**
     * 镜像配置内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MirrorConfig {
        private Integer behaviorId;
        private Integer mirrorType;
        private String interfaceName;
        private Integer vlanId;
    }

    /**
     * 标记信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RemarkInfo {
        private Integer behaviorId;
        private Integer remarkType;
        private Integer remarkValue;
        private Integer remarkColor;
        private String remarkAddress;
        private String apnInstance;
    }
}
