package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 查询SRv6 Policy业务列表响应DTO
 */
@Data
public class GetSrv6PolicyTrailResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private PageVO result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }

    /**
     * 分页数据结构
     */
    @Data
    public static class PageVO {
        /**
         * 页码
         */
        private Long pageNum;

        /**
         * 每页展示的行数
         */
        private Long pageSize;

        /**
         * 总记录数
         */
        private Long totalItem;

        /**
         * 总页数
         */
        private Long totalPage;

        /**
         * 记录列表
         */
        private List<GetSrv6PolicyTrailVO> records;
    }

    /**
     * SRv6 Policy业务信息
     */
    @Data
    public static class GetSrv6PolicyTrailVO {
        /**
         * 主键
         */
        private Long dataId;

        /**
         * 应用组ID
         */
        private Long groupId;

        /**
         * 业务名称
         */
        private String trailName;

        /**
         * 描述信息，0~255个字符
         */
        private String description;

        /**
         * 源设备ID
         */
        private Long sourceNeId;

        /**
         * 源设备名称
         */
        private String sourceNeName;

        /**
         * 源设备IP
         */
        private String sourceNeIp;

        /**
         * 目的设备ID
         */
        private Long destinationNeId;

        /**
         * 目的设备名称
         */
        private String destinationNeName;

        /**
         * 目的设备IP
         */
        private String destinationNeIp;

        /**
         * 隧道业务方向，可选值如下：
         * UNIDIRECTIONAL - 单向
         * BIDIRECTIONAL - 双向
         */
        private String direction;

        /**
         * 使能共路，默认false
         */
        private Boolean coRouted;

        /**
         * 路径规划，可选值如下：
         * AUTO - 自动
         * MANUAL - 手动
         */
        private String pathPlanningMode;

        /**
         * 候选路径数，可选值如下：
         * NONE - 0
         * SINGLE - 1
         * MULTIPLE - 2
         */
        private String candipathNum;

        /**
         * 部署方式，可选值如下：
         * NETCONF - Netconf
         * BGP_SR_POLICY - BGP SR Policy
         */
        private String policyDeployMode;

        /**
         * 优选策略，可选值如下：
         * METRIC - Metric优先
         * JITTER - 抖动优先
         * DELAY - 时延优先
         * PACKAGELOSS - 丢包率优先
         * USABLE- 可用度优先
         * BANDWIDTH_BALANCING - 带宽均衡优先
         * BANDWIDTH_RATIO_BALANCING_PRIOR - 剩余带宽占比优先
         * NON - 无
         */
        private String calcLimit;

        /**
         * 实例状态，可选值如下：
         * TO_BE_DEPLOY - 待部署
         * DEPLOY_SUCCESS - 部署成功
         * DEPLOY_FAILED - 部署失败
         * DEPLOYING - 部署中
         * TO_BE_DELETE - 待删除
         * DELETE_FAILED - 删除失败
         * DELETING - 删除中
         * DELETE_SUCCESS - 删除成功
         */
        private String deployStatus;

        /**
         * 业务规划状态，可选值如下：
         * NONE - null
         * PRE_PLAN - 待规划
         * PLANNING - 规划中
         * PLAN_SUCCEEDED - 规划成功
         * PLAN_FAILED - 规划失败
         */
        private String planStatus;

        /**
         * 部署失败原因
         */
        private String failReason;

        /**
         * Policy参数信息
         */
        private List<GetSrv6PolicyParamsVO> srv6PolicyInfos;
    }

    /**
     * Policy参数信息
     */
    @Data
    public static class GetSrv6PolicyParamsVO {
        /**
         * 主键
         */
        private Long dataId;

        /**
         * 业务名称
         */
        private String srv6PolicyName;

        /**
         * 目的设备IP
         */
        private String endPointIp;

        /**
         * Color ID
         */
        private Long colorId;

        /**
         * 隧道的方向，true正向、false反向
         */
        private Boolean positive;

        /**
         * 带宽，单位kbps，范围10~2000000000之间的整数，默认10
         */
        private Integer bandwidth;

        /**
         * 优先级，取值范围0~7，值越大优先级越高
         */
        private Integer priority;

        /**
         * 软锁定，默认0，可选值如下：
         * 0 - 关闭
         * 1 - 开启
         */
        private Integer lockMode;

        /**
         * 运行状态，可选值如下：
         * 1 - Up
         * 2 - Down
         * 3 - AdminDown
         */
        private Integer operStatus;

        /**
         * 失败原因
         */
        private String failReason;
    }
}
