package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 查询SRv6 Policy业务部署详情响应DTO
 */
@Data
public class GetSrv6PolicyTrailDeployDetailResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private GetSrv6PolicyTrailDeployDetailVO result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }

    /**
     * 部署详情信息
     */
    @Data
    public static class GetSrv6PolicyTrailDeployDetailVO {
        /**
         * 正向policy主键
         */
        private Long forwardPolicyId;

        /**
         * 正向业务名称
         */
        private String forwardName;

        /**
         * 正向业务的源设备名称
         */
        private String forwardSourceNeName;

        /**
         * 正向业务的目的设备名称
         */
        private String forwardDestinationNeName;

        /**
         * 正向应用流量
         */
        private Long forwardTraffic;

        /**
         * 正向候选路径
         */
        private List<Srv6PolicyCandiDeployPathEntity> forwardCandiPathInfo;

        /**
         * 反向policy主键
         */
        private Long reversePolicyId;

        /**
         * 反向业务名称
         */
        private String reverseName;

        /**
         * 反向业务的源设备名称
         */
        private String reverseSourceNeName;

        /**
         * 反向业务的目的设备名称
         */
        private String reverseDestinationNeName;

        /**
         * 反向应用流量
         */
        private Long reverseTraffic;

        /**
         * 反向候选路径
         */
        private List<Srv6PolicyCandiDeployPathEntity> reverseCandiPathInfo;
    }

    /**
     * 候选路径部署信息
     */
    @Data
    public static class Srv6PolicyCandiDeployPathEntity {
        /**
         * 候选路径主键
         */
        private Long dataId;

        /**
         * srv6Policy表主键
         */
        private Long srv6PolicyId;

        /**
         * 候选路径索引，可选值如下：
         * 0 - 候选路径1
         * 1 - 候选路径2
         */
        private Integer pathIndex;

        /**
         * Color ID
         */
        private Long colorId;

        /**
         * CPath状态，可选值如下：
         * 0 - 无效
         * 1 - 有效
         */
        private Integer active;

        /**
         * 候选路径下分段路径的约束权重列表
         */
        private Integer[] weightList;

        /**
         * BSID
         */
        private String bindingSid;

        /**
         * 候选路径优先级
         */
        private Integer preference;

        /**
         * 亲和属性包含，链路着色bit位集合
         */
        private String includeAffinityAny;

        /**
         * 亲和属性排除，链路着色bit位集合
         */
        private String excludeAffinity;

        /**
         * 最大跳数，取值范围1~2147483647
         */
        private Integer hopLimit;

        /**
         * 添加分段路径方式，可选值如下：
         * MANUAL - 手动
         * AUTO - 自动
         */
        private String sidPathMode;

        /**
         * 初始分段个数，取值范围1~128
         */
        private Integer initSidPathNum;

        /**
         * 最大分段个数，取值范围1~128
         */
        private Integer maxSidPathNum;

        /**
         * SLA符合度，可选值如下：
         * 0 - 勉强
         * 1 - 严格
         * 2 - 不可用
         * 3 - 未选路
         */
        private Integer pathStatus;

        /**
         * 候选路径优选状态，可选值如下：
         * 0 - 不符合优选
         * 1 - 符合优选
         */
        private Integer preferColorStatus;

        /**
         * 实际部署路径
         */
        private List<Srv6PolicySidPathEntity> sidPathList;

        /**
         * 软锁定路径
         */
        private List<SoftLockPathEntity> softLockPathList;
    }

    /**
     * 分段路径信息
     */
    @Data
    public static class Srv6PolicySidPathEntity {
        /**
         * 主键
         */
        private Long dataId;

        /**
         * 分段路径名称
         */
        private String sidPathName;

        /**
         * 候选路径表主键
         */
        private Long candidatePathId;

        /**
         * 分段路径编号
         */
        private Integer sidPathIndex;

        /**
         * 候选路径下分段路径的权重
         */
        private Long weight;

        /**
         * 链路ID列表
         */
        private Long[] linkIdList;

        /**
         * 标签集合用"->"拼接
         */
        private String labelStr;

        /**
         * 路径详情列表
         */
        private List<Srv6PolicySidPathHopEntity> detailList;

        /**
         * 路径SLA符合度状态，可选值如下：
         * 0 - 勉强
         * 1 - 严格
         * 2 - 不可用
         * 3 - 未选路
         * 4 - 未知
         */
        private Integer pathStatus;

        /**
         * 非严格提示原因
         */
        private List<SlaPrompt> slaPrompts;

        /**
         * 时延
         */
        private Long delay;

        /**
         * 抖动
         */
        private Long jitter;

        /**
         * 丢包率
         */
        private Long packageLossRate;

        /**
         * 带宽
         */
        private Long bandwidth;

        /**
         * 设备上读取上来的路径状态：
         * 0-无效
         * 1-有效
         * null-未知
         */
        private Integer status;

        /**
         * 更新时间
         */
        private String updateTime;
    }

    /**
     * 路径跳点信息
     */
    @Data
    public static class Srv6PolicySidPathHopEntity {
        /**
         * 路径成员ID
         */
        private Long memberId;

        /**
         * 路径成员名称
         */
        private String memberName;

        /**
         * 路径成员类型，可选值如下：
         * NODE - 节点
         * LINK - 链路
         * ANYCAST - ANYCAST节点
         * BSID - 指定policy
         * SFC - 指定服务链节点
         * DT4 - DT4
         */
        private String memberType;

        /**
         * 顺序
         */
        private Integer memberIndex;
    }

    /**
     * SLA提示信息
     */
    @Data
    public static class SlaPrompt {
        /**
         * 非严格原因
         */
        private Integer noStrictReason;

        /**
         * 当前质量值
         */
        private String actQualityData;

        /**
         * SLA策略要求
         */
        private String slaControlNumber;

        /**
         * 路径非严格选路原因更新时间
         */
        private String updateTime;
    }

    /**
     * 软锁定路径信息
     */
    @Data
    public static class SoftLockPathEntity {
        /**
         * Srv6 Policy主键
         */
        private Long srv6PolicyId;

        /**
         * 候选路径表主键
         */
        private Long candidatePathId;

        /**
         * 候选路径编号
         */
        private Integer pathIndex;

        /**
         * 分段路径编号
         */
        private Integer sidPathIndex;

        /**
         * 候选路径下分段路径的权重
         */
        private Long weight;

        /**
         * 链路ID列表
         */
        private Long[] linkIdList;

        /**
         * 路径SLA符合度状态，可选值如下：
         * 0 - 勉强
         * 1 - 严格
         * 2 - 不可用
         * 3 - 未选路
         * 4 - 未知
         */
        private Integer pathStatus;

        /**
         * 更新时间
         */
        private String updateTime;
    }
}
