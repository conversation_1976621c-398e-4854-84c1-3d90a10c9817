package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 平台硬件版本查询响应DTO
 */
@Data
public class PlatformInventoryInfoResponseDTO {

    /**
     * 分页信息
     */
    private Paging paging;

    /**
     * 硬件信息数据列表
     */
    private List<InventoryData> data;

    /**
     * 分页信息
     */
    @Data
    public static class Paging {
        /**
         * 起始位置
         */
        private Integer start;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 总记录数
         */
        private Integer total;
    }

    /**
     * 硬件信息数据
     */
    @Data
    public static class InventoryData {
        /**
         * 设备ID
         */
        private Long deviceId;

        /**
         * 物理索引
         */
        private Integer physicalIndex;

        /**
         * 设备IP
         */
        private String deviceIp;

        /**
         * 设备名称
         */
        private String deviceName;

        /**
         * 资源区域ID
         */
        private Long resourceRegionId;

        /**
         * 区域ID
         */
        private Long regionId;

        /**
         * 区域IP
         */
        private String regionIp;

        /**
         * 物理描述
         */
        private String physicalDescr;

        /**
         * 接口索引
         */
        private Integer ifIndex;

        /**
         * 接口描述
         */
        private String ifDesc;

        /**
         * 物理OID
         */
        private String physicalOid;

        /**
         * 物理类型
         */
        private Integer physicalClass;

        /**
         * 物理名称
         */
        private String physicalName;

        /**
         * 物理硬件版本
         */
        private String physicalHardwareRev;

        /**
         * 物理固件版本
         */
        private String physicalFirmwareRev;

        /**
         * 物理软件版本
         */
        private String physicalSoftwareRev;

        /**
         * 物理序列号
         */
        private String physicalSerialNum;

        /**
         * 物理制造商名称
         */
        private String physicalMfgName;

        /**
         * 物理型号名称
         */
        private String physicalModelName;

        /**
         * 物理资产ID
         */
        private String physicalAssetId;

        /**
         * 物理是否为FRU
         */
        private Integer physicalIsFru;

        /**
         * 物理制造日期字符串
         */
        private String physicalManufacturingDateStr;

        /**
         * 物理CLEI代码
         */
        private String physicalCleiCode;

        /**
         * 物理构建信息
         */
        private String physicalBuildInfo;

        /**
         * 物理设备BOM
         */
        private String physicalDeviceBom;

        /**
         * 物理板卡序列号
         */
        private String physicalBoardSerialNumber;

        /**
         * 物理维护最大日期字符串
         */
        private String physicalMaintainMaxDateStr;

        /**
         * 产品号
         */
        private String productNumber;

        /**
         * 堆叠类型
         */
        private Integer stackType;

        /**
         * 堆叠成员ID
         */
        private Integer stackMemberId;

        /**
         * 堆叠成员名称
         */
        private String stackMemberName;

        /**
         * 堆叠成员角色
         */
        private String stackMemberRole;

        /**
         * 采集时间字符串
         */
        private String collectTimeStr;
    }
}
