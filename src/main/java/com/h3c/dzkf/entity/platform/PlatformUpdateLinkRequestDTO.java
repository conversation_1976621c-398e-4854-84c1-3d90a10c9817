package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台更新链路请求DTO (UpdateLinkDto)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformUpdateLinkRequestDTO {

    /**
     * 链路ID（必选）
     */
    private Long linkId;

    /**
     * 链路着色（可选）
     */
    private Long attributeFlags;

    /**
     * 链路可用度（可选）
     */
    private Integer available;

    /**
     * 可用度设置（可选）
     * 1 - 手动设置, 2 - 自动感知
     */
    private Integer availableState;

    /**
     * 目的设备ID（可选）
     */
    private Long destNodeId;

    /**
     * 目的接口ID（可选）
     */
    private Long destTpId;

    /**
     * 链路名称（可选）
     */
    private String linkName;

    /**
     * 用户是否设置链路名称（可选）
     */
    private Integer linkNameUserSet;

    /**
     * 源设备ID（可选）
     */
    private Long srcNodeId;

    /**
     * 源接口ID（可选）
     */
    private Long srcTpId;

    /**
     * 链路代价（可选）
     */
    private Long metric;

    /**
     * 链路带宽（可选）
     */
    private Long bandwidth;

    /**
     * 可分配带宽（可选）
     */
    private Long resvBandwidth;

    /**
     * 可分配带宽阈值（可选）
     */
    private Integer threshold;

    /**
     * 链路属性同步（可选）
     */
    private String linkAttrMode;

    /**
     * 下一跳IP地址（可选）
     */
    private String nextHopAddress;

    /**
     * 共享风险链路组Id（可选）
     */
    private String srlgIdList;

    /**
     * ip mtu值（可选）
     */
    private Integer ipMtu;

    /**
     * mtu值（可选）
     */
    private Integer mtu;
} 