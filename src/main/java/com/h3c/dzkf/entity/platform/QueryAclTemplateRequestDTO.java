package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 查询ACL模板请求DTO
 */
@Data
public class QueryAclTemplateRequestDTO {
    
    /**
     * 是否降序
     */
    private Boolean desc;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 每页起始位置
     */
    private Integer start;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * ACL模板名称
     */
    private String aclName;
    
    /**
     * ACL标识
     */
    private String idValue;
    
    /**
     * 标识类型
     */
    private Integer idType;
    
    /**
     * IP类型，1：IPv4ACL 2：IPv6ACL
     */
    private Integer groupType;
    
    /**
     * 模板描述
     */
    private String description;
    
    /**
     * 设备操作结果 0：未部署，1：部署成功，2：失败，3：部分失败，4：正在执行
     */
    private Integer devResultList;
    
    /**
     * 接口查询结果 0：未部署，1：部署成功，2：失败，3：部分失败，4：正在执行
     */
    private Integer ifResultList;
} 