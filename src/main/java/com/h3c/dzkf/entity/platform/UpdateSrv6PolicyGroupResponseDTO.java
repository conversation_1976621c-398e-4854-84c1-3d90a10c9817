package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 更新应用组响应DTO
 */
@Data
public class UpdateSrv6PolicyGroupResponseDTO {

    /**
     * 返回值状态码
     */
    private String code;

    /**
     * 返回值消息
     */
    private String message;

    /**
     * 请求结果
     */
    private Boolean successful;

    /**
     * 返回结果
     */
    private Object result;

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(code) && Boolean.TRUE.equals(successful);
    }
}
