package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台新增流策略响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 流策略ID
         */
        private Integer policyId;

        /**
         * 流策略名称
         */
        private String policyName;

        /**
         * 流策略描述
         */
        private String description;

        /**
         * QoS策略分类行为对列表
         */
        private List<QosPolicyCbpair> qosPolicyCbpairsList;

        /**
         * 租户ID
         */
        private String tenantId;

        /**
         * 设备操作结果
         */
        private Integer devResult;

        /**
         * 接口操作结果
         */
        private Integer ifResult;

        /**
         * SRv6操作结果
         */
        private Integer srv6Result;

        /**
         * 类型
         */
        private Integer type;
    }

    /**
     * QoS策略分类行为对内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class QosPolicyCbpair {
        /**
         * ID
         */
        private Integer id;

        /**
         * 策略ID
         */
        private Integer policyId;

        /**
         * 分类ID
         */
        private Integer classifierId;

        /**
         * 行为ID
         */
        private Integer behaviorId;

        /**
         * QoS策略模板
         */
        private Object qosPolicyTemplate;

        /**
         * QoS分类模板
         */
        private Object qosClassifierTemplate;

        /**
         * QoS行为模板
         */
        private Object qosBehaviorTemplate;
    }
}
