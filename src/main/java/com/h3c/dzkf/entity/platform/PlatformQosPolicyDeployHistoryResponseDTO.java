package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询流策略部署历史响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyDeployHistoryResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 部署类型
         */
        private Integer deployType;

        /**
         * 模板ID
         */
        private Integer templateId;

        /**
         * 模板名称
         */
        private String templateName;

        /**
         * 部署历史列表
         */
        private List<DeployHistoryInfo> list;

        /**
         * 总记录数
         */
        private Integer rowCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 起始位置
         */
        private Integer start;
    }

    /**
     * 部署历史信息
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DeployHistoryInfo {
        /**
         * 部署时间
         */
        private String deployTime;

        /**
         * 部署类型
         */
        private Integer deployType;

        /**
         * 设备ID
         */
        private Integer devId;

        /**
         * 设备IP
         */
        private String devIp;

        /**
         * 设备名称
         */
        private String devName;

        /**
         * 设备接口方向
         */
        private Integer direction;

        /**
         * 设备接口id
         */
        private String ifUuid;

        /**
         * 设备接口描述
         */
        private String interfaceDescription;

        /**
         * 设备接口名称
         */
        private String interfaceName;

        /**
         * 模板ID
         */
        private Integer policyId;

        /**
         * 模板名称
         */
        private String policyName;

        /**
         * preorder
         */
        private Integer preorder;

        /**
         * 失败原因
         */
        private String reason;

        /**
         * 失败原因（英文）
         */
        private String reasonEn;

        /**
         * 部署结果
         */
        private Integer result;

        /**
         * rmUID
         */
        private String rmUID;

        /**
         * shareMode
         */
        private Boolean shareMode;
    }
}
