package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询流策略列表响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyListResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 流策略列表
         */
        private List<PolicyInfo> list;

        /**
         * 总行数
         */
        private Integer rowCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 起始位置
         */
        private Integer start;
    }

    /**
     * 流策略信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PolicyInfo {
        /**
         * 流策略ID
         */
        private Integer policyId;

        /**
         * 流策略名称
         */
        private String policyName;

        /**
         * 流策略描述
         */
        private String description;

        /**
         * 分类行为对列表
         */
        private List<CbPair> cbpairList;

        /**
         * 设备操作结果
         */
        private Integer devResult;

        /**
         * 接口操作结果
         */
        private Integer ifResult;

        /**
         * SRv6操作结果
         */
        private Integer srv6Result;

        /**
         * 类型
         */
        private Integer type;

        /**
         * 关联应用
         */
        private Object relationApp;
    }

    /**
     * 分类行为对内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CbPair {
        /**
         * 流行为Id
         */
        private Integer behaviorId;

        /**
         * 流行为名称
         */
        private String behaviorName;

        /**
         * 流分类Id
         */
        private Integer classifierId;

        /**
         * 流分类名称
         */
        private String classifierName;

        /**
         * 记录Id
         */
        private Integer pairId;
    }
}
