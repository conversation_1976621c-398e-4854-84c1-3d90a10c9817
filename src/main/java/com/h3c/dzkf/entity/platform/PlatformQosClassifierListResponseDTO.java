package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询流分类列表响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosClassifierListResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    /**
     * 输出结果内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 流分类列表
         */
        private List<ClassifierInfo> list;

        /**
         * 总行数
         */
        private Integer rowCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 起始位置
         */
        private Integer start;
    }

    /**
     * 流分类信息内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ClassifierInfo {
        /**
         * 流分类ID
         */
        private Integer classifierId;

        /**
         * 流分类名称
         */
        private String classifierName;

        /**
         * 流分类描述
         */
        private String description;

        /**
         * 逻辑关系
         */
        private Integer logic;

        /**
         * 匹配规则列表
         */
        private List<MatchRule> matchList;

        /**
         * 设备操作结果
         */
        private Integer devResult;
    }

    /**
     * 匹配规则内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MatchRule {
        /**
         * 字符串值1
         */
        private String strValue1;

        /**
         * 整数值1
         */
        private Integer intValue1;

        /**
         * 整数值2
         */
        private Integer intValue2;

        /**
         * 索引号
         */
        private Integer indexNum;

        /**
         * 选择类型
         */
        private Integer selectType;

        /**
         * 匹配关系
         */
        private Integer matchRelation;
    }
}
