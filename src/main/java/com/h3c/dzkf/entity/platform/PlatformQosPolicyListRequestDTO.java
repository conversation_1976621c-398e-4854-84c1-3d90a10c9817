package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询流策略列表请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyListRequestDTO {

    /**
     * 流策略名称
     */
    private String policyName;

    /**
     * 流策略描述
     */
    private String description;

    /**
     * 模板类型
     */
    private Integer type;

    /**
     * 设备操作结果
     */
    private Integer devResultList;

    /**
     * 接口操作结果
     */
    private Integer ifResultList;

    /**
     * SRv6操作结果
     */
    private Integer srv6ResultList;

    /**
     * 是否降序
     */
    private Boolean desc;

    /**
     * 每页起始位置
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 构造函数
     */
    public PlatformQosPolicyListRequestDTO() {
        this.desc = false;
        this.start = 0;
        this.size = 15;
    }

    /**
     * 构造函数 - 按名称查询
     */
    public PlatformQosPolicyListRequestDTO(String policyName) {
        this();
        this.policyName = policyName;
    }
}
