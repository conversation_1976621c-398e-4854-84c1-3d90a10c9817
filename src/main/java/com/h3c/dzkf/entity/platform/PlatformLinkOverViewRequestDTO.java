package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台链路总览查询请求DTO (GetLinkOverViewDto)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformLinkOverViewRequestDTO {

    /**
     * 历史时间实体类（必选）
     */
    private HistoryTimeEntity historyTimeEntity;

    /**
     * 开始页（必选）
     */
    private Integer pageNo;

    /**
     * 页大小（必选）
     */
    private Integer pageSize;

    /**
     * 链路名称（可选）
     */
    private String linkName;

    /**
     * 源设备名称（可选）
     */
    private List<Long> srcDeviceName;

    /**
     * 目的设备名称（可选）
     */
    private List<Long> dstDeviceName;

    /**
     * 源设备接口名称（可选）
     */
    private List<Long> srcTpName;

    /**
     * 源设备接口地址（可选）
     */
    private List<String> srcAddress;

    /**
     * 目的设备接口名称（可选）
     */
    private List<Long> dstTpName;

    /**
     * 目的设备接口地址（可选）
     */
    private List<String> dstAddress;

    /**
     * 比较丢包率大小信息（可选）
     */
    private SearchEntity searchPlRatio;

    /**
     * 比较时延大小信息（可选）
     */
    private SearchEntity searchDelay;

    /**
     * 比较抖动大小信息（可选）
     */
    private SearchEntity searchJitter;

    /**
     * 比较带宽大小信息（可选）
     */
    private SearchEntity searchBd;

    /**
     * 比较带宽利用率大小信息（可选）
     */
    private SearchEntity searchBdPercent;

    /**
     * 排序名称（可选）
     */
    private String sortName;

    /**
     * 正序/倒序（可选）
     * 1 - 正序, -1 - 逆序
     */
    private Integer sortOrder;

    /**
     * 历史时间实体类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class HistoryTimeEntity {
        /**
         * 历史时间选择类型（必选）
         * REALTIME - 实时数据
         * LAST_ONE_HOUR - 最近一小时
         * LAST_THREE_HOUR - 最近三小时
         * LAST_SIX_HOUR - 最近六小时
         * LAST_TWELVE_HOUR - 最近十二小时
         * LAST_ONE_DAY - 最近一天
         * LAST_ONE_WEEK - 最近一周
         * LAST_ONE_MONTH - 最近一个月
         * SELF_DEFINE - 自定义
         */
        private String historyTimeInterval;

        /**
         * 开始时间（可选）
         * 格式："年-月-日 小时-分钟-秒 时区"，如2016-04-19 16:10:00 GMT+8
         */
        private String startTime;

        /**
         * 结束时间（可选）
         * 格式："年-月-日 小时-分钟-秒 时区"，如2016-04-19 16:10:00 GMT+8
         */
        private String endTime;
    }

    /**
     * 搜索实体类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SearchEntity {
        /**
         * 操作类型（必选）
         * GT - >, GTE - >=, LT - <, LTE - <=, EQ - =, NE - !=
         */
        private String operator;

        /**
         * 比较大小的值（必选）
         */
        private String value;

        /**
         * 比较大小的字段名称（必选）
         */
        private String searchName;
    }

    /**
     * 构造方法
     */
    public PlatformLinkOverViewRequestDTO() {
    }

    /**
     * 构造方法 - 创建实时数据查询请求
     * @param pageNo 页码
     * @param pageSize 页大小
     */
    public static PlatformLinkOverViewRequestDTO createRealtimeRequest(Integer pageNo, Integer pageSize) {
        PlatformLinkOverViewRequestDTO request = new PlatformLinkOverViewRequestDTO();
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        
        HistoryTimeEntity historyTimeEntity = new HistoryTimeEntity();
        historyTimeEntity.setHistoryTimeInterval("REALTIME");
        request.setHistoryTimeEntity(historyTimeEntity);
        
        return request;
    }

    /**
     * 构造方法 - 创建按链路名称查询的请求
     * @param linkNames 链路名称列表（用逗号分隔后作为linkName传递）
     * @param pageNo 页码
     * @param pageSize 页大小
     */
    public static PlatformLinkOverViewRequestDTO createByLinkNames(List<String> linkNames, Integer pageNo, Integer pageSize) {
        PlatformLinkOverViewRequestDTO request = createRealtimeRequest(pageNo, pageSize);
        
        if (linkNames != null && !linkNames.isEmpty()) {
            // 可以根据实际需要选择合适的查询方式
            // 这里假设一次查询一个链路名称，如果需要批量查询可能需要调整
            request.setLinkName(linkNames.get(0));
        }
        
        return request;
    }
} 