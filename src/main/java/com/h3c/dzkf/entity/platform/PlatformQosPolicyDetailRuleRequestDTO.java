package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 平台查询流策略CB对请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyDetailRuleRequestDTO {

    /**
     * 流策略ID
     */
    private Integer policyId;

    /**
     * 每页起始位置
     */
    private Integer start;

    /**
     * 每页大小
     */
    private Integer size;

    /**
     * 构造函数
     */
    public PlatformQosPolicyDetailRuleRequestDTO() {
        this.start = 0;
        this.size = 100; // 设置较大的默认值，确保能获取所有CB对
    }

    /**
     * 构造函数 - 按策略ID查询
     */
    public PlatformQosPolicyDetailRuleRequestDTO(Integer policyId) {
        this();
        this.policyId = policyId;
    }
}
