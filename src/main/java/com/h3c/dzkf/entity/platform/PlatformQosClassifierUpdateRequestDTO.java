package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台修改流分类请求DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosClassifierUpdateRequestDTO {

    /**
     * 流分类ID
     */
    private Integer classifierId;

    /**
     * 流分类名称
     */
    private String classifierName;

    /**
     * 流分类描述
     */
    private String description;

    /**
     * 逻辑关系，固定为1
     */
    private Integer logic;

    /**
     * 匹配规则列表
     */
    private List<MatchRule> matchList;

    /**
     * 匹配规则内嵌类
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MatchRule {
        /**
         * 匹配规则ID
         */
        private Integer id;

        /**
         * 匹配规则ID（可为空）
         */
        private Integer matchRuleId;

        /**
         * 选择类型
         */
        private Integer selectType;

        /**
         * 匹配关系
         */
        private Integer matchRelation;

        /**
         * 整数值1
         */
        private Integer intValue1;

        /**
         * 整数值2
         */
        private Integer intValue2;

        /**
         * 字符串值1
         */
        private String strValue1;

        /**
         * 字符串值2
         */
        private String strValue2;

        /**
         * SDWAN TTE对象
         */
        private Object sdwanTteObj;

        /**
         * 应用源
         */
        private Object applicationSource;

        /**
         * 索引号
         */
        private Integer indexNum;
    }
}
