package com.h3c.dzkf.entity.platform;

import lombok.Data;

/**
 * 查询SRv6 Policy业务列表请求DTO
 */
@Data
public class GetSrv6PolicyTrailRequestDTO {

    /**
     * 主键
     */
    private Long dataId;

    /**
     * 应用组ID
     */
    private Long groupId;

    /**
     * 业务名称
     */
    private String trailName;

    /**
     * 源设备ID
     */
    private String srcNodeId;

    /**
     * 目的设备ID
     */
    private String desNodeId;

    /**
     * 隧道业务方向，可选值如下：
     * UNIDIRECTIONAL - 单向
     * BIDIRECTIONAL - 双向
     */
    private String direction;

    /**
     * 部署状态，可选值如下：
     * TO_BE_DEPLOY - 待部署
     * DEPLOY_SUCCESS - 部署成功
     * DEPLOY_FAILED - 部署失败
     * DEPLOYING - 部署中
     * TO_BE_DELETE - 待删除
     * DELETE_FAILED - 删除失败
     * DELETING - 删除中
     * DELETE_SUCCESS - 删除成功
     */
    private String deployStatus;

    /**
     * 运行状态，可选值如下：
     * 1 - Up
     * 2 - Down
     * 3 - AdminDown
     */
    private Integer operStatus;

    /**
     * 页码
     */
    private Long pageNum;

    /**
     * 每页展示的行数
     */
    private Long pageSize;

    /**
     * 排序字段名称
     */
    private String sortName;

    /**
     * 排序顺序，可选值如下：
     * 1 - 升序
     * -1 - 降序
     */
    private Integer sortOrder;
}
