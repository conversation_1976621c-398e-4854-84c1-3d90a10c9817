package com.h3c.dzkf.entity.platform;

import lombok.Data;

import java.util.List;

/**
 * 更新应用组请求DTO
 */
@Data
public class UpdateSrv6PolicyGroupRequestDTO {

    /**
     * 应用组ID，应用组表主键
     */
    private Long dataId;

    /**
     * 应用组名称，0~255个字符
     */
    private String groupName;

    /**
     * 应用组描述，0~255个字符
     */
    private String description;

    /**
     * 组网模型，可选值如下：
     * 0 - Any-to-Any
     * 1 - Hub-Spoke
     */
    private Integer networkingModel;

    /**
     * 候选路径数，可选值如下：
     * 1 - 单向(UNIDIRECTIONAL)
     * 2 - 双向(BIDIRECTIONAL)
     */
    private Integer candipathNum;

    /**
     * 业务方向，可选值如下：
     * SINGLE - 1
     * MULTIPLE - 2
     */
    private Integer direction;

    /**
     * 使能共路，默认false
     */
    private Boolean coRouted;

    /**
     * 优选策略
     */
    private String calcLimit;

    private Boolean cbts;

    private Integer scheduleType;

    /**
     * Srv6 Policy部署方式（南向协议）
     * 1 - Netconf
     * 2 - BGP SR Policy
     */
    private Integer policyDeployMode;

    /**
     * 是否更新软锁定路径
     */
    private Boolean updateSoftLockPath;

    /**
     * Policy参数信息
     */
    private List<UpdateSrv6PolicyGroupParamsDTO> srv6PolicyGroupParamsList;

    /**
     * Policy参数信息
     */
    @Data
    public static class UpdateSrv6PolicyGroupParamsDTO {
        /**
         * Policy参数ID，Policy参数表主键
         */
        private Long dataId;

        private Long colorId;

        private Integer serviceClass;

        private Boolean positive;

        /**
         * 带宽，单位kbps，范围10~2000000000之间的整数，默认10
         */
        private Integer bandwidth;

        /**
         * 优先级，取值范围0~7，值越大优先级越小
         */
        private Integer priority;

        /**
         * 最大丢包率，取值范围0~10000
         */
        private Long maxPacketLossRate;

        /**
         * 最大抖动，单位微秒，取值范围0~10000000
         */
        private Long maxJitter;

        /**
         * 最小时延，单位微秒，取值范围0~60000000
         */
        private Long minDelay;

        /**
         * 最大时延，单位微秒，取值范围0~60000000
         */
        private Long maxDelay;

        /**
         * 软锁定
         */
        private Boolean softLocking;

        private Boolean hotStandbyEnable;

        private Boolean currentBandwidthEnable;

        private Boolean gsrv6Enable;

        private String gsrv6CompressMode;

        private Boolean bfdEnable;

        private String bfdType;

        private String bfdReverseType;

        private Long mainTemplateId;

        private Long backupTemplateId;

        private Integer trigPathEnable;

        private Boolean statisticEnable;

        private String bypassEnable;

        /**
         * 候选路径约束
         */
        private List<Srv6PolicyGroupCandiConstraintPathEntity> candiPathConstraints;
    }

    /**
     * 候选路径约束实体
     */
    @Data
    public static class Srv6PolicyGroupCandiConstraintPathEntity {
        /**
         * 候选路径主键
         */
        private Long dataId;

        private Boolean positive;

        /**
         * 候选路径索引，可选值如下：
         * 0 - 候选路径1
         * 1 - 候选路径2
         */
        private Integer pathIndex;

        /**
         * 候选路径优先级
         */
        //private Integer preference;

        /**
         * 亲和属性着色类型，可选值如下：
         * 0 - TE亲和属性
         * 1 - Flex-Algo亲和属性
         */
        //private Integer affinityType;

        /**
         * 优选链路着色
         */
        private Integer preferColor;

        /**
         * 亲和属性包含，链路着色bit位集合
         */
        private List<Integer> includeAffinityAny;

        /**
         * 亲和属性必选，链路着色bit位集合
         */
        private List<Integer> includeAffinityAll;

        /**
         * 亲和属性排除，链路着色bit位集合
         */
        private List<Integer> excludeAffinity;

        /**
         * 最大跳数，取值范围1~2147483647
         */
        private Integer hopLimit;

        private Integer sidPathMode;

        /**
         * 初始分段个数，取值范围1~128
         */
        private Integer initSidPathNum;

        /**
         * 最大分段个数，取值范围1~128
         */
        private Integer maxSidPathNum;
    }
}
