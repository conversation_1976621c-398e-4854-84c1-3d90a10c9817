package com.h3c.dzkf.entity.platform;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 平台查询流策略CB对响应DTO
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformQosPolicyDetailRuleResponseDTO {

    /**
     * 输出结果
     */
    private Output output;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Output {
        /**
         * 总行数
         */
        private Integer rowCount;

        /**
         * 每页大小
         */
        private Integer size;

        /**
         * 起始位置
         */
        private Integer start;

        /**
         * CB对列表
         */
        private List<CbPair> cbList;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class CbPair {
        /**
         * CB对ID
         */
        private Integer pairId;

        /**
         * 流分类ID
         */
        private Integer classifierId;

        /**
         * 流分类名称
         */
        private String classifierName;

        /**
         * 流行为ID
         */
        private Integer behaviorId;

        /**
         * 流行为名称
         */
        private String behaviorName;
    }
}
