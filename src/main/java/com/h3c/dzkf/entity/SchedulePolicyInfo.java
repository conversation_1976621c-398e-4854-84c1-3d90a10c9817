package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 调度策略流策略信息实体类
 */
@Data
@TableName("schedule_policy_info")
public class SchedulePolicyInfo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 调度策略ID
     */
    private Long appScheduleId;

    /**
     * 平台流策略ID
     */
    private Integer policyId;

    /**
     * 流策略名称
     */
    private String policyName;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 平台节点ID
     */
    private String platformNodeId;

    /**
     * 设备IP
     */
    private String deviceIp;

    /**
     * 关联的流分类ID
     */
    private Integer classifierId;

    /**
     * 关联的流行为ID
     */
    private Integer behaviorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
}
