package com.h3c.dzkf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 应用ACL关系实体类
 */
@Data
@TableName("app_acl_relation")
public class AppAclRelation {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的应用定制信息ID（app_custom_info表主键）
     */
    private Long appCustomId;

    /**
     * 应用ID（便于直接查询）
     */
    private Integer appId;

    /**
     * ACL模板ID
     */
    private Integer aclId;

    /**
     * IP版本：4-IPv4，6-IPv6
     */
    private Integer ipVersion;

    /**
     * 目标IP地址（拆分后的单个IP）
     */
    private String destinationIp;

    /**
     * 目标端口（拆分后的单个端口）
     */
    private String destinationPort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;
} 