package com.h3c.dzkf.service;

import com.h3c.dzkf.uc2linker.common.conf.network.helper.ApiServiceHelper;
import com.h3c.dzkf.uc2linker.common.conf.network.response.OriginalResponse;
import retrofit2.http.GET;
import retrofit2.http.QueryMap;

import java.util.Map;

public interface CustomApiService extends ApiServiceHelper {
    @GET("/asset/asset/assetList")
    OriginalResponse<String> queryAsset(@QueryMap Map<String, Object> params);
}
