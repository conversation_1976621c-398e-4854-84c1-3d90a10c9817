package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.result.TokenValidationResult;

/**
 * Token验证服务接口
 * 定义Token验证的契约方法
 */
public interface TokenValidationService {

    /**
     * 验证Token有效性
     * @param token Token字符串
     * @return 验证结果，true表示有效，false表示无效
     */
    boolean validateToken(String token);

    /**
     * 验证Token有效性（提供详细的错误信息）
     * @param token Token字符串
     * @return TokenValidationResult 验证结果对象
     */
    TokenValidationResult validateTokenWithResult(String token);
} 