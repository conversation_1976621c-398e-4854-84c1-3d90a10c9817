package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.AddTeTunnelTemplateRequestDTO;
import com.h3c.dzkf.entity.dto.DeleteTeTunnelTemplateRequestDTO;
import com.h3c.dzkf.entity.dto.QueryTeTunnelTemplateResponseDTO;
import com.h3c.dzkf.entity.dto.UpdateTeTunnelTemplateRequestDTO;

/**
 * 隧道模板服务接口
 */
public interface TeTunnelTemplateService {

    /**
     * 新增隧道模板
     *
     * @param request 隧道模板请求参数
     * @param requestId 请求ID
     */
    void addTeTunnelTemplate(AddTeTunnelTemplateRequestDTO request, String requestId);

    /**
     * 删除隧道模板
     *
     * @param request 删除隧道模板请求参数
     * @param requestId 请求ID
     */
    void deleteTeTunnelTemplate(DeleteTeTunnelTemplateRequestDTO request, String requestId);

    /**
     * 修改隧道模板
     *
     * @param request 修改隧道模板请求参数
     * @param requestId 请求ID
     */
    void updateTeTunnelTemplate(UpdateTeTunnelTemplateRequestDTO request, String requestId);

    /**
     * 查询隧道模板列表
     *
     * @param requestId 请求ID
     * @return 响应结果
     */
    QueryTeTunnelTemplateResponseDTO queryTeTunnelTemplateList(String requestId);

} 