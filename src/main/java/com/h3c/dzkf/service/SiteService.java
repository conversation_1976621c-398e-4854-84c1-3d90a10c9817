package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.AddSiteRequestDTO;
import com.h3c.dzkf.entity.dto.BatchImportSiteResponseDTO;
import com.h3c.dzkf.entity.dto.QuerySiteInfoListResponseDTO;
import com.h3c.dzkf.entity.dto.UpdateSiteRequestDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 站点服务接口
 */
public interface SiteService {

    /**
     * 新增站点
     *
     * @param request   请求参数
     * @param requestId 请求ID
     */
    void addSite(AddSiteRequestDTO request, String requestId);

    /**
     * 删除站点
     *
     * @param siteId    站点ID
     * @param requestId 请求ID
     */
    void deleteSite(Integer siteId, String requestId);

    /**
     * 修改站点
     *
     * @param request   请求参数
     * @param requestId 请求ID
     */
    void updateSite(UpdateSiteRequestDTO request, String requestId);

    /**
     * 批量导入站点模板
     *
     * @param file      Excel文件
     * @param requestId 请求ID
     * @return 批量导入响应
     */
    BatchImportSiteResponseDTO importSiteTemplate(MultipartFile file, String requestId);

    /**
     * 查询站点信息列表
     *
     * @param requestId 请求ID
     * @return 站点信息列表响应
     */
    QuerySiteInfoListResponseDTO querySiteInfoList(String requestId);
} 