package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 应用服务接口
 */
public interface AppService {

    /**
     * 新增应用
     *
     * @param request   请求参数
     * @param requestId 请求ID
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    void addApp(AddAppRequestDTO request, String requestId);

    /**
     * 删除应用
     *
     * @param appId     应用ID
     * @param requestId 请求ID
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    void deleteApp(Integer appId, String requestId);

    /**
     * 修改应用
     *
     * @param request   请求参数
     * @param requestId 请求ID
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    void modifyApp(ModifyAppRequestDTO request, String requestId);

    /**
     * 查询应用列表
     *
     * @param requestId 请求ID
     * @return 查询应用列表响应
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    QueryAppInfoListResponseDTO queryAppInfoList(String requestId);

    /**
     * 根据应用ID查询单个应用
     *
     * @param appId     应用ID
     * @param requestId 请求ID
     * @return 查询单个应用响应
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    QueryAppInfoByAppIdResponseDTO queryAppInfoByAppId(Integer appId, String requestId);

    /**
     * 批量导入应用模板
     *
     * @param file      Excel文件
     * @param requestId 请求ID
     * @return 批量导入响应
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    BatchImportAppResponseDTO importAppTemplate(MultipartFile file, String requestId);
}