package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.ApiRequestLog;
import com.h3c.dzkf.entity.dto.QueryApiRequestLogResponseDTO;

public interface ApiRequestLogService {
    void saveLog(ApiRequestLog log);

    /**
     * 根据请求ID查询API请求日志
     *
     * @param requestId        请求ID
     * @param currentRequestId 当前请求ID
     * @return 查询结果
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    QueryApiRequestLogResponseDTO queryApiRequestLogByRequestId(String requestId, String currentRequestId);

    /**
     * 清理历史日志数据
     * @param keepDays 保留天数
     * @return 删除的记录数
     */
    int cleanupOldLogs(int keepDays);

    /**
     * 性能优化的清理历史日志数据
     * 支持分批删除，避免长时间锁表
     * @param keepDays 保留天数
     * @param batchSize 每批删除的数量
     * @param maxDeleteCount 单次任务最大删除数量限制
     * @param batchDelayMs 批次间延迟时间（毫秒）
     * @return 删除的记录数
     */
    int cleanupOldLogsOptimized(int keepDays, int batchSize, long maxDeleteCount, long batchDelayMs);
}