package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.AddScheduleRequestDTO;
import com.h3c.dzkf.entity.dto.DeleteScheduleRequestDTO;
import com.h3c.dzkf.entity.dto.ModifyScheduleRequestDTO;
import com.h3c.dzkf.entity.dto.QueryScheduleListResponseDTO;

/**
 * 调度策略服务接口
 */
public interface ScheduleService {

    /**
     * 新增调度策略
     *
     * @param request   新增调度策略请求参数
     * @param requestId 请求ID
     */
    void addSchedule(AddScheduleRequestDTO request, String requestId);

    /**
     * 修改调度策略
     *
     * @param request   修改调度策略请求参数
     * @param requestId 请求ID
     */
    void modifySchedule(ModifyScheduleRequestDTO request, String requestId);

    /**
     * 删除调度策略
     *
     * @param request   删除调度策略请求参数
     * @param requestId 请求ID
     */
    void deleteSchedule(DeleteScheduleRequestDTO request, String requestId);

    /**
     * 查询调度策略列表
     *
     * @param requestId 请求ID
     * @return 调度策略列表响应
     */
    QueryScheduleListResponseDTO queryScheduleList(String requestId);
}
