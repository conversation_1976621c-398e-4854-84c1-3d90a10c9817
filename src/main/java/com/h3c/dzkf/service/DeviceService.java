package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 设备服务接口
 */
public interface DeviceService {
    
    /**
     * 新增设备
     * @param request 新增设备请求
     * @param requestId 请求ID
     */
    void addDevice(AddDeviceRequestDTO request, String requestId);

    /**
     * 删除设备
     * @param deviceId 设备ID
     * @param requestId 请求ID
     */
    void deleteDevice(Long deviceId, String requestId);

    /**
     * 更新设备名称
     * @param request 更新设备请求
     * @param requestId 请求ID
     */
    void updateDevice(UpdateDeviceRequestDTO request, String requestId);
    
    /**
     * 查询单个设备信息
     * @param deviceId 设备ID
     * @param requestId 请求ID
     * @return 设备详细信息
     */
    QueryDeviceInfoDetailResponseDTO queryDeviceInfoDetail(Long deviceId, String requestId);
    
    /**
     * 批量导入设备模板
     * @param file Excel文件
     * @param requestId 请求ID
     * @return 批量导入响应结果
     */
    BatchImportDeviceResponseDTO importDeviceTemplate(MultipartFile file, String requestId);

    /**
     * 查询站点下所有设备信息
     * @param siteId 站点ID
     * @param requestId 请求ID
     * @return 设备信息列表响应
     */
    QueryDeviceInfoListBySiteIdResponseDTO queryDeviceInfoListBySiteId(Integer siteId, String requestId);

    /**
     * 查询设备型号列表
     * @param requestId 请求ID
     * @return 设备型号列表响应
     */
    QueryDeviceModelResponseDTO queryDeviceModel(String requestId);

    /**
     * 查询设备状态信息
     * @param siteId 站点ID
     * @param requestId 请求ID
     * @return 设备状态统计信息
     */
    QueryDeviceStatusResponseDTO queryDeviceStatus(Integer siteId, String requestId);
} 