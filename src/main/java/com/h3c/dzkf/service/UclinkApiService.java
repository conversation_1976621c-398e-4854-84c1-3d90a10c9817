package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.uclink.*;
import com.h3c.dzkf.uc2linker.framework.fault.model.Fault;
import com.h3c.dzkf.uc2linker.framework.perf.model.SimplePerf;
import com.h3c.dzkf.uc2linker.framework.res.model.RsDeviceEntity;

import java.util.List;

/**
 * 网管接口服务（通过UC2SessionFactory调用）
 */
public interface UclinkApiService {

    /**
     * 查询资产列表 - 返回第一个匹配的资产项
     */
    AssetQueryResponse.AssetItem getAssetList(AssetListRequestDTO request);

    /**
     * 查询设备详情 - 返回第一个匹配的设备实体
     */
    RsDeviceEntity getDeviceDetail(DeviceDetailRequestDTO request);

    /**
     * 查询性能数据
     */
    List<SimplePerf> getPerfData(PerfDataRequestDTO request);

    /**
     * 查询告警信息 - 返回所有匹配的告警列表
     */
    List<Fault> getAlarmInfo(AlarmRequestDTO request);

    /**
     * 根据IP查询资源信息，返回设备ID
     */
    Long getDeviceIdByIp(ResourceQueryRequestDTO request);

    /**
     * 根据设备ID查询设备详情
     */
    DeviceDetailNewResponseDTO getDeviceDetailById(Long deviceId);

    /**
     * 根据设备ID和任务ID查询性能数据
     */
    PerformanceDataResponseDTO getPerformanceDataByDeviceIdAndTaskId(Long deviceId, Integer taskId);
} 