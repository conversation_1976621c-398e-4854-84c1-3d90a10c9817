package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.*;

/**
 * 隧道组服务接口
 */
public interface TeGroupService {

    /**
     * 新增隧道组
     *
     * @param request 新增隧道组请求参数
     * @param requestId 请求ID
     */
    void addTeGroup(AddTeGroupRequestDTO request, String requestId);

    /**
     * 删除隧道组
     *
     * @param request 删除隧道组请求参数
     * @param requestId 请求ID
     */
    void deleteTeGroup(DeleteTeGroupRequestDTO request, String requestId);

    /**
     * 部署隧道组
     *
     * @param request 部署隧道组请求参数
     * @param requestId 请求ID
     */
    void deployTeGroup(DeployTeGroupRequestDTO request, String requestId);

    /**
     * 修改隧道组
     *
     * @param request 修改隧道组请求参数
     * @param requestId 请求ID
     */
    void modifyTeGroup(ModifyTeGroupRequestDTO request, String requestId);

    /**
     * 查询隧道组
     *
     * @param requestId 请求ID
     * @return 查询结果
     */
    GetTeGroupResponseDTO getTeGroup(String requestId);

    /**
     * 查询隧道详情
     *
     * @param request   查询隧道详情请求参数
     * @param requestId 请求ID
     * @return 查询结果
     */
    GetTeGroupDetailsResponseDTO getTeGroupDetails(GetTeGroupDetailsRequestDTO request, String requestId);
}