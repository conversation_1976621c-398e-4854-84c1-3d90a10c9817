package com.h3c.dzkf.service;

/**
 * ServiceClass池管理服务接口
 */
public interface ServiceClassPoolService {

    /**
     * 分配ServiceClass给隧道组（正反向使用同一个ServiceClass）
     *
     * @param teGroupId 隧道组ID
     * @return 分配的ServiceClass值，失败返回null
     */
    Integer allocateServiceClass(Long teGroupId);

    /**
     * 回收隧道组使用的ServiceClass
     *
     * @param teGroupId 隧道组ID
     * @return 是否回收成功
     */
    boolean releaseServiceClass(Long teGroupId);

    /**
     * ServiceClass对（保留用于兼容性，但新增隧道组时正反向使用同一个ServiceClass）
     */
    class ServiceClassPair {
        private Integer positiveServiceClass;  // 正向Policy的ServiceClass
        private Integer negativeServiceClass;  // 反向Policy的ServiceClass

        public ServiceClassPair(Integer positiveServiceClass, Integer negativeServiceClass) {
            this.positiveServiceClass = positiveServiceClass;
            this.negativeServiceClass = negativeServiceClass;
        }

        // 新增构造函数：正反向使用同一个ServiceClass
        public ServiceClassPair(Integer serviceClass) {
            this.positiveServiceClass = serviceClass;
            this.negativeServiceClass = serviceClass;
        }

        public Integer getPositiveServiceClass() {
            return positiveServiceClass;
        }

        public Integer getNegativeServiceClass() {
            return negativeServiceClass;
        }
    }
}