package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.AddLinkRequestDTO;
import com.h3c.dzkf.entity.dto.QueryLinkInfoListBySiteIdResponseDTO;
import com.h3c.dzkf.entity.dto.UpdateLinkRequestDTO;

import java.util.List;

/**
 * 链路服务接口
 */
public interface LinkService {

    /**
     * 新增链路
     * @param request 新增链路请求
     * @param requestId 请求ID
     * @return 平台链路ID
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    Long addLink(AddLinkRequestDTO request, String requestId);

    /**
     * 更新链路
     * @param request 更新链路请求
     * @param requestId 请求ID
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    void updateLink(UpdateLinkRequestDTO request, String requestId);

    /**
     * 查询站点下所有链路信息
     * @param siteId 站点ID
     * @param requestId 请求ID
     * @return 链路信息列表
     * @throws com.h3c.dzkf.common.exceptions.ServiceException 业务异常
     */
    List<QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO> queryLinkInfoListBySiteId(Integer siteId, String requestId);
}