package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.AddAppGroupRequestDTO;
import com.h3c.dzkf.entity.dto.ModifyAppGroupRequestDTO;
import com.h3c.dzkf.entity.dto.QueryAppGroupListResponseDTO;

/**
 * 应用组服务接口
 */
public interface AppGroupService {

    /**
     * 新增应用组
     *
     * @param request   请求参数
     * @param requestId 请求ID
     */
    void addAppGroup(AddAppGroupRequestDTO request, String requestId);

    /**
     * 删除应用组
     *
     * @param appGroupId 应用组ID
     * @param requestId  请求ID
     */
    void deleteAppGroup(Integer appGroupId, String requestId);

    /**
     * 修改应用组
     *
     * @param request   请求参数
     * @param requestId 请求ID
     */
    void modifyAppGroup(ModifyAppGroupRequestDTO request, String requestId);

    /**
     * 查询应用组列表
     *
     * @param requestId 请求ID
     * @return 应用组列表响应
     */
    QueryAppGroupListResponseDTO queryAppGroupList(String requestId);
} 