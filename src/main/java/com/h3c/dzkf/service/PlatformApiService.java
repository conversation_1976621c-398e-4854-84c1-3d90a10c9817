package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.platform.*;

/**
 * 平台API服务接口
 */
public interface PlatformApiService {

    /**
     * 调用平台接口新增设备
     *
     * @param request 平台设备请求参数
     * @return 平台返回的设备节点ID
     */
    String addDevice(PlatformAddDeviceRequestDTO request);

    /**
     * 调用平台接口删除设备
     *
     * @param request 平台删除设备请求参数
     * @return 是否删除成功
     */
    boolean deleteDevice(PlatformDeleteRequestDTO request);

    /**
     * 调用平台接口查询设备信息
     *
     * @param request 平台查询设备请求参数
     * @return 设备信息响应
     */
    PlatformGetNodesResponseDTO getNodes(PlatformGetNodesRequestDTO request);

    /**
     * 调用平台接口更新设备信息
     *
     * @param request 平台更新设备请求参数
     * @return 是否更新成功
     */
    boolean updateNode(PlatformUpdateNodeRequestDTO request);

    /**
     * 查询设备接口信息（ADWAN平台接口）
     *
     * @param request 接口查询请求
     * @return 接口信息
     */
    PlatformInterfaceListResponseDTO getBatchInterfaceList(PlatformInterfaceListRequestDTO request);

    /**
     * 调用平台接口新增链路
     *
     * @param request 平台新增链路请求参数
     * @return 平台返回的链路ID
     */
    String addLink(PlatformAddLinkRequestDTO request);

    /**
     * 查询设备接口信息
     *
     * @param request 查询设备接口请求参数
     * @return 设备接口信息响应
     */
    PlatformGetInterfacesResponseDTO getInterfaces(PlatformGetInterfacesRequestDTO request);

    /**
     * 调用平台接口更新链路
     *
     * @param request 平台更新链路请求参数
     * @return 是否更新成功
     */
    boolean updateLink(PlatformUpdateLinkRequestDTO request);

    /**
     * 查询链路信息
     *
     * @param request 查询链路请求参数
     * @return 链路信息响应
     */
    PlatformGetLinkResponseDTO getLink(PlatformGetLinkRequestDTO request);

    /**
     * 查询链路总览信息
     *
     * @param request 查询链路总览请求参数
     * @return 链路总览信息响应
     */
    PlatformLinkOverViewResponseDTO getLinkOverView(PlatformLinkOverViewRequestDTO request);

    /**
     * 查询NETCONF模板
     *
     * @param request 查询NETCONF模板请求参数
     * @return NETCONF模板响应
     */
    GetNetconfTemplateResponseDTO getNetconfTemplate(GetNetconfTemplateRequestDTO request);

    /**
     * 查询SNMP模板
     *
     * @param request 查询SNMP模板请求参数
     * @return SNMP模板响应
     */
    GetSnmpTemplateResponseDTO getSnmpTemplate(GetSnmpTemplateRequestDTO request);

    /**
     * 维护设备
     *
     * @param request 维护设备请求参数
     * @return 是否维护成功
     */
    boolean maintainDevice(MaintainNodeDTO request);

    /**
     * 新增ACL模板
     *
     * @param request ACL模板请求参数
     * @return ACL模板响应
     */
    PlatformAclResponseDTO addAclTemplate(PlatformAclRequestDTO request);

    /**
     * 删除ACL模板
     *
     * @param aclId ACL模板ID
     * @param requestId 请求ID
     * @return 是否删除成功
     */
    boolean deleteAclTemplate(Integer aclId, String requestId);

    /**
     * 查询ACL模板列表
     *
     * @param request 查询ACL模板请求参数
     * @return ACL模板列表响应
     */
    QueryAclTemplateResponseDTO queryAclTemplates(QueryAclTemplateRequestDTO request);


    /**
     * 修改ACL模板
     *
     * @param request ACL模板请求参数
     * @return ACL模板响应
     */
    PlatformAclResponseDTO updateAclTemplate(PlatformAclRequestDTO request);

    /**
     * 调用平台接口新增SRv6 Policy应用组
     *
     * @param request 平台新增SRv6 Policy应用组请求参数
     * @return 平台返回的应用组信息
     */
    PlatformAddSrv6PolicyGroupResponseDTO addSrv6PolicyGroup(PlatformAddSrv6PolicyGroupRequestDTO request);

    /**
     * 调用平台接口更新SRv6 Policy应用组作用域
     *
     * @param request 平台更新SRv6 Policy应用组作用域请求参数
     * @return 是否更新成功
     */
    boolean updateSrv6PolicyGroupNetwork(PlatformUpdateSrv6PolicyGroupNetworkRequestDTO request);

    /**
     * 调用平台接口查询应用组信息
     *
     * @param request 查询应用组信息请求参数
     * @return 应用组信息响应
     */
    GetSrv6PolicyGroupResponseDTO getSrv6PolicyGroup(GetSrv6PolicyGroupRequestDTO request);

    /**
     * 调用平台接口查询BFD模板
     *
     * @param request 查询BFD模板请求参数
     * @return BFD模板响应
     */
    GetBfdTemplateResponseDTO getBfdTemplate(GetBfdTemplateRequestDTO request);

    /**
     * 调用平台接口删除SRv6 Policy应用组
     *
     * @param groupId 平台应用组ID
     * @return 是否删除成功
     */
    boolean deleteSrv6PolicyGroup(String groupId);

    /**
     * 调用平台接口规划应用组路径
     *
     * @param request 规划应用组路径请求参数
     * @return 是否规划成功
     */
    boolean startPlanSrv6PolicyGroup(StartPlanSrv6PolicyGroupDTO request);

    /**
     * 调用平台接口部署应用组配置
     *
     * @param request 部署应用组配置请求参数
     * @return 是否部署成功
     */
    boolean deploySrv6PolicyGroup(DeploySrv6PolicyGroupDTO request);

    /**
     * 调用平台接口查询应用组详情
     *
     * @param request 查询应用组详情请求参数
     * @return 应用组详情响应
     */
    GetSrv6PolicyGroupDetailResponseDTO getSrv6PolicyGroupDetail(GetSrv6PolicyGroupDetailRequestDTO request);

    /**
     * 调用平台接口更新应用组
     *
     * @param request 更新应用组请求参数
     * @return 更新应用组响应
     */
    UpdateSrv6PolicyGroupResponseDTO updateSrv6PolicyGroup(UpdateSrv6PolicyGroupRequestDTO request);

    /**
     * 调用平台接口查询作用域
     *
     * @param request 查询作用域请求参数
     * @return 作用域响应
     */
    GetCustomNetworkScopeResponseDTO getCustomNetworkScope(GetCustomNetworkScopeRequestDTO request);

    /**
     * 调用平台接口查询设备硬件版本信息
     *
     * @param request 查询硬件版本请求参数
     * @return 硬件版本信息响应
     */
    PlatformInventoryInfoResponseDTO getInventoryInfo(PlatformInventoryInfoRequestDTO request);

    /**
     * 调用平台接口查询SRv6 Policy业务列表
     *
     * @param request 查询SRv6 Policy业务列表请求参数
     * @return SRv6 Policy业务列表响应
     */
    GetSrv6PolicyTrailResponseDTO getSrv6PolicyTrail(GetSrv6PolicyTrailRequestDTO request);

    /**
     * 调用平台接口查询SRv6 Policy业务部署详情
     *
     * @param request 查询SRv6 Policy业务部署详情请求参数
     * @return SRv6 Policy业务部署详情响应
     */
    GetSrv6PolicyTrailDeployDetailResponseDTO getSrv6PolicyTrailDeployDetail(GetSrv6PolicyTrailDeployDetailRequestDTO request);

    // ==================== QoS相关接口 ====================

    /**
     * 调用平台接口新增流分类
     *
     * @param request 新增流分类请求参数
     * @return 是否新增成功
     */
    boolean addQosClassifier(PlatformQosClassifierRequestDTO request);

    /**
     * 调用平台接口修改流分类
     *
     * @param request 修改流分类请求参数
     * @return 是否修改成功
     */
    boolean updateQosClassifier(PlatformQosClassifierUpdateRequestDTO request);

    /**
     * 调用平台接口删除流分类
     *
     * @param classifierId 流分类ID
     * @return 是否删除成功
     */
    boolean deleteQosClassifier(Integer classifierId);

    /**
     * 调用平台接口删除流分类（带详细结果）
     *
     * @param classifierId 流分类ID
     * @return 删除结果详情
     */
    PlatformDeleteResult deleteQosClassifierWithResult(Integer classifierId);

    /**
     * 调用平台接口查询流分类列表
     *
     * @param request 查询流分类列表请求参数
     * @return 流分类列表响应
     */
    PlatformQosClassifierListResponseDTO getQosClassifierList(PlatformQosClassifierListRequestDTO request);

    /**
     * 调用平台接口新增流行为
     *
     * @param request 新增流行为请求参数
     * @return 流行为响应
     */
    PlatformQosBehaviorResponseDTO addQosBehavior(PlatformQosBehaviorRequestDTO request);

    /**
     * 调用平台接口查询流行为详情
     *
     * @param request 查询流行为详情请求参数
     * @return 流行为详情响应
     */
    PlatformQosBehaviorDetailResponseDTO getQosBehaviorDetail(PlatformQosBehaviorDetailRequestDTO request);

    /**
     * 调用平台接口修改流行为
     *
     * @param request 修改流行为请求参数
     * @return 是否修改成功
     */
    boolean updateQosBehavior(PlatformQosBehaviorUpdateRequestDTO request);

    /**
     * 调用平台接口删除流行为
     *
     * @param behaviorId 流行为ID
     * @return 是否删除成功
     */
    boolean deleteQosBehavior(Integer behaviorId);

    /**
     * 调用平台接口删除流行为（带详细结果）
     *
     * @param behaviorId 流行为ID
     * @return 删除结果详情
     */
    PlatformDeleteResult deleteQosBehaviorWithResult(Integer behaviorId);

    /**
     * 调用平台接口新增流策略
     *
     * @param request 新增流策略请求参数
     * @return 流策略响应
     */
    PlatformQosPolicyResponseDTO addQosPolicy(PlatformQosPolicyRequestDTO request);

    /**
     * 调用平台接口删除流策略
     *
     * @param policyId 流策略ID
     * @return 是否删除成功
     */
    boolean deleteQosPolicy(Integer policyId);

    /**
     * 调用平台接口修改流策略
     *
     * @param request 修改流策略请求参数
     * @return 是否修改成功
     */
    boolean updateQosPolicy(PlatformQosPolicyUpdateRequestDTO request);

    /**
     * 调用平台接口查询流策略列表
     *
     * @param request 查询流策略列表请求参数
     * @return 流策略列表响应
     */
    PlatformQosPolicyListResponseDTO getQosPolicyList(PlatformQosPolicyListRequestDTO request);

    /**
     * 调用平台接口查询流策略CB对详情
     *
     * @param request 查询流策略CB对请求参数
     * @return 流策略CB对响应
     */
    PlatformQosPolicyDetailRuleResponseDTO getQosPolicyRuleDetail(PlatformQosPolicyDetailRuleRequestDTO request);

    /**
     * 调用平台接口部署流策略到设备接口
     *
     * @param request 部署流策略请求参数
     * @return 部署响应
     */
    PlatformQosPolicyDeployResponseDTO deployQosPolicy(PlatformQosPolicyDeployRequestDTO request);

    /**
     * 调用平台接口查询QoS设备列表
     *
     * @param request 查询QoS设备列表请求参数
     * @return QoS设备列表响应
     */
    PlatformQosDeviceListResponseDTO getQosDeviceList(PlatformQosDeviceListRequestDTO request);

    /**
     * 调用平台接口查询设备接口列表
     *
     * @param request 查询设备接口列表请求参数
     * @return 设备接口列表响应
     */
    PlatformQosDeviceInterfaceListResponseDTO getQosDeviceInterfaceList(PlatformQosDeviceInterfaceListRequestDTO request);

    /**
     * 调用平台接口查询ACL模板详情
     *
     * @param aclId ACL模板ID
     * @return ACL模板详情响应
     */
    PlatformAclDetailResponseDTO getAclDetail(Integer aclId);

    /**
     * 调用平台接口查询流策略部署历史
     *
     * @param request 查询流策略部署历史请求参数
     * @return 流策略部署历史响应
     */
    PlatformQosPolicyDeployHistoryResponseDTO getQosPolicyDeployHistory(PlatformQosPolicyDeployHistoryRequestDTO request);
}