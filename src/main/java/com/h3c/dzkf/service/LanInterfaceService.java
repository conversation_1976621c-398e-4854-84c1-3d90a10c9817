package com.h3c.dzkf.service;

import com.h3c.dzkf.entity.dto.*;

/**
 * LAN口管理Service接口
 */
public interface LanInterfaceService {

    /**
     * 新增LAN口接口
     * @param request 请求参数
     * @param requestId 请求ID
     * @return 响应结果
     */
    AddLanInterfaceResponseDTO addLanInterface(AddLanInterfaceRequestDTO request, String requestId);

    /**
     * 删除LAN口接口
     * @param lanId LAN口ID
     * @param requestId 请求ID
     * @return 响应结果
     */
    DeleteLanInterfaceResponseDTO deleteLanInterface(Long lanId, String requestId);

    /**
     * 修改LAN口接口
     * @param request 请求参数
     * @param requestId 请求ID
     */
    void updateLanInterface(UpdateLanInterfaceRequestDTO request, String requestId);

    /**
     * 查询LAN口接口
     * @param requestId 请求ID
     * @return 响应结果
     */
    QueryLanInterfaceResponseDTO queryLanInterface(String requestId);
} 