package com.h3c.dzkf.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.h3c.dzkf.common.config.DeviceApiConfig;
import com.h3c.dzkf.common.config.TestModeConfig;
import com.h3c.dzkf.common.exceptions.PlatformApiException;
import com.h3c.dzkf.entity.uclink.*;
import com.h3c.dzkf.service.CustomApiService;
import com.h3c.dzkf.service.UclinkApiService;
import com.h3c.dzkf.uc2linker.framework.fault.model.Fault;
import com.h3c.dzkf.uc2linker.framework.fault.model.FaultCondition;
import com.h3c.dzkf.uc2linker.framework.fault.model.Faults;
import com.h3c.dzkf.uc2linker.framework.perf.model.SimplePerf;
import com.h3c.dzkf.uc2linker.framework.res.model.RsDeviceEntity;
import com.h3c.dzkf.uc2linker.framework.res.model.RsDeviceEntitys;
import com.h3c.dzkf.uc2linker.framework.sessionfactory.UC2SessionFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 网管接口服务实现类（通过UC2SessionFactory调用）
 * <p>
 * 负责通过UC2SessionFactory调用网管系统的各种接口：
 * 1. 资产列表查询
 * 2. 设备详情查询
 * 3. 性能数据查询
 * 4. 告警信息查询
 * <p>
 * 注意：具体的UC2SessionFactory调用方式需要根据实际API进行调整
 */
@Slf4j
@Service
public class UclinkApiServiceImpl implements UclinkApiService {

    @Autowired
    private TestModeConfig testModeConfig;

    @Autowired
    private UC2SessionFactory uc2SessionFactory;

    @Autowired
    private DeviceApiConfig deviceApiConfig;

    @Override
    public AssetQueryResponse.AssetItem getAssetList(AssetListRequestDTO request) {
        try {
            log.info("开始调用网管资产接口，请求参数：{}", JSON.toJSONString(request));

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetAssetList(request);
            }

            // 网管查询是模糊查询，需要遍历所有页面进行精准IP匹配
            return queryAllPagesForExactIp(request);

        } catch (Exception e) {
            log.error("调用网管资产接口异常", e);
            throw new PlatformApiException("调用网管资产接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public RsDeviceEntity getDeviceDetail(DeviceDetailRequestDTO request) {
        try {
            log.info("开始调用网管设备详情接口，请求参数：{}", JSON.toJSONString(request));

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetDeviceDetail(request);
            }

            // 网管查询是模糊查询，需要遍历所有页面进行精准IP匹配
            return queryDeviceDetailAllPagesForExactIp(request);

        } catch (Exception e) {
            log.error("调用网管设备详情接口异常", e);
            throw new PlatformApiException("调用网管设备详情接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public List<SimplePerf> getPerfData(PerfDataRequestDTO request) {
        try {
            log.info("开始调用网管性能数据接口，请求参数：{}", JSON.toJSONString(request));

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetPerfData(request);
            }

            // 将taskIds和devIds转换为List格式
            String taskIds = request.getTaskIds();
            String devIds = request.getDevIds();

            // 转换taskIds为List<Integer>
            List<Integer> taskIdList = Arrays.stream(taskIds.split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());

            // 转换devIds为List<Long>（单个设备ID）
            List<Long> deviceLatest = Collections.singletonList(Long.parseLong(devIds));

            // 调用UC2SessionFactory性能数据接口并直接返回
            List<SimplePerf> simplePerfs = uc2SessionFactory.getPerfComponent()
                    .getMonitorPerfFunction().deviceLatest(1, taskIdList, deviceLatest);

            log.info("网管性能数据接口调用成功");
            return simplePerfs;

        } catch (Exception e) {
            log.error("调用网管性能数据接口异常", e);
            throw new PlatformApiException("调用网管性能数据接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public List<Fault> getAlarmInfo(AlarmRequestDTO request) {
        try {
            log.info("开始调用网管告警接口，请求参数：{}", JSON.toJSONString(request));

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetAlarmInfo(request);
            }

            // 网管查询是模糊查询，需要进行精准IP匹配
            return queryAlarmInfoForExactIp(request);

        } catch (Exception e) {
            log.error("调用网管告警接口异常", e);
            throw new PlatformApiException("调用网管告警接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟网管资产接口调用
     */
    private AssetQueryResponse.AssetItem mockGetAssetList(AssetListRequestDTO request) {
        log.info("测试模式：模拟网管资产接口调用，设备IP：{}", request.getIp());

        // 构建模拟响应
        AssetQueryResponse.AssetItem item = new AssetQueryResponse.AssetItem();
        item.setIp(request.getIp());
        item.setModelName("Mock-Model");
        item.setHardwareRev("REV.A");
        item.setSysName("Mock-Device-Name");
        item.setSerialNum("SN123456");

        log.info("测试模式：生成模拟资产查询响应");
        return item;
    }

    /**
     * 模拟网管设备详情接口调用
     */
    private RsDeviceEntity mockGetDeviceDetail(DeviceDetailRequestDTO request) {
        log.info("测试模式：模拟网管设备详情接口调用，设备IP：{}", request.getDevIp());

        // 创建模拟的RsDeviceEntity对象
        RsDeviceEntity mockDevice = new RsDeviceEntity();

        // 设置基本设备信息
        mockDevice.setId(12345L); // 模拟设备ID
        mockDevice.setIpAddress(request.getDevIp()); // 使用请求的IP
        mockDevice.setMac("00:0C:29:F9:C8:6D"); // 模拟MAC地址
        mockDevice.setDevVersion("V7.1.075 Release 6555P06"); // 模拟软件版本

        // 可以根据需要添加更多字段
        log.info("测试模式：生成模拟设备详情，设备ID：{}，IP：{}，MAC：{}",
                mockDevice.getId(), mockDevice.getIpAddress(), mockDevice.getMac());

        return mockDevice;
    }

    /**
     * 模拟网管性能数据接口调用
     */
    private List<SimplePerf> mockGetPerfData(PerfDataRequestDTO request) {
        log.info("测试模式：模拟网管性能数据接口调用，任务ID：{}，设备ID：{}", request.getTaskIds(), request.getDevIds());

        List<SimplePerf> mockPerfList = new ArrayList<>();
        String[] taskIds = request.getTaskIds().split(",");

        for (String taskIdStr : taskIds) {
            int taskId = Integer.parseInt(taskIdStr.trim());

            SimplePerf mockPerf = new SimplePerf();
            mockPerf.setTaskId(taskId);

            switch (taskId) {
                case 2: // CPU利用率
                    mockPerf.setCollectValue(75.5f); // 模拟CPU使用率75.5%
                    mockPerf.setInstanceId(1);
                    break;
                case 4: // 内存利用率
                    mockPerf.setCollectValue(68.2f); // 模拟内存使用率68.2%
                    mockPerf.setInstanceId(1);
                    break;
                case 49: // 温度
                    // 模拟多个温度传感器
                    for (int i = 1; i <= 3; i++) {
                        SimplePerf tempPerf = new SimplePerf();
                        tempPerf.setTaskId(49);
                        tempPerf.setCollectValue(35.0f + i * 2); // 模拟温度37°C, 39°C, 41°C
                        tempPerf.setInstanceId(i);
                        mockPerfList.add(tempPerf);
                    }
                    continue; // 跳过后面的add，因为已经在循环中添加了
                case 908: // 电源状态
                    // 模拟2个电源模块
                    for (int i = 1; i <= 2; i++) {
                        SimplePerf powerPerf = new SimplePerf();
                        powerPerf.setTaskId(908);
                        powerPerf.setCollectValue(1.0f); // 1表示正常
                        powerPerf.setInstanceId(i);
                        mockPerfList.add(powerPerf);
                    }
                    continue;
                case 902: // 风扇状态
                    // 模拟4个风扇
                    for (int i = 1; i <= 4; i++) {
                        SimplePerf fanPerf = new SimplePerf();
                        fanPerf.setTaskId(902);
                        fanPerf.setCollectValue(i <= 3 ? 1.0f : 0.0f); // 前3个正常，第4个异常
                        fanPerf.setInstanceId(i);
                        mockPerfList.add(fanPerf);
                    }
                    continue;
                default:
                    mockPerf.setCollectValue(0.0f);
                    mockPerf.setInstanceId(1);
            }

            if (taskId != 49 && taskId != 908 && taskId != 902) {
                mockPerfList.add(mockPerf);
            }
        }

        log.info("测试模式：生成{}条模拟性能数据", mockPerfList.size());
        return mockPerfList;
    }

    /**
     * 模拟网管告警接口调用
     */
    private List<Fault> mockGetAlarmInfo(AlarmRequestDTO request) {
        log.info("测试模式：模拟网管告警接口调用，设备IP：{}", request.getResourceIp());

        List<Fault> mockAlarmList = new ArrayList<>();

        // 创建模拟告警1：设备温度过高
        Fault alarm1 = new Fault();
        alarm1.setDevIp(request.getResourceIp());
        alarm1.setSeverity(1); // 1-严重
        alarm1.setFaultName("设备温度过高告警");
        alarm1.setDescInfo("设备温度超过安全阈值，当前温度：45°C");
        alarm1.setFaultTimeReachString("2024-01-15 10:30:00");
        mockAlarmList.add(alarm1);

        // 创建模拟告警2：接口DOWN
        Fault alarm2 = new Fault();
        alarm2.setDevIp(request.getResourceIp());
        alarm2.setSeverity(2); // 2-重要
        alarm2.setFaultName("接口状态异常");
        alarm2.setDescInfo("GigabitEthernet1/0/24接口状态变为DOWN");
        alarm2.setFaultTimeReachString("2024-01-15 12:00:00");
        mockAlarmList.add(alarm2);

        // 创建模拟告警3：风扇故障
        Fault alarm3 = new Fault();
        alarm3.setDevIp(request.getResourceIp());
        alarm3.setSeverity(3); // 3-次要
        alarm3.setFaultName("风扇运行异常");
        alarm3.setDescInfo("风扇FAN-4运行状态异常，请检查硬件");
        alarm3.setFaultTimeReachString("2024-01-15 12:15:00");
        mockAlarmList.add(alarm3);

        log.info("测试模式：生成{}条模拟告警数据，设备IP：{}", mockAlarmList.size(), request.getResourceIp());
        return mockAlarmList;
    }


    /**
     * 遍历所有页面查找精准IP匹配
     */
    private AssetQueryResponse.AssetItem queryAllPagesForExactIp(AssetListRequestDTO request) {
        CustomApiService apiService = uc2SessionFactory.getApiService(CustomApiService.class);
        String targetIp = request.getIp();

        int currentPage = 1;
        int pageSize = Math.max(request.getPageSize(), 10); // 确保页面大小至少为10
        int totalChecked = 0;
        Integer totalPages = null; // 总页数，从第一次查询结果中获取

        log.info("开始分页查询精准匹配IP：{}，页面大小：{}", targetIp, pageSize);

        while (true) {
            // 查询当前页
            Map<String, Object> params = new HashMap<>();
            params.put("pageNum", currentPage);
            params.put("pageSize", pageSize);
            params.put("ip", request.getIp()); // 保持原始查询条件

            String body = apiService.queryAsset(params).body();
            AssetQueryResponse pageResponse = JSON.parseObject(body, AssetQueryResponse.class);

            if (pageResponse == null || pageResponse.getAssets() == null) {
                log.warn("第{}页查询结果为空，结束查询", currentPage);
                break;
            }

            // 第一次查询时，根据rowCount计算总页数
            if (totalPages == null && pageResponse.getRowCount() != null) {
                totalPages = (int) Math.ceil((double) pageResponse.getRowCount() / pageSize);
                log.info("获取总记录数：{}，计算总页数：{}", pageResponse.getRowCount(), totalPages);
            }

            List<AssetQueryResponse.AssetItem> currentPageAssets = pageResponse.getAssets();
            totalChecked += currentPageAssets.size();

            log.info("查询第{}页，获得{}条记录，累计检查{}条记录",
                    currentPage, currentPageAssets.size(), totalChecked);

            // 在当前页中查找精准匹配（只需要第一条）
            AssetQueryResponse.AssetItem matchedAsset = currentPageAssets.stream()
                    .filter(item -> targetIp.equals(item.getIp()))
                    .findFirst()
                    .orElse(null);

            if (matchedAsset != null) {
                // 找到匹配的记录，构建结果并返回
                log.info("找到精准匹配IP：{}，返回第一条匹配记录，总共查询{}页，检查{}条记录",
                        targetIp, currentPage, totalChecked);
                return matchedAsset;
            }

            // 检查是否还有下一页
            if (totalPages != null && currentPage >= totalPages) {
                // 已查询完所有页面
                log.info("已查询完所有页面（共{}页），未找到精准匹配IP：{}，总共检查{}条记录",
                        totalPages, targetIp, totalChecked);
                break;
            } else if (totalPages == null && currentPageAssets.size() < pageSize) {
                // 如果没有rowCount信息，通过页面记录数判断是否为最后一页
                log.info("已查询到最后一页（第{}页），未找到精准匹配IP：{}，总共检查{}条记录",
                        currentPage, targetIp, totalChecked);
                break;
            }

            currentPage++;

            // 额外的安全检查，防止异常情况下的无限循环
            if (currentPage > 1000) {
                log.warn("查询页数超过安全限制(1000页)，停止查询，目标IP：{}", targetIp);
                break;
            }
        }

        // 未找到匹配记录，抛出异常
        log.info("遍历完所有页面，未找到精准匹配IP：{}，总共查询{}页，检查{}条记录",
                targetIp, totalPages != null ? totalPages : currentPage - 1, totalChecked);

        throw new PlatformApiException("未找到精准匹配IP的资产信息", "ASSET_NOT_FOUND");
    }


    /**
     * 遍历所有页面查找设备详情精准IP匹配
     */
    private RsDeviceEntity queryDeviceDetailAllPagesForExactIp(DeviceDetailRequestDTO request) {
        String targetIp = request.getDevIp();
        int currentPage = 1;
        int pageSize = Math.max(request.getPageSize(), 10);
        int totalChecked = 0;
        Integer totalPages = null; // 总页数，从第一次查询结果中获取

        log.info("开始分页查询设备详情精准匹配IP：{}，页面大小：{}", targetIp, pageSize);

        while (true) {
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("devIp", request.getDevIp());
            paramMap.put("isQueryStatus", request.getIsQueryStatus());
            paramMap.put("isQueryIfCount", request.getIsQueryIfCount());
            paramMap.put("isQueryExt", request.getIsQueryExt());

            RsDeviceEntitys pageResponse = uc2SessionFactory.getResComponent()
                    .getDeviceFunction().deviceListV2(currentPage, pageSize, paramMap);

            if (pageResponse == null) {
                log.warn("设备详情第{}页查询结果为空，结束查询", currentPage);
                break;
            }

            // 第一次查询时，根据rowCount计算总页数
            if (totalPages == null && pageResponse.getRowCount() != null) {
                totalPages = (int) Math.ceil((double) pageResponse.getRowCount() / pageSize);
                log.info("获取设备详情总记录数：{}，计算总页数：{}", pageResponse.getRowCount(), totalPages);
            }

            // 基于实际RsDeviceEntitys结构实现IP精准匹配
            List<RsDeviceEntity> currentPageDevices = pageResponse.getDevices();
            if (currentPageDevices != null) {
                totalChecked += currentPageDevices.size();
                log.info("设备详情查询第{}页，获得{}条记录，累计检查{}条记录",
                        currentPage, currentPageDevices.size(), totalChecked);

                // 在当前页中查找精准匹配（只需要第一条）
                RsDeviceEntity matchedDevice = currentPageDevices.stream()
                        .filter(device -> targetIp.equals(device.getIpAddress()))
                        .findFirst()
                        .orElse(null);

                if (matchedDevice != null) {
                    // 找到匹配的记录，构建结果并返回
                    log.info("找到设备详情精准匹配IP：{}，返回第一条匹配记录，总共查询{}页，检查{}条记录",
                            targetIp, currentPage, totalChecked);
                    return matchedDevice;
                }
            }

            log.info("设备详情查询第{}页完成，未找到IP匹配", currentPage);

            // 检查是否还有下一页
            if (totalPages != null && currentPage >= totalPages) {
                // 已查询完所有页面
                log.info("已查询完设备详情所有页面（共{}页），未找到精准匹配IP：{}", totalPages, targetIp);
                break;
            } else if (totalPages == null) {
                // 如果没有rowCount信息，暂时只查询第一页
                log.warn("设备详情查询缺少rowCount信息，暂时只查询第一页");
                break;
            }

            currentPage++;

            // 额外的安全检查，防止异常情况下的无限循环
            if (currentPage > 1000) {
                log.warn("设备详情查询页数超过安全限制(1000页)，停止查询，目标IP：{}", targetIp);
                break;
            }
        }

        log.info("遍历完设备详情所有页面，未找到精准匹配IP：{}，总共查询{}页",
                targetIp, totalPages != null ? totalPages : currentPage - 1);
        throw new PlatformApiException("未找到精准匹配IP的设备详情", "DEVICE_DETAIL_NOT_FOUND");
    }


    /**
     * 查询告警信息精准IP匹配 - 返回所有匹配的记录
     */
    private List<Fault> queryAlarmInfoForExactIp(AlarmRequestDTO request) {
        String targetIp = request.getResourceIp();
        int requestedSize = request.getSize() != null ? request.getSize() : 10;
        int pageSize = Math.max(requestedSize, 10); // 确保页面大小至少为10
        int currentStart = request.getStart() != null ? request.getStart() : 0;
        int totalChecked = 0;
        Integer totalRecords = null; // 总记录数，从第一次查询结果中获取
        Integer totalPages = null; // 总页数
        List<Fault> allMatchedFaults = new ArrayList<>(); // 收集所有匹配的告警

        log.info("开始分页查询告警信息精准匹配IP：{}，页面大小：{}，起始位置：{}", targetIp, pageSize, currentStart);

        while (true) {
            // 根据实际UC2SessionFactory API调用
            FaultCondition faultCondition = new FaultCondition();
            faultCondition.setAlarmType(request.getAlarmType());
            faultCondition.setResourceIp(request.getResourceIp());
            faultCondition.setSize(pageSize);
            faultCondition.setStart(currentStart);

            Faults pageResponse = uc2SessionFactory.getFaultComponent().getFullFaultFunction().fullFault(faultCondition);

            if (pageResponse == null) {
                log.warn("告警信息查询起始位置{}结果为空，结束查询", currentStart);
                break;
            }

            // 第一次查询时，根据total计算总页数
            if (totalPages == null && pageResponse.getTotal() != null) {
                totalRecords = pageResponse.getTotal().intValue();
                totalPages = (int) Math.ceil((double) totalRecords / pageSize);
                log.info("获取告警信息总记录数：{}，计算总页数：{}", totalRecords, totalPages);
            }

            // 基于实际Faults结构实现IP精准匹配
            List<Fault> currentPageFaults = pageResponse.getData();
            if (currentPageFaults != null) {
                totalChecked += currentPageFaults.size();
                log.info("告警信息查询起始位置{}，获得{}条记录，累计检查{}条记录",
                        currentStart, currentPageFaults.size(), totalChecked);

                // 在当前页中查找所有精准匹配的记录
                List<Fault> pageMatchedFaults = currentPageFaults.stream()
                        .filter(fault -> targetIp.equals(fault.getDevIp()))
                        .collect(Collectors.toList());

                if (!pageMatchedFaults.isEmpty()) {
                    allMatchedFaults.addAll(pageMatchedFaults);
                    log.info("在第{}页找到{}条精准匹配IP：{}的告警记录",
                            (currentStart / pageSize) + 1, pageMatchedFaults.size(), targetIp);
                }
            }

            log.info("告警信息查询起始位置{}完成，当前累计匹配{}条记录", currentStart, allMatchedFaults.size());

            // 检查是否还有下一页
            if (totalPages != null) {
                int currentPageNum = (currentStart / pageSize) + 1;
                if (currentPageNum >= totalPages) {
                    // 已查询完所有页面
                    log.info("已查询完告警信息所有页面（共{}页），总匹配{}条记录", totalPages, allMatchedFaults.size());
                    break;
                }
            } else {
                // 如果没有total信息，通过页面记录数判断是否为最后一页
                if (currentPageFaults == null || currentPageFaults.size() < pageSize) {
                    log.info("告警信息查询到最后一页，总匹配{}条记录", allMatchedFaults.size());
                    break;
                }
            }

            currentStart += pageSize;

            // 额外的安全检查，防止异常情况下的无限循环
            if (currentStart > 10000) {
                log.warn("告警信息查询起始位置超过安全限制(10000)，停止查询，目标IP：{}", targetIp);
                break;
            }
        }

        // 构建最终结果
        if (allMatchedFaults.isEmpty()) {
            log.info("遍历完告警信息所有页面，未找到精准匹配IP：{}，总记录数：{}",
                    targetIp, totalRecords != null ? totalRecords : "未知");
        } else {
            log.info("告警信息查询完成，精准匹配IP：{}，共找到{}条匹配记录，总共检查{}条记录",
                    targetIp, allMatchedFaults.size(), totalChecked);
        }

        return allMatchedFaults;
    }

    @Override
    public Long getDeviceIdByIp(ResourceQueryRequestDTO request) {
        try {
            log.info("开始调用资源查询接口，请求参数：{}", JSON.toJSONString(request));

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetDeviceIdByIp(request);
            }

            String url = deviceApiConfig.getBaseUrl() + deviceApiConfig.getResourceQueryUrl();
            log.info("调用资源查询接口URL：{}", url);

            HttpResponse response = HttpRequest.post(url)
                    .header("accept", "application/json;charset=UTF-8")
                    .header("Content-Type", "application/json")
                    .body(JSON.toJSONString(request))
                    .timeout(30000)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("资源查询接口调用成功，响应：{}", responseBody);

                ResourceQueryResponseDTO responseDTO = JSON.parseObject(responseBody, ResourceQueryResponseDTO.class);
                if (responseDTO != null && responseDTO.getCode() == 0 && responseDTO.getData() != null
                        && responseDTO.getData().getData() != null && !responseDTO.getData().getData().isEmpty()) {

                    // 精准匹配IP地址
                    for (ResourceQueryResponseDTO.ResourceItem item : responseDTO.getData().getData()) {
                        if (request.getIp().equals(item.getIp())) {
                            log.info("找到精准匹配IP：{}，设备ID：{}", request.getIp(), item.getId());
                            return item.getId();
                        }
                    }

                    log.warn("未找到精准匹配IP：{}的资源", request.getIp());
                    throw new PlatformApiException("未找到精准匹配IP的资源", "RESOURCE_NOT_FOUND");
                } else {
                    log.warn("资源查询接口返回空结果或失败，IP：{}", request.getIp());
                    throw new PlatformApiException("资源查询接口返回空结果或失败", "EMPTY_RESULT");
                }
            } else {
                String responseBody = response.body();
                log.error("资源查询接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("资源查询接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用资源查询接口异常，IP：{}", request.getIp(), e);
            throw new PlatformApiException("调用资源查询接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public DeviceDetailNewResponseDTO getDeviceDetailById(Long deviceId) {
        try {
            log.info("开始调用设备详情查询接口，设备ID：{}", deviceId);

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetDeviceDetailById(deviceId);
            }

            String url = deviceApiConfig.getBaseUrl() + deviceApiConfig.getDeviceDetailUrl() + "/" + deviceId;
            log.info("调用设备详情查询接口URL：{}", url);

            HttpResponse response = HttpRequest.get(url)
                    .header("Accept", "application/json")
                    .timeout(30000)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("设备详情查询接口调用成功，设备ID：{}，响应：{}", deviceId, responseBody);

                DeviceDetailNewResponseDTO responseDTO = JSON.parseObject(responseBody, DeviceDetailNewResponseDTO.class);
                if (responseDTO != null) {
                    log.info("设备详情查询成功，设备ID：{}，设备名称：{}", deviceId, responseDTO.getDeviceName());
                    return responseDTO;
                } else {
                    log.warn("设备详情查询响应解析失败，设备ID：{}", deviceId);
                    throw new PlatformApiException("设备详情查询响应解析失败", "PARSE_ERROR");
                }
            } else {
                String responseBody = response.body();
                log.error("设备详情查询接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("设备详情查询接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用设备详情查询接口异常，设备ID：{}", deviceId, e);
            throw new PlatformApiException("调用设备详情查询接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PerformanceDataResponseDTO getPerformanceDataByDeviceIdAndTaskId(Long deviceId, Integer taskId) {
        try {
            log.info("开始调用性能数据查询接口，设备ID：{}，任务ID：{}", deviceId, taskId);

            // 测试模式下模拟接口调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetPerformanceDataByDeviceIdAndTaskId(deviceId, taskId);
            }

            String url = deviceApiConfig.getBaseUrl() + deviceApiConfig.getPerformanceDataUrl() + 
                    "?devId=" + deviceId + "&taskId=" + taskId;
            log.info("调用性能数据查询接口URL：{}", url);

            HttpResponse response = HttpRequest.get(url)
                    .header("Accept", "application/json")
                    .timeout(30000)
                    .execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.debug("性能数据查询接口调用成功，设备ID：{}，任务ID：{}，响应：{}", deviceId, taskId, responseBody);

                PerformanceDataResponseDTO responseDTO = JSON.parseObject(responseBody, PerformanceDataResponseDTO.class);
                if (responseDTO != null && responseDTO.getErrorCode() == 0) {
                    log.debug("性能数据查询成功，设备ID：{}，任务ID：{}，数据条数：{}", 
                            deviceId, taskId, responseDTO.getData() != null ? responseDTO.getData().size() : 0);
                    return responseDTO;
                } else {
                    log.warn("性能数据查询响应失败或解析失败，设备ID：{}，任务ID：{}", deviceId, taskId);
                    throw new PlatformApiException("性能数据查询响应失败或解析失败", "QUERY_FAILED");
                }
            } else {
                String responseBody = response.body();
                log.error("性能数据查询接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("性能数据查询接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用性能数据查询接口异常，设备ID：{}，任务ID：{}", deviceId, taskId, e);
            throw new PlatformApiException("调用性能数据查询接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟根据IP获取设备ID
     */
    private Long mockGetDeviceIdByIp(ResourceQueryRequestDTO request) {
        log.info("测试模式：模拟资源查询接口调用，IP：{}", request.getIp());
        return 4166665348641788L; // 模拟设备ID
    }

    /**
     * 模拟根据设备ID查询设备详情
     */
    private DeviceDetailNewResponseDTO mockGetDeviceDetailById(Long deviceId) {
        log.info("测试模式：模拟设备详情查询接口调用，设备ID：{}", deviceId);

        DeviceDetailNewResponseDTO mockResponse = new DeviceDetailNewResponseDTO();
        mockResponse.setDeviceId(deviceId);
        mockResponse.setDeviceIp("************");
        mockResponse.setMac("74:25:8a:e3:ab:73");
        mockResponse.setDeviceName("网络设备_************");
        mockResponse.setSerialNumber("000c298864a398df25061609");
        mockResponse.setIsStack(false);
        mockResponse.setLatestPollTime("2025-06-24 09:33:30");
        mockResponse.setCategoryName("路由器");
        mockResponse.setTypeName("H3C VSR1000");
        mockResponse.setSysVersion("Extended");

        return mockResponse;
    }

    /**
     * 模拟根据设备ID和任务ID查询性能数据
     */
    private PerformanceDataResponseDTO mockGetPerformanceDataByDeviceIdAndTaskId(Long deviceId, Integer taskId) {
        log.info("测试模式：模拟性能数据查询接口调用，设备ID：{}，任务ID：{}", deviceId, taskId);

        PerformanceDataResponseDTO mockResponse = new PerformanceDataResponseDTO();
        mockResponse.setErrorCode(0);
        mockResponse.setErrorMessage("成功");
        mockResponse.setSuccess(true);
        mockResponse.setFail(false);

        java.util.List<PerformanceDataResponseDTO.PerfData> dataList = new java.util.ArrayList<>();

        // 根据taskId返回不同的模拟数据，模拟多实例情况
        switch (taskId) {
            case 2: // CPU利用率 - 通常只有一个实例
                PerformanceDataResponseDTO.PerfData cpuData = new PerformanceDataResponseDTO.PerfData();
                cpuData.setTaskId(taskId);
                cpuData.setInstanceId("[Entity:MPU]");
                cpuData.setInstanceName("[Entity:MPU]");
                cpuData.setDevId(deviceId);
                cpuData.setInstanceData("23");
                cpuData.setInstanceDataUnit("%");
                cpuData.setCollectTime(System.currentTimeMillis() / 1000);
                dataList.add(cpuData);
                break;
                
            case 4: // 内存利用率 - 通常只有一个实例
                PerformanceDataResponseDTO.PerfData memData = new PerformanceDataResponseDTO.PerfData();
                memData.setTaskId(taskId);
                memData.setInstanceId("[Entity:MPU]");
                memData.setInstanceName("[Entity:MPU]");
                memData.setDevId(deviceId);
                memData.setInstanceData("65");
                memData.setInstanceDataUnit("%");
                memData.setCollectTime(System.currentTimeMillis() / 1000);
                dataList.add(memData);
                break;
                
            case 49: // 温度 - 模拟多个温度传感器
                for (int i = 1; i <= 3; i++) {
                    PerformanceDataResponseDTO.PerfData tempData = new PerformanceDataResponseDTO.PerfData();
                    tempData.setTaskId(taskId);
                    tempData.setInstanceId("[Entity:Sensor" + i + "]");
                    tempData.setInstanceName("[Entity:Sensor" + i + "]");
                    tempData.setDevId(deviceId);
                    tempData.setInstanceData(String.valueOf(40 + i * 2)); // 42, 44, 46度
                    tempData.setInstanceDataUnit("°C");
                    tempData.setCollectTime(System.currentTimeMillis() / 1000);
                    dataList.add(tempData);
                }
                break;
                
            case 908: // 电源 - 模拟多个电源模块
                for (int i = 1; i <= 2; i++) {
                    PerformanceDataResponseDTO.PerfData powerData = new PerformanceDataResponseDTO.PerfData();
                    powerData.setTaskId(taskId);
                    powerData.setInstanceId("[Entity:Power" + i + "]");
                    powerData.setInstanceName("[Entity:Power" + i + "]");
                    powerData.setDevId(deviceId);
                    powerData.setInstanceData("1"); // 1表示正常
                    powerData.setInstanceDataUnit("");
                    powerData.setCollectTime(System.currentTimeMillis() / 1000);
                    dataList.add(powerData);
                }
                break;
                
            case 902: // 风扇 - 模拟多个风扇
                for (int i = 1; i <= 4; i++) {
                    PerformanceDataResponseDTO.PerfData fanData = new PerformanceDataResponseDTO.PerfData();
                    fanData.setTaskId(taskId);
                    fanData.setInstanceId("[Entity:Fan" + i + "]");
                    fanData.setInstanceName("[Entity:Fan" + i + "]");
                    fanData.setDevId(deviceId);
                    fanData.setInstanceData(i <= 3 ? "1" : "0"); // 前3个正常，第4个异常
                    fanData.setInstanceDataUnit("");
                    fanData.setCollectTime(System.currentTimeMillis() / 1000);
                    dataList.add(fanData);
                }
                break;
                
            default:
                PerformanceDataResponseDTO.PerfData defaultData = new PerformanceDataResponseDTO.PerfData();
                defaultData.setTaskId(taskId);
                defaultData.setInstanceId("[Entity:Unknown]");
                defaultData.setInstanceName("[Entity:Unknown]");
                defaultData.setDevId(deviceId);
                defaultData.setInstanceData("0");
                defaultData.setInstanceDataUnit("");
                defaultData.setCollectTime(System.currentTimeMillis() / 1000);
                dataList.add(defaultData);
        }

        mockResponse.setData(dataList);
        log.info("测试模式：生成{}条模拟性能数据，任务ID：{}", dataList.size(), taskId);
        return mockResponse;
    }
} 