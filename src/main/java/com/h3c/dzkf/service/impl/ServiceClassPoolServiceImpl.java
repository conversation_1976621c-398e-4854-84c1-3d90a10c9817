package com.h3c.dzkf.service.impl;

import com.h3c.dzkf.dao.ServiceClassPoolMapper;
import com.h3c.dzkf.service.ServiceClassPoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ServiceClass池管理服务实现类
 */
@Slf4j
@Service
public class ServiceClassPoolServiceImpl implements ServiceClassPoolService {

    @Autowired
    private ServiceClassPoolMapper serviceClassPoolMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer allocateServiceClass(Long teGroupId) {
        try {
            log.info("开始为隧道组分配ServiceClass（正反向使用同一个），隧道组ID：{}", teGroupId);

            // 分配一个ServiceClass供正反向Policy共同使用
            Integer serviceClass = getNextAvailableServiceClass();
            if (serviceClass == null) {
                log.error("没有可用的ServiceClass，隧道组ID：{}", teGroupId);
                return null;
            }

            // 分配ServiceClass，policy_direction设置为BOTH表示正反向共用
            int result = serviceClassPoolMapper.allocateServiceClass(
                    serviceClass, teGroupId, "BOTH");
            if (result <= 0) {
                log.error("分配ServiceClass失败，隧道组ID：{}，ServiceClass：{}", teGroupId, serviceClass);
                return null;
            }

            log.info("ServiceClass分配成功（正反向共用），隧道组ID：{}，ServiceClass：{}",
                    teGroupId, serviceClass);

            return serviceClass;

        } catch (Exception e) {
            log.error("分配ServiceClass异常，隧道组ID：{}", teGroupId, e);
            // 发生异常时尝试回滚
            try {
                serviceClassPoolMapper.releaseServiceClassByTeGroupId(teGroupId);
            } catch (Exception rollbackException) {
                log.error("回滚ServiceClass分配异常", rollbackException);
            }
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releaseServiceClass(Long teGroupId) {
        try {
            log.info("开始回收隧道组的ServiceClass，隧道组ID：{}", teGroupId);

            int result = serviceClassPoolMapper.releaseServiceClassByTeGroupId(teGroupId);
            if (result > 0) {
                log.info("ServiceClass回收成功，隧道组ID：{}，回收数量：{}", teGroupId, result);
                return true;
            } else {
                log.warn("没有找到需要回收的ServiceClass，隧道组ID：{}", teGroupId);
                return false;
            }

        } catch (Exception e) {
            log.error("回收ServiceClass异常，隧道组ID：{}", teGroupId, e);
            return false;
        }
    }

    /**
     * 获取下一个可用的ServiceClass
     */
    private Integer getNextAvailableServiceClass() {
        try {
            return serviceClassPoolMapper.getNextAvailableServiceClass();
        } catch (Exception e) {
            log.error("获取可用ServiceClass异常", e);
            return null;
        }
    }
} 