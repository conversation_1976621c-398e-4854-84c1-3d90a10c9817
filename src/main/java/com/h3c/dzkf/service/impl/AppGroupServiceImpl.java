package com.h3c.dzkf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.AppCustomInfoMapper;
import com.h3c.dzkf.dao.AppGroupCustomInfoMapper;
import com.h3c.dzkf.entity.AppCustomInfo;
import com.h3c.dzkf.entity.AppGroupCustomInfo;
import com.h3c.dzkf.entity.dto.AddAppGroupRequestDTO;
import com.h3c.dzkf.entity.dto.ModifyAppGroupRequestDTO;
import com.h3c.dzkf.entity.dto.QueryAppGroupListResponseDTO;
import com.h3c.dzkf.service.AppGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 应用组服务实现类
 */
@Slf4j
@Service
public class AppGroupServiceImpl implements AppGroupService {

    @Autowired
    private AppGroupCustomInfoMapper appGroupCustomInfoMapper;

    @Autowired
    private AppCustomInfoMapper appCustomInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAppGroup(AddAppGroupRequestDTO request, String requestId) {
        log.info("开始新增应用组，请求ID：{}，应用组ID：{}，应用组名称：{}",
                requestId, request.getAppGroupId(), request.getAppGroupName());

        // 1. 检查应用组ID是否已存在
        LambdaQueryWrapper<AppGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppGroupCustomInfo::getAppGroupId, request.getAppGroupId());
        AppGroupCustomInfo existingAppGroup = appGroupCustomInfoMapper.selectOne(queryWrapper);
        if (existingAppGroup != null) {
            log.warn("应用组ID已存在，请求ID：{}，应用组ID：{}", requestId, request.getAppGroupId());
            throw new ServiceException("APP_GROUP_ID_EXISTS", "应用组ID已存在", "应用组ID: " + request.getAppGroupId());
        }

        // 2. 构建应用组信息对象
        AppGroupCustomInfo appGroupCustomInfo = new AppGroupCustomInfo();
        appGroupCustomInfo.setAppGroupId(request.getAppGroupId());
        appGroupCustomInfo.setAppGroupName(request.getAppGroupName());
        appGroupCustomInfo.setCreateTime(new Date());
        appGroupCustomInfo.setUpdateTime(new Date());
        appGroupCustomInfo.setIsDeleted(0);

        // 3. 保存到数据库
        int result = appGroupCustomInfoMapper.insert(appGroupCustomInfo);
        if (result <= 0) {
            log.error("新增应用组失败，请求ID：{}，应用组ID：{}", requestId, request.getAppGroupId());
            throw new ServiceException("APP_GROUP_INSERT_FAILED", "应用组新增失败", "应用组ID: " + request.getAppGroupId());
        }

        log.info("新增应用组成功，请求ID：{}，应用组ID：{}，数据库ID：{}",
                requestId, request.getAppGroupId(), appGroupCustomInfo.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAppGroup(Integer appGroupId, String requestId) {
        log.info("开始删除应用组，请求ID：{}，应用组ID：{}", requestId, appGroupId);

        // 1. 检查应用组是否存在且未被删除
        LambdaQueryWrapper<AppGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppGroupCustomInfo::getAppGroupId, appGroupId);
        AppGroupCustomInfo existingAppGroup = appGroupCustomInfoMapper.selectOne(queryWrapper);
        if (existingAppGroup == null) {
            log.warn("应用组不存在或已被删除，请求ID：{}，应用组ID：{}", requestId, appGroupId);
            throw new ServiceException("APP_GROUP_NOT_EXISTS", "应用组不存在或已被删除", "应用组ID: " + appGroupId);
        }

        // 2. 检查应用组下是否存在关联应用
        // 如果存在应用，则不允许删除，返回提示信息
        LambdaQueryWrapper<AppCustomInfo> appQueryWrapper = new LambdaQueryWrapper<>();
        appQueryWrapper.eq(AppCustomInfo::getAppGroupId, appGroupId);
        List<AppCustomInfo> relatedApps = appCustomInfoMapper.selectList(appQueryWrapper);
        if (!relatedApps.isEmpty()) {
            log.warn("应用组下存在关联应用，无法删除，请求ID：{}，应用组ID：{}，关联应用数量：{}",
                    requestId, appGroupId, relatedApps.size());
            throw new ServiceException("APP_GROUP_HAS_APPS", "应用组下存在关联应用，无法删除",
                    "应用组ID: " + appGroupId + ", 关联应用数量: " + relatedApps.size());
        }

        // 3. 执行软删除
        int result = appGroupCustomInfoMapper.deleteById(existingAppGroup.getId());
        if (result <= 0) {
            log.error("删除应用组失败，请求ID：{}，应用组ID：{}", requestId, appGroupId);
            throw new ServiceException("APP_GROUP_DELETE_FAILED", "应用组删除失败", "应用组ID: " + appGroupId);
        }

        log.info("删除应用组成功，请求ID：{}，应用组ID：{}，数据库ID：{}",
                requestId, appGroupId, existingAppGroup.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyAppGroup(ModifyAppGroupRequestDTO request, String requestId) {
        log.info("开始修改应用组，请求ID：{}，应用组ID：{}，应用组名称：{}",
                requestId, request.getAppGroupId(), request.getAppGroupName());

        // 1. 检查应用组是否存在且未被删除
        LambdaQueryWrapper<AppGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppGroupCustomInfo::getAppGroupId, request.getAppGroupId());
        AppGroupCustomInfo existingAppGroup = appGroupCustomInfoMapper.selectOne(queryWrapper);
        if (existingAppGroup == null) {
            log.warn("应用组不存在或已被删除，请求ID：{}，应用组ID：{}", requestId, request.getAppGroupId());
            throw new ServiceException("APP_GROUP_NOT_EXISTS", "应用组不存在或已被删除", "应用组ID: " + request.getAppGroupId());
        }

        // 2. 更新应用组信息
        existingAppGroup.setAppGroupName(request.getAppGroupName());
        existingAppGroup.setUpdateTime(new Date());

        // 3. 保存到数据库
        int result = appGroupCustomInfoMapper.updateById(existingAppGroup);
        if (result <= 0) {
            log.error("修改应用组失败，请求ID：{}，应用组ID：{}", requestId, request.getAppGroupId());
            throw new ServiceException("APP_GROUP_UPDATE_FAILED", "应用组修改失败", "应用组ID: " + request.getAppGroupId());
        }

        log.info("修改应用组成功，请求ID：{}，应用组ID：{}，数据库ID：{}",
                requestId, request.getAppGroupId(), existingAppGroup.getId());
    }

    @Override
    public QueryAppGroupListResponseDTO queryAppGroupList(String requestId) {
        log.info("开始查询应用组列表，请求ID：{}", requestId);

        // 1. 查询所有未删除的应用组
        LambdaQueryWrapper<AppGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(AppGroupCustomInfo::getCreateTime);
        List<AppGroupCustomInfo> appGroupCustomInfoList = appGroupCustomInfoMapper.selectList(queryWrapper);

        // 2. 转换为响应DTO
        List<QueryAppGroupListResponseDTO.AppGroupInfo> appGroupInfoList = new ArrayList<>();
        if (appGroupCustomInfoList != null && !appGroupCustomInfoList.isEmpty()) {
            for (AppGroupCustomInfo appGroupCustomInfo : appGroupCustomInfoList) {
                QueryAppGroupListResponseDTO.AppGroupInfo appGroupInfo = new QueryAppGroupListResponseDTO.AppGroupInfo();
                appGroupInfo.setAppGroupId(appGroupCustomInfo.getAppGroupId());
                appGroupInfo.setAppGroupName(appGroupCustomInfo.getAppGroupName());
                appGroupInfoList.add(appGroupInfo);
            }
        }

        log.info("查询应用组列表成功，请求ID：{}，数量：{}", requestId, appGroupInfoList.size());
        return QueryAppGroupListResponseDTO.success(requestId, appGroupInfoList);
    }
} 