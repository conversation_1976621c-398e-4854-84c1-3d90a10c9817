package com.h3c.dzkf.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.DeviceCustomInfoMapper;
import com.h3c.dzkf.dao.TeGroupCustomInfoMapper;
import com.h3c.dzkf.dao.TeTunnelTemplateCustomInfoMapper;
import com.h3c.dzkf.entity.DeviceCustomInfo;
import com.h3c.dzkf.entity.TeGroupCustomInfo;
import com.h3c.dzkf.entity.TeTunnelTemplateCustomInfo;
import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.entity.platform.*;
import com.h3c.dzkf.service.PlatformApiService;
import com.h3c.dzkf.service.ServiceClassPoolService;
import com.h3c.dzkf.service.TeGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 隧道组服务实现类
 */
@Slf4j
@Service
public class TeGroupServiceImpl implements TeGroupService {

    @Autowired
    private TeGroupCustomInfoMapper teGroupCustomInfoMapper;

    @Autowired
    private TeTunnelTemplateCustomInfoMapper teTunnelTemplateCustomInfoMapper;

    @Autowired
    private PlatformApiService platformApiService;

    @Autowired
    private ServiceClassPoolService serviceClassPoolService;

    @Autowired
    private DeviceCustomInfoMapper deviceCustomInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTeGroup(AddTeGroupRequestDTO request, String requestId) {
        AddTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        log.info("开始处理新增隧道组请求，隧道组ID：{}，请求ID：{}", teGroupDto.getId(), requestId);

        // 1. 参数验证
        validateAddTeGroupRequest(request);

        // 2. 检查隧道组ID是否重复
        LambdaQueryWrapper<TeGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeGroupCustomInfo::getTeGroupId, teGroupDto.getId());
        TeGroupCustomInfo existingGroup = teGroupCustomInfoMapper.selectOne(queryWrapper);
        if (existingGroup != null) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组ID已存在", "隧道组ID：" + teGroupDto.getId());
        }

        // 3. 查询隧道模板信息
        TeTunnelTemplateCustomInfo tunnelTemplate = getTunnelTemplateById(teGroupDto.getScheduleStrategyId());
        if (tunnelTemplate == null) {
            throw new ServiceException("VALIDATION_ERROR", "隧道模板不存在", "模板ID：" + teGroupDto.getScheduleStrategyId());
        }

        // 4. 分配ServiceClass（正反向使用同一个）
        Integer serviceClass = serviceClassPoolService.allocateServiceClass(teGroupDto.getId());
        if (serviceClass == null) {
            // TODO 这里要考虑循环使用的逻辑，而不是返回资源不足
            throw new ServiceException("RESOURCE_ERROR", "ServiceClass资源不足", "隧道组ID：" + teGroupDto.getId());
        }

        try {
            // 创建ServiceClassPair对象以保持兼容性（正反向使用同一个ServiceClass）
            ServiceClassPoolService.ServiceClassPair serviceClassPair = new ServiceClassPoolService.ServiceClassPair(serviceClass);

            // 5. 获取BFD模板ID（必填参数）
            Long bfdTemplateId = getBfdTemplateId();
            if (bfdTemplateId == null) {
                throw new ServiceException("PLATFORM_ERROR", "获取BFD模板ID失败", "隧道组ID：" + teGroupDto.getId());
            }

            // 6. 调用平台接口创建SRv6 Policy应用组，PlatformApiException会被全局异常处理器处理
            PlatformAddSrv6PolicyGroupResponseDTO platformResponse = createSrv6PolicyGroup(request, tunnelTemplate, serviceClassPair, bfdTemplateId);
            if (platformResponse == null || !platformResponse.isSuccess()) {
                throw new ServiceException("PLATFORM_ERROR", "平台创建应用组失败",
                        "隧道组ID：" + teGroupDto.getId() + "，返回码：" + (platformResponse != null ? platformResponse.getCode() : "null"));
            }

            // 7. 查询应用组信息获取dataId，PlatformApiException会被全局异常处理器处理
            String platformGroupId = getPlatformGroupIdByName(teGroupDto.getName());
            if (platformGroupId == null) {
                throw new ServiceException("PLATFORM_ERROR", "查询平台应用组ID失败", "隧道组名称：" + teGroupDto.getName());
            }

            // 8. 处理隧道组作用域
            if (!CollectionUtils.isEmpty(teGroupDto.getTeGroupDcs())) {
                boolean scopeResult = updatePlatformScopeNetwork(request, platformGroupId);
                if (!scopeResult) {
                    throw new ServiceException("PLATFORM_ERROR", "更新隧道组作用域到平台失败", "隧道组ID：" + teGroupDto.getId());
                }
            }

            // 9. 保存隧道组信息
            TeGroupCustomInfo teGroup = createTeGroupEntity(request, platformGroupId, serviceClassPair);
            int insertResult = teGroupCustomInfoMapper.insert(teGroup);
            if (insertResult <= 0) {
                throw new ServiceException("DATABASE_ERROR", "保存隧道组信息失败", "隧道组ID：" + teGroupDto.getId());
            }

            log.info("新增隧道组成功，隧道组ID：{}，平台应用组ID：{}", teGroupDto.getId(), platformGroupId);

        } catch (Exception e) {
            // 发生异常时回收ServiceClass
            serviceClassPoolService.releaseServiceClass(teGroupDto.getId());
            throw e; // 重新抛出异常，让全局异常处理器处理
        }
    }

    /**
     * 通过应用组名称查询平台应用组ID
     *
     * @param groupName 应用组名称
     * @return 平台应用组ID，查询失败抛出异常
     */
    private String getPlatformGroupIdByName(String groupName) {
        GetSrv6PolicyGroupRequestDTO queryRequest = new GetSrv6PolicyGroupRequestDTO();
        queryRequest.setGroupName(groupName);
        queryRequest.setPageNum(1L);
        queryRequest.setPageSize(10L);
        queryRequest.setSortOrder(-1); // 降序排列，新增的应用组在最前面

        // 调用平台接口查询应用组，PlatformApiException会被全局异常处理器处理
        GetSrv6PolicyGroupResponseDTO queryResponse = platformApiService.getSrv6PolicyGroup(queryRequest);

        if (queryResponse != null && queryResponse.isSuccess() && queryResponse.getResult() != null) {
            List<GetSrv6PolicyGroupResponseDTO.VasSrv6PolicyGroupVO> records = queryResponse.getResult().getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                // 查找匹配的应用组
                for (GetSrv6PolicyGroupResponseDTO.VasSrv6PolicyGroupVO group : records) {
                    if (groupName.equals(group.getGroupName())) {
                        log.info("成功查询到平台应用组ID：{}，应用组名称：{}", group.getDataId(), groupName);
                        return String.valueOf(group.getDataId());
                    }
                }
            }
        }

        // 未查询到匹配的平台应用组，抛出异常
        throw new ServiceException("PLATFORM_ERROR", "未查询到匹配的平台应用组", "应用组名称：" + groupName);
    }

    /**
     * 查询BFD模板ID
     *
     * @return BFD模板ID，查询失败抛出异常
     */
    private Long getBfdTemplateId() {
        GetBfdTemplateRequestDTO queryRequest = new GetBfdTemplateRequestDTO();
        queryRequest.setPageNum(1L);
        queryRequest.setPageSize(1L); // 只获取第一条数据

        // 调用平台接口查询BFD模板，PlatformApiException会被全局异常处理器处理
        GetBfdTemplateResponseDTO queryResponse = platformApiService.getBfdTemplate(queryRequest);

        if (queryResponse != null && queryResponse.isSuccess() && queryResponse.getResult() != null) {
            List<GetBfdTemplateResponseDTO.GetBfdTemplateVO> records = queryResponse.getResult().getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                Long templateId = records.get(0).getDataId();
                log.info("成功查询到BFD模板ID：{}", templateId);
                return templateId;
            }
        }

        // 未查询到BFD模板，抛出异常
        throw new ServiceException("PLATFORM_ERROR", "未查询到BFD模板，无法获取必需的MainTemplateId和BackupTemplateId", "查询结果为空");
    }

    /**
     * 根据设备ID查询平台节点ID
     *
     * @param deviceId 设备ID
     * @return 平台节点ID，查询失败抛出异常
     */
    private Long getPlatformNodeIdByDeviceId(Long deviceId) {
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
        queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0);

        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);
        if (deviceInfo != null && deviceInfo.getPlatformNodeId() != null) {
            try {
                Long platformNodeId = Long.valueOf(deviceInfo.getPlatformNodeId());
                log.debug("成功查询到平台节点ID：{}，设备ID：{}", platformNodeId, deviceId);
                return platformNodeId;
            } catch (NumberFormatException e) {
                throw new ServiceException("DATA_ERROR", "平台节点ID格式错误", "设备ID：" + deviceId + "，平台节点ID：" + deviceInfo.getPlatformNodeId());
            }
        }

        // 未查询到设备对应的平台节点ID，抛出异常
        throw new ServiceException("DATA_ERROR", "未查询到设备对应的平台节点ID", "设备ID：" + deviceId);
    }

    /**
     * 验证新增隧道组请求参数
     */
    private void validateAddTeGroupRequest(AddTeGroupRequestDTO request) {
        AddTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        // 如果按设备配置，检查设备信息
        if (!teGroupDto.getIsVirtualNet()) {
            if (CollectionUtils.isEmpty(teGroupDto.getTeGroupDcs())) {
                throw new ServiceException("VALIDATION_ERROR", "按设备配置时，隧道组源目的信息不能为空", "隧道组ID：" + teGroupDto.getId());
            }

            // 验证设备ID和名称列表长度一致
            for (AddTeGroupRequestDTO.TeGroupDcInfo dcInfo : teGroupDto.getTeGroupDcs()) {
                if (CollectionUtils.isEmpty(dcInfo.getSrcDeviceIds()) || CollectionUtils.isEmpty(dcInfo.getSrcDeviceNames()) ||
                    dcInfo.getSrcDeviceIds().size() != dcInfo.getSrcDeviceNames().size()) {
                    throw new ServiceException("VALIDATION_ERROR", "源设备ID和名称列表长度不一致", "隧道组ID：" + teGroupDto.getId());
                }

                if (CollectionUtils.isEmpty(dcInfo.getDstDeviceIds()) || CollectionUtils.isEmpty(dcInfo.getDstDeviceNames()) ||
                    dcInfo.getDstDeviceIds().size() != dcInfo.getDstDeviceNames().size()) {
                    throw new ServiceException("VALIDATION_ERROR", "目的设备ID和名称列表长度不一致", "隧道组ID：" + teGroupDto.getId());
                }
            }
        } else {
            // 如果按业务网络配置，检查业务网络ID
            if (teGroupDto.getVirtualNetId() == null) {
                throw new ServiceException("VALIDATION_ERROR", "按业务网络配置时，业务网络ID不能为空", "隧道组ID：" + teGroupDto.getId());
            }
        }
    }

    /**
     * 根据ID查询隧道模板
     */
    private TeTunnelTemplateCustomInfo getTunnelTemplateById(Long templateId) {
        LambdaQueryWrapper<TeTunnelTemplateCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeTunnelTemplateCustomInfo::getStrategyId, templateId);
        return teTunnelTemplateCustomInfoMapper.selectOne(queryWrapper);
    }

    /**
     * 创建SRv6 Policy应用组
     */
    private PlatformAddSrv6PolicyGroupResponseDTO createSrv6PolicyGroup(AddTeGroupRequestDTO request,
                                                                          TeTunnelTemplateCustomInfo tunnelTemplate,
                                                                          ServiceClassPoolService.ServiceClassPair serviceClassPair,
                                                                          Long bfdTemplateId) {

        AddTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        PlatformAddSrv6PolicyGroupRequestDTO platformRequest = new PlatformAddSrv6PolicyGroupRequestDTO();

        // 基本信息
        platformRequest.setGroupName(teGroupDto.getName());
        //platformRequest.setDescription("隧道组：" + teGroupDto.getName());
        platformRequest.setNetworkingModel(3); // 固定值为3
        // 主备模式：候选路径数=2，负载模式：候选路径数=1
        platformRequest.setCandipathNum(teGroupDto.getBalanceMode() == 1 ? 2 : 1);
        platformRequest.setDirection(2); // 固定值：2
        platformRequest.setCoRouted(teGroupDto.getBalanceMode() == 1); // 主备模式为true，负载模式为false
        platformRequest.setCalcLimit("METRIC"); // 固定METRIC

        // 创建Policy参数列表（正向和反向各一条）
        List<PlatformAddSrv6PolicyGroupRequestDTO.AddSrv6PolicyGroupParamsDTO> paramsList = new ArrayList<>();
        
        // 正向Policy
        PlatformAddSrv6PolicyGroupRequestDTO.AddSrv6PolicyGroupParamsDTO positiveParams = createPolicyParams(
            request, tunnelTemplate, serviceClassPair.getPositiveServiceClass(), true, bfdTemplateId);
        paramsList.add(positiveParams);
        
        // 反向Policy
        PlatformAddSrv6PolicyGroupRequestDTO.AddSrv6PolicyGroupParamsDTO negativeParams = createPolicyParams(
            request, tunnelTemplate, serviceClassPair.getNegativeServiceClass(), false, bfdTemplateId);
        paramsList.add(negativeParams);
        
        platformRequest.setSrv6PolicyGroupParamsList(paramsList);

        return platformApiService.addSrv6PolicyGroup(platformRequest);
    }

    /**
     * 创建Policy参数
     */
    private PlatformAddSrv6PolicyGroupRequestDTO.AddSrv6PolicyGroupParamsDTO createPolicyParams(
            AddTeGroupRequestDTO request, TeTunnelTemplateCustomInfo tunnelTemplate,
            Integer serviceClass, boolean isPositive, Long bfdTemplateId) {

        AddTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        PlatformAddSrv6PolicyGroupRequestDTO.AddSrv6PolicyGroupParamsDTO params = new PlatformAddSrv6PolicyGroupRequestDTO.AddSrv6PolicyGroupParamsDTO();

        params.setColorId(Long.valueOf(teGroupDto.getColor()));
        params.setServiceClass(serviceClass);
        params.setPositive(isPositive);
        
        // 从隧道模板映射参数
        if (tunnelTemplate.getBandwidth() != null) {
            // 单位转换：隧道模板中的带宽单位是bps，平台接口需要kbps，所以除以1000
            params.setBandwidth((int) (tunnelTemplate.getBandwidth() / 1000));
        }
        
        // SLA等级映射：EF(5)、AF4(4)、AF3(3)、AF2(2)、AF1(1)、BE(0)
        if (tunnelTemplate.getSlaId() != null) {
            params.setPriority(mapSlaToPriority(tunnelTemplate.getSlaId()));
        }
        
        if (tunnelTemplate.getPacketLossRate() != null) {
            try {
                params.setMaxPacketLossRate(Long.valueOf(tunnelTemplate.getPacketLossRate()));
            } catch (NumberFormatException e) {
                throw new ServiceException("DATA_ERROR", "隧道模板丢包率格式错误", "丢包率：" + tunnelTemplate.getPacketLossRate());
            }
        }

        if (tunnelTemplate.getNetworkJitter() != null) {
            try {
                params.setMaxJitter(Long.parseLong(tunnelTemplate.getNetworkJitter()));
            } catch (NumberFormatException e) {
                throw new ServiceException("DATA_ERROR", "隧道模板网络抖动格式错误", "网络抖动：" + tunnelTemplate.getNetworkJitter());
            }
        }

        if (tunnelTemplate.getDelayTime() != null) {
            try {
                params.setMaxDelay(Long.parseLong(tunnelTemplate.getDelayTime()));
            } catch (NumberFormatException e) {
                throw new ServiceException("DATA_ERROR", "隧道模板延迟时间格式错误", "延迟时间：" + tunnelTemplate.getDelayTime());
            }
        }
        
        // 固定值配置
        params.setSoftLocking(false);
        params.setStatisticEnable(true);
        // 主备模式：hotStandbyEnable=true，负载模式：hotStandbyEnable=false
        params.setHotStandbyEnable(teGroupDto.getBalanceMode() == 1);
        params.setBypassEnable("DEFAULT");
        params.setGsrv6Enable(false);
        params.setCurrentBandwidthEnable(true);
        params.setBfdEnable(true);
        params.setBfdType("BFD_ECHO");
        params.setTrigPathEnable(-1);

        // BFD模板配置（mainTemplateId为必填）
        params.setMainTemplateId(bfdTemplateId);
        // 当主备模式时，才需要backupTemplateId
        if (teGroupDto.getBalanceMode() == 1) {
            params.setBackupTemplateId(bfdTemplateId);
        }
        log.debug("设置BFD模板ID - mainTemplateId: {}, backupTemplateId: {}", bfdTemplateId, bfdTemplateId);
        
        // 候选路径约束
        List<PlatformAddSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity> constraints = new ArrayList<>();
        
        // 候选路径1
        PlatformAddSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity constraint1 =
            new PlatformAddSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity();
        constraint1.setPositive(isPositive);
        constraint1.setPathIndex(0);
        if (tunnelTemplate.getUpAllowLinkPriority() != null) {
            Integer preferColor = mapPriorityToNumber(tunnelTemplate.getUpAllowLinkPriority());
            if (preferColor != null) {
                constraint1.setPreferColor(preferColor);
            }
        }
        // 解析 downAllowLinkPriority JSON 字符串
        List<String> downAllowLinkPriorityList = parseDownAllowLinkPriority(tunnelTemplate.getDownAllowLinkPriority());
        if (!downAllowLinkPriorityList.isEmpty()) {
            List<Integer> includeAffinityAny = new ArrayList<>();
            for (String priority : downAllowLinkPriorityList) {
                Integer priorityNumber = mapPriorityToNumber(priority);
                if (priorityNumber != null) {
                    includeAffinityAny.add(priorityNumber);
                }
            }
            if (!includeAffinityAny.isEmpty()) {
                constraint1.setIncludeAffinityAny(includeAffinityAny);
            }
        }
        if (teGroupDto.getHopLimit() != null) {
            constraint1.setHopLimit(teGroupDto.getHopLimit());
        }
        // 主备模式：initSidPathNum和maxSidPathNum=1，负载模式：initSidPathNum和maxSidPathNum=pathSize
        constraint1.setInitSidPathNum(teGroupDto.getBalanceMode() == 1 ? 1 : teGroupDto.getPathSize());
        constraint1.setMaxSidPathNum(teGroupDto.getBalanceMode() == 1 ? 1 : teGroupDto.getPathSize());
        constraints.add(constraint1);

        // 主备模式时才添加候选路径2
        if (teGroupDto.getBalanceMode() == 1) {
            PlatformAddSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity constraint2 =
                    new PlatformAddSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity();
            constraint2.setPositive(isPositive);
            constraint2.setPathIndex(1);
            // 解析 downAllowLinkPriority JSON 字符串
            List<String> downAllowLinkPriorityList2 = parseDownAllowLinkPriority(tunnelTemplate.getDownAllowLinkPriority());
            if (!downAllowLinkPriorityList2.isEmpty()) {
                List<Integer> includeAffinityAny = new ArrayList<>();
                for (String priority : downAllowLinkPriorityList2) {
                    Integer priorityNumber = mapPriorityToNumber(priority);
                    if (priorityNumber != null) {
                        includeAffinityAny.add(priorityNumber);
                    }
                }
                if (!includeAffinityAny.isEmpty()) {
                    constraint2.setIncludeAffinityAny(includeAffinityAny);
                }
            }
            if (teGroupDto.getHopLimit() != null) {
                constraint2.setHopLimit(teGroupDto.getHopLimit());
            }
            // 主备模式：initSidPathNum和maxSidPathNum=1
            constraint2.setInitSidPathNum(1);
            constraint2.setMaxSidPathNum(1);
            constraints.add(constraint2);
        }
        
        params.setCandiPathConstraints(constraints);
        
        return params;
    }

    /**
     * SLA等级映射到优先级
     */
    private Integer mapSlaToPriority(String slaId) {
        switch (slaId.toUpperCase()) {
            case "EF": return 5;
            case "AF4": return 4;
            case "AF3": return 3;
            case "AF2": return 2;
            case "AF1": return 1;
            case "BE": return 0;
            default: return 0;
        }
    }

    /**
     * 创建隧道组实体
     */
    private TeGroupCustomInfo createTeGroupEntity(AddTeGroupRequestDTO request, String platformGroupId,
                                                   ServiceClassPoolService.ServiceClassPair serviceClassPair) {
        AddTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        TeGroupCustomInfo teGroup = new TeGroupCustomInfo();

        teGroup.setTeGroupId(teGroupDto.getId());
        teGroup.setName(teGroupDto.getName());
        teGroup.setPlatformGroupId(platformGroupId);
        teGroup.setIsLoose(teGroupDto.getIsLoose());
        teGroup.setBalanceMode(teGroupDto.getBalanceMode());
        teGroup.setBalanceProportion(teGroupDto.getBalanceProportion());
        teGroup.setScheduleStrategyId(teGroupDto.getScheduleStrategyId());
        teGroup.setColor(teGroupDto.getColor());
        teGroup.setPathSize(teGroupDto.getPathSize());
        teGroup.setPlaneRoutingType(teGroupDto.getPlaneRoutingType());
        teGroup.setIsVirtualNet(teGroupDto.getIsVirtualNet());
        teGroup.setVirtualNetId(String.valueOf(teGroupDto.getVirtualNetId()));
        teGroup.setHopLimit(teGroupDto.getHopLimit());
        teGroup.setPositiveServiceClass(serviceClassPair.getPositiveServiceClass());
        teGroup.setNegativeServiceClass(serviceClassPair.getNegativeServiceClass());

        // 如果配置方式为按设备，存储teGroupDcs信息为JSON
        if (!CollectionUtils.isEmpty(teGroupDto.getTeGroupDcs())) {
            String teGroupDcsJson = JSON.toJSONString(teGroupDto.getTeGroupDcs());
            teGroup.setTeGroupDcs(teGroupDcsJson);
            log.debug("存储隧道组设备配置信息为JSON：{}", teGroupDcsJson);
        }
        
        teGroup.setCreateTime(new Date());
        teGroup.setUpdateTime(new Date());
        teGroup.setIsDeleted(0);
        
        return teGroup;
    }

    /**
     * 更新平台作用域网络
     */
    private boolean updatePlatformScopeNetwork(AddTeGroupRequestDTO request, String platformGroupId) {
        PlatformUpdateSrv6PolicyGroupNetworkRequestDTO platformRequest =
                new PlatformUpdateSrv6PolicyGroupNetworkRequestDTO();

        platformRequest.setGroupId(Long.valueOf(platformGroupId));
        platformRequest.setNetworkingModel(3);

        List<PlatformUpdateSrv6PolicyGroupNetworkRequestDTO.NetworkInfo> networkList = new ArrayList<>();

        // 每个teGroupDcs元素对应一个NetworkInfo
        for (AddTeGroupRequestDTO.TeGroupDcInfo dcInfo : request.getTeGroupDto().getTeGroupDcs()) {
            PlatformUpdateSrv6PolicyGroupNetworkRequestDTO.NetworkInfo networkInfo =
                    new PlatformUpdateSrv6PolicyGroupNetworkRequestDTO.NetworkInfo();

            networkInfo.setNodeType(3);

            // 转换源设备ID列表为平台节点ID列表
            List<Long> srcNodes = new ArrayList<>();
            for (Long deviceId : dcInfo.getSrcDeviceIds()) {
                Long platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
                srcNodes.add(platformNodeId);
            }
            networkInfo.setSrcNodes(srcNodes);

            // 转换目的设备ID列表为平台节点ID列表
            List<Long> dstNodes = new ArrayList<>();
            for (Long deviceId : dcInfo.getDstDeviceIds()) {
                Long platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
                dstNodes.add(platformNodeId);
            }
            networkInfo.setDstNodes(dstNodes);

            networkList.add(networkInfo);

            log.debug("创建NetworkInfo - srcNodes: {}, dstNodes: {}", srcNodes, dstNodes);
        }

        platformRequest.setNetworkList(networkList);

        log.info("调用平台更新作用域网络接口，NetworkInfo数量：{}", networkList.size());
        // 调用平台接口，PlatformApiException会被全局异常处理器处理
        return platformApiService.updateSrv6PolicyGroupNetwork(platformRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTeGroup(DeleteTeGroupRequestDTO request, String requestId) {
        log.info("开始处理删除隧道组请求，隧道组ID集合：{}，请求ID：{}", request.getTeGroupIds(), requestId);

        // 1. 参数验证
        if (request.getTeGroupIds() == null || request.getTeGroupIds().isEmpty()) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组ID集合不能为空", "请求ID：" + requestId);
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder failReasons = new StringBuilder();

        // 2. 逐个处理隧道组删除
        for (Long teGroupId : request.getTeGroupIds()) {
            try {
                log.info("开始删除隧道组，隧道组ID：{}，请求ID：{}", teGroupId, requestId);

                // 2.1 查询隧道组信息
                LambdaQueryWrapper<TeGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TeGroupCustomInfo::getTeGroupId, teGroupId)
                        .eq(TeGroupCustomInfo::getIsDeleted, 0);
                TeGroupCustomInfo teGroup = teGroupCustomInfoMapper.selectOne(queryWrapper);

                if (teGroup == null) {
                    log.warn("隧道组不存在或已被删除，隧道组ID：{}，请求ID：{}", teGroupId, requestId);
                    failCount++;
                    if (failReasons.length() > 0) {
                        failReasons.append("; ");
                    }
                    failReasons.append("隧道组").append(teGroupId).append("不存在或已被删除");
                    continue;
                }

                // 2.2 调用平台接口删除SRv6 Policy应用组，PlatformApiException会被全局异常处理器处理
                if (teGroup.getPlatformGroupId() != null) {
                    boolean platformDeleteResult = platformApiService.deleteSrv6PolicyGroup(teGroup.getPlatformGroupId());
                    if (!platformDeleteResult) {
                        log.error("平台删除SRv6 Policy应用组失败，隧道组ID：{}，平台应用组ID：{}，请求ID：{}",
                                teGroupId, teGroup.getPlatformGroupId(), requestId);
                        failCount++;
                        if (failReasons.length() > 0) {
                            failReasons.append("; ");
                        }
                        failReasons.append("隧道组").append(teGroupId).append("平台删除失败");
                        continue;
                    }
                    log.info("平台删除SRv6 Policy应用组成功，隧道组ID：{}，平台应用组ID：{}", teGroupId, teGroup.getPlatformGroupId());
                }

                // 2.3 回收ServiceClass资源
                boolean serviceClassReleased = serviceClassPoolService.releaseServiceClass(teGroupId);
                if (!serviceClassReleased) {
                    log.warn("回收ServiceClass失败，隧道组ID：{}，请求ID：{}", teGroupId, requestId);
                    // ServiceClass回收失败不影响删除流程，只记录警告
                } else {
                    log.info("ServiceClass回收成功，隧道组ID：{}", teGroupId);
                }

                // 2.4 软删除本地数据库记录
                int deleteResult = teGroupCustomInfoMapper.deleteById(teGroup.getId());
                if (deleteResult > 0) {
                    log.info("删除隧道组成功，隧道组ID：{}，数据库ID：{}，请求ID：{}",
                            teGroupId, teGroup.getId(), requestId);
                    successCount++;
                } else {
                    log.error("删除隧道组失败，隧道组ID：{}，请求ID：{}", teGroupId, requestId);
                    failCount++;
                    if (failReasons.length() > 0) {
                        failReasons.append("; ");
                    }
                    failReasons.append("隧道组").append(teGroupId).append("数据库删除失败");
                }

            } catch (Exception e) {
                log.error("删除隧道组异常，隧道组ID：{}，请求ID：{}", teGroupId, requestId, e);
                failCount++;
                if (failReasons.length() > 0) {
                    failReasons.append("; ");
                }
                failReasons.append("隧道组").append(teGroupId).append("删除异常：").append(e.getMessage());
            }
        }

        // 3. 检查处理结果，如果有失败则抛出异常
        if (failCount > 0) {
            if (successCount == 0) {
                log.error("所有隧道组删除失败，失败数量：{}，请求ID：{}", failCount, requestId);
                throw new ServiceException("OPERATION_ERROR", "所有隧道组删除失败", failReasons.toString());
            } else {
                log.warn("部分隧道组删除成功，成功数量：{}，失败数量：{}，请求ID：{}", successCount, failCount, requestId);
                throw new ServiceException("OPERATION_ERROR", "部分隧道组删除失败", "成功：" + successCount + "，失败：" + failCount + "，失败原因：" + failReasons.toString());
            }
        }

        log.info("所有隧道组删除成功，成功数量：{}，请求ID：{}", successCount, requestId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deployTeGroup(DeployTeGroupRequestDTO request, String requestId) {
        log.info("开始处理部署隧道组请求，隧道组ID集合：{}，是否下发配置：{}，请求ID：{}",
                request.getTeGroupIds(), request.getDistribute(), requestId);

        // 1. 参数验证
        if (request.getTeGroupIds() == null || request.getTeGroupIds().isEmpty()) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组ID集合不能为空", "请求ID：" + requestId);
        }

        // 设置默认值
        if (request.getDistribute() == null) {
            request.setDistribute(true);
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder failReasons = new StringBuilder();

        // 2. 逐个处理隧道组部署
        for (Long teGroupId : request.getTeGroupIds()) {
            try {
                log.info("开始部署隧道组，隧道组ID：{}，请求ID：{}", teGroupId, requestId);

                // 2.1 查询隧道组信息
                LambdaQueryWrapper<TeGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(TeGroupCustomInfo::getTeGroupId, teGroupId)
                        .eq(TeGroupCustomInfo::getIsDeleted, 0);
                TeGroupCustomInfo teGroup = teGroupCustomInfoMapper.selectOne(queryWrapper);

                if (teGroup == null) {
                    log.warn("隧道组不存在或已被删除，隧道组ID：{}，请求ID：{}", teGroupId, requestId);
                    failCount++;
                    if (failReasons.length() > 0) {
                        failReasons.append("; ");
                    }
                    failReasons.append("隧道组").append(teGroupId).append("不存在或已被删除");
                    continue;
                }

                if (teGroup.getPlatformGroupId() == null || teGroup.getPlatformGroupId().isEmpty()) {
                    log.warn("隧道组未关联平台应用组，隧道组ID：{}，请求ID：{}", teGroupId, requestId);
                    failCount++;
                    if (failReasons.length() > 0) {
                        failReasons.append("; ");
                    }
                    failReasons.append("隧道组").append(teGroupId).append("未关联平台应用组");
                    continue;
                }

                // 2.2 调用平台接口规划应用组路径，PlatformApiException会被全局异常处理器处理
                StartPlanSrv6PolicyGroupDTO planRequest = new StartPlanSrv6PolicyGroupDTO();
                planRequest.setGroupId(Long.valueOf(teGroup.getPlatformGroupId()));

                boolean planResult = platformApiService.startPlanSrv6PolicyGroup(planRequest);
                if (!planResult) {
                    log.error("平台规划应用组路径失败，隧道组ID：{}，平台应用组ID：{}，请求ID：{}",
                            teGroupId, teGroup.getPlatformGroupId(), requestId);
                    failCount++;
                    if (failReasons.length() > 0) {
                        failReasons.append("; ");
                    }
                    failReasons.append("隧道组").append(teGroupId).append("规划失败");
                    continue;
                }
                log.info("平台规划应用组路径成功，隧道组ID：{}，平台应用组ID：{}", teGroupId, teGroup.getPlatformGroupId());

                // 2.3 根据distribute参数决定是否部署
                if (request.getDistribute()) {
                    // 调用平台接口部署应用组配置，PlatformApiException会被全局异常处理器处理
                    DeploySrv6PolicyGroupDTO deployRequest = new DeploySrv6PolicyGroupDTO();
                    deployRequest.setGroupId(Long.valueOf(teGroup.getPlatformGroupId()));

                    boolean deployResult = platformApiService.deploySrv6PolicyGroup(deployRequest);
                    if (!deployResult) {
                        log.error("平台部署应用组配置失败，隧道组ID：{}，平台应用组ID：{}，请求ID：{}",
                                teGroupId, teGroup.getPlatformGroupId(), requestId);
                        failCount++;
                        if (failReasons.length() > 0) {
                            failReasons.append("; ");
                        }
                        failReasons.append("隧道组").append(teGroupId).append("部署失败");
                        continue;
                    }
                    log.info("平台部署应用组配置成功，隧道组ID：{}，平台应用组ID：{}", teGroupId, teGroup.getPlatformGroupId());
                } else {
                    log.info("仅执行规划，跳过部署，隧道组ID：{}，平台应用组ID：{}", teGroupId, teGroup.getPlatformGroupId());
                }

                log.info("隧道组部署成功，隧道组ID：{}，请求ID：{}", teGroupId, requestId);
                successCount++;

            } catch (Exception e) {
                log.error("部署隧道组异常，隧道组ID：{}，请求ID：{}", teGroupId, requestId, e);
                failCount++;
                if (failReasons.length() > 0) {
                    failReasons.append("; ");
                }
                failReasons.append("隧道组").append(teGroupId).append("部署异常：").append(e.getMessage());
            }
        }

        // 3. 检查处理结果，如果有失败则抛出异常
        if (failCount > 0) {
            if (successCount == 0) {
                log.error("所有隧道组部署失败，失败数量：{}，请求ID：{}", failCount, requestId);
                throw new ServiceException("OPERATION_ERROR", "所有隧道组部署失败", failReasons.toString());
            } else {
                log.warn("部分隧道组部署成功，成功数量：{}，失败数量：{}，请求ID：{}", successCount, failCount, requestId);
                throw new ServiceException("OPERATION_ERROR", "部分隧道组部署失败", "成功：" + successCount + "，失败：" + failCount + "，失败原因：" + failReasons.toString());
            }
        }

        log.info("所有隧道组部署成功，成功数量：{}，请求ID：{}", successCount, requestId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyTeGroup(ModifyTeGroupRequestDTO request, String requestId) {
        ModifyTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        log.info("开始处理修改隧道组请求，隧道组ID：{}，请求ID：{}", teGroupDto.getId(), requestId);

        // 1. 参数验证
        validateModifyTeGroupRequest(request);

        // 2. 查询原有隧道组信息
        LambdaQueryWrapper<TeGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeGroupCustomInfo::getTeGroupId, teGroupDto.getId());
        TeGroupCustomInfo existingGroup = teGroupCustomInfoMapper.selectOne(queryWrapper);
        if (existingGroup == null) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组不存在", "隧道组ID：" + teGroupDto.getId());
        }

        if (!existingGroup.getBalanceMode().equals(teGroupDto.getBalanceMode())) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组路径模式不允许修改", "隧道组ID：" + teGroupDto.getId());
        }

        if (!existingGroup.getColor().equals(teGroupDto.getColor())) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组color值不允许修改", "隧道组ID：" + teGroupDto.getId());
        }

        if (!existingGroup.getPathSize().equals(teGroupDto.getPathSize())) {
            throw new ServiceException("VALIDATION_ERROR", "隧道组路径条数不允许修改", "隧道组ID：" + teGroupDto.getId());
        }

        // 3. 查询隧道模板信息
        TeTunnelTemplateCustomInfo tunnelTemplate = getTunnelTemplateById(teGroupDto.getScheduleStrategyId());
        if (tunnelTemplate == null) {
            throw new ServiceException("VALIDATION_ERROR", "隧道模板不存在", "模板ID：" + teGroupDto.getScheduleStrategyId());
        }

        // 4. 查询原有应用组详情，PlatformApiException会被全局异常处理器处理
        GetSrv6PolicyGroupDetailRequestDTO detailRequest = new GetSrv6PolicyGroupDetailRequestDTO();
        detailRequest.setGroupId(Long.valueOf(existingGroup.getPlatformGroupId()));
        GetSrv6PolicyGroupDetailResponseDTO detailResponse = platformApiService.getSrv6PolicyGroupDetail(detailRequest);

        if (detailResponse == null || !detailResponse.isSuccess()) {
            throw new ServiceException("PLATFORM_ERROR", "查询平台应用组详情失败",
                    "隧道组ID：" + teGroupDto.getId() + "，平台应用组ID：" + existingGroup.getPlatformGroupId());
        }

        // 5. 查询BFD模板ID（使用默认BFD模板）
        /*Long bfdTemplateId = getBfdTemplateId();
        if (bfdTemplateId == null) {
            throw new ServiceException("PLATFORM_ERROR", "BFD模板不存在", "隧道组ID：" + teGroupDto.getId());
        }*/

        // 6. 构建更新应用组请求
        UpdateSrv6PolicyGroupRequestDTO updateRequest = buildUpdateSrv6PolicyGroupRequest(
                request, detailResponse.getResult(), tunnelTemplate);

        // 7. 调用平台接口更新应用组，PlatformApiException会被全局异常处理器处理
        UpdateSrv6PolicyGroupResponseDTO updateResponse = platformApiService.updateSrv6PolicyGroup(updateRequest);
        if (updateResponse == null || !updateResponse.isSuccess()) {
            throw new ServiceException("PLATFORM_ERROR", "平台更新应用组失败",
                    "隧道组ID：" + teGroupDto.getId() + "，返回码：" + (updateResponse != null ? updateResponse.getCode() : "null"));
        }

        // 8. 处理作用域修改
        if (!CollectionUtils.isEmpty(teGroupDto.getTeGroupDcs())) {
            boolean scopeResult = updateTeGroupScopesForModify(request, existingGroup.getPlatformGroupId());
            if (!scopeResult) {
                throw new ServiceException("PLATFORM_ERROR", "更新隧道组作用域失败", "隧道组ID：" + teGroupDto.getId());
            }
        }

        // 9. 更新本地隧道组信息
        updateLocalTeGroupInfo(existingGroup, request);
        int updateResult = teGroupCustomInfoMapper.updateById(existingGroup);
        if (updateResult <= 0) {
            throw new ServiceException("DATABASE_ERROR", "更新本地隧道组信息失败", "隧道组ID：" + teGroupDto.getId());
        }

        log.info("修改隧道组成功，隧道组ID：{}，平台应用组ID：{}", teGroupDto.getId(), existingGroup.getPlatformGroupId());
    }

    /**
     * 验证修改隧道组请求参数（抛出异常版本）
     */
    private void validateModifyTeGroupRequest(ModifyTeGroupRequestDTO request) {
        ModifyTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        // 基本参数验证已通过注解完成，这里可以添加业务逻辑验证

        // 验证配置方式与参数的一致性
        if (Boolean.TRUE.equals(teGroupDto.getIsVirtualNet())) {
            // 按业务网络配置时，virtualNetId不能为空
            if (teGroupDto.getVirtualNetId() == null) {
                throw new ServiceException("VALIDATION_ERROR", "配置方式为按业务网络时，业务网络ID不能为空", "隧道组ID：" + teGroupDto.getId());
            }
        } else {
            // 按设备配置时，teGroupDcs不能为空
            if (CollectionUtils.isEmpty(teGroupDto.getTeGroupDcs())) {
                throw new ServiceException("VALIDATION_ERROR", "配置方式为按设备时，隧道组源目的信息不能为空", "隧道组ID：" + teGroupDto.getId());
            }
        }
    }

    /**
     * 构建更新应用组请求
     */
    private UpdateSrv6PolicyGroupRequestDTO buildUpdateSrv6PolicyGroupRequest(
            ModifyTeGroupRequestDTO request,
            GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupDetailVO originalDetail,
            TeTunnelTemplateCustomInfo tunnelTemplate) {

        UpdateSrv6PolicyGroupRequestDTO updateRequest = new UpdateSrv6PolicyGroupRequestDTO();

        ModifyTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        // 基本信息
        updateRequest.setDataId(originalDetail.getDataId());
        updateRequest.setGroupName(teGroupDto.getName());
        updateRequest.setDescription(originalDetail.getDescription());
        updateRequest.setNetworkingModel(3); // 固定值为3
        // 主备模式：候选路径数=2，负载模式：候选路径数=1
        updateRequest.setCandipathNum(originalDetail.getCandipathNum());
        updateRequest.setDirection(originalDetail.getDirection()); // 固定值：2
        updateRequest.setCoRouted(originalDetail.getCoRouted()); // 主备模式为true，负载模式为false
        updateRequest.setCalcLimit(originalDetail.getCalcLimit()); // 固定值：METRIC
        updateRequest.setCbts(originalDetail.getCbts());
        updateRequest.setScheduleType(originalDetail.getScheduleType());
        updateRequest.setPolicyDeployMode(originalDetail.getPolicyDeployMode());
        //updateRequest.setUpdateSoftLockPath(false);

        // 构建Policy参数列表，保持平台的color和pathSize不变（只更新本地定制表）
        List<UpdateSrv6PolicyGroupRequestDTO.UpdateSrv6PolicyGroupParamsDTO> paramsList = new ArrayList<>();

        // 遍历修改Policy参数信息
        if (originalDetail.getSrv6PolicyGroupParamsList() != null) {
            for (GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupParamsVO originalParam : originalDetail.getSrv6PolicyGroupParamsList()) {
                UpdateSrv6PolicyGroupRequestDTO.UpdateSrv6PolicyGroupParamsDTO updateParam =
                    new UpdateSrv6PolicyGroupRequestDTO.UpdateSrv6PolicyGroupParamsDTO();

                updateParam.setDataId(originalParam.getDataId());
                updateParam.setServiceClass(originalParam.getServiceClass());
                updateParam.setColorId(originalParam.getColorId());
                updateParam.setPositive(originalParam.getPositive());
                updateParam.setSoftLocking(false);
                updateParam.setCurrentBandwidthEnable(originalParam.getCurrentBandwidthEnable());
                updateParam.setGsrv6Enable(originalParam.getGsrv6Enable());
                updateParam.setGsrv6CompressMode(originalParam.getGsrv6CompressMode());
                updateParam.setBfdEnable(originalParam.getBfdEnable());
                updateParam.setBfdType(originalParam.getBfdType());
                updateParam.setBfdReverseType(originalParam.getBfdReverseType());
                updateParam.setTrigPathEnable(originalParam.getTrigPathEnable());
                updateParam.setHotStandbyEnable(originalParam.getHotStandbyEnable());
                updateParam.setMainTemplateId(originalParam.getMainTemplateId());
                updateParam.setBackupTemplateId(originalParam.getBackupTemplateId());
                updateParam.setStatisticEnable(originalParam.getStatisticEnable());
                updateParam.setBypassEnable(originalParam.getBypassEnable());

                // 从隧道模板重新填充参数
                updateParam.setBandwidth(tunnelTemplate.getBandwidth() != null ? (int) (tunnelTemplate.getBandwidth() / 1000) : 0);
                updateParam.setPriority(tunnelTemplate.getSlaId() != null ? mapSlaToPriority(tunnelTemplate.getSlaId()) : 0); // 默认优先级
                try {
                    updateParam.setMaxPacketLossRate(tunnelTemplate.getPacketLossRate() != null ?
                            Long.parseLong(tunnelTemplate.getPacketLossRate()) : 0L);
                } catch (NumberFormatException e) {
                    throw new ServiceException("DATA_ERROR", "隧道模板丢包率格式错误", "丢包率：" + tunnelTemplate.getPacketLossRate());
                }

                try {
                    updateParam.setMaxJitter(tunnelTemplate.getNetworkJitter() != null ?
                            Long.parseLong(tunnelTemplate.getNetworkJitter()) : 0L);
                } catch (NumberFormatException e) {
                    throw new ServiceException("DATA_ERROR", "隧道模板网络抖动格式错误", "网络抖动：" + tunnelTemplate.getNetworkJitter());
                }

                try {
                    updateParam.setMaxDelay(tunnelTemplate.getDelayTime() != null ?
                            Long.parseLong(tunnelTemplate.getDelayTime()) : 0L);
                } catch (NumberFormatException e) {
                    throw new ServiceException("DATA_ERROR", "隧道模板延迟时间格式错误", "延迟时间：" + tunnelTemplate.getDelayTime());
                }

                /*// BFD模板配置（mainTemplateId为必填）
                updateParam.setMainTemplateId(bfdTemplateId);
                // 当主备模式时，才需要backupTemplateId
                if (teGroupDto.getBalanceMode() == 1) {
                    updateParam.setBackupTemplateId(bfdTemplateId);
                }*/

                // 构建候选路径约束，直接遍历原有候选路径列表
                List<UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity> constraints = new ArrayList<>();
                List<GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupCandidatePathVO> originalConstraints = originalParam.getCandiPathConstraints();

                if (originalConstraints != null) {
                    for (GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupCandidatePathVO originalConstraint : originalConstraints) {
                        UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity constraint =
                            new UpdateSrv6PolicyGroupRequestDTO.Srv6PolicyGroupCandiConstraintPathEntity();

                        // 基于原有路径配置
                        constraint.setDataId(originalConstraint.getDataId());
                        constraint.setPositive(originalConstraint.getPathIndex() == 0);
                        constraint.setPathIndex(originalConstraint.getPathIndex());
//                        constraint.setPreference(originalConstraint.getPreference());
//                        constraint.setAffinityType(originalConstraint.getAffinityType());
                        constraint.setIncludeAffinityAll(originalConstraint.getIncludeAffinityAll());
                        constraint.setExcludeAffinity(originalConstraint.getExcludeAffinity());
                        constraint.setSidPathMode(originalConstraint.getSidPathMode());

                        // preferColor：候选路径1用模板里的upAllowLinkPriority，路径2不做配置
                        if (originalConstraint.getPathIndex() != null && originalConstraint.getPathIndex() == 0) {
                            // 路径1使用隧道模板的upAllowLinkPriority
                            Integer preferColor = mapPriorityToNumber(tunnelTemplate.getUpAllowLinkPriority());
                            if (preferColor != null) {
                                constraint.setPreferColor(preferColor);
                            }
                        } else {
                            // 路径2不做配置
                            constraint.setPreferColor(originalConstraint.getPreferColor());
                        }

                        // includeAffinityAny：对应隧道模板downAllowLinkPriority字段
                        List<String> downAllowLinkPriorityList3 = parseDownAllowLinkPriority(tunnelTemplate.getDownAllowLinkPriority());
                        if (!downAllowLinkPriorityList3.isEmpty()) {
                            List<Integer> includeAffinityAny = new ArrayList<>();
                            for (String priority : downAllowLinkPriorityList3) {
                                Integer priorityNumber = mapPriorityToNumber(priority);
                                if (priorityNumber != null) {
                                    includeAffinityAny.add(priorityNumber);
                                }
                            }
                            if (!includeAffinityAny.isEmpty()) {
                                constraint.setIncludeAffinityAny(includeAffinityAny);
                            } else {
                                constraint.setIncludeAffinityAny(originalConstraint.getIncludeAffinityAny());
                            }
                        } else {
                            //constraint.setIncludeAffinityAny(originalConstraint.getIncludeAffinityAny());
                            constraint.setIncludeAffinityAny(new ArrayList<>());
                        }

                        // 根据balanceMode重新计算initSidPathNum和maxSidPathNum
                        // 主备模式：initSidPathNum和maxSidPathNum=1，负载模式：initSidPathNum和maxSidPathNum=pathSize
//                        constraint.setInitSidPathNum(request.getBalanceMode() == 1 ? 1 : request.getPathSize());
//                        constraint.setMaxSidPathNum(request.getBalanceMode() == 1 ? 1 : request.getPathSize());
                        constraint.setInitSidPathNum(originalConstraint.getInitSidPathNum());
                        constraint.setMaxSidPathNum(originalConstraint.getMaxSidPathNum());

                        // hopLimit从请求参数取值，如果没有则保持原有值
                        if (teGroupDto.getHopLimit() != null) {
                            constraint.setHopLimit(teGroupDto.getHopLimit());
                        } else {
                            constraint.setHopLimit(originalConstraint.getHopLimit());
                        }

                        constraints.add(constraint);
                    }
                }

                updateParam.setCandiPathConstraints(constraints);

                paramsList.add(updateParam);
            }
        }

        updateRequest.setSrv6PolicyGroupParamsList(paramsList);

        return updateRequest;
    }

    /**
     * 更新隧道组作用域（修改场景）
     */
    private boolean updateTeGroupScopesForModify(ModifyTeGroupRequestDTO request, String platformGroupId) {
        // 1. 先查询原有作用域，PlatformApiException会被全局异常处理器处理
        GetCustomNetworkScopeRequestDTO scopeRequest = new GetCustomNetworkScopeRequestDTO(Long.valueOf(platformGroupId));
        GetCustomNetworkScopeResponseDTO scopeResponse = platformApiService.getCustomNetworkScope(scopeRequest);

        // 2. 判断是否获取到原始作用域
        if (scopeResponse == null || !scopeResponse.isSuccess() ||
                scopeResponse.getResult() == null ||
                CollectionUtils.isEmpty(scopeResponse.getResult().getRecords())) {
            throw new ServiceException("PLATFORM_ERROR", "获取原始作用域失败", "应用组ID：" + platformGroupId);
        }

        // 3. 构建新的作用域请求，保持groupId和dataId
        PlatformUpdateSrv6PolicyGroupNetworkRequestDTO updateScopeRequest =
                new PlatformUpdateSrv6PolicyGroupNetworkRequestDTO();
        updateScopeRequest.setGroupId(Long.valueOf(platformGroupId));
        updateScopeRequest.setDataId(scopeResponse.getResult().getRecords().get(0).getDataId());
        updateScopeRequest.setNetworkingModel(3); // 固定值

        List<PlatformUpdateSrv6PolicyGroupNetworkRequestDTO.NetworkInfo> networkList = new ArrayList<>();

        // 4. 根据用户传入的TeGroupDcs生成全新的NetworkInfo
        for (ModifyTeGroupRequestDTO.TeGroupDcInfo dcInfo : request.getTeGroupDto().getTeGroupDcs()) {
            PlatformUpdateSrv6PolicyGroupNetworkRequestDTO.NetworkInfo networkInfo =
                    new PlatformUpdateSrv6PolicyGroupNetworkRequestDTO.NetworkInfo();

            networkInfo.setNodeType(3); // 固定值

            // 转换源设备ID列表为平台节点ID列表，getPlatformNodeIdByDeviceId已经会抛出异常
            List<Long> srcNodes = new ArrayList<>();
            for (Long deviceId : dcInfo.getSrcDeviceIds()) {
                Long platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
                srcNodes.add(platformNodeId);
            }
            networkInfo.setSrcNodes(srcNodes);

            // 转换目的设备ID列表为平台节点ID列表，getPlatformNodeIdByDeviceId已经会抛出异常
            List<Long> dstNodes = new ArrayList<>();
            for (Long deviceId : dcInfo.getDstDeviceIds()) {
                Long platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
                dstNodes.add(platformNodeId);
            }
            networkInfo.setDstNodes(dstNodes);

            networkList.add(networkInfo);
        }

        updateScopeRequest.setNetworkList(networkList);

        // 5. 调用平台接口更新作用域，PlatformApiException会被全局异常处理器处理
        return platformApiService.updateSrv6PolicyGroupNetwork(updateScopeRequest);
    }

    /**
     * 更新本地隧道组信息
     */
    private void updateLocalTeGroupInfo(TeGroupCustomInfo existingGroup, ModifyTeGroupRequestDTO request) {
        ModifyTeGroupRequestDTO.TeGroupDto teGroupDto = request.getTeGroupDto();
        // 更新基本信息
        existingGroup.setName(teGroupDto.getName()); // 使用正确的字段名
        existingGroup.setIsLoose(teGroupDto.getIsLoose());
        existingGroup.setBalanceMode(teGroupDto.getBalanceMode());
        existingGroup.setBalanceProportion(teGroupDto.getBalanceProportion());
        existingGroup.setScheduleStrategyId(teGroupDto.getScheduleStrategyId());
        existingGroup.setPlaneRoutingType(teGroupDto.getPlaneRoutingType());
        existingGroup.setIsVirtualNet(teGroupDto.getIsVirtualNet());
        existingGroup.setVirtualNetId(String.valueOf(teGroupDto.getVirtualNetId()));
        existingGroup.setHopLimit(teGroupDto.getHopLimit());

        // 更新color和pathSize字段到本地定制表（但不更新到平台应用组）
        existingGroup.setColor(teGroupDto.getColor());
        existingGroup.setPathSize(teGroupDto.getPathSize());
        log.debug("更新本地隧道组color：{}，pathSize：{}", teGroupDto.getColor(), teGroupDto.getPathSize());

        // 如果配置方式为按设备，更新teGroupDcs信息
        if (!CollectionUtils.isEmpty(teGroupDto.getTeGroupDcs())) {
            String teGroupDcsJson = JSON.toJSONString(teGroupDto.getTeGroupDcs());
            existingGroup.setTeGroupDcs(teGroupDcsJson);
            log.debug("更新隧道组设备配置信息为JSON：{}", teGroupDcsJson);
        }

        existingGroup.setUpdateTime(new Date());
    }

    @Override
    public GetTeGroupResponseDTO getTeGroup(String requestId) {
        log.info("开始查询隧道组信息，请求ID：{}", requestId);

        // 1. 查询所有隧道组信息
        LambdaQueryWrapper<TeGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeGroupCustomInfo::getIsDeleted, 0);
        List<TeGroupCustomInfo> teGroupList = teGroupCustomInfoMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(teGroupList)) {
            log.info("未查询到隧道组信息，请求ID：{}", requestId);
            return GetTeGroupResponseDTO.success(requestId, new ArrayList<>());
        }

        // 2. 转换为响应DTO，异常会被全局异常处理器处理
        List<GetTeGroupResponseDTO.TeGroupInfo> teGroupInfoList = new ArrayList<>();
        for (TeGroupCustomInfo teGroup : teGroupList) {
            GetTeGroupResponseDTO.TeGroupInfo teGroupInfo = convertToTeGroupInfo(teGroup);
            teGroupInfoList.add(teGroupInfo);
        }

        log.info("查询隧道组信息成功，请求ID：{}，隧道组数量：{}", requestId, teGroupInfoList.size());
        return GetTeGroupResponseDTO.success(requestId, teGroupInfoList);
    }

    /**
     * 获取单个平台应用组的部署状态
     */
    private String getPlatformGroupDeployStatus(String platformGroupId) {
        if (platformGroupId == null || platformGroupId.isEmpty()) {
            return null;
        }

        GetSrv6PolicyGroupRequestDTO request = new GetSrv6PolicyGroupRequestDTO();
        request.setFlowGroupId(Long.valueOf(platformGroupId));
        request.setPageNum(1L);
        request.setPageSize(1L);

        // 调用平台接口查询部署状态，PlatformApiException会被全局异常处理器处理
        GetSrv6PolicyGroupResponseDTO response = platformApiService.getSrv6PolicyGroup(request);
        if (response != null && response.isSuccess() && response.getResult() != null) {
            List<GetSrv6PolicyGroupResponseDTO.VasSrv6PolicyGroupVO> records = response.getResult().getRecords();
            if (!CollectionUtils.isEmpty(records)) {
                return records.get(0).getDeployStatus();
            }
        }
        return null;
    }

    /**
     * 转换隧道组实体为响应DTO
     */
    private GetTeGroupResponseDTO.TeGroupInfo convertToTeGroupInfo(TeGroupCustomInfo teGroup) {
        GetTeGroupResponseDTO.TeGroupInfo teGroupInfo = new GetTeGroupResponseDTO.TeGroupInfo();

        // 基本信息
        teGroupInfo.setId(teGroup.getTeGroupId());
        teGroupInfo.setName(teGroup.getName());
        teGroupInfo.setIsLoose(teGroup.getIsLoose());
        teGroupInfo.setBalanceMode(teGroup.getBalanceMode());
        teGroupInfo.setBalanceProportion(teGroup.getBalanceProportion());
        teGroupInfo.setScheduleStrategyId(teGroup.getScheduleStrategyId());
        teGroupInfo.setColor(teGroup.getColor());
        teGroupInfo.setPathSize(teGroup.getPathSize());
        teGroupInfo.setPlaneRoutingType(teGroup.getPlaneRoutingType());
        teGroupInfo.setIsVirtualNet(teGroup.getIsVirtualNet());
        teGroupInfo.setHopLimit(teGroup.getHopLimit());

        // 单独查询部署状态
        String deployStatus = getPlatformGroupDeployStatus(teGroup.getPlatformGroupId());
        teGroupInfo.setDeployState(convertDeployStatus(deployStatus));

        if (teGroup.getIsVirtualNet()) {
            // 业务网络ID转换为Integer
            if (teGroup.getVirtualNetId() != null) {
                try {
                    teGroupInfo.setVirtualNetId(Integer.valueOf(teGroup.getVirtualNetId()));
                } catch (NumberFormatException e) {
                    throw new ServiceException("DATA_ERROR", "业务网络ID格式错误", "隧道组ID：" + teGroup.getTeGroupId() + "，业务网络ID：" + teGroup.getVirtualNetId());
                }
            }

        } else {
            // 处理设备配置信息（从JSON中解析）
            if (teGroup.getTeGroupDcs() != null && !teGroup.getTeGroupDcs().isEmpty()) {
                try {
                    // 直接转换为响应DTO类型（字段完全一致），原样返回数据库中保存的数据
                    List<GetTeGroupResponseDTO.TeGroupDc> responseTeGroupDcs = JSON.parseArray(teGroup.getTeGroupDcs(), GetTeGroupResponseDTO.TeGroupDc.class);
                    teGroupInfo.setTeGroupDcs(responseTeGroupDcs);
                } catch (Exception e) {
                    throw new ServiceException("DATA_ERROR", "解析隧道组设备配置信息异常", "隧道组ID：" + teGroup.getTeGroupId() + "，JSON内容：" + teGroup.getTeGroupDcs());
                }
            }
        }

        // 注：其他字段如mandatoryNodeIds、excludeNodeIds等在当前数据库表中没有对应字段，暂时设置为空
        // 如果后续需要支持这些字段，需要扩展数据库表结构

        return teGroupInfo;
    }

    /**
     * 转换部署状态
     * deployStatus：TO_BE_DEPLOY  未部署 对应状态 1
     * deployStatus：DEPLOY_SUCCESS  部署成功 对应状态 3
     * deployStatus：DEPLOY_FAILED  部署失败 4
     * deployStatus：TO_BE_DELETE  待删除  对应状态 5
     */
    private Integer convertDeployStatus(String deployStatus) {
        if (deployStatus == null) {
            return 1; // 默认未部署
        }

        switch (deployStatus) {
            case "TO_BE_DEPLOY":
                return 1; // 未部署
            case "DEPLOY_SUCCESS":
                return 3; // 部署成功
            case "DEPLOY_FAILED":
                return 4; // 部署失败
            case "TO_BE_DELETE":
                return 5; // 待删除
            default:
                return 1; // 默认未部署
        }
    }

    @Override
    public GetTeGroupDetailsResponseDTO getTeGroupDetails(GetTeGroupDetailsRequestDTO request, String requestId) {
        log.info("开始查询隧道详情，隧道组ID列表：{}，设备名称：{}，部署状态：{}，请求ID：{}",
                request.getTeGroupIds(), request.getDevName(), request.getDeployState(), requestId);

        // 1. 查询定制库中的隧道组信息
        List<TeGroupCustomInfo> teGroupList = queryTeGroupsByConditions(request);
        if (CollectionUtils.isEmpty(teGroupList)) {
            log.info("未找到符合条件的隧道组，请求ID：{}", requestId);
            return GetTeGroupDetailsResponseDTO.success(requestId, new ArrayList<>());
        }

        // 2. 查询每个隧道组的详情信息，异常会被全局异常处理器处理
        List<GetTeGroupDetailsResponseDTO.TeGroupDetail> allDetails = new ArrayList<>();
        List<TeGroupCustomInfo> validTeGroups = new ArrayList<>();

        for (TeGroupCustomInfo teGroup : teGroupList) {
            List<GetTeGroupDetailsResponseDTO.TeGroupDetail> details = queryTeGroupDetails(teGroup, request);
            if (!details.isEmpty()) {
                // 只有查询到业务数据的隧道组才保留
                allDetails.addAll(details);
                validTeGroups.add(teGroup);
            } else {
                log.info("隧道组{}未查询到业务数据，从结果中移除", teGroup.getTeGroupId());
            }
        }

        log.info("查询隧道详情完成，共查询{}个隧道组，有效隧道组{}个，隧道详情{}条，请求ID：{}",
                teGroupList.size(), validTeGroups.size(), allDetails.size(), requestId);
        return GetTeGroupDetailsResponseDTO.success(requestId, allDetails);
    }

    /**
     * 根据条件查询隧道组信息
     */
    private List<TeGroupCustomInfo> queryTeGroupsByConditions(GetTeGroupDetailsRequestDTO request) {
        LambdaQueryWrapper<TeGroupCustomInfo> queryWrapper = new LambdaQueryWrapper<>();

        // 根据隧道组ID列表过滤
        queryWrapper.in(TeGroupCustomInfo::getTeGroupId, request.getTeGroupIds());

        // 根据设备名称过滤（如果提供）
        if (request.getDevName() != null && !request.getDevName().trim().isEmpty()) {
            // 使用JSON查询特性，查询teGroupDcs字段中包含指定设备名称的记录
            String devName = request.getDevName().trim();
            queryWrapper.and(wrapper -> wrapper
                    .apply("JSON_CONTAINS(te_group_dcs, JSON_QUOTE({0}), '$.srcDeviceNames')", devName)
                    .or()
                    .apply("JSON_CONTAINS(te_group_dcs, JSON_QUOTE({0}), '$.dstDeviceNames')", devName)
            );
        }

        return teGroupCustomInfoMapper.selectList(queryWrapper);
    }

    /**
     * 查询单个隧道组的详情信息
     */
    private List<GetTeGroupDetailsResponseDTO.TeGroupDetail> queryTeGroupDetails(
            TeGroupCustomInfo teGroup, GetTeGroupDetailsRequestDTO request) {

        List<GetTeGroupDetailsResponseDTO.TeGroupDetail> details = new ArrayList<>();

        // 查询SRv6 Policy业务列表，PlatformApiException会被全局异常处理器处理
        GetSrv6PolicyTrailRequestDTO trailRequest = new GetSrv6PolicyTrailRequestDTO();
        trailRequest.setGroupId(Long.valueOf(teGroup.getPlatformGroupId()));
        trailRequest.setPageNum(1L);
        trailRequest.setPageSize(1000L); // 设置较大的页面大小以获取所有数据

        // 如果传入了部署状态，转换为平台接口的deployStatus参数
        if (request.getDeployState() != null) {
            String platformDeployStatus = convertToPlatformDeployStatus(request.getDeployState());
            if (platformDeployStatus != null) {
                trailRequest.setDeployStatus(platformDeployStatus);
            }
        }

        GetSrv6PolicyTrailResponseDTO trailResponse = platformApiService.getSrv6PolicyTrail(trailRequest);
        if (trailResponse == null || !trailResponse.isSuccess()) {
            log.warn("调用平台业务列表接口失败，隧道组ID：{}，响应：{}", teGroup.getTeGroupId(),
                    trailResponse != null ? trailResponse.getMessage() : "null");
            return details;
        }

        if (trailResponse.getResult() == null || CollectionUtils.isEmpty(trailResponse.getResult().getRecords())) {
            log.info("隧道组{}在平台上未查询到业务数据，可能尚未创建业务或业务已被删除", teGroup.getTeGroupId());
            return details;
        }

        // 处理每个业务
        for (GetSrv6PolicyTrailResponseDTO.GetSrv6PolicyTrailVO trail : trailResponse.getResult().getRecords()) {
            // 查询部署详情，PlatformApiException会被全局异常处理器处理
            GetSrv6PolicyTrailDeployDetailRequestDTO detailRequest = new GetSrv6PolicyTrailDeployDetailRequestDTO();
            detailRequest.setDataId(trail.getDataId());

            GetSrv6PolicyTrailDeployDetailResponseDTO detailResponse =
                    platformApiService.getSrv6PolicyTrailDeployDetail(detailRequest);

            if (detailResponse != null && detailResponse.isSuccess() && detailResponse.getResult() != null) {
                // 生成正向和反向隧道详情
                List<GetTeGroupDetailsResponseDTO.TeGroupDetail> tunnelDetails =
                        generateTunnelDetails(teGroup, trail, detailResponse.getResult());

                // 由于在查询业务列表时已经按deployState过滤，这里不需要再次过滤
                details.addAll(tunnelDetails);
            }
        }

        return details;
    }

    /**
     * 生成隧道详情信息（正向和反向）
     */
    private List<GetTeGroupDetailsResponseDTO.TeGroupDetail> generateTunnelDetails(
            TeGroupCustomInfo teGroup,
            GetSrv6PolicyTrailResponseDTO.GetSrv6PolicyTrailVO trail,
            GetSrv6PolicyTrailDeployDetailResponseDTO.GetSrv6PolicyTrailDeployDetailVO deployDetail) {

        List<GetTeGroupDetailsResponseDTO.TeGroupDetail> details = new ArrayList<>();

        // 转换部署状态
        Integer deployState = convertDeployStatus(trail.getDeployStatus());

        // 1. 生成正向隧道详情
        GetTeGroupDetailsResponseDTO.TeGroupDetail forwardDetail = new GetTeGroupDetailsResponseDTO.TeGroupDetail();
        forwardDetail.setId(deployDetail.getForwardPolicyId());
        forwardDetail.setName(deployDetail.getForwardName());
        forwardDetail.setTeGroupId(teGroup.getTeGroupId());
        // 正向隧道：srcNodeId取sourceNeId，dstNodeId取destinationNeId，需要转换为上游设备ID
        forwardDetail.setSrcNodeId(convertPlatformNodeIdToDeviceId(String.valueOf(trail.getSourceNeId())));
        forwardDetail.setDstNodeId(convertPlatformNodeIdToDeviceId(String.valueOf(trail.getDestinationNeId())));
        forwardDetail.setColor(teGroup.getColor());
        forwardDetail.setSrcNodeName(deployDetail.getForwardSourceNeName());
        forwardDetail.setDstNodeName(deployDetail.getForwardDestinationNeName());
        forwardDetail.setDeployState(deployState);
        forwardDetail.setReversed(false);
        forwardDetail.setReverseId(deployDetail.getReversePolicyId());
        forwardDetail.setDeployMsg(trail.getFailReason());

        // 设置正向路径信息
        if (!CollectionUtils.isEmpty(deployDetail.getForwardCandiPathInfo())) {
            List<GetTeGroupDetailsResponseDTO.PathDetail> pathDetails = extractPathDetails(deployDetail.getForwardCandiPathInfo());
            forwardDetail.setPath(pathDetails);
        }

        details.add(forwardDetail);

        // 2. 生成反向隧道详情
        GetTeGroupDetailsResponseDTO.TeGroupDetail reverseDetail = new GetTeGroupDetailsResponseDTO.TeGroupDetail();
        reverseDetail.setId(deployDetail.getReversePolicyId());
        reverseDetail.setName(deployDetail.getReverseName());
        reverseDetail.setTeGroupId(teGroup.getTeGroupId());
        // 反向隧道：srcNodeId取destinationNeId，dstNodeId取sourceNeId，需要转换为上游设备ID
        reverseDetail.setSrcNodeId(convertPlatformNodeIdToDeviceId(String.valueOf(trail.getDestinationNeId())));
        reverseDetail.setDstNodeId(convertPlatformNodeIdToDeviceId(String.valueOf(trail.getSourceNeId())));
        reverseDetail.setColor(teGroup.getColor());
        reverseDetail.setSrcNodeName(deployDetail.getReverseSourceNeName());
        reverseDetail.setDstNodeName(deployDetail.getReverseDestinationNeName());
        reverseDetail.setDeployState(deployState);
        reverseDetail.setReversed(true);
        reverseDetail.setReverseId(deployDetail.getForwardPolicyId());
        reverseDetail.setDeployMsg(trail.getFailReason());

        // 设置反向路径信息
        if (!CollectionUtils.isEmpty(deployDetail.getReverseCandiPathInfo())) {
            List<GetTeGroupDetailsResponseDTO.PathDetail> pathDetails = extractPathDetails(deployDetail.getReverseCandiPathInfo());
            reverseDetail.setPath(pathDetails);
        }

        details.add(reverseDetail);

        return details;
    }

    /**
     * 从候选路径信息中提取路径详情
     */
    private List<GetTeGroupDetailsResponseDTO.PathDetail> extractPathDetails(
            List<GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicyCandiDeployPathEntity> candiPathInfo) {

        List<GetTeGroupDetailsResponseDTO.PathDetail> pathDetails = new ArrayList<>();

        for (GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicyCandiDeployPathEntity candiPath : candiPathInfo) {
            if (!CollectionUtils.isEmpty(candiPath.getSidPathList())) {
                for (GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathEntity sidPath : candiPath.getSidPathList()) {
                    if (!CollectionUtils.isEmpty(sidPath.getDetailList())) {
                        for (GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathHopEntity hopEntity : sidPath.getDetailList()) {
                            GetTeGroupDetailsResponseDTO.PathDetail pathDetail = new GetTeGroupDetailsResponseDTO.PathDetail();
                            pathDetail.setMemberId(hopEntity.getMemberId());
                            pathDetail.setMemberName(hopEntity.getMemberName());
                            pathDetail.setMemberType(hopEntity.getMemberType());
                            pathDetail.setMemberIndex(hopEntity.getMemberIndex());
                            pathDetails.add(pathDetail);
                        }
                    }
                }
            }
        }

        return pathDetails;
    }

    /**
     * 将接口的部署状态转换为平台接口的deployStatus参数
     *
     * @param deployState 接口部署状态 (1-未部署, 3-部署成功, 4-部署失败, 5-待删除)
     * @return 平台deployStatus参数
     */
    private String convertToPlatformDeployStatus(Integer deployState) {
        if (deployState == null) {
            return null;
        }

        switch (deployState) {
            case 1:
                return "TO_BE_DEPLOY"; // 未部署
            case 3:
                return "DEPLOY_SUCCESS"; // 部署成功
            case 4:
                return "DEPLOY_FAILED"; // 部署失败
            case 5:
                return "TO_BE_DELETE"; // 待删除
            default:
                log.warn("未知的部署状态：{}，将不设置平台查询条件", deployState);
                return null; // 未知状态，不设置查询条件
        }
    }

    /**
     * 将平台节点ID转换为上游设备ID
     * 通过device_custom_info表查询platformNodeId对应的deviceId
     *
     * @param platformNodeId 平台节点ID
     * @return 上游设备ID，如果未找到则返回null
     */
    private Long convertPlatformNodeIdToDeviceId(String platformNodeId) {
        if (platformNodeId == null || platformNodeId.trim().isEmpty()) {
            return null;
        }

        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getPlatformNodeId, platformNodeId.trim());

        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);
        if (deviceInfo != null) {
            log.debug("平台节点ID{}转换为设备ID：{}", platformNodeId, deviceInfo.getDeviceId());
            return deviceInfo.getDeviceId();
        } else {
            log.warn("未找到平台节点ID{}对应的设备信息", platformNodeId);
            return null;
        }
    }

    /**
     * 将中文链路等级映射为数字
     * 高 -> 0x00000001, 中 -> 0x00000002, 低 -> 0x00000003
     */
    private Integer mapPriorityToNumber(String priority) {
        if (priority == null) {
            return null;
        }
        switch (priority) {
            case "高":
                //return "0x00000001";
                return 1;
            case "中":
                //return "0x00000002";
                return 2;
            case "低":
                //return "0x00000003";
                return 3;
            default:
                log.warn("未知的链路等级：{}", priority);
                return null;
        }
    }

    /**
     * 解析 downAllowLinkPriority JSON 字符串为 List<String>
     */
    private List<String> parseDownAllowLinkPriority(String downAllowLinkPriorityJson) {
        if (downAllowLinkPriorityJson == null || downAllowLinkPriorityJson.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<String> result = JSON.parseArray(downAllowLinkPriorityJson, String.class);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            throw new ServiceException("DATA_ERROR", "解析反向链路等级失败", "原始数据：" + downAllowLinkPriorityJson);
        }
    }
}