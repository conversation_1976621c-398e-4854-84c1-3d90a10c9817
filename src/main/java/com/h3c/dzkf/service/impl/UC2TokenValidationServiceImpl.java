package com.h3c.dzkf.service.impl;

import cn.hutool.core.util.StrUtil;
import com.h3c.dzkf.common.config.TestModeConfig;
import com.h3c.dzkf.entity.result.TokenValidationResult;
import com.h3c.dzkf.service.TokenValidationService;
import com.h3c.dzkf.uc2linker.framework.sessionfactory.UC2SessionFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.security.auth.login.AccountNotFoundException;

/**
 * UC2 Token验证服务实现类
 * 基于UC2平台的Token验证实现
 */
@Slf4j
@Service
public class UC2TokenValidationServiceImpl implements TokenValidationService {

    @Autowired
    private UC2SessionFactory uc2SessionFactory;

    @Autowired
    private TestModeConfig testModeConfig;

    @Override
    public boolean validateToken(String token) {
        // 测试模式下可跳过Token验证
        if (testModeConfig.isSkipTokenValidation()) {
            log.info("测试模式：跳过Token验证，Token：{}", token);
            return true;
        }

        // 检查Token是否为空
        if (StrUtil.isBlank(token)) {
            log.warn("Token验证失败：Token为空");
            return false;
        }

        try {
            // 调用平台验证Token
            String tokenVerify = uc2SessionFactory.getPlatComponent().getTokenFunction().tokenVerify(token);
            if (StrUtil.isBlank(tokenVerify)) {
                log.warn("Token验证失败，Token：{}", token);
                return false;
            }
            
            log.debug("Token验证成功，Token：{}", token);
            return true;
            
        } catch (AccountNotFoundException e) {
            log.warn("Token验证失败，Token：{}，错误信息：{}", token, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("Token验证异常，Token：{}，错误信息：{}", token, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public TokenValidationResult validateTokenWithResult(String token) {
        // 测试模式下可跳过Token验证
        if (testModeConfig.isSkipTokenValidation()) {
            log.info("测试模式：跳过Token验证，Token：{}", token);
            return TokenValidationResult.success();
        }

        // 检查Token是否为空
        if (StrUtil.isBlank(token)) {
            log.warn("Token验证失败：Token为空");
            return TokenValidationResult.fail("Token不能为空");
        }

        try {
            // 调用平台验证Token
            String tokenVerify = uc2SessionFactory.getPlatComponent().getTokenFunction().tokenVerify(token);
            if (StrUtil.isBlank(tokenVerify)) {
                log.warn("Token验证失败，Token：{}", token);
                return TokenValidationResult.fail("Token无效或已过期");
            }
            
            log.debug("Token验证成功，Token：{}", token);
            return TokenValidationResult.success();
            
        } catch (AccountNotFoundException e) {
            log.warn("Token验证失败，Token：{}，错误信息：{}", token, e.getMessage());
            return TokenValidationResult.fail("Token无效或已过期");
        } catch (Exception e) {
            log.error("Token验证异常，Token：{}，错误信息：{}", token, e.getMessage(), e);
            return TokenValidationResult.fail("Token验证异常");
        }
    }
} 