package com.h3c.dzkf.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.h3c.dzkf.common.config.TestModeConfig;
import com.h3c.dzkf.common.exceptions.PlatformApiException;
import com.h3c.dzkf.entity.platform.*;
import com.h3c.dzkf.service.PlatformApiService;
import com.h3c.dzkf.util.TokenContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * ADWAN平台API服务实现类
 * <p>
 * 专门负责ADWAN平台的HTTP接口调用：
 * 1. 设备新增、删除、查询、更新
 * 2. 接口信息查询
 * <p>
 * 网管接口（uclink）已分离到 UclinkApiServiceImpl 中实现
 */
@Slf4j
@Service
public class PlatformApiServiceImpl implements PlatformApiService {

    @Value("${adwan.platform.baseUrl:http://10.88.44.37:30000}")
    private String platformBaseUrl;

    @Value("${adwan.platform.addDeviceUrl:/adwan/proxy/nfm/physicalNetwork/node/addNode}")
    private String addDeviceUrl;

    @Value("${adwan.platform.deleteDeviceUrl:/adwan/proxy/nfm/physicalNetwork/node/deleteMaintainedNodes}")
    private String deleteDeviceUrl;

    @Value("${adwan.platform.getNodesUrl:/adwan/proxy/nfm/physicalNetwork/node/getNodes}")
    private String getNodesUrl;

    @Value("${adwan.platform.updateNodeUrl:/adwan/proxy/nfm/physicalNetwork/node/updateNode}")
    private String updateNodeUrl;

    @Value("${adwan.platform.getBatchInterfaceListUrl:/adwan/proxy/nfm/physicalNetwork/node/getBatchInterfaceList}")
    private String getBatchInterfaceListUrl;

    @Value("${adwan.platform.addLinkUrl:/adwan/proxy/nfm/physicalNetwork/link/addLink}")
    private String addLinkUrl;

    @Value("${adwan.platform.getInterfacesUrl:/adwan/proxy/nfm/physicalNetwork/interface/getInterfaces}")
    private String getInterfacesUrl;

    @Value("${adwan.platform.updateLinkUrl:/adwan/proxy/nfm/physicalNetwork/link/updateLink}")
    private String updateLinkUrl;

    @Value("${adwan.platform.getLinkUrl:/adwan/proxy/nfm/physicalNetwork/link/getLink}")
    private String getLinkUrl;

    @Value("${adwan.platform.getLinkOverViewUrl:/oam/linkOverView/getLinkOverView}")
    private String getLinkOverViewUrl;

    @Value("${adwan.platform.getNetconfTemplateUrl:/adwan/proxy/nfm/template/getNetconfTemplate}")
    private String getNetconfTemplateUrl;

    @Value("${adwan.platform.getSnmpTemplateUrl:/adwan/proxy/nfm/template/getSnmpTemplate}")
    private String getSnmpTemplateUrl;

    @Value("${adwan.platform.maintainDeviceUrl:/adwan/proxy/nfm/template/node/maintain}")
    private String maintainDeviceUrl;

    @Value("${adwan.platform.addAclTemplateUrl:/qostrs/qos/acl}")
    private String addAclTemplateUrl;

    @Value("${adwan.platform.deleteAclTemplateUrl:/qostrs/qos/acl/all}")
    private String deleteAclTemplateUrl;

    @Value("${adwan.platform.queryAclTemplatesUrl:/qostrs/qos/acl}")
    private String queryAclTemplatesUrl;

    @Value("${adwan.platform.updateAclTemplateUrl:/qostrs/qos/acl/all}")
    private String updateAclTemplateUrl;

    @Value("${adwan.platform.addSrv6PolicyGroupUrl:/vas/srv6PolicyGroup/addSrv6PolicyGroup}")
    private String addSrv6PolicyGroupUrl;

    @Value("${adwan.platform.updateSrv6PolicyGroupNetworkUrl:/vas/srv6PolicyGroupNetwork/updateNetwork}")
    private String updateSrv6PolicyGroupNetworkUrl;

    @Value("${adwan.platform.getSrv6PolicyGroupUrl:/vas/srv6PolicyGroup/getSrv6PolicyGroup}")
    private String getSrv6PolicyGroupUrl;

    @Value("${adwan.platform.getBfdTemplateUrl:/vas/bfd/template/get}")
    private String getBfdTemplateUrl;

    @Value("${adwan.platform.deleteSrv6PolicyGroupUrl:/vas/srv6PolicyGroup/deleteSrv6PolicyGroup}")
    private String deleteSrv6PolicyGroupUrl;

    @Value("${adwan.platform.startPlanSrv6PolicyGroupUrl:/vas/srv6PolicyGroup/startPlanSrv6PolicyGroup}")
    private String startPlanSrv6PolicyGroupUrl;

    @Value("${adwan.platform.deploySrv6PolicyGroupUrl:/vas/srv6PolicyGroup/deploySrv6PolicyGroup}")
    private String deploySrv6PolicyGroupUrl;

    @Value("${adwan.platform.getSrv6PolicyGroupDetailUrl:/vas/srv6PolicyGroup/getSrv6PolicyGroupDetail}")
    private String getSrv6PolicyGroupDetailUrl;

    @Value("${adwan.platform.updateSrv6PolicyGroupUrl:/vas/srv6PolicyGroup/updateSrv6PolicyGroup}")
    private String updateSrv6PolicyGroupUrl;

    @Value("${adwan.platform.getCustomNetworkScopeUrl:/vas/srv6PolicyGroupNetwork/getCustomNetworkScope}")
    private String getCustomNetworkScopeUrl;

    @Value("${adwan.platform.getInventoryInfoUrl:/inventoryInfo}")
    private String getInventoryInfoUrl;

    @Value("${adwan.platform.getSrv6PolicyTrailUrl:/vas/srv6PolicyTrail/getSrv6PolicyTrail}")
    private String getSrv6PolicyTrailUrl;

    @Value("${adwan.platform.getSrv6PolicyTrailDeployDetailUrl:/vas/srv6PolicyTrail/getSrv6PolicyTrailDeployDetail}")
    private String getSrv6PolicyTrailDeployDetailUrl;

    // QoS相关接口URL配置
    @Value("${adwan.platform.addQosClassifierUrl:/qostrs/qos/classifier}")
    private String addQosClassifierUrl;

    @Value("${adwan.platform.updateQosClassifierUrl:/qostrs/qos/classifier/all}")
    private String updateQosClassifierUrl;

    @Value("${adwan.platform.deleteQosClassifierUrl:/qostrs/qos/classifier/all}")
    private String deleteQosClassifierUrl;

    @Value("${adwan.platform.getQosClassifierListUrl:/qostrs/qos/classifier}")
    private String getQosClassifierListUrl;

    @Value("${adwan.platform.addQosBehaviorUrl:/qostrs/qos/behavior}")
    private String addQosBehaviorUrl;

    @Value("${adwan.platform.getQosBehaviorDetailUrl:/qostrs/qos/behavior/detail}")
    private String getQosBehaviorDetailUrl;

    @Value("${adwan.platform.updateQosBehaviorUrl:/qostrs/qos/behavior/all}")
    private String updateQosBehaviorUrl;

    @Value("${adwan.platform.deleteQosBehaviorUrl:/qostrs/qos/behavior/all}")
    private String deleteQosBehaviorUrl;

    @Value("${adwan.platform.addQosPolicyUrl:/qostrs/qos/policy}")
    private String addQosPolicyUrl;

    @Value("${adwan.platform.deleteQosPolicyUrl:/qostrs/qos/policy/all}")
    private String deleteQosPolicyUrl;

    @Value("${adwan.platform.updateQosPolicyUrl:/qostrs/qos/policy/all}")
    private String updateQosPolicyUrl;

    @Value("${adwan.platform.getQosPolicyListUrl:/qostrs/qos/policy}")
    private String getQosPolicyListUrl;

    @Value("${adwan.platform.getQosPolicyDetailRuleUrl:/qostrs/qos/policy/detail/rule}")
    private String getQosPolicyDetailRuleUrl;

    @Value("${adwan.platform.deployQosPolicyUrl:/qostrs/qos/policy/deploy/devif}")
    private String deployQosPolicyUrl;

    @Value("${adwan.platform.getQosDeviceListUrl:/qostrs/qos/device}")
    private String getQosDeviceListUrl;

    @Value("${adwan.platform.getQosDeviceInterfaceListUrl:/qostrs/qos/device/if}")
    private String getQosDeviceInterfaceListUrl;

    @Value("${adwan.platform.getAclDetailUrl:/qostrs/qos/acl/detail}")
    private String getAclDetailUrl;

    @Value("${adwan.platform.getQosPolicyDeployHistoryUrl:/qostrs/qos/policy/deploy/devif}")
    private String getQosPolicyDeployHistoryUrl;

    @Autowired
    private TestModeConfig testModeConfig;

    private final Random random = new Random();

    /**
     * 创建带认证的HTTP请求
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param token 认证token
     * @return HttpRequest对象
     */
    private HttpRequest createAuthenticatedRequest(String url, Object requestBody, String token) {
        HttpRequest httpRequest = HttpRequest.post(url)
                .header("Content-Type", "application/json;charset=UTF-8")
                .body(JSON.toJSONString(requestBody))
                .timeout(30000);

        // 添加认证token到请求头
        if (token != null && !token.isEmpty()) {
            // 根据ADWAN平台的认证要求，使用Cookie方式传递token
            httpRequest.header("Cookie", "X-Subject-Token=" + token);  // ADWAN平台使用Cookie认证
            
            log.debug("已添加认证Token到Cookie: X-Subject-Token");
        } else {
            log.warn("认证Token为空，请求可能失败");
        }

        return httpRequest;
    }

    @Override
    public String addDevice(PlatformAddDeviceRequestDTO request) {
        try {
            String url = platformBaseUrl + addDeviceUrl;
            log.info("开始调用平台接口新增设备，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddDevice(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台接口调用成功，响应：{}", responseBody);

                // 解析响应获取设备节点ID
                PlatformAddResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformAddResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    if (platformResponse.getResult() != null && platformResponse.getResult().getDataId() != null) {
                        return platformResponse.getResult().getDataId().toString();
                    } else {
                        log.error("平台接口返回成功但未包含dataId，响应：{}", responseBody);
                        throw new PlatformApiException("平台接口返回成功但未包含设备ID", null, responseBody);
                    }
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台接口异常", e);
            throw new PlatformApiException("调用平台接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台新增设备调用
     */
    private String mockAddDevice(PlatformAddDeviceRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增设备调用，设备名称：{}", request.getNodeName());

        // 生成随机设备ID
        long min = testModeConfig.getMockDeviceIdRange().getMin();
        long max = testModeConfig.getMockDeviceIdRange().getMax();
        long mockDeviceId = min + (long) (random.nextDouble() * (max - min));

        log.info("测试模式：生成模拟设备ID：{}", mockDeviceId);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return String.valueOf(mockDeviceId);
    }

    @Override
    public boolean deleteDevice(PlatformDeleteRequestDTO request) {
        try {
            String url = platformBaseUrl + deleteDeviceUrl;
            log.info("开始调用平台接口删除设备，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteDevice(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台删除接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformDeleteResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformDeleteResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台设备删除成功，节点ID：{}", request.getNodeIds());
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台删除接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台删除接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台删除接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台删除接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台删除接口异常", e);
            throw new PlatformApiException("调用平台删除接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台删除设备调用
     */
    private boolean mockDeleteDevice(PlatformDeleteRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台删除设备调用，节点ID：{}", request.getNodeIds());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟设备删除成功");
        return true;
    }

    @Override
    public PlatformGetNodesResponseDTO getNodes(PlatformGetNodesRequestDTO request) {
        try {
            String url = platformBaseUrl + getNodesUrl;
            log.info("开始调用平台接口查询设备，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetNodes(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformGetNodesResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformGetNodesResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台设备查询成功，数据ID：{}", request.getDataId());
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台查询接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台查询接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询接口异常", e);
            throw new PlatformApiException("调用平台查询接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateNode(PlatformUpdateNodeRequestDTO request) {
        try {
            String url = platformBaseUrl + updateNodeUrl;
            log.info("开始调用平台接口更新设备，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateNode(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台更新接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformUpdateNodeResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformUpdateNodeResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台设备更新成功，数据ID：{}", request.getDataId());
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台更新接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台更新接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台更新接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台更新接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台更新接口异常", e);
            throw new PlatformApiException("调用平台更新接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询设备调用
     */
    private PlatformGetNodesResponseDTO mockGetNodes(PlatformGetNodesRequestDTO request) {
        // 判断是单个查询还是批量查询
        if (request.getDataIds() != null && !request.getDataIds().isEmpty()) {
            log.info("测试模式：模拟ADWAN平台批量查询设备调用，数据ID列表：{}", request.getDataIds());
            return mockBatchGetNodes(request);
        } else {
            log.info("测试模式：模拟ADWAN平台单个查询设备调用，数据ID：{}", request.getDataId());
            return mockSingleGetNodes(request);
        }
    }

    /**
     * 模拟单个设备查询
     */
    private PlatformGetNodesResponseDTO mockSingleGetNodes(PlatformGetNodesRequestDTO request) {
        // 构建模拟响应
        PlatformGetNodesResponseDTO response = new PlatformGetNodesResponseDTO();
        response.setMessage("SUCCESS");
        response.setCode("SUCCESS");
        response.setSuccessful(true);

        PlatformGetNodesResponseDTO.GetNodesResult result = new PlatformGetNodesResponseDTO.GetNodesResult();
        result.setPageNum(1);
        result.setPageSize(15);
        result.setTotalItem(1);
        result.setTotalPage(1);

        PlatformGetNodesResponseDTO.NodeRecord record = new PlatformGetNodesResponseDTO.NodeRecord();
        record.setDataId(request.getDataId());
        record.setNodeName("测试设备名称");
        record.setManageIp("*************");
        // 模拟设备状态，随机生成在线/离线状态
        record.setNodeDisplay(random.nextInt(5) + 1); // 1-5的随机值

        result.setRecords(Arrays.asList(record));
        response.setResult(result);

        log.info("测试模式：生成模拟单个查询响应");
        return response;
    }

    /**
     * 模拟批量设备查询
     */
    private PlatformGetNodesResponseDTO mockBatchGetNodes(PlatformGetNodesRequestDTO request) {
        // 构建模拟响应
        PlatformGetNodesResponseDTO response = new PlatformGetNodesResponseDTO();
        response.setMessage("SUCCESS");
        response.setCode("SUCCESS");
        response.setSuccessful(true);

        PlatformGetNodesResponseDTO.GetNodesResult result = new PlatformGetNodesResponseDTO.GetNodesResult();
        result.setPageNum(1);
        result.setPageSize(request.getPageSize());
        result.setTotalItem(request.getDataIds().size());
        result.setTotalPage(1);

        // 为每个dataId创建模拟记录
        List<PlatformGetNodesResponseDTO.NodeRecord> records = new ArrayList<>();
        for (Long dataId : request.getDataIds()) {
            PlatformGetNodesResponseDTO.NodeRecord record = new PlatformGetNodesResponseDTO.NodeRecord();
            record.setDataId(dataId);
            record.setNodeName("测试设备名称_" + dataId);
            record.setManageIp("192.168.1." + (dataId % 254 + 1));
            // 模拟设备状态，大部分在线，少数离线
            record.setNodeDisplay(random.nextFloat() < 0.8f ? (random.nextInt(4) + 1) : 3); // 80%概率在线，20%概率离线
            record.setCompany(0); // H3C
            record.setNodeModel("Test-Model-" + dataId);
            record.setSerialNumbers(Arrays.asList("SN" + dataId));
            record.setManageMac("00:0C:29:F9:C8:" + String.format("%02X", dataId % 256));
            records.add(record);
        }

        result.setRecords(records);
        response.setResult(result);

        log.info("测试模式：生成模拟批量查询响应，返回{}条记录", records.size());
        return response;
    }

    /**
     * 模拟平台更新设备调用
     */
    private boolean mockUpdateNode(PlatformUpdateNodeRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台更新设备调用，数据ID：{}，新名称：{}", request.getDataId(), request.getNodeName());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟设备更新成功");
        return true;
    }

    @Override
    public PlatformInterfaceListResponseDTO getBatchInterfaceList(PlatformInterfaceListRequestDTO request) {
        try {
            String url = platformBaseUrl + getBatchInterfaceListUrl;
            log.info("开始调用ADWAN平台查询接口信息，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetBatchInterfaceList(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("ADWAN平台查询接口信息成功，响应：{}", responseBody);

                // 解析响应
                PlatformInterfaceListResponseDTO interfaceResponse = JSON.parseObject(responseBody, PlatformInterfaceListResponseDTO.class);
                if (interfaceResponse != null && interfaceResponse.getSuccessful() != null && interfaceResponse.getSuccessful()) {
                    return interfaceResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = interfaceResponse != null ? interfaceResponse.getMessage() : "ADWAN平台查询接口信息失败";
                    String errorCode = interfaceResponse != null ? interfaceResponse.getCode() : null;

                    log.error("ADWAN平台查询接口信息失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("ADWAN平台查询接口信息失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("ADWAN平台查询接口信息失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用ADWAN平台查询接口信息异常", e);
            throw new PlatformApiException("调用ADWAN平台查询接口信息异常：" + e.getMessage(), e);
        }
    }


    // Mock方法实现

    /**
     * 模拟ADWAN平台接口查询调用
     */
    private PlatformInterfaceListResponseDTO mockGetBatchInterfaceList(PlatformInterfaceListRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台接口查询调用，管理IP列表：{}", request.getManageIps());

        // 构建模拟响应
        PlatformInterfaceListResponseDTO response = new PlatformInterfaceListResponseDTO();
        response.setSuccessful(true);
        response.setMessage("SUCCESS");
        response.setCode("SUCCESS");

        // 为每个设备IP创建模拟节点接口数据
        List<PlatformInterfaceListResponseDTO.NodeInterfaceData> resultList = new ArrayList<>();

        for (String manageIp : request.getManageIps()) {
            PlatformInterfaceListResponseDTO.NodeInterfaceData nodeData = new PlatformInterfaceListResponseDTO.NodeInterfaceData();
            nodeData.setDataId(System.currentTimeMillis() + (long) (Math.random() * 1000));
            nodeData.setManageIp(manageIp);
            nodeData.setResourceId(System.currentTimeMillis() + (long) (Math.random() * 2000));

            // 创建模拟接口数据
            List<PlatformInterfaceListResponseDTO.InterfaceData> interfaceList = new ArrayList<>();

            // 模拟GigabitEthernet接口
            PlatformInterfaceListResponseDTO.InterfaceData interfaceData = new PlatformInterfaceListResponseDTO.InterfaceData();
            interfaceData.setDataId(System.currentTimeMillis() + (long) (Math.random() * 3000));
            interfaceData.setNodeId(nodeData.getDataId());
            interfaceData.setIfType(1);
            interfaceData.setIfIndex(17);
            interfaceData.setMac("00:0C:29:F9:C8:6D");
            interfaceData.setIfName("GigabitEthernet1/1/0");
            interfaceData.setAbbreviatedName("GE1/1/0");
            interfaceData.setIfNumber("1/1/0");
            interfaceData.setBandwidth(10000000L);
            interfaceData.setActivated(1);
            interfaceData.setDescription("Mock interface");
            interfaceData.setIfMode(3);
            interfaceData.setStatus(1);
            interfaceData.setAdminStatus(1);

            // 创建地址信息
            List<PlatformInterfaceListResponseDTO.AddressData> addresses = new ArrayList<>();
            PlatformInterfaceListResponseDTO.AddressData address = new PlatformInterfaceListResponseDTO.AddressData();
            address.setIfId(interfaceData.getDataId());
            address.setAddress(manageIp);
            address.setMaskLen(24);
            address.setAddressOrigin(1);
            address.setIpType(1);
            addresses.add(address);
            interfaceData.setAddresses(addresses);

            interfaceList.add(interfaceData);
            nodeData.setInterfaceList(interfaceList);
            resultList.add(nodeData);
        }

        response.setResult(resultList);

        log.info("测试模式：生成模拟接口查询响应");
        return response;
    }

    @Override
    public String addLink(PlatformAddLinkRequestDTO request) {
        try {
            String url = platformBaseUrl + addLinkUrl;
            log.info("开始调用平台接口新增链路，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddLink(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台新增链路接口调用成功，响应：{}", responseBody);

                // 解析响应获取链路ID
                PlatformAddLinkResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformAddLinkResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    if (platformResponse.getResult() != null && platformResponse.getResult().getLinkId() != null) {
                        return platformResponse.getResult().getLinkId().toString();
                    } else {
                        log.error("平台接口返回成功但未包含linkId，响应：{}", responseBody);
                        throw new PlatformApiException("平台接口返回成功但未包含链路ID", null, responseBody);
                    }
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台新增链路接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台新增链路接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台新增链路接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台新增链路接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台新增链路接口异常", e);
            throw new PlatformApiException("调用平台新增链路接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台新增链路调用
     */
    private String mockAddLink(PlatformAddLinkRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增链路调用，链路名称：{}", request.getLinkName());

        // 生成随机链路ID
        long mockLinkId = System.currentTimeMillis() + (long) (random.nextDouble() * 1000000);

        log.info("测试模式：生成模拟链路ID：{}", mockLinkId);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return String.valueOf(mockLinkId);
    }

    @Override
    public PlatformGetInterfacesResponseDTO getInterfaces(PlatformGetInterfacesRequestDTO request) {
        try {
            String url = platformBaseUrl + getInterfacesUrl;
            log.info("开始调用平台接口查询设备接口信息，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetInterfaces(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询设备接口信息调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformGetInterfacesResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformGetInterfacesResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台查询设备接口信息成功，设备ID：{}", request.getNodeId());
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台查询设备接口信息失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台查询设备接口信息失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询设备接口信息失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询设备接口信息失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询设备接口信息异常", e);
            throw new PlatformApiException("调用平台查询设备接口信息异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询设备接口信息调用
     */
    private PlatformGetInterfacesResponseDTO mockGetInterfaces(PlatformGetInterfacesRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询设备接口信息调用，设备ID：{}, 设备IP：{}", request.getNodeId(), request.getNodeIp());

        // 构建模拟响应
        PlatformGetInterfacesResponseDTO response = new PlatformGetInterfacesResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        PlatformGetInterfacesResponseDTO.PageVO pageVO = new PlatformGetInterfacesResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(15L);
        pageVO.setTotalItem(2L);
        pageVO.setTotalPage(1L);

        // 创建模拟接口数据
        List<PlatformGetInterfacesResponseDTO.InterfaceVO> interfaceList = new ArrayList<>();

        // 模拟GigabitEthernet接口
        PlatformGetInterfacesResponseDTO.InterfaceVO ge1 = new PlatformGetInterfacesResponseDTO.InterfaceVO();
        ge1.setDataId(System.currentTimeMillis() + 1000L);
        ge1.setNodeId(request.getNodeId());
        ge1.setIfType(1);
        ge1.setIfIndex(17L);
        ge1.setMac("00:0C:29:5B:72:33");
        ge1.setIfName("Ten-GigabitEthernet2/0/25");
        ge1.setAbbreviatedName("GE1/1/0");
        ge1.setIfNumber("1/1/0");
        ge1.setBandwidth(1000000L);
        ge1.setActivated(1);
        ge1.setIfMode(3);
        ge1.setStatus(1);
        ge1.setAdminStatus(1);

        PlatformGetInterfacesResponseDTO.InterfaceVO ge2 = new PlatformGetInterfacesResponseDTO.InterfaceVO();
        ge2.setDataId(System.currentTimeMillis() + 2000L);
        ge2.setNodeId(request.getNodeId());
        ge2.setIfType(1);
        ge2.setIfIndex(18L);
        ge2.setMac("00:0C:29:5B:72:34");
        ge2.setIfName("Ten-GigabitEthernet2/0/26");
        ge2.setAbbreviatedName("GE0/0/1");
        ge2.setIfNumber("0/0/1");
        ge2.setBandwidth(1000000L);
        ge2.setActivated(1);
        ge2.setIfMode(3);
        ge2.setStatus(1);
        ge2.setAdminStatus(1);

        interfaceList.add(ge1);
        interfaceList.add(ge2);
        pageVO.setRecords(interfaceList);
        response.setResult(pageVO);

        log.info("测试模式：生成模拟设备接口信息响应，返回{}条接口记录", interfaceList.size());
        return response;
    }

    @Override
    public boolean updateLink(PlatformUpdateLinkRequestDTO request) {
        try {
            String url = platformBaseUrl + updateLinkUrl;
            log.info("开始调用平台接口更新链路，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateLink(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台更新链路接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformUpdateLinkResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformUpdateLinkResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台更新链路成功，链路ID：{}", request.getLinkId());
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台更新链路接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台更新链路接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台更新链路接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台更新链路接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台更新链路接口异常", e);
            throw new PlatformApiException("调用平台更新链路接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台更新链路调用
     */
    private boolean mockUpdateLink(PlatformUpdateLinkRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台更新链路调用，链路ID：{}", request.getLinkId());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟链路更新成功");
        return true;
    }

    @Override
    public PlatformGetLinkResponseDTO getLink(PlatformGetLinkRequestDTO request) {
        try {
            String url = platformBaseUrl + getLinkUrl;
            log.info("开始调用平台接口查询链路信息，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetLink(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询链路信息调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformGetLinkResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformGetLinkResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台查询链路信息成功，链路ID：{}", request.getLinkId());
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台查询链路信息失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台查询链路信息失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询链路信息失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询链路信息失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询链路信息异常", e);
            throw new PlatformApiException("调用平台查询链路信息异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询链路信息调用
     */
    private PlatformGetLinkResponseDTO mockGetLink(PlatformGetLinkRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询链路信息调用，链路ID：{}", request.getLinkId());

        // 构建模拟响应
        PlatformGetLinkResponseDTO response = new PlatformGetLinkResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        PlatformGetLinkResponseDTO.PageVO pageVO = new PlatformGetLinkResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(15L);
        pageVO.setTotalItem(1L);
        pageVO.setTotalPage(1L);

        // 创建模拟链路数据
        List<PlatformGetLinkResponseDTO.GetLinkVo> linkList = new ArrayList<>();
        PlatformGetLinkResponseDTO.GetLinkVo linkVo = new PlatformGetLinkResponseDTO.GetLinkVo();
        
        linkVo.setLinkId(request.getLinkId() != null ? request.getLinkId() : 279237602312193L);
        linkVo.setLinkName(request.getLinkName() != null ? request.getLinkName() : "模拟链路_" + linkVo.getLinkId());
        linkVo.setLinkNameUserSet(1);
        linkVo.setLinkNetworkType("ipv4");
        linkVo.setBandwidth(1000000000L); // 1Gbps
        linkVo.setResvBandwidth(1000000000L);
        linkVo.setSrcNodeId(278699540152321L);
        linkVo.setSrcNodeName("源设备_212");
        linkVo.setSrcTpId(278699617746945L);
        linkVo.setSrcTpIndex(17L);
        linkVo.setSrcTpName("GigabitEthernet1/1/0");
        linkVo.setSrcAddressStr("***************");
        linkVo.setDestNodeId(278698810343426L);
        linkVo.setDestNodeName("目标设备_211");
        linkVo.setDestTpId(278698825023494L);
        linkVo.setDestTpIndex(17L);
        linkVo.setDestTpName("GigabitEthernet1/1/0");
        linkVo.setDestAddressStr("***************");
        linkVo.setMetric(1L);
        linkVo.setLinkType(100);
        linkVo.setLinkTypeInt(100);
        linkVo.setLinkStatus(1);
        linkVo.setAvailable(100);
        linkVo.setAvailableState(1);
        linkVo.setAttributeFlags(0L);
        linkVo.setIsDeleted(0);
        linkVo.setMaintainStatus(0);
        linkVo.setLinkQualityAlarmLevel(0);
        linkVo.setNqaDistributeStatus(0);
        linkVo.setIsForwardLink(true);
        linkVo.setLinkAttrMode("asyn");
        linkVo.setIpMtu(1500);
        linkVo.setMtu(1500);

        linkList.add(linkVo);
        pageVO.setRecords(linkList);
        response.setResult(pageVO);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟链路信息响应，返回{}条链路记录", linkList.size());
        return response;
    }

    @Override
    public PlatformLinkOverViewResponseDTO getLinkOverView(PlatformLinkOverViewRequestDTO request) {
        try {
            String url = platformBaseUrl + getLinkOverViewUrl;
            log.info("开始调用平台接口查询链路总览信息，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetLinkOverView(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询链路总览信息调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformLinkOverViewResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformLinkOverViewResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台查询链路总览信息成功，查询条件：{}", request.getLinkName());
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台查询链路总览信息失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台查询链路总览信息失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询链路总览信息失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询链路总览信息失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询链路总览信息异常", e);
            throw new PlatformApiException("调用平台查询链路总览信息异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询链路总览信息调用
     */
    private PlatformLinkOverViewResponseDTO mockGetLinkOverView(PlatformLinkOverViewRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询链路总览信息调用，查询条件：linkName={}", request.getLinkName());

        // 构建模拟响应
        PlatformLinkOverViewResponseDTO response = new PlatformLinkOverViewResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        PlatformLinkOverViewResponseDTO.GetLinkOverViewVo result = new PlatformLinkOverViewResponseDTO.GetLinkOverViewVo();
        result.setTotalSize(1L);
        result.setPageNum(1L);
        result.setTotalPage(1L);

        // 创建模拟链路数据
        List<PlatformLinkOverViewResponseDTO.LinkOverViewInfo> linkList = new ArrayList<>();
        
        PlatformLinkOverViewResponseDTO.LinkOverViewInfo linkInfo = new PlatformLinkOverViewResponseDTO.LinkOverViewInfo();
        linkInfo.setLinkId("283986525421569");
        linkInfo.setLinkName("************** To ************** Link1 IPv6");
        linkInfo.setLinkQualityAlarmLevel(0);
        linkInfo.setLinkStatus(1);
        linkInfo.setSrcNodeName("**************");
        linkInfo.setSrcTpName("GigabitEthernet1/2/0");
        linkInfo.setSrcAddress("101::102:2");
        linkInfo.setDestNodeName("**************");
        linkInfo.setDestTpName("GigabitEthernet1/2/0");
        linkInfo.setDestAddress("101::102:1");
        linkInfo.setPacketLossRate(-1.0);
        linkInfo.setNetQualityPacketLossTimeStamp("2024-04-18 16:40:43");
        linkInfo.setDelay(-1.0);
        linkInfo.setJitter(-1L);
        linkInfo.setNetQualityJitterDelayTimeStamp("2024-04-18 16:40:43");
        linkInfo.setBandwidth(58L);
        linkInfo.setBandwidthPercent(5.8);
        linkInfo.setBandwidthTimeStamp("2024-04-18 16:40:43");

        linkList.add(linkInfo);

        result.setLinkOverViewInfoList(linkList);
        response.setResult(result);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟链路总览信息响应，返回{}条链路记录", linkList.size());
        return response;
    }

    @Override
    public GetNetconfTemplateResponseDTO getNetconfTemplate(GetNetconfTemplateRequestDTO request) {
        try {
            String url = platformBaseUrl + getNetconfTemplateUrl;
            log.info("开始调用平台接口查询NETCONF模板，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetNetconfTemplate(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询NETCONF模板调用成功，响应：{}", responseBody);

                // 解析响应
                GetNetconfTemplateResponseDTO platformResponse = JSON.parseObject(responseBody, GetNetconfTemplateResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台查询NETCONF模板成功");
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台查询NETCONF模板失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台查询NETCONF模板失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询NETCONF模板失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询NETCONF模板失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询NETCONF模板异常", e);
            throw new PlatformApiException("调用平台查询NETCONF模板异常：" + e.getMessage(), e);
        }
    }

    @Override
    public GetSnmpTemplateResponseDTO getSnmpTemplate(GetSnmpTemplateRequestDTO request) {
        try {
            String url = platformBaseUrl + getSnmpTemplateUrl;
            log.info("开始调用平台接口查询SNMP模板，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetSnmpTemplate(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询SNMP模板调用成功，响应：{}", responseBody);

                // 解析响应
                GetSnmpTemplateResponseDTO platformResponse = JSON.parseObject(responseBody, GetSnmpTemplateResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台查询SNMP模板成功");
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台查询SNMP模板失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台查询SNMP模板失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询SNMP模板失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询SNMP模板失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询SNMP模板异常", e);
            throw new PlatformApiException("调用平台查询SNMP模板异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询NETCONF模板调用
     */
    private GetNetconfTemplateResponseDTO mockGetNetconfTemplate(GetNetconfTemplateRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询NETCONF模板调用");

        GetNetconfTemplateResponseDTO response = new GetNetconfTemplateResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        GetNetconfTemplateResponseDTO.PageVO pageVO = new GetNetconfTemplateResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(1L);
        pageVO.setTotalItem(1L);
        pageVO.setTotalPage(1L);

        GetNetconfTemplateResponseDTO.GetNetconfTmpVO template = new GetNetconfTemplateResponseDTO.GetNetconfTmpVO();
        template.setDataId(906143610176393L);
        template.setName("GLOBAL");
        template.setNetconfUserName("admin");
        template.setNetconfPassword("JOhxy8aWQfaYriZnmvmmdQ==");
        template.setNetconfSshPort(830);
        template.setNetconfHttpsPort(832);
        template.setDescribe("全局NETCONF模板");

        pageVO.setRecords(Arrays.asList(template));
        response.setResult(pageVO);

        log.info("测试模式：生成模拟NETCONF模板响应");
        return response;
    }

    /**
     * 模拟平台查询SNMP模板调用
     */
    private GetSnmpTemplateResponseDTO mockGetSnmpTemplate(GetSnmpTemplateRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询SNMP模板调用");

        GetSnmpTemplateResponseDTO response = new GetSnmpTemplateResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        GetSnmpTemplateResponseDTO.PageVO pageVO = new GetSnmpTemplateResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(1L);
        pageVO.setTotalItem(1L);
        pageVO.setTotalPage(1L);

        GetSnmpTemplateResponseDTO.GetSnmpTmpVO template = new GetSnmpTemplateResponseDTO.GetSnmpTmpVO();
        template.setDataId(906143610176394L);
        template.setName("GLOBAL");
        template.setType("SNMPv2c");
        template.setReadOnly("public");
        template.setReadWrite("private");
        template.setPort(161);
        template.setDescribe("全局SNMP模板");

        pageVO.setRecords(Arrays.asList(template));
        response.setResult(pageVO);

        log.info("测试模式：生成模拟SNMP模板响应");
        return response;
    }

    @Override
    public boolean maintainDevice(MaintainNodeDTO request) {
        try {
            String url = platformBaseUrl + maintainDeviceUrl;
            log.info("开始调用平台接口维护设备，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockMaintainDevice(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台维护设备接口调用成功，响应：{}", responseBody);

                // 解析响应
                MaintainNodeResponseDTO platformResponse = JSON.parseObject(responseBody, MaintainNodeResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台设备维护成功，节点ID：{}", Arrays.toString(request.getNodeIds()));
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台维护设备接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台维护设备接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台维护设备接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台维护设备接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台维护设备接口异常", e);
            throw new PlatformApiException("调用平台维护设备接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台维护设备调用
     */
    private boolean mockMaintainDevice(MaintainNodeDTO request) {
        log.info("测试模式：模拟ADWAN平台维护设备调用，节点ID：{}", Arrays.toString(request.getNodeIds()));

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟设备维护成功");
        return true;
    }

    @Override
    public PlatformAclResponseDTO addAclTemplate(PlatformAclRequestDTO request) {
        try {
            String url = platformBaseUrl + addAclTemplateUrl;
            log.info("开始调用平台接口新增ACL模板，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddAclTemplate(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                // 平台成功响应，返回成功状态，ACL ID由业务层调用getAclIdByName获取
                log.info("平台新增ACL模板接口调用成功，ACL名称：{}", request.getAclName());

                // 构造成功响应，不包含ACL ID
                PlatformAclResponseDTO platformResponse = new PlatformAclResponseDTO();
                platformResponse.setSuccessful(true);
                platformResponse.setReturnCode("200");
                platformResponse.setReturnMessage("成功");

                PlatformAclResponseDTO.AclResult result = new PlatformAclResponseDTO.AclResult();
                result.setAclName(request.getAclName());
                // ACL ID由业务层通过getAclIdByName方法获取
                platformResponse.setResult(result);

                log.info("新增ACL模板成功，ACL名称：{}，ACL ID需要业务层查询获取", request.getAclName());
                return platformResponse;
            } else {
                // 非200状态码都按错误处理
                String responseBody = response.body();
                int statusCode = response.getStatus();
                log.error("平台新增ACL模板失败，状态码：{}，响应：{}", statusCode, responseBody);

                // 尝试解析错误响应
                String errorMessage = "平台新增ACL模板失败";
                String errorCode = String.valueOf(statusCode);

                try {
                    AclErrorResponseDTO errorResponse = JSON.parseObject(responseBody, AclErrorResponseDTO.class);
                    if (errorResponse != null && errorResponse.getErrorInfo() != null) {
                        errorMessage = errorResponse.getErrorInfo().getErrorMessage();
                        errorCode = errorResponse.getErrorInfo().getErrorCode();
                    }
                } catch (Exception e) {
                    log.warn("解析错误响应失败", e);
                }

                throw new PlatformApiException(errorMessage, errorCode, responseBody, statusCode);
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台新增ACL模板接口异常", e);
            throw new PlatformApiException("调用平台新增ACL模板接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台新增ACL模板调用
     */
    private PlatformAclResponseDTO mockAddAclTemplate(PlatformAclRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增ACL模板调用，ACL名称：{}", request.getAclName());

        PlatformAclResponseDTO response = new PlatformAclResponseDTO();
        response.setSuccessful(true);
        response.setReturnCode("200");
        response.setReturnMessage("成功");

        PlatformAclResponseDTO.AclResult result = new PlatformAclResponseDTO.AclResult();
        result.setAclName(request.getAclName());
        // 测试模式下也不返回ACL ID，由业务层通过getAclIdByName方法获取
        response.setResult(result);

        log.info("测试模式：新增ACL模板成功，ACL名称：{}，ACL ID需要业务层查询获取", request.getAclName());

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return response;
    }

    @Override
    public boolean deleteAclTemplate(Integer aclId, String requestId) {
        try {
            String url = platformBaseUrl + deleteAclTemplateUrl + "?aclId=" + aclId;
            log.info("开始调用平台接口删除ACL模板，url：{}，ACL ID：{}，请求ID：{}", url, aclId, requestId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteAclTemplate(aclId, requestId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("Cookie", "X-Subject-Token=" + token)
                    .body("{}");
            
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台删除ACL模板接口调用成功，响应：{}", responseBody);

                // 检查响应是否表示成功（根据实际平台API响应格式调整）
                if (responseBody == null || responseBody.trim().isEmpty()) {
                    log.info("平台删除ACL模板成功，ACL ID：{}", aclId);
                    return true;
                } else {
                    log.error("平台删除ACL模板失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台删除ACL模板失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台删除ACL模板接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台删除ACL模板接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台删除ACL模板接口异常，ACL ID：{}", aclId, e);
            throw new PlatformApiException("调用平台删除ACL模板接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台删除ACL模板调用
     */
    private boolean mockDeleteAclTemplate(Integer aclId, String requestId) {
        log.info("测试模式：模拟ADWAN平台删除ACL模板调用，ACL ID：{}，请求ID：{}", aclId, requestId);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟删除ACL模板成功");
        return true;
    }

    @Override
    public QueryAclTemplateResponseDTO queryAclTemplates(QueryAclTemplateRequestDTO request) {
        try {
            String url = buildQueryAclTemplatesUrl(request);
            log.info("开始调用平台接口查询ACL模板，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockQueryAclTemplates(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询ACL模板接口调用成功，响应：{}", responseBody);

                // 解析响应
                QueryAclTemplateResponseDTO platformResponse = JSON.parseObject(responseBody, QueryAclTemplateResponseDTO.class);
                if (platformResponse != null) {
                    int recordCount = 0;
                    if (platformResponse.getOutput() != null && platformResponse.getOutput().getList() != null) {
                        recordCount = platformResponse.getOutput().getList().size();
                    }
                    log.info("平台查询ACL模板成功，返回{}条记录", recordCount);
                    return platformResponse;
                } else {
                    log.error("平台查询ACL模板响应解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询ACL模板响应解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询ACL模板接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询ACL模板接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询ACL模板接口异常", e);
            throw new PlatformApiException("调用平台查询ACL模板接口异常：" + e.getMessage(), e);
        }
    }


    @Override
    public PlatformAclResponseDTO updateAclTemplate(PlatformAclRequestDTO request) {
        try {
            String url = platformBaseUrl + updateAclTemplateUrl;
            log.info("开始调用平台接口修改ACL模板，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 验证必要参数
            if (request.getAclId() == null) {
                log.error("修改ACL模板时aclId为必填参数");
                throw new PlatformApiException("修改ACL模板时aclId为必填参数", "MISSING_ACL_ID");
            }

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateAclTemplate(request);
            }

            // 使用带认证的HTTP请求（PUT方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台修改ACL模板接口调用成功，响应：{}", responseBody);

                // 构造成功响应
                PlatformAclResponseDTO platformResponse = new PlatformAclResponseDTO();
                platformResponse.setSuccessful(true);
                platformResponse.setReturnCode("200");
                platformResponse.setReturnMessage("修改成功");
                
                PlatformAclResponseDTO.AclResult result = new PlatformAclResponseDTO.AclResult();
                result.setAclId(request.getAclId());
                result.setAclName(request.getAclName());
                platformResponse.setResult(result);
                
                log.info("修改ACL模板成功，ACL ID：{}，ACL名称：{}", request.getAclId(), request.getAclName());
                return platformResponse;
            } else {
                // 非200状态码都按错误处理
                String responseBody = response.body();
                int statusCode = response.getStatus();
                log.error("平台修改ACL模板失败，状态码：{}，响应：{}", statusCode, responseBody);
                
                // 尝试解析错误响应
                String errorMessage = "平台修改ACL模板失败";
                String errorCode = String.valueOf(statusCode);

                try {
                    AclErrorResponseDTO errorResponse = JSON.parseObject(responseBody, AclErrorResponseDTO.class);
                    if (errorResponse != null && errorResponse.getErrorInfo() != null) {
                        errorMessage = errorResponse.getErrorInfo().getErrorMessage();
                        errorCode = errorResponse.getErrorInfo().getErrorCode();
                    }
                } catch (Exception e) {
                    log.warn("解析错误响应失败", e);
                }

                throw new PlatformApiException(errorMessage, errorCode, responseBody, statusCode);
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台修改ACL模板接口异常", e);
            throw new PlatformApiException("调用平台修改ACL模板接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台修改ACL模板调用
     */
    private PlatformAclResponseDTO mockUpdateAclTemplate(PlatformAclRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台修改ACL模板调用，ACL ID：{}，ACL名称：{}", 
            request.getAclId(), request.getAclName());

        PlatformAclResponseDTO response = new PlatformAclResponseDTO();
        response.setSuccessful(true);
        response.setReturnCode("200");
        response.setReturnMessage("修改成功");

        PlatformAclResponseDTO.AclResult result = new PlatformAclResponseDTO.AclResult();
        result.setAclId(request.getAclId());
        result.setAclName(request.getAclName());
        response.setResult(result);

        log.info("测试模式：模拟修改ACL模板成功");

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return response;
    }

    /**
     * 构建查询ACL模板的URL
     */
    private String buildQueryAclTemplatesUrl(QueryAclTemplateRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append(platformBaseUrl).append(queryAclTemplatesUrl);
        
        // 添加查询参数
        boolean firstParam = true;
        
        if (request.getDesc() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("desc=").append(request.getDesc());
            firstParam = false;
        }
        
        if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
            urlBuilder.append(firstParam ? "?" : "&").append("sortBy=").append(request.getSortBy());
            firstParam = false;
        }
        
        if (request.getStart() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("start=").append(request.getStart());
            firstParam = false;
        }
        
        if (request.getSize() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("size=").append(request.getSize());
            firstParam = false;
        }
        
        if (request.getAclName() != null && !request.getAclName().isEmpty()) {
            urlBuilder.append(firstParam ? "?" : "&").append("aclName=").append(request.getAclName());
            firstParam = false;
        }
        
        if (request.getIdValue() != null && !request.getIdValue().isEmpty()) {
            urlBuilder.append(firstParam ? "?" : "&").append("idValue=").append(request.getIdValue());
            firstParam = false;
        }
        
        if (request.getIdType() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("idType=").append(request.getIdType());
            firstParam = false;
        }
        
        if (request.getGroupType() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("groupType=").append(request.getGroupType());
            firstParam = false;
        }
        
        if (request.getDescription() != null && !request.getDescription().isEmpty()) {
            urlBuilder.append(firstParam ? "?" : "&").append("description=").append(request.getDescription());
            firstParam = false;
        }
        
        if (request.getDevResultList() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("devResultList=").append(request.getDevResultList());
            firstParam = false;
        }
        
        if (request.getIfResultList() != null) {
            urlBuilder.append(firstParam ? "?" : "&").append("ifResultList=").append(request.getIfResultList());
            firstParam = false;
        }
        
        return urlBuilder.toString();
    }

    /**
     * 模拟查询ACL模板调用
     */
    private QueryAclTemplateResponseDTO mockQueryAclTemplates(QueryAclTemplateRequestDTO request) {
        log.info("测试模式：模拟查询ACL模板调用，ACL名称：{}", request.getAclName());

        QueryAclTemplateResponseDTO response = new QueryAclTemplateResponseDTO();
        QueryAclTemplateResponseDTO.Output output = new QueryAclTemplateResponseDTO.Output();
        
        // 如果是按名称查询，返回匹配的模拟数据
        if (request.getAclName() != null && !request.getAclName().isEmpty()) {
            List<QueryAclTemplateResponseDTO.AclTemplate> templateList = new ArrayList<>();
            
            QueryAclTemplateResponseDTO.AclTemplate template = new QueryAclTemplateResponseDTO.AclTemplate();
            template.setAclId(random.nextInt(90000) + 10000);
            template.setAclName(request.getAclName());
            template.setDescription("模拟ACL模板");
            template.setGroupType(1); // IPv4ACL
            template.setIdType(1); // 名称标志
            template.setIdValue("测试应用组");
            template.setDevResult(0); // 未部署
            template.setIfResult(0); // 未部署
            template.setMatchOrder(1);
            template.setStep(5);
            template.setMatchList(null); // 匹配列表为null
            
            templateList.add(template);
            output.setList(templateList);
            output.setRowCount(1);
            output.setSize(1);
            output.setStart(0);
        } else {
            // 返回空列表
            output.setList(new ArrayList<>());
            output.setRowCount(0);
            output.setSize(0);
            output.setStart(request.getStart() != null ? request.getStart() : 0);
        }
        
        response.setOutput(output);

        log.info("测试模式：生成模拟ACL模板查询响应");
        return response;
    }

    @Override
    public PlatformAddSrv6PolicyGroupResponseDTO addSrv6PolicyGroup(PlatformAddSrv6PolicyGroupRequestDTO request) {
        try {
            String url = platformBaseUrl + addSrv6PolicyGroupUrl;
            log.info("开始调用平台接口新增SRv6 Policy应用组，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddSrv6PolicyGroup(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台新增SRv6 Policy应用组接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformAddSrv6PolicyGroupResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformAddSrv6PolicyGroupResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    return platformResponse;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台创建应用组失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台新增SRv6 Policy应用组失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台新增SRv6 Policy应用组接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台新增SRv6 Policy应用组接口异常", e);
            throw new PlatformApiException("调用平台新增SRv6 Policy应用组接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台新增SRv6 Policy应用组调用
     */
    private PlatformAddSrv6PolicyGroupResponseDTO mockAddSrv6PolicyGroup(PlatformAddSrv6PolicyGroupRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增SRv6 Policy应用组调用，应用组名称：{}", request.getGroupName());

        PlatformAddSrv6PolicyGroupResponseDTO response = new PlatformAddSrv6PolicyGroupResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);
        response.setResult(null); // 成功时result为null

        log.info("测试模式：模拟新增SRv6 Policy应用组成功");

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return response;
    }

    @Override
    public boolean updateSrv6PolicyGroupNetwork(PlatformUpdateSrv6PolicyGroupNetworkRequestDTO request) {
        try {
            String url = platformBaseUrl + updateSrv6PolicyGroupNetworkUrl;
            log.info("开始调用平台接口更新SRv6 Policy应用组作用域，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateSrv6PolicyGroupNetwork(request);
            }

            // 使用带认证的HTTP请求（PUT方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台更新SRv6 Policy应用组作用域接口调用成功，响应：{}", responseBody);
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台更新SRv6 Policy应用组作用域接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台更新SRv6 Policy应用组作用域接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台更新SRv6 Policy应用组作用域接口异常", e);
            throw new PlatformApiException("调用平台更新SRv6 Policy应用组作用域接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台更新SRv6 Policy应用组作用域调用
     */
    private boolean mockUpdateSrv6PolicyGroupNetwork(PlatformUpdateSrv6PolicyGroupNetworkRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台更新SRv6 Policy应用组作用域调用，应用组ID：{}", request.getGroupId());

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟更新SRv6 Policy应用组作用域成功");
        return true;
    }

    @Override
    public GetSrv6PolicyGroupResponseDTO getSrv6PolicyGroup(GetSrv6PolicyGroupRequestDTO request) {
        try {
            String url = platformBaseUrl + getSrv6PolicyGroupUrl;
            log.info("开始调用平台接口查询应用组信息，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetSrv6PolicyGroup(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询应用组信息接口调用成功，响应：{}", responseBody);

                // 解析响应
                GetSrv6PolicyGroupResponseDTO platformResponse = JSON.parseObject(responseBody, GetSrv6PolicyGroupResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询应用组信息接口返回数据解析失败", "PARSE_ERROR", responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询应用组信息接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询应用组信息接口异常", e);
            throw new PlatformApiException("调用平台查询应用组信息接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询应用组信息调用
     */
    private GetSrv6PolicyGroupResponseDTO mockGetSrv6PolicyGroup(GetSrv6PolicyGroupRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询应用组信息调用，应用组名称：{}", request.getGroupName());

        GetSrv6PolicyGroupResponseDTO response = new GetSrv6PolicyGroupResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        // 创建分页结果
        GetSrv6PolicyGroupResponseDTO.PageVO pageVO = new GetSrv6PolicyGroupResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(10L);
        pageVO.setTotalItem(1L);
        pageVO.setTotalPage(1L);

        // 创建应用组信息列表
        List<GetSrv6PolicyGroupResponseDTO.VasSrv6PolicyGroupVO> records = new ArrayList<>();
        if (request.getGroupName() != null && !request.getGroupName().isEmpty()) {
            GetSrv6PolicyGroupResponseDTO.VasSrv6PolicyGroupVO groupVO = new GetSrv6PolicyGroupResponseDTO.VasSrv6PolicyGroupVO();
            groupVO.setDataId((long) (random.nextInt(90000) + 10000));
            groupVO.setGroupName(request.getGroupName());
            groupVO.setDescription("模拟应用组");
            groupVO.setFlowGroupStatus(0); // 正常状态
            groupVO.setNetworkingModel(3);
            groupVO.setCandipathNum(2);
            groupVO.setDirection(2);
            groupVO.setCoRouted(true);
            groupVO.setPolicyDeployMode(1);
            groupVO.setCalcLimit("METRIC");
            
            records.add(groupVO);
        }
        
        pageVO.setRecords(records);
        response.setResult(pageVO);

        log.info("测试模式：生成模拟应用组查询响应，匹配数量：{}", records.size());

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return response;
    }

    @Override
    public GetBfdTemplateResponseDTO getBfdTemplate(GetBfdTemplateRequestDTO request) {
        try {
            String url = platformBaseUrl + getBfdTemplateUrl;
            log.info("开始调用平台接口查询BFD模板，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetBfdTemplate(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询BFD模板接口调用成功，响应：{}", responseBody);

                // 解析响应
                GetBfdTemplateResponseDTO platformResponse = JSON.parseObject(responseBody, GetBfdTemplateResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询BFD模板接口返回数据解析失败", "PARSE_ERROR", responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询BFD模板接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询BFD模板接口异常", e);
            throw new PlatformApiException("调用平台查询BFD模板接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询BFD模板调用
     */
    private GetBfdTemplateResponseDTO mockGetBfdTemplate(GetBfdTemplateRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询BFD模板调用");

        GetBfdTemplateResponseDTO response = new GetBfdTemplateResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        // 创建分页结果
        GetBfdTemplateResponseDTO.PageVO pageVO = new GetBfdTemplateResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(10L);
        pageVO.setTotalItem(1L);
        pageVO.setTotalPage(1L);

        // 创建BFD模板信息列表
        List<GetBfdTemplateResponseDTO.GetBfdTemplateVO> records = new ArrayList<>();
        GetBfdTemplateResponseDTO.GetBfdTemplateVO templateVO = new GetBfdTemplateResponseDTO.GetBfdTemplateVO();
        templateVO.setDataId((long) (random.nextInt(90000) + 10000));
        templateVO.setTemplateName("默认BFD模板");
        templateVO.setDetectMultiplier(3);
        templateVO.setMinTransmitInterval(1000);
        templateVO.setMinReceiveInterval(1000);
        templateVO.setMinEchoReceiveInterval(0);
        
        records.add(templateVO);
        pageVO.setRecords(records);
        response.setResult(pageVO);

        log.info("测试模式：生成模拟BFD模板查询响应，模板ID：{}", templateVO.getDataId());

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return response;
    }

    @Override
    public boolean deleteSrv6PolicyGroup(String groupId) {
        try {
            String url = platformBaseUrl + deleteSrv6PolicyGroupUrl + "?groupId=" + groupId;
            log.info("开始调用平台接口删除SRv6 Policy应用组，url：{}，应用组ID：{}", url, groupId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteSrv6PolicyGroup(groupId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台删除SRv6 Policy应用组接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformDeleteResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformDeleteResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台SRv6 Policy应用组删除成功，应用组ID：{}", groupId);
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台删除SRv6 Policy应用组接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台删除SRv6 Policy应用组接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台删除SRv6 Policy应用组接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台删除SRv6 Policy应用组接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台删除SRv6 Policy应用组接口异常，应用组ID：{}", groupId, e);
            throw new PlatformApiException("调用平台删除SRv6 Policy应用组接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台删除SRv6 Policy应用组调用
     */
    private boolean mockDeleteSrv6PolicyGroup(String groupId) {
        log.info("测试模式：模拟ADWAN平台删除SRv6 Policy应用组调用，应用组ID：{}", groupId);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟SRv6 Policy应用组删除成功");
        return true;
    }

    @Override
    public boolean startPlanSrv6PolicyGroup(StartPlanSrv6PolicyGroupDTO request) {
        try {
            String url = platformBaseUrl + startPlanSrv6PolicyGroupUrl;
            log.info("开始调用平台接口规划应用组路径，url：{}，应用组ID：{}", url, request.getGroupId());

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockStartPlanSrv6PolicyGroup(request);
            }

            // 使用带认证的HTTP请求（PUT方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台规划应用组路径接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformDeleteResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformDeleteResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台应用组路径规划成功，应用组ID：{}", request.getGroupId());
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台规划应用组路径接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台规划应用组路径接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台规划应用组路径接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台规划应用组路径接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台规划应用组路径接口异常，应用组ID：{}", request.getGroupId(), e);
            throw new PlatformApiException("调用平台规划应用组路径接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台规划应用组路径调用
     */
    private boolean mockStartPlanSrv6PolicyGroup(StartPlanSrv6PolicyGroupDTO request) {
        log.info("测试模式：模拟ADWAN平台规划应用组路径调用，应用组ID：{}", request.getGroupId());

        // 模拟一些处理时间
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟应用组路径规划成功");
        return true;
    }

    @Override
    public boolean deploySrv6PolicyGroup(DeploySrv6PolicyGroupDTO request) {
        try {
            String url = platformBaseUrl + deploySrv6PolicyGroupUrl;
            log.info("开始调用平台接口部署应用组配置，url：{}，应用组ID：{}", url, request.getGroupId());

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeploySrv6PolicyGroup(request);
            }

            // 使用带认证的HTTP请求（PUT方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台部署应用组配置接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformDeleteResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformDeleteResponseDTO.class);
                if (platformResponse != null && platformResponse.getSuccessful() != null && platformResponse.getSuccessful()) {
                    log.info("平台应用组配置部署成功，应用组ID：{}", request.getGroupId());
                    return true;
                } else {
                    // 平台接口调用失败，提取错误信息
                    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台部署应用组配置接口调用失败";
                    String errorCode = platformResponse != null ? platformResponse.getCode() : null;

                    log.error("平台部署应用组配置接口调用失败，响应：{}", responseBody);
                    throw new PlatformApiException(errorMessage, errorCode, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台部署应用组配置接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台部署应用组配置接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台部署应用组配置接口异常，应用组ID：{}", request.getGroupId(), e);
            throw new PlatformApiException("调用平台部署应用组配置接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台部署应用组配置调用
     */
    private boolean mockDeploySrv6PolicyGroup(DeploySrv6PolicyGroupDTO request) {
        log.info("测试模式：模拟ADWAN平台部署应用组配置调用，应用组ID：{}", request.getGroupId());

        // 模拟一些处理时间
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟应用组配置部署成功");
        return true;
    }

    @Override
    public GetSrv6PolicyGroupDetailResponseDTO getSrv6PolicyGroupDetail(GetSrv6PolicyGroupDetailRequestDTO request) {
        try {
            String url = platformBaseUrl + getSrv6PolicyGroupDetailUrl;
            log.info("开始调用平台接口查询应用组详情，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetSrv6PolicyGroupDetail(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询应用组详情接口调用成功，响应：{}", responseBody);

                // 解析响应
                GetSrv6PolicyGroupDetailResponseDTO platformResponse = JSON.parseObject(responseBody, GetSrv6PolicyGroupDetailResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询应用组详情接口返回数据解析失败", "PARSE_ERROR", responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询应用组详情失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询应用组详情接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询应用组详情异常", e);
            throw new PlatformApiException("调用平台查询应用组详情接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台查询应用组详情调用
     */
    private GetSrv6PolicyGroupDetailResponseDTO mockGetSrv6PolicyGroupDetail(GetSrv6PolicyGroupDetailRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询应用组详情调用，应用组ID：{}", request.getGroupId());

        GetSrv6PolicyGroupDetailResponseDTO response = new GetSrv6PolicyGroupDetailResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        // 创建模拟的应用组详情
        GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupDetailVO detail =
            new GetSrv6PolicyGroupDetailResponseDTO.VasSrv6PolicyGroupDetailVO();
        detail.setDataId(request.getGroupId());
        detail.setGroupName("TestGroup_" + request.getGroupId());
        detail.setDescription("测试应用组");
        detail.setNetworkingModel(3);
        detail.setCandipathNum(2);
        detail.setDirection(2);
        detail.setCoRouted(true);
        detail.setPolicyDeployMode(1);
        detail.setCalcLimit("METRIC");

        response.setResult(detail);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟应用组详情响应");
        return response;
    }

    @Override
    public UpdateSrv6PolicyGroupResponseDTO updateSrv6PolicyGroup(UpdateSrv6PolicyGroupRequestDTO request) {
        try {
            String url = platformBaseUrl + updateSrv6PolicyGroupUrl;
            log.info("开始调用平台接口更新应用组，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateSrv6PolicyGroup(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台更新应用组接口调用成功，响应：{}", responseBody);

                // 解析响应
                UpdateSrv6PolicyGroupResponseDTO platformResponse = JSON.parseObject(responseBody, UpdateSrv6PolicyGroupResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台更新应用组接口返回数据解析失败", "PARSE_ERROR", responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台更新应用组失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台更新应用组接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台更新应用组异常", e);
            throw new PlatformApiException("调用平台更新应用组接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台更新应用组调用
     */
    private UpdateSrv6PolicyGroupResponseDTO mockUpdateSrv6PolicyGroup(UpdateSrv6PolicyGroupRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台更新应用组调用，应用组ID：{}", request.getDataId());

        UpdateSrv6PolicyGroupResponseDTO response = new UpdateSrv6PolicyGroupResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        // 模拟一些处理时间
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟更新应用组成功");
        return response;
    }

    @Override
    public GetCustomNetworkScopeResponseDTO getCustomNetworkScope(GetCustomNetworkScopeRequestDTO request) {
        try {
            String url = platformBaseUrl + getCustomNetworkScopeUrl;
            log.info("开始调用平台接口查询作用域，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetCustomNetworkScope(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询作用域接口调用成功，响应：{}", responseBody);

                // 解析响应
                GetCustomNetworkScopeResponseDTO platformResponse = JSON.parseObject(responseBody, GetCustomNetworkScopeResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    GetCustomNetworkScopeResponseDTO errorResponse = new GetCustomNetworkScopeResponseDTO();
                    errorResponse.setCode("500");
                    errorResponse.setMessage("响应数据解析失败");
                    return errorResponse;
                }
            } else {
                log.error("平台查询作用域失败，状态码：{}，响应：{}", response.getStatus(), response.body());
                GetCustomNetworkScopeResponseDTO errorResponse = new GetCustomNetworkScopeResponseDTO();
                errorResponse.setCode(String.valueOf(response.getStatus()));
                errorResponse.setMessage("平台接口调用失败");
                return errorResponse;
            }

        } catch (Exception e) {
            log.error("调用平台查询作用域异常", e);
            GetCustomNetworkScopeResponseDTO errorResponse = new GetCustomNetworkScopeResponseDTO();
            errorResponse.setCode("500");
            errorResponse.setMessage("系统异常：" + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 模拟平台查询作用域调用
     */
    private GetCustomNetworkScopeResponseDTO mockGetCustomNetworkScope(GetCustomNetworkScopeRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询作用域调用，应用组ID：{}", request.getGroupId());

        GetCustomNetworkScopeResponseDTO response = new GetCustomNetworkScopeResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("SUCCESS");
        response.setSuccessful(true);

        // 创建分页结果
        GetCustomNetworkScopeResponseDTO.PageVO pageVO = new GetCustomNetworkScopeResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(-1L);
        pageVO.setTotalItem(0L);
        pageVO.setTotalPage(0L);
        pageVO.setRecords(new ArrayList<>());

        response.setResult(pageVO);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟作用域查询响应");
        return response;
    }

    @Override
    public PlatformInventoryInfoResponseDTO getInventoryInfo(PlatformInventoryInfoRequestDTO request) {
        try {
            String url = platformBaseUrl + getInventoryInfoUrl;
            log.info("开始调用平台接口查询设备硬件版本信息，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetInventoryInfo(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询硬件版本信息成功，响应：{}", responseBody);

                // 解析响应
                PlatformInventoryInfoResponseDTO inventoryResponse = JSON.parseObject(responseBody, PlatformInventoryInfoResponseDTO.class);
                if (inventoryResponse != null && inventoryResponse.getData() != null) {
                    log.info("平台硬件版本查询成功，设备ID：{}，返回记录数：{}", request.getDeviceIds(), inventoryResponse.getData().size());
                    return inventoryResponse;
                } else {
                    log.error("平台查询硬件版本信息失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询硬件版本信息失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询硬件版本信息失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询硬件版本信息失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询硬件版本信息接口异常", e);
            throw new PlatformApiException("调用平台查询硬件版本信息接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟平台硬件版本查询调用
     */
    private PlatformInventoryInfoResponseDTO mockGetInventoryInfo(PlatformInventoryInfoRequestDTO request) {
        log.info("测试模式：模拟平台硬件版本查询调用，设备ID：{}", request.getDeviceIds());

        PlatformInventoryInfoResponseDTO response = new PlatformInventoryInfoResponseDTO();

        // 创建分页信息
        PlatformInventoryInfoResponseDTO.Paging paging = new PlatformInventoryInfoResponseDTO.Paging();
        paging.setStart(0);
        paging.setSize(1);
        paging.setTotal(1);
        response.setPaging(paging);

        // 创建模拟硬件信息数据
        PlatformInventoryInfoResponseDTO.InventoryData inventoryData = new PlatformInventoryInfoResponseDTO.InventoryData();
        inventoryData.setDeviceId(Long.valueOf(request.getDeviceIds() != null ? request.getDeviceIds() : "4162712099619362"));
        inventoryData.setPhysicalIndex(194);
        inventoryData.setDeviceIp("************");
        inventoryData.setDeviceName("H3C");
        inventoryData.setResourceRegionId(1742378382178L);
        inventoryData.setRegionId(1742378382178L);
        inventoryData.setRegionIp("localhost");
        inventoryData.setPhysicalDescr("PSU");
        inventoryData.setIfDesc("");
        inventoryData.setPhysicalOid("*******.4.1.25506.*******");
        inventoryData.setPhysicalClass(6);
        inventoryData.setPhysicalName("PSU 2");
        inventoryData.setPhysicalHardwareRev("V1.0.0"); // 模拟硬件版本
        inventoryData.setPhysicalFirmwareRev("");
        inventoryData.setPhysicalSoftwareRev("");
        inventoryData.setPhysicalSerialNum("210231A1SUH18A000581");
        inventoryData.setPhysicalMfgName("");
        inventoryData.setPhysicalModelName("");
        inventoryData.setPhysicalAssetId("");
        inventoryData.setPhysicalIsFru(1);
        inventoryData.setPhysicalManufacturingDateStr("");
        inventoryData.setPhysicalCleiCode("");
        inventoryData.setPhysicalBuildInfo("");
        inventoryData.setPhysicalDeviceBom("");
        inventoryData.setPhysicalBoardSerialNumber("210231A1SUH18A000581");
        inventoryData.setPhysicalMaintainMaxDateStr("");
        inventoryData.setProductNumber("");
        inventoryData.setStackType(2);
        inventoryData.setStackMemberId(-1);
        inventoryData.setStackMemberName("");
        inventoryData.setStackMemberRole("");
        inventoryData.setCollectTimeStr("2025-03-21 10:44:29");

        response.setData(Arrays.asList(inventoryData));

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟硬件版本查询响应");
        return response;
    }

    @Override
    public GetSrv6PolicyTrailResponseDTO getSrv6PolicyTrail(GetSrv6PolicyTrailRequestDTO request) {
        try {
            String url = platformBaseUrl + getSrv6PolicyTrailUrl;
            log.info("开始调用平台接口查询SRv6 Policy业务列表，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetSrv6PolicyTrail(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询SRv6 Policy业务列表接口调用成功，响应：{}", responseBody);

                // 解析响应
                GetSrv6PolicyTrailResponseDTO platformResponse = JSON.parseObject(responseBody, GetSrv6PolicyTrailResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    GetSrv6PolicyTrailResponseDTO errorResponse = new GetSrv6PolicyTrailResponseDTO();
                    errorResponse.setCode("500");
                    errorResponse.setMessage("响应数据解析失败");
                    return errorResponse;
                }
            } else {
                log.error("平台查询SRv6 Policy业务列表接口调用失败，状态码：{}，响应：{}", response.getStatus(), response.body());
                GetSrv6PolicyTrailResponseDTO errorResponse = new GetSrv6PolicyTrailResponseDTO();
                errorResponse.setCode(String.valueOf(response.getStatus()));
                errorResponse.setMessage("平台接口调用失败");
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("调用平台查询SRv6 Policy业务列表接口异常", e);
            GetSrv6PolicyTrailResponseDTO errorResponse = new GetSrv6PolicyTrailResponseDTO();
            errorResponse.setCode("500");
            errorResponse.setMessage("接口调用异常：" + e.getMessage());
            return errorResponse;
        }
    }

    @Override
    public GetSrv6PolicyTrailDeployDetailResponseDTO getSrv6PolicyTrailDeployDetail(GetSrv6PolicyTrailDeployDetailRequestDTO request) {
        try {
            String url = platformBaseUrl + getSrv6PolicyTrailDeployDetailUrl + "?dataId=" + request.getDataId();
            log.info("开始调用平台接口查询SRv6 Policy业务部署详情，url：{}，业务ID：{}", url, request.getDataId());

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetSrv6PolicyTrailDeployDetail(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询SRv6 Policy业务部署详情接口调用成功，响应：{}", responseBody);

                // 解析响应
                GetSrv6PolicyTrailDeployDetailResponseDTO platformResponse = JSON.parseObject(responseBody, GetSrv6PolicyTrailDeployDetailResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    GetSrv6PolicyTrailDeployDetailResponseDTO errorResponse = new GetSrv6PolicyTrailDeployDetailResponseDTO();
                    errorResponse.setCode("500");
                    errorResponse.setMessage("响应数据解析失败");
                    return errorResponse;
                }
            } else {
                log.error("平台查询SRv6 Policy业务部署详情接口调用失败，状态码：{}，响应：{}", response.getStatus(), response.body());
                GetSrv6PolicyTrailDeployDetailResponseDTO errorResponse = new GetSrv6PolicyTrailDeployDetailResponseDTO();
                errorResponse.setCode(String.valueOf(response.getStatus()));
                errorResponse.setMessage("平台接口调用失败");
                return errorResponse;
            }
        } catch (Exception e) {
            log.error("调用平台查询SRv6 Policy业务部署详情接口异常", e);
            GetSrv6PolicyTrailDeployDetailResponseDTO errorResponse = new GetSrv6PolicyTrailDeployDetailResponseDTO();
            errorResponse.setCode("500");
            errorResponse.setMessage("接口调用异常：" + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 模拟平台查询SRv6 Policy业务列表调用
     */
    private GetSrv6PolicyTrailResponseDTO mockGetSrv6PolicyTrail(GetSrv6PolicyTrailRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询SRv6 Policy业务列表调用，应用组ID：{}", request.getGroupId());

        GetSrv6PolicyTrailResponseDTO response = new GetSrv6PolicyTrailResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("查询成功");
        response.setSuccessful(true);

        GetSrv6PolicyTrailResponseDTO.PageVO pageVO = new GetSrv6PolicyTrailResponseDTO.PageVO();
        pageVO.setPageNum(1L);
        pageVO.setPageSize(10L);
        pageVO.setTotalItem(1L);
        pageVO.setTotalPage(1L);

        // 创建模拟业务数据
        GetSrv6PolicyTrailResponseDTO.GetSrv6PolicyTrailVO trailVO = new GetSrv6PolicyTrailResponseDTO.GetSrv6PolicyTrailVO();
        trailVO.setDataId(request.getGroupId() != null ? request.getGroupId() + 1000 : 1001L);
        trailVO.setGroupId(request.getGroupId());
        trailVO.setTrailName("TestTrail-" + request.getGroupId());
        trailVO.setDescription("测试业务");
        trailVO.setSourceNeId(1001L);
        trailVO.setSourceNeName("Device-1001");
        trailVO.setSourceNeIp("***********");
        trailVO.setDestinationNeId(1002L);
        trailVO.setDestinationNeName("Device-1002");
        trailVO.setDestinationNeIp("***********");
        trailVO.setDirection("BIDIRECTIONAL");
        trailVO.setCoRouted(false);
        trailVO.setPathPlanningMode("AUTO");
        trailVO.setCandipathNum("MULTIPLE");
        trailVO.setPolicyDeployMode("NETCONF");
        trailVO.setCalcLimit("METRIC");
        trailVO.setDeployStatus("DEPLOY_SUCCESS");
        trailVO.setPlanStatus("PLAN_SUCCEEDED");

        pageVO.setRecords(Arrays.asList(trailVO));
        response.setResult(pageVO);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟SRv6 Policy业务列表查询响应");
        return response;
    }

    /**
     * 模拟平台查询SRv6 Policy业务部署详情调用
     */
    private GetSrv6PolicyTrailDeployDetailResponseDTO mockGetSrv6PolicyTrailDeployDetail(GetSrv6PolicyTrailDeployDetailRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询SRv6 Policy业务部署详情调用，业务ID：{}", request.getDataId());

        GetSrv6PolicyTrailDeployDetailResponseDTO response = new GetSrv6PolicyTrailDeployDetailResponseDTO();
        response.setCode("SUCCESS");
        response.setMessage("查询成功");
        response.setSuccessful(true);

        GetSrv6PolicyTrailDeployDetailResponseDTO.GetSrv6PolicyTrailDeployDetailVO detailVO =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.GetSrv6PolicyTrailDeployDetailVO();

        // 正向信息
        detailVO.setForwardPolicyId(request.getDataId() + 100);
        detailVO.setForwardName("Forward-" + request.getDataId());
        detailVO.setForwardSourceNeName("Device-1001");
        detailVO.setForwardDestinationNeName("Device-1002");
        detailVO.setForwardTraffic(1000L);

        // 反向信息
        detailVO.setReversePolicyId(request.getDataId() + 200);
        detailVO.setReverseName("Reverse-" + request.getDataId());
        detailVO.setReverseSourceNeName("Device-1002");
        detailVO.setReverseDestinationNeName("Device-1001");
        detailVO.setReverseTraffic(1000L);

        // 创建模拟路径信息
        GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicyCandiDeployPathEntity forwardPath =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicyCandiDeployPathEntity();
        forwardPath.setDataId(1001L);
        forwardPath.setSrv6PolicyId(request.getDataId() + 100);
        forwardPath.setPathIndex(0);
        forwardPath.setColorId(100L);
        forwardPath.setActive(1);

        // 创建模拟分段路径
        GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathEntity sidPath =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathEntity();
        sidPath.setDataId(2001L);
        sidPath.setSidPathName("SidPath-1");
        sidPath.setCandidatePathId(1001L);
        sidPath.setSidPathIndex(0);
        sidPath.setWeight(1L);

        // 创建模拟路径详情
        GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathHopEntity hopEntity =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathHopEntity();
        hopEntity.setMemberId(3001L);
        hopEntity.setMemberName("Node-1001");
        hopEntity.setMemberType("NODE");
        hopEntity.setMemberIndex(1);

        sidPath.setDetailList(Arrays.asList(hopEntity));
        forwardPath.setSidPathList(Arrays.asList(sidPath));
        detailVO.setForwardCandiPathInfo(Arrays.asList(forwardPath));

        // 反向路径信息（类似正向）
        GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicyCandiDeployPathEntity reversePath =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicyCandiDeployPathEntity();
        reversePath.setDataId(1002L);
        reversePath.setSrv6PolicyId(request.getDataId() + 200);
        reversePath.setPathIndex(0);
        reversePath.setColorId(100L);
        reversePath.setActive(1);

        GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathEntity reverseSidPath =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathEntity();
        reverseSidPath.setDataId(2002L);
        reverseSidPath.setSidPathName("SidPath-2");
        reverseSidPath.setCandidatePathId(1002L);
        reverseSidPath.setSidPathIndex(0);
        reverseSidPath.setWeight(1L);

        GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathHopEntity reverseHopEntity =
                new GetSrv6PolicyTrailDeployDetailResponseDTO.Srv6PolicySidPathHopEntity();
        reverseHopEntity.setMemberId(3002L);
        reverseHopEntity.setMemberName("Node-1002");
        reverseHopEntity.setMemberType("NODE");
        reverseHopEntity.setMemberIndex(1);

        reverseSidPath.setDetailList(Arrays.asList(reverseHopEntity));
        reversePath.setSidPathList(Arrays.asList(reverseSidPath));
        detailVO.setReverseCandiPathInfo(Arrays.asList(reversePath));

        response.setResult(detailVO);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟SRv6 Policy业务部署详情查询响应");
        return response;
    }

    // ==================== QoS相关接口实现 ====================

    @Override
    public boolean addQosClassifier(PlatformQosClassifierRequestDTO request) {
        try {
            String url = platformBaseUrl + addQosClassifierUrl;
            log.info("开始调用平台接口新增流分类，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddQosClassifier(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台新增流分类接口调用成功，响应：{}", responseBody);
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台新增流分类接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台新增流分类接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台新增流分类接口异常", e);
            throw new PlatformApiException("调用平台新增流分类接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateQosClassifier(PlatformQosClassifierUpdateRequestDTO request) {
        try {
            String url = platformBaseUrl + updateQosClassifierUrl;
            log.info("开始调用平台接口修改流分类，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateQosClassifier(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                log.info("平台修改流分类接口调用成功");
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台修改流分类接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台修改流分类接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台修改流分类接口异常", e);
            throw new PlatformApiException("调用平台修改流分类接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteQosClassifier(Integer classifierId) {
        try {
            String url = platformBaseUrl + deleteQosClassifierUrl + "?classifierId=" + classifierId;
            log.info("开始调用平台接口删除流分类，url：{}，流分类ID：{}", url, classifierId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteQosClassifier(classifierId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台删除流分类接口调用成功，响应：{}", responseBody);
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台删除流分类接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台删除流分类接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台删除流分类接口异常，流分类ID：{}", classifierId, e);
            throw new PlatformApiException("调用平台删除流分类接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformDeleteResult deleteQosClassifierWithResult(Integer classifierId) {
        try {
            String url = platformBaseUrl + deleteQosClassifierUrl + "?classifierId=" + classifierId;
            log.info("开始调用平台接口删除流分类（带结果），url：{}，流分类ID：{}", url, classifierId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteQosClassifierWithResult(classifierId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();
            String responseBody = response.body();

            if (response.isOk()) {
                // 即使状态码是200，也需要检查响应内容判断是否真正成功
                if (isDeleteSuccessResponse(responseBody)) {
                    log.info("平台删除流分类接口调用成功，流分类ID：{}", classifierId);
                    return PlatformDeleteResult.success();
                } else {
                    // 响应内容表明删除失败，需要重试
                    log.warn("平台删除流分类接口返回失败响应，流分类ID：{}，响应：{}", classifierId, responseBody);

                    // 解析错误信息
                    String errorCode = parseErrorCodeFromSuccessResponse(responseBody);
                    String errorMessage = parseErrorMessageFromSuccessResponse(responseBody);

                    // 对于服务器忙的情况，返回500状态码以触发重试
                    if (isServerBusyResponse(responseBody)) {
                        return PlatformDeleteResult.failure(500, "SERVER_BUSY", errorMessage, responseBody);
                    } else {
                        return PlatformDeleteResult.failure(200, errorCode, errorMessage, responseBody);
                    }
                }
            } else {
                log.error("平台删除流分类接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);

                // 解析错误信息
                String errorCode = parseErrorCode(responseBody);
                String errorMessage = parseErrorMessage(responseBody);

                return PlatformDeleteResult.failure(response.getStatus(), errorCode, errorMessage, responseBody);
            }
        } catch (Exception e) {
            log.error("调用平台删除流分类接口异常，流分类ID：{}", classifierId, e);
            return PlatformDeleteResult.failure(500, null, e.getMessage(), null);
        }
    }

    @Override
    public PlatformQosClassifierListResponseDTO getQosClassifierList(PlatformQosClassifierListRequestDTO request) {
        try {
            String url = buildQosClassifierListUrl(request);
            log.info("开始调用平台接口查询流分类列表，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosClassifierList(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询流分类列表接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosClassifierListResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosClassifierListResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询流分类列表接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询流分类列表接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询流分类列表接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询流分类列表接口异常", e);
            throw new PlatformApiException("调用平台查询流分类列表接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosBehaviorResponseDTO addQosBehavior(PlatformQosBehaviorRequestDTO request) {
        try {
            String url = platformBaseUrl + addQosBehaviorUrl;
            log.info("开始调用平台接口新增流行为，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddQosBehavior(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台新增流行为接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosBehaviorResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosBehaviorResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台新增流行为接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台新增流行为接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台新增流行为接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台新增流行为接口异常", e);
            throw new PlatformApiException("调用平台新增流行为接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosBehaviorDetailResponseDTO getQosBehaviorDetail(PlatformQosBehaviorDetailRequestDTO request) {
        try {
            String url = platformBaseUrl + getQosBehaviorDetailUrl + "?behaviorId=" + request.getBehaviorId();
            log.info("开始调用平台接口查询流行为详情，url：{}，流行为ID：{}", url, request.getBehaviorId());

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosBehaviorDetail(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询流行为详情接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosBehaviorDetailResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosBehaviorDetailResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询流行为详情接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询流行为详情接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询流行为详情接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询流行为详情接口异常，流行为ID：{}", request.getBehaviorId(), e);
            throw new PlatformApiException("调用平台查询流行为详情接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateQosBehavior(PlatformQosBehaviorUpdateRequestDTO request) {
        try {
            String url = platformBaseUrl + updateQosBehaviorUrl;
            log.info("开始调用平台接口修改流行为，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateQosBehavior(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                log.info("平台修改流行为接口调用成功");
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台修改流行为接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台修改流行为接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台修改流行为接口异常", e);
            throw new PlatformApiException("调用平台修改流行为接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteQosBehavior(Integer behaviorId) {
        try {
            String url = platformBaseUrl + deleteQosBehaviorUrl + "?policyId=" + behaviorId;
            log.info("开始调用平台接口删除流行为，url：{}，流行为ID：{}", url, behaviorId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteQosBehavior(behaviorId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                log.info("平台删除流行为接口调用成功，流行为ID：{}", behaviorId);
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台删除流行为接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台删除流行为接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台删除流行为接口异常，流行为ID：{}", behaviorId, e);
            throw new PlatformApiException("调用平台删除流行为接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformDeleteResult deleteQosBehaviorWithResult(Integer behaviorId) {
        try {
            String url = platformBaseUrl + deleteQosBehaviorUrl + "?behaviorId=" + behaviorId;
            log.info("开始调用平台接口删除流行为（带结果），url：{}，流行为ID：{}", url, behaviorId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteQosBehaviorWithResult(behaviorId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body("{}")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();
            String responseBody = response.body();

            if (response.isOk()) {
                // 即使状态码是200，也需要检查响应内容判断是否真正成功
                if (isDeleteSuccessResponse(responseBody)) {
                    log.info("平台删除流行为接口调用成功，流行为ID：{}", behaviorId);
                    return PlatformDeleteResult.success();
                } else {
                    // 响应内容表明删除失败，需要重试
                    log.warn("平台删除流行为接口返回失败响应，流行为ID：{}，响应：{}", behaviorId, responseBody);

                    // 解析错误信息
                    String errorCode = parseErrorCodeFromSuccessResponse(responseBody);
                    String errorMessage = parseErrorMessageFromSuccessResponse(responseBody);

                    // 对于服务器忙的情况，返回500状态码以触发重试
                    if (isServerBusyResponse(responseBody)) {
                        return PlatformDeleteResult.failure(500, "SERVER_BUSY", errorMessage, responseBody);
                    } else {
                        return PlatformDeleteResult.failure(200, errorCode, errorMessage, responseBody);
                    }
                }
            } else {
                log.error("平台删除流行为接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);

                // 解析错误信息
                String errorCode = parseErrorCode(responseBody);
                String errorMessage = parseErrorMessage(responseBody);

                return PlatformDeleteResult.failure(response.getStatus(), errorCode, errorMessage, responseBody);
            }
        } catch (Exception e) {
            log.error("调用平台删除流行为接口异常，流行为ID：{}", behaviorId, e);
            return PlatformDeleteResult.failure(500, null, e.getMessage(), null);
        }
    }

    @Override
    public PlatformQosPolicyResponseDTO addQosPolicy(PlatformQosPolicyRequestDTO request) {
        try {
            String url = platformBaseUrl + addQosPolicyUrl;
            log.info("开始调用平台接口新增流策略，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockAddQosPolicy(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台新增流策略接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosPolicyResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosPolicyResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台新增流策略接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台新增流策略接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台新增流策略接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台新增流策略接口异常", e);
            throw new PlatformApiException("调用平台新增流策略接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean deleteQosPolicy(Integer policyId) {
        try {
            String url = platformBaseUrl + deleteQosPolicyUrl + "?policyId=" + policyId;
            log.info("开始调用平台接口删除流策略，url：{}，流策略ID：{}", url, policyId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeleteQosPolicy(policyId);
            }

            // 使用带认证的HTTP请求（DELETE方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.delete(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                log.info("平台删除流策略接口调用成功，流策略ID：{}", policyId);
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台删除流策略接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台删除流策略接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台删除流策略接口异常，流策略ID：{}", policyId, e);
            throw new PlatformApiException("调用平台删除流策略接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean updateQosPolicy(PlatformQosPolicyUpdateRequestDTO request) {
        try {
            String url = platformBaseUrl + updateQosPolicyUrl;
            log.info("开始调用平台接口修改流策略，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockUpdateQosPolicy(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.put(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .body(JSON.toJSONString(request))
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台修改流策略接口调用成功，响应：{}", responseBody);
                return true;
            } else {
                String responseBody = response.body();
                log.error("平台修改流策略接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台修改流策略接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台修改流策略接口异常", e);
            throw new PlatformApiException("调用平台修改流策略接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosPolicyListResponseDTO getQosPolicyList(PlatformQosPolicyListRequestDTO request) {
        try {
            String url = buildQosPolicyListUrl(request);
            log.info("开始调用平台接口查询流策略列表，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosPolicyList(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询流策略列表接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosPolicyListResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosPolicyListResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询流策略列表接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询流策略列表接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询流策略列表接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询流策略列表接口异常", e);
            throw new PlatformApiException("调用平台查询流策略列表接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosPolicyDetailRuleResponseDTO getQosPolicyRuleDetail(PlatformQosPolicyDetailRuleRequestDTO request) {
        try {
            String url = buildQosPolicyDetailRuleUrl(request);
            log.info("开始调用平台接口查询流策略CB对详情，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosPolicyDetail(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询流策略CB对详情接口调用成功，响应：{}", responseBody);

                PlatformQosPolicyDetailRuleResponseDTO result = JSON.parseObject(responseBody, PlatformQosPolicyDetailRuleResponseDTO.class);
                if (result != null) {
                    return result;
                } else {
                    log.error("平台查询流策略CB对详情接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询流策略CB对详情接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询流策略CB对详情接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询流策略CB对详情接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询流策略CB对详情接口异常", e);
            throw new PlatformApiException("调用平台查询流策略CB对详情接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosPolicyDeployResponseDTO deployQosPolicy(PlatformQosPolicyDeployRequestDTO request) {
        try {
            String url = platformBaseUrl + deployQosPolicyUrl;
            log.info("开始调用平台接口部署流策略到设备接口，url：{}，请求参数：{}", url, JSON.toJSONString(request));

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockDeployQosPolicy(request);
            }

            // 使用带认证的HTTP请求
            String token = TokenContext.getToken();
            HttpRequest httpRequest = createAuthenticatedRequest(url, request, token);
            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台部署流策略接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosPolicyDeployResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosPolicyDeployResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台部署流策略接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台部署流策略接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台部署流策略接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台部署流策略接口异常", e);
            throw new PlatformApiException("调用平台部署流策略接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosDeviceListResponseDTO getQosDeviceList(PlatformQosDeviceListRequestDTO request) {
        try {
            String url = buildQosDeviceListUrl(request);
            log.info("开始调用平台接口查询QoS设备列表，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosDeviceList(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询QoS设备列表接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosDeviceListResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosDeviceListResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询QoS设备列表接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询QoS设备列表接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询QoS设备列表接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询QoS设备列表接口异常", e);
            throw new PlatformApiException("调用平台查询QoS设备列表接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosDeviceInterfaceListResponseDTO getQosDeviceInterfaceList(PlatformQosDeviceInterfaceListRequestDTO request) {
        try {
            String url = buildQosDeviceInterfaceListUrl(request);
            log.info("开始调用平台接口查询设备接口列表，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosDeviceInterfaceList(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询设备接口列表接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformQosDeviceInterfaceListResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformQosDeviceInterfaceListResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询设备接口列表接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询设备接口列表接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询设备接口列表接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询设备接口列表接口异常", e);
            throw new PlatformApiException("调用平台查询设备接口列表接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformAclDetailResponseDTO getAclDetail(Integer aclId) {
        try {
            String url = platformBaseUrl + getAclDetailUrl + "?aclId=" + aclId;
            log.info("开始调用平台接口查询ACL模板详情，url：{}，ACL ID：{}", url, aclId);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetAclDetail(aclId);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();

            if (response.isOk()) {
                String responseBody = response.body();
                log.info("平台查询ACL模板详情接口调用成功，响应：{}", responseBody);

                // 解析响应
                PlatformAclDetailResponseDTO platformResponse = JSON.parseObject(responseBody, PlatformAclDetailResponseDTO.class);
                if (platformResponse != null) {
                    return platformResponse;
                } else {
                    log.error("平台接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询ACL模板详情接口返回数据解析失败", null, responseBody);
                }
            } else {
                String responseBody = response.body();
                log.error("平台查询ACL模板详情接口调用失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询ACL模板详情接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }
        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台查询ACL模板详情接口异常，ACL ID：{}", aclId, e);
            throw new PlatformApiException("调用平台查询ACL模板详情接口异常：" + e.getMessage(), e);
        }
    }

    @Override
    public PlatformQosPolicyDeployHistoryResponseDTO getQosPolicyDeployHistory(PlatformQosPolicyDeployHistoryRequestDTO request) {
        try {
            String url = buildQosPolicyDeployHistoryUrl(request);
            log.info("开始调用平台接口查询流策略部署历史，url：{}", url);

            // 测试模式下模拟平台调用
            if (testModeConfig.isMockAdwanPlatform()) {
                return mockGetQosPolicyDeployHistory(request);
            }

            // 使用带认证的HTTP请求（GET方法）
            String token = TokenContext.getToken();
            HttpRequest httpRequest = HttpRequest.get(url)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .timeout(30000);

            // 添加认证token到请求头
            if (token != null && !token.isEmpty()) {
                httpRequest.header("Cookie", "X-Subject-Token=" + token);
                log.debug("已添加认证Token到Cookie: X-Subject-Token");
            } else {
                log.warn("认证Token为空，请求可能失败");
            }

            HttpResponse response = httpRequest.execute();
            String responseBody = response.body();
            log.info("平台接口查询流策略部署历史响应：{}", responseBody);

            if (response.getStatus() == 200) {
                PlatformQosPolicyDeployHistoryResponseDTO result = JSON.parseObject(responseBody, PlatformQosPolicyDeployHistoryResponseDTO.class);
                if (result != null) {
                    return result;
                } else {
                    log.error("平台查询流策略部署历史接口返回数据解析失败，响应：{}", responseBody);
                    throw new PlatformApiException("平台查询流策略部署历史接口返回数据解析失败", null, responseBody);
                }
            } else {
                log.error("平台接口查询流策略部署历史失败，状态码：{}，响应：{}", response.getStatus(), responseBody);
                throw new PlatformApiException("平台查询流策略部署历史接口调用失败，HTTP状态码：" + response.getStatus(),
                        null, responseBody, response.getStatus());
            }

        } catch (PlatformApiException e) {
            // 重新抛出平台API异常
            throw e;
        } catch (Exception e) {
            log.error("调用平台接口查询流策略部署历史异常", e);
            throw new PlatformApiException("调用平台查询流策略部署历史接口异常：" + e.getMessage(), e);
        }
    }

    /**
     * 模拟查询流策略部署历史调用
     */
    private PlatformQosPolicyDeployHistoryResponseDTO mockGetQosPolicyDeployHistory(PlatformQosPolicyDeployHistoryRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询流策略部署历史调用，策略ID：{}", request.getPolicyId());

        PlatformQosPolicyDeployHistoryResponseDTO response = new PlatformQosPolicyDeployHistoryResponseDTO();
        PlatformQosPolicyDeployHistoryResponseDTO.Output output = new PlatformQosPolicyDeployHistoryResponseDTO.Output();

        output.setDeployType(6);
        output.setTemplateId(request.getPolicyId());
        output.setTemplateName(request.getPolicyName());
        output.setRowCount(1);
        output.setSize(request.getSize() != null ? request.getSize() : 15);
        output.setStart(request.getStart() != null ? request.getStart() : 0);

        // 创建模拟部署历史数据
        PlatformQosPolicyDeployHistoryResponseDTO.DeployHistoryInfo historyInfo =
                new PlatformQosPolicyDeployHistoryResponseDTO.DeployHistoryInfo();
        historyInfo.setDeployTime("2025-01-09 10:30:00");
        historyInfo.setDeployType(1);
        historyInfo.setDevId(random.nextInt(1000) + 1000);
        historyInfo.setDevIp("192.168.1." + (random.nextInt(200) + 1));
        historyInfo.setDevName("test_device_" + random.nextInt(100));
        historyInfo.setDirection(0);
        historyInfo.setIfUuid("if_uuid_" + random.nextInt(1000));
        historyInfo.setInterfaceDescription("测试接口");
        historyInfo.setInterfaceName("GigabitEthernet1/0/" + random.nextInt(10));
        historyInfo.setPolicyId(request.getPolicyId());
        historyInfo.setPolicyName(request.getPolicyName());
        historyInfo.setPreorder(1);
        historyInfo.setResult(1); // 1表示成功
        historyInfo.setReason("");
        historyInfo.setReasonEn("");
        historyInfo.setRmUID("rm_uid_" + random.nextInt(1000));
        historyInfo.setShareMode(false);

        output.setList(Arrays.asList(historyInfo));
        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟流策略部署历史响应");
        return response;
    }

    // ==================== QoS相关辅助方法 ====================

    /**
     * 构建流分类列表查询URL
     */
    private String buildQosClassifierListUrl(PlatformQosClassifierListRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder(platformBaseUrl + getQosClassifierListUrl);
        urlBuilder.append("?desc=").append(request.getDesc() != null ? request.getDesc() : true);
        urlBuilder.append("&start=").append(request.getStart() != null ? request.getStart() : 0);
        urlBuilder.append("&size=").append(request.getSize() != null ? request.getSize() : 15);

        if (request.getClassifierName() != null && !request.getClassifierName().isEmpty()) {
            urlBuilder.append("&classifierName=").append(request.getClassifierName());
        }
        if (request.getDescription() != null && !request.getDescription().isEmpty()) {
            urlBuilder.append("&description=").append(request.getDescription());
        }
        if (request.getDevResultList() != null) {
            urlBuilder.append("&devResultList=").append(request.getDevResultList());
        }
        if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
            urlBuilder.append("&sortBy=").append(request.getSortBy());
        }
        if (request.getIsWithRule() != null) {
            urlBuilder.append("&isWithRule=").append(request.getIsWithRule());
        }

        return urlBuilder.toString();
    }

    /**
     * 构建流策略列表查询URL
     */
    private String buildQosPolicyListUrl(PlatformQosPolicyListRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder(platformBaseUrl + getQosPolicyListUrl);
        urlBuilder.append("?desc=").append(request.getDesc() != null ? request.getDesc() : false);
        urlBuilder.append("&start=").append(request.getStart() != null ? request.getStart() : 0);
        urlBuilder.append("&size=").append(request.getSize() != null ? request.getSize() : 15);

        if (request.getPolicyName() != null && !request.getPolicyName().isEmpty()) {
            urlBuilder.append("&policyName=").append(request.getPolicyName());
        }
        if (request.getDescription() != null && !request.getDescription().isEmpty()) {
            urlBuilder.append("&description=").append(request.getDescription());
        }
        if (request.getType() != null) {
            urlBuilder.append("&type=").append(request.getType());
        }
        if (request.getDevResultList() != null) {
            urlBuilder.append("&devResultList=").append(request.getDevResultList());
        }
        if (request.getIfResultList() != null) {
            urlBuilder.append("&ifResultList=").append(request.getIfResultList());
        }
        if (request.getSrv6ResultList() != null) {
            urlBuilder.append("&srv6ResultList=").append(request.getSrv6ResultList());
        }
        if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
            urlBuilder.append("&sortBy=").append(request.getSortBy());
        }

        return urlBuilder.toString();
    }

    /**
     * 构建流策略CB对详情查询URL
     */
    private String buildQosPolicyDetailRuleUrl(PlatformQosPolicyDetailRuleRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder(platformBaseUrl + getQosPolicyDetailRuleUrl);
        urlBuilder.append("?policyId=").append(request.getPolicyId());
        urlBuilder.append("&start=").append(request.getStart() != null ? request.getStart() : 0);
        urlBuilder.append("&size=").append(request.getSize() != null ? request.getSize() : 100);

        return urlBuilder.toString();
    }

    /**
     * 构建QoS设备列表查询URL
     */
    private String buildQosDeviceListUrl(PlatformQosDeviceListRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder(platformBaseUrl + getQosDeviceListUrl);
        urlBuilder.append("?desc=").append(request.getDesc() != null ? request.getDesc() : true);
        urlBuilder.append("&start=").append(request.getStart() != null ? request.getStart() : 0);
        urlBuilder.append("&size=").append(request.getSize() != null ? request.getSize() : 15);

        if (request.getDevName() != null && !request.getDevName().isEmpty()) {
            urlBuilder.append("&devName=").append(request.getDevName());
        }
        if (request.getDevIp() != null && !request.getDevIp().isEmpty()) {
            urlBuilder.append("&devIp=").append(request.getDevIp());
        }
        if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
            urlBuilder.append("&sortBy=").append(request.getSortBy());
        }
        if (request.getPqId() != null) {
            urlBuilder.append("&pqId=").append(request.getPqId());
        }
        if (request.getDevType() != null) {
            urlBuilder.append("&devType=").append(request.getDevType());
        }

        return urlBuilder.toString();
    }

    /**
     * 构建设备接口列表查询URL
     */
    private String buildQosDeviceInterfaceListUrl(PlatformQosDeviceInterfaceListRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder(platformBaseUrl + getQosDeviceInterfaceListUrl);
        urlBuilder.append("?desc=").append(request.getDesc() != null ? request.getDesc() : true);
        urlBuilder.append("&start=").append(request.getStart() != null ? request.getStart() : 0);
        urlBuilder.append("&size=").append(request.getSize() != null ? request.getSize() : 15);

        if (request.getDevId() != null) {
            urlBuilder.append("&devId=").append(request.getDevId());
        }
        if (request.getDevuuId() != null && !request.getDevuuId().isEmpty()) {
            urlBuilder.append("&devuuId=").append(request.getDevuuId());
        }
        if (request.getInterfaceName() != null && !request.getInterfaceName().isEmpty()) {
            urlBuilder.append("&interfaceName=").append(request.getInterfaceName());
        }
        if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
            urlBuilder.append("&sortBy=").append(request.getSortBy());
        }

        return urlBuilder.toString();
    }

    /**
     * 构建流策略部署历史查询URL
     */
    private String buildQosPolicyDeployHistoryUrl(PlatformQosPolicyDeployHistoryRequestDTO request) {
        StringBuilder urlBuilder = new StringBuilder(platformBaseUrl + getQosPolicyDeployHistoryUrl);
        urlBuilder.append("?desc=").append(request.getDesc() != null ? request.getDesc() : true);
        urlBuilder.append("&start=").append(request.getStart() != null ? request.getStart() : 0);
        urlBuilder.append("&size=").append(request.getSize() != null ? request.getSize() : 15);

        if (request.getPolicyId() != null) {
            urlBuilder.append("&policyId=").append(request.getPolicyId());
        }
        if (request.getPolicyName() != null && !request.getPolicyName().isEmpty()) {
            urlBuilder.append("&policyName=").append(request.getPolicyName());
        }
        if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
            urlBuilder.append("&sortBy=").append(request.getSortBy());
        }
        if (request.getInterfaceName() != null && !request.getInterfaceName().isEmpty()) {
            urlBuilder.append("&interfaceName=").append(request.getInterfaceName());
        }
        if (request.getInterfaceDescription() != null && !request.getInterfaceDescription().isEmpty()) {
            urlBuilder.append("&interfaceDescription=").append(request.getInterfaceDescription());
        }
        if (request.getDeployKey() != null && !request.getDeployKey().isEmpty()) {
            urlBuilder.append("&deployKey=").append(request.getDeployKey());
        }
        if (request.getDevName() != null && !request.getDevName().isEmpty()) {
            urlBuilder.append("&devName=").append(request.getDevName());
        }
        if (request.getDirection() != null) {
            urlBuilder.append("&direction=").append(request.getDirection());
        }
        if (request.getStatusList() != null) {
            urlBuilder.append("&statusList=").append(request.getStatusList());
        }
        if (request.getStartTime() != null && !request.getStartTime().isEmpty()) {
            urlBuilder.append("&startTime=").append(request.getStartTime());
        }
        if (request.getEndTime() != null && !request.getEndTime().isEmpty()) {
            urlBuilder.append("&endTime=").append(request.getEndTime());
        }
        if (request.getTarget() != null) {
            urlBuilder.append("&target=").append(request.getTarget());
        }

        return urlBuilder.toString();
    }

    // ==================== QoS相关模拟方法 ====================

    /**
     * 模拟新增流分类调用
     */
    private boolean mockAddQosClassifier(PlatformQosClassifierRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增流分类调用，流分类名称：{}", request.getClassifierName());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流分类新增成功");
        return true;
    }

    /**
     * 模拟修改流分类调用
     */
    private boolean mockUpdateQosClassifier(PlatformQosClassifierUpdateRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台修改流分类调用，流分类ID：{}，流分类名称：{}",
                request.getClassifierId(), request.getClassifierName());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流分类修改成功");
        return true;
    }

    /**
     * 模拟删除流分类调用
     */
    private boolean mockDeleteQosClassifier(Integer classifierId) {
        log.info("测试模式：模拟ADWAN平台删除流分类调用，流分类ID：{}", classifierId);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流分类删除成功");
        return true;
    }

    /**
     * 模拟查询流分类列表调用
     */
    private PlatformQosClassifierListResponseDTO mockGetQosClassifierList(PlatformQosClassifierListRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询流分类列表调用，流分类名称：{}", request.getClassifierName());

        PlatformQosClassifierListResponseDTO response = new PlatformQosClassifierListResponseDTO();
        PlatformQosClassifierListResponseDTO.Output output = new PlatformQosClassifierListResponseDTO.Output();

        // 创建模拟流分类数据
        PlatformQosClassifierListResponseDTO.ClassifierInfo classifierInfo = new PlatformQosClassifierListResponseDTO.ClassifierInfo();
        classifierInfo.setClassifierId(random.nextInt(1000) + 1);
        classifierInfo.setClassifierName(request.getClassifierName() != null ? request.getClassifierName() : "test_classifier");
        classifierInfo.setDescription("测试流分类");
        classifierInfo.setLogic(1);
        classifierInfo.setDevResult(0);

        output.setList(Arrays.asList(classifierInfo));
        output.setRowCount(1);
        output.setSize(15);
        output.setStart(0);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟流分类列表查询响应");
        return response;
    }

    /**
     * 模拟新增流行为调用
     */
    private PlatformQosBehaviorResponseDTO mockAddQosBehavior(PlatformQosBehaviorRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增流行为调用，流行为名称：{}", request.getBehaviorName());

        PlatformQosBehaviorResponseDTO response = new PlatformQosBehaviorResponseDTO();
        PlatformQosBehaviorResponseDTO.Output output = new PlatformQosBehaviorResponseDTO.Output();

        output.setBehaviorId(random.nextInt(1000) + 1);
        output.setBehaviorName(request.getBehaviorName());
        output.setDescription(null);
        output.setDevResult(0);
        output.setDeploying(false);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流行为新增成功");
        return response;
    }

    /**
     * 模拟查询流行为详情调用
     */
    private PlatformQosBehaviorDetailResponseDTO mockGetQosBehaviorDetail(PlatformQosBehaviorDetailRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询流行为详情调用，流行为ID：{}", request.getBehaviorId());

        PlatformQosBehaviorDetailResponseDTO response = new PlatformQosBehaviorDetailResponseDTO();
        PlatformQosBehaviorDetailResponseDTO.Output output = new PlatformQosBehaviorDetailResponseDTO.Output();

        output.setBehaviorId(request.getBehaviorId());
        output.setBehaviorName("test_behavior_" + request.getBehaviorId());
        output.setDescription("测试流行为");
        output.setDevResult(0);

        // 设置基本配置
        output.setCar(new PlatformQosBehaviorDetailResponseDTO.CarConfig());
        output.setGts(new PlatformQosBehaviorDetailResponseDTO.GtsConfig());
        output.setQueue(new PlatformQosBehaviorDetailResponseDTO.QueueConfig());
        output.setFilter(new PlatformQosBehaviorDetailResponseDTO.FilterConfig());
        output.setAccount(new PlatformQosBehaviorDetailResponseDTO.AccountConfig());
        output.setRedirect(new PlatformQosBehaviorDetailResponseDTO.RedirectConfig());
        output.setMirror(new PlatformQosBehaviorDetailResponseDTO.MirrorConfig());
        output.setPolicy(new PlatformQosBehaviorDetailResponseDTO.PolicyConfig());

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟查询流行为详情成功");
        return response;
    }

    /**
     * 模拟修改流行为调用
     */
    private boolean mockUpdateQosBehavior(PlatformQosBehaviorUpdateRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台修改流行为调用，流行为ID：{}，流行为名称：{}",
                request.getBehaviorId(), request.getBehaviorName());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流行为修改成功");
        return true;
    }

    /**
     * 模拟删除流行为调用
     */
    private boolean mockDeleteQosBehavior(Integer behaviorId) {
        log.info("测试模式：模拟ADWAN平台删除流行为调用，流行为ID：{}", behaviorId);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流行为删除成功");
        return true;
    }

    /**
     * 模拟新增流策略调用
     */
    private PlatformQosPolicyResponseDTO mockAddQosPolicy(PlatformQosPolicyRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台新增流策略调用，流策略名称：{}", request.getPolicyName());

        PlatformQosPolicyResponseDTO response = new PlatformQosPolicyResponseDTO();
        PlatformQosPolicyResponseDTO.Output output = new PlatformQosPolicyResponseDTO.Output();

        output.setPolicyId(random.nextInt(1000) + 1);
        output.setPolicyName(request.getPolicyName());
        output.setDescription(request.getDescription());
        output.setDevResult(0);
        output.setIfResult(0);
        output.setSrv6Result(0);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流策略新增成功");
        return response;
    }

    /**
     * 模拟删除流策略调用
     */
    private boolean mockDeleteQosPolicy(Integer policyId) {
        log.info("测试模式：模拟ADWAN平台删除流策略调用，流策略ID：{}", policyId);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流策略删除成功");
        return true;
    }

    /**
     * 模拟修改流策略调用
     */
    private boolean mockUpdateQosPolicy(PlatformQosPolicyUpdateRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台修改流策略调用，策略ID：{}，策略名称：{}",
                request.getPolicyId(), request.getPolicyName());

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流策略修改成功");
        return true;
    }

    /**
     * 模拟查询流策略列表调用
     */
    private PlatformQosPolicyListResponseDTO mockGetQosPolicyList(PlatformQosPolicyListRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询流策略列表调用，流策略名称：{}", request.getPolicyName());

        PlatformQosPolicyListResponseDTO response = new PlatformQosPolicyListResponseDTO();
        PlatformQosPolicyListResponseDTO.Output output = new PlatformQosPolicyListResponseDTO.Output();

        // 创建模拟流策略数据
        PlatformQosPolicyListResponseDTO.PolicyInfo policyInfo = new PlatformQosPolicyListResponseDTO.PolicyInfo();
        policyInfo.setPolicyId(random.nextInt(1000) + 1);
        policyInfo.setPolicyName(request.getPolicyName() != null ? request.getPolicyName() : "test_policy");
        policyInfo.setDescription("测试流策略");
        policyInfo.setDevResult(0);
        policyInfo.setIfResult(0);
        policyInfo.setSrv6Result(0);

        output.setList(Arrays.asList(policyInfo));
        output.setRowCount(1);
        output.setSize(15);
        output.setStart(0);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟流策略列表查询响应");
        return response;
    }

    /**
     * 模拟部署流策略调用
     */
    private PlatformQosPolicyDeployResponseDTO mockDeployQosPolicy(PlatformQosPolicyDeployRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台部署流策略调用，模板ID：{}", request.getTemplateId());

        PlatformQosPolicyDeployResponseDTO response = new PlatformQosPolicyDeployResponseDTO();
        response.setOutput("AACKy5CPVjKXsZ7t8kv13vLIDTQq4fvz"); // 模拟任务ID

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟流策略部署成功");
        return response;
    }

    /**
     * 模拟查询流策略CB对详情调用
     */
    private PlatformQosPolicyDetailRuleResponseDTO mockGetQosPolicyDetail(PlatformQosPolicyDetailRuleRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询流策略CB对详情调用，策略ID：{}", request.getPolicyId());

        PlatformQosPolicyDetailRuleResponseDTO response = new PlatformQosPolicyDetailRuleResponseDTO();
        PlatformQosPolicyDetailRuleResponseDTO.Output output = new PlatformQosPolicyDetailRuleResponseDTO.Output();

        // 模拟CB对列表
        List<PlatformQosPolicyDetailRuleResponseDTO.CbPair> cbList = new ArrayList<>();

        // 模拟一个CB对
        PlatformQosPolicyDetailRuleResponseDTO.CbPair cbPair = new PlatformQosPolicyDetailRuleResponseDTO.CbPair();
        cbPair.setPairId(1);
        cbPair.setClassifierId(random.nextInt(1000) + 1000);
        cbPair.setClassifierName("test_classifier_" + request.getPolicyId());
        cbPair.setBehaviorId(random.nextInt(1000) + 2000);
        cbPair.setBehaviorName("test_behavior_" + request.getPolicyId());
        cbList.add(cbPair);

        output.setRowCount(1);
        output.setSize(request.getSize());
        output.setStart(request.getStart());
        output.setCbList(cbList);
        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：模拟查询流策略CB对详情成功");
        return response;
    }

    /**
     * 模拟查询QoS设备列表调用
     */
    private PlatformQosDeviceListResponseDTO mockGetQosDeviceList(PlatformQosDeviceListRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询QoS设备列表调用，设备IP：{}", request.getDevIp());

        PlatformQosDeviceListResponseDTO response = new PlatformQosDeviceListResponseDTO();
        PlatformQosDeviceListResponseDTO.Output output = new PlatformQosDeviceListResponseDTO.Output();

        // 创建模拟设备数据
        PlatformQosDeviceListResponseDTO.DeviceInfo deviceInfo = new PlatformQosDeviceListResponseDTO.DeviceInfo();
        deviceInfo.setDevId(random.nextInt(1000) + 1);
        deviceInfo.setDevuuId(String.valueOf(random.nextLong()));
        deviceInfo.setDevType(1);
        deviceInfo.setDevName("vsr" + random.nextInt(100));
        deviceInfo.setDevSource(1);
        deviceInfo.setDevIp(request.getDevIp() != null ? request.getDevIp() : "************");
        deviceInfo.setSyncTime("2025-06-26 10:10:31");
        deviceInfo.setSynResult(1);
        deviceInfo.setDevModel("H3C VSR1000");
        deviceInfo.setPolicyIdIn(-1);
        deviceInfo.setPolicyIdOut(-1);

        output.setList(Arrays.asList(deviceInfo));
        output.setRowCount(1);
        output.setSize(15);
        output.setStart(0);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟QoS设备列表查询响应");
        return response;
    }

    /**
     * 模拟查询设备接口列表调用
     */
    private PlatformQosDeviceInterfaceListResponseDTO mockGetQosDeviceInterfaceList(PlatformQosDeviceInterfaceListRequestDTO request) {
        log.info("测试模式：模拟ADWAN平台查询设备接口列表调用，设备UUID：{}", request.getDevuuId());

        PlatformQosDeviceInterfaceListResponseDTO response = new PlatformQosDeviceInterfaceListResponseDTO();
        PlatformQosDeviceInterfaceListResponseDTO.Output output = new PlatformQosDeviceInterfaceListResponseDTO.Output();

        // 创建模拟接口数据
        PlatformQosDeviceInterfaceListResponseDTO.InterfaceInfo interfaceInfo = new PlatformQosDeviceInterfaceListResponseDTO.InterfaceInfo();
        interfaceInfo.setDevId(random.nextInt(1000) + 1);
        interfaceInfo.setDevName("vsr" + random.nextInt(100));
        interfaceInfo.setIfStatus(1);
        interfaceInfo.setInterfaceName(request.getInterfaceName() != null ? request.getInterfaceName() : "GigabitEthernet1/0");
        interfaceInfo.setIfuuidId(String.valueOf(random.nextLong()));
        interfaceInfo.setDevUuid(request.getDevuuId());

        output.setList(Arrays.asList(interfaceInfo));
        output.setRowCount(1);
        output.setSize(15);
        output.setStart(0);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟设备接口列表查询响应");
        return response;
    }

    /**
     * 模拟查询ACL模板详情调用
     */
    private PlatformAclDetailResponseDTO mockGetAclDetail(Integer aclId) {
        log.info("测试模式：模拟ADWAN平台查询ACL模板详情调用，ACL ID：{}", aclId);

        PlatformAclDetailResponseDTO response = new PlatformAclDetailResponseDTO();
        PlatformAclDetailResponseDTO.Output output = new PlatformAclDetailResponseDTO.Output();

        // 创建模拟ACL详情数据
        output.setAclId(aclId);
        output.setAclName("test_acl_" + aclId + "_ipv4");
        output.setDescription("测试ACL模板");
        output.setGroupType(1);
        output.setIdType(1);
        output.setIdValue("testGroup" + aclId);
        output.setDevResult(1);
        output.setIfResult(0);
        output.setMatchOrder(1);
        output.setStep(5);

        response.setOutput(output);

        // 模拟一些处理时间
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟ACL模板详情查询响应");
        return response;
    }

    /**
     * 解析响应中的错误码
     */
    private String parseErrorCode(String responseBody) {
        try {
            if (responseBody != null && responseBody.contains("errorCode")) {
                // 使用JSON解析获取errorCode
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null && jsonObject.containsKey("errorInfo")) {
                    com.alibaba.fastjson2.JSONObject errorInfo = jsonObject.getJSONObject("errorInfo");
                    if (errorInfo != null && errorInfo.containsKey("errorCode")) {
                        return errorInfo.getString("errorCode");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析错误码失败：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 解析响应中的错误信息
     */
    private String parseErrorMessage(String responseBody) {
        try {
            if (responseBody != null && responseBody.contains("errorMessage")) {
                // 使用JSON解析获取errorMessage
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null && jsonObject.containsKey("errorInfo")) {
                    com.alibaba.fastjson2.JSONObject errorInfo = jsonObject.getJSONObject("errorInfo");
                    if (errorInfo != null && errorInfo.containsKey("errorMessage")) {
                        return errorInfo.getString("errorMessage");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析错误信息失败：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 模拟删除流分类调用（带结果）
     */
    private PlatformDeleteResult mockDeleteQosClassifierWithResult(Integer classifierId) {
        log.info("测试模式：模拟ADWAN平台删除流分类调用（带结果），流分类ID：{}", classifierId);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟删除流分类响应");
        return PlatformDeleteResult.success();
    }

    /**
     * 模拟删除流行为调用（带结果）
     */
    private PlatformDeleteResult mockDeleteQosBehaviorWithResult(Integer behaviorId) {
        log.info("测试模式：模拟ADWAN平台删除流行为调用（带结果），流行为ID：{}", behaviorId);

        // 模拟一些处理时间
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("测试模式：生成模拟删除流行为响应");
        return PlatformDeleteResult.success();
    }

    /**
     * 判断删除操作是否成功（基于响应内容）
     */
    private boolean isDeleteSuccessResponse(String responseBody) {
        try {
            if (responseBody != null && responseBody.contains("output")) {
                // 使用JSON解析检查是否包含output字段
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null && jsonObject.containsKey("output")) {
                    String output = jsonObject.getString("output");
                    // 如果output不为空且不为null，认为删除成功
                    return output != null && !output.trim().isEmpty();
                }
            }
        } catch (Exception e) {
            log.warn("解析删除成功响应失败：{}", e.getMessage());
        }
        return false;
    }

    /**
     * 判断是否是服务器忙的响应
     */
    private boolean isServerBusyResponse(String responseBody) {
        try {
            if (responseBody != null && responseBody.contains("Server is busy")) {
                return true;
            }

            // 也可以通过JSON解析检查
            if (responseBody != null && responseBody.contains("code")) {
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null && jsonObject.containsKey("code")) {
                    String code = jsonObject.getString("code");
                    return "EXCEPTION_ERROR".equals(code);
                }
            }
        } catch (Exception e) {
            log.warn("解析服务器忙响应失败：{}", e.getMessage());
        }
        return false;
    }

    /**
     * 从成功状态码的响应中解析错误码
     */
    private String parseErrorCodeFromSuccessResponse(String responseBody) {
        try {
            if (responseBody != null && responseBody.contains("code")) {
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null && jsonObject.containsKey("code")) {
                    return jsonObject.getString("code");
                }
            }
        } catch (Exception e) {
            log.warn("解析成功响应中的错误码失败：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 从成功状态码的响应中解析错误信息
     */
    private String parseErrorMessageFromSuccessResponse(String responseBody) {
        try {
            if (responseBody != null && responseBody.contains("message")) {
                com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(responseBody);
                if (jsonObject != null && jsonObject.containsKey("message")) {
                    return jsonObject.getString("message");
                }
            }
        } catch (Exception e) {
            log.warn("解析成功响应中的错误信息失败：{}", e.getMessage());
        }
        return null;
    }

}