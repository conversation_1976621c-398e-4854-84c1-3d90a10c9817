package com.h3c.dzkf.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.config.DeviceModelConfig;
import com.h3c.dzkf.common.exceptions.PlatformApiException;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.DeviceCustomInfoMapper;
import com.h3c.dzkf.dao.SiteCustomInfoMapper;
import com.h3c.dzkf.entity.DeviceCustomInfo;
import com.h3c.dzkf.entity.SiteCustomInfo;
import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.entity.platform.*;
import com.h3c.dzkf.entity.uclink.AlarmRequestDTO;
import com.h3c.dzkf.entity.uclink.DeviceDetailNewResponseDTO;
import com.h3c.dzkf.entity.uclink.PerformanceDataResponseDTO;
import com.h3c.dzkf.entity.uclink.ResourceQueryRequestDTO;
import com.h3c.dzkf.service.DeviceService;
import com.h3c.dzkf.service.PlatformApiService;
import com.h3c.dzkf.service.UclinkApiService;
import com.h3c.dzkf.uc2linker.framework.fault.model.Fault;
import com.h3c.dzkf.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 设备服务实现类
 */
@Slf4j
@Service
public class DeviceServiceImpl implements DeviceService {

    @Autowired
    private DeviceCustomInfoMapper deviceCustomInfoMapper;

    @Autowired
    private SiteCustomInfoMapper siteCustomInfoMapper;

    @Autowired
    private PlatformApiService platformApiService;

    @Autowired
    private DeviceModelConfig deviceModelConfig;

    @Autowired
    private UclinkApiService uclinkApiService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDevice(AddDeviceRequestDTO request, String requestId) {
        log.info("开始新增设备，请求ID：{}，请求参数：{}", requestId, JSON.toJSONString(request));

        // 1. 验证站点是否存在并设置站点ID
        if (request.getDeviceSiteId() != null) {
            // 情况1：新增设备接口调用，站点ID已存在，只需验证站点是否存在
            Integer siteId = getSiteId(request.getDeviceSiteId(), null);
            if (siteId == null) {
                throw new ServiceException("VALIDATION_ERROR", "所属站点不存在", "设备ID：" + request.getDeviceId());
            }
            // 站点ID已存在，无需重新设置
        } else if (StrUtil.isNotBlank(request.getDeviceSite())) {
            // 情况2：批量导入调用，通过站点名称查询站点ID并设置
            Integer siteId = getSiteId(null, request.getDeviceSite());
            if (siteId == null) {
                throw new ServiceException("VALIDATION_ERROR", "所属站点不存在", "设备ID：" + request.getDeviceId());
            }
            request.setDeviceSiteId(siteId);
        }

        // 2. 验证设备ID是否已存在（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, request.getDeviceId());
        DeviceCustomInfo existingDevice = deviceCustomInfoMapper.selectOne(queryWrapper);
        if (existingDevice != null) {
            throw new ServiceException("VALIDATION_ERROR", "设备ID已存在", "设备ID：" + request.getDeviceId());
        }

        // 3. 构建平台设备请求参数
        PlatformAddDeviceRequestDTO platformRequest = buildPlatformRequest(request);

        // 4. 调用平台接口新增设备，PlatformApiException会被全局异常处理器处理
        String platformNodeId = platformApiService.addDevice(platformRequest);

        // 5. 创建新记录（MyBatis-Plus会自动设置isDeleted=0）
        DeviceCustomInfo deviceCustomInfo = buildDeviceCustomInfo(request, platformNodeId);
        deviceCustomInfoMapper.insert(deviceCustomInfo);
        log.info("设备新增成功，请求ID：{}，平台设备ID：{}", requestId, platformNodeId);

        log.info("设备新增成功，请求ID：{}，设备ID：{}", requestId, request.getDeviceId());
    }



    /**
     * 构建平台设备请求参数
     */
    private PlatformAddDeviceRequestDTO buildPlatformRequest(AddDeviceRequestDTO request) {
        PlatformAddDeviceRequestDTO platformRequest = new PlatformAddDeviceRequestDTO();

        // 必选字段
        platformRequest.setNodeName(request.getDeviceName());
        platformRequest.setManageIp(request.getDeviceIp());
        
        // 获取NETCONF模板ID
        Long netconfTemplateId = getNetconfTemplateId();
        platformRequest.setNetconfTemplateId(netconfTemplateId != null ? netconfTemplateId : 0L);
        
        // 获取SNMP模板ID
        Long snmpTemplateId = getSnmpTemplateId();
        platformRequest.setSnmpTemplateId(snmpTemplateId != null ? snmpTemplateId : 0L);

        // 可选字段 - 设备类型，默认真实设备
        //platformRequest.setNodeType(1);

        // 可选字段 - 设备厂商信息映射为数字
        Integer company = mapDeviceManufacturer(request.getDeviceManufacturer());
        platformRequest.setCompany(company);

        // 可选字段 - 设备角色固定为PE
        platformRequest.setNodeRole(2); // 固定为PE

        // 可选字段 - 其他信息
        platformRequest.setSerialNumbers(request.getDeviceSn());
        platformRequest.setNodeModel(request.getDeviceModel());

        return platformRequest;
    }

    /**
     * 获取NETCONF模板ID
     *
     * @return NETCONF模板ID，如果获取失败抛出异常
     */
    private Long getNetconfTemplateId() {
        log.info("开始分页查询NETCONF模板，查找name为GLOBAL的模板");

        Long currentPageNum = 1L;

        while (true) {
            GetNetconfTemplateRequestDTO request = new GetNetconfTemplateRequestDTO();
            request.setPageNum(currentPageNum);

            log.debug("查询NETCONF模板第{}页", currentPageNum);
            GetNetconfTemplateResponseDTO response = platformApiService.getNetconfTemplate(request);

            if (response != null && response.getSuccessful() != null && response.getSuccessful()
                    && response.getResult() != null) {

                // 记录分页信息
                Long pageNum = response.getResult().getPageNum();
                Long pageSize = response.getResult().getPageSize();
                Long totalItem = response.getResult().getTotalItem();
                Long totalPage = response.getResult().getTotalPage();

                log.debug("NETCONF模板分页信息: 当前页={}, 页大小={}, 总记录数={}, 总页数={}",
                        pageNum, pageSize, totalItem, totalPage);

                if (response.getResult().getRecords() != null && !response.getResult().getRecords().isEmpty()) {
                    // 遍历当前页查询结果，查找name为GLOBAL的模板
                    for (GetNetconfTemplateResponseDTO.GetNetconfTmpVO template : response.getResult().getRecords()) {
                        if ("Global".equals(template.getName())) {
                            Long templateId = template.getDataId();
                            log.info("在第{}页找到GLOBAL NETCONF模板，ID：{}", pageNum, templateId);
                            return templateId;
                        }
                    }

                    log.debug("第{}页({}/{}页)未找到GLOBAL模板，当前页记录数：{}",
                            pageNum, pageNum, totalPage, response.getResult().getRecords().size());
                }

                // 基于平台返回的分页信息判断是否还有下一页
                if (totalPage == null || pageNum == null || pageNum >= totalPage) {
                    log.warn("已查询完所有{}页NETCONF模板，未找到name为GLOBAL的模板", totalPage);
                    break;
                }

                // 查询下一页
                currentPageNum = pageNum + 1;
            } else {
                log.warn("第{}页NETCONF模板查询失败或返回空结果", currentPageNum);
                break;
            }
        }

        // 未找到GLOBAL模板，抛出异常
        throw new ServiceException("PLATFORM_ERROR", "未找到name为GLOBAL的NETCONF模板", "查询页数：" + currentPageNum);
    }

    /**
     * 获取SNMP模板ID
     *
     * @return SNMP模板ID，如果获取失败抛出异常
     */
    private Long getSnmpTemplateId() {
        log.info("开始分页查询SNMP模板，查找name为GLOBAL的模板");

        Long currentPageNum = 1L;

        while (true) {
            GetSnmpTemplateRequestDTO request = new GetSnmpTemplateRequestDTO();
            request.setPageNum(currentPageNum);

            log.debug("查询SNMP模板第{}页", currentPageNum);
            GetSnmpTemplateResponseDTO response = platformApiService.getSnmpTemplate(request);

            if (response != null && response.getSuccessful() != null && response.getSuccessful()
                    && response.getResult() != null) {

                // 记录分页信息
                Long pageNum = response.getResult().getPageNum();
                Long pageSize = response.getResult().getPageSize();
                Long totalItem = response.getResult().getTotalItem();
                Long totalPage = response.getResult().getTotalPage();

                log.debug("SNMP模板分页信息: 当前页={}, 页大小={}, 总记录数={}, 总页数={}",
                        pageNum, pageSize, totalItem, totalPage);

                if (response.getResult().getRecords() != null && !response.getResult().getRecords().isEmpty()) {
                    // 遍历当前页查询结果，查找name为GLOBAL的模板
                    for (GetSnmpTemplateResponseDTO.GetSnmpTmpVO template : response.getResult().getRecords()) {
                        if ("Global".equals(template.getName())) {
                            Long templateId = template.getDataId();
                            log.info("在第{}页找到GLOBAL SNMP模板，ID：{}", pageNum, templateId);
                            return templateId;
                        }
                    }

                    log.debug("第{}页({}/{}页)未找到GLOBAL模板，当前页记录数：{}",
                            pageNum, pageNum, totalPage, response.getResult().getRecords().size());
                }

                // 基于平台返回的分页信息判断是否还有下一页
                if (totalPage == null || pageNum == null || pageNum >= totalPage) {
                    log.warn("已查询完所有{}页SNMP模板，未找到name为GLOBAL的模板", totalPage);
                    break;
                }

                // 查询下一页
                currentPageNum = pageNum + 1;
            } else {
                log.warn("第{}页SNMP模板查询失败或返回空结果", currentPageNum);
                break;
            }
        }

        // 未找到GLOBAL模板，抛出异常
        throw new ServiceException("PLATFORM_ERROR", "未找到name为GLOBAL的SNMP模板", "查询页数：" + currentPageNum);
    }

    /**
     * 映射设备厂商信息为数字
     * 0：H3C, 1：HP, 5：UNIS, 65535: Unknown
     */
    private Integer mapDeviceManufacturer(String deviceManufacturer) {
        if ("H3C".equalsIgnoreCase(deviceManufacturer)) {
            return 0;
        } else if ("HP".equalsIgnoreCase(deviceManufacturer)) {
            return 1;
        } else if ("UNIS".equalsIgnoreCase(deviceManufacturer)) {
            return 5;
        }
        return 65535; // Unknown
    }


    /**
     * 构建设备自定义信息
     */
    private DeviceCustomInfo buildDeviceCustomInfo(AddDeviceRequestDTO request, String platformNodeId) {
        DeviceCustomInfo deviceCustomInfo = new DeviceCustomInfo();
        deviceCustomInfo.setDeviceId(request.getDeviceId());
        deviceCustomInfo.setPlatformNodeId(platformNodeId);
        deviceCustomInfo.setDeviceSiteId(request.getDeviceSiteId());
        deviceCustomInfo.setDeviceSite(request.getDeviceSite());
        deviceCustomInfo.setDeviceRole(request.getDeviceRole());
        deviceCustomInfo.setIsRr(request.getIsRR());
        deviceCustomInfo.setDevicePlaneId(request.getDevicePlaneId());
        deviceCustomInfo.setDeviceIpv6(request.getDeviceIpv6());
        deviceCustomInfo.setDeviceGroup(request.getDeviceGroup());
        deviceCustomInfo.setCreateTime(new Date());
        deviceCustomInfo.setUpdateTime(new Date());
        // 不需要手动设置isDeleted，MyBatis-Plus会自动处理
        return deviceCustomInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDevice(Long deviceId, String requestId) {
        log.info("开始删除设备，请求ID：{}，设备ID：{}", requestId, deviceId);

        // 1. 根据设备ID查询设备信息（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);

        if (deviceInfo == null) {
            throw new ServiceException("VALIDATION_ERROR", "设备不存在", "设备ID：" + deviceId);
        }

        // 2. 检查是否有平台节点ID
        if (StrUtil.isBlank(deviceInfo.getPlatformNodeId())) {
            throw new ServiceException("VALIDATION_ERROR", "设备未关联平台设备节点ID", "设备ID：" + deviceId);
        }

        try {
            Long platformNodeId = Long.parseLong(deviceInfo.getPlatformNodeId());

            // 3. 先调用平台接口维护设备
            MaintainNodeDTO maintainRequest = new MaintainNodeDTO(platformNodeId);
            platformApiService.maintainDevice(maintainRequest);
            log.info("平台设备维护成功，设备ID：{}，平台节点ID：{}", deviceId, platformNodeId);

            // 4. 调用平台接口删除设备，PlatformApiException会被全局异常处理器处理
            PlatformDeleteRequestDTO deleteRequest = new PlatformDeleteRequestDTO(platformNodeId);
            platformApiService.deleteDevice(deleteRequest);

            log.info("平台设备删除成功，设备ID：{}，平台节点ID：{}", deviceId, platformNodeId);

            // 5. 使用MyBatis-Plus逻辑删除特性删除记录
            deviceCustomInfoMapper.deleteById(deviceInfo.getId());
    
            log.info("设备删除成功，请求ID：{}，设备ID：{}", requestId, deviceId);
        } catch (NumberFormatException e) {
            throw new ServiceException("VALIDATION_ERROR", "平台设备节点ID格式错误", "设备ID：" + deviceId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDevice(UpdateDeviceRequestDTO request, String requestId) {
        log.info("开始更新设备名称，请求ID：{}，设备ID：{}，新名称：{}", requestId, request.getDeviceId(), request.getDeviceName());

        // 1. 根据设备ID查询设备信息（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, request.getDeviceId());
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);

        if (deviceInfo == null) {
            throw new ServiceException("VALIDATION_ERROR", "设备不存在", "设备ID：" + request.getDeviceId());
        }

        // 2. 检查是否有平台节点ID
        if (StrUtil.isBlank(deviceInfo.getPlatformNodeId())) {
            throw new ServiceException("VALIDATION_ERROR", "设备未关联平台节点ID", "设备ID：" + request.getDeviceId());
        }

        try {
            Long platformNodeId = Long.parseLong(deviceInfo.getPlatformNodeId());

            // 3. 调用平台接口查询设备详细信息，PlatformApiException会被全局异常处理器处理
            PlatformGetNodesRequestDTO getNodesRequest = new PlatformGetNodesRequestDTO(platformNodeId);
            PlatformGetNodesResponseDTO getNodesResponse = platformApiService.getNodes(getNodesRequest);

            if (getNodesResponse == null || !getNodesResponse.getSuccessful() ||
                    getNodesResponse.getResult() == null ||
                    getNodesResponse.getResult().getRecords() == null ||
                    getNodesResponse.getResult().getRecords().isEmpty()) {
                throw new ServiceException("PLATFORM_ERROR", "未查询到平台设备信息", "设备ID：" + request.getDeviceId());
            }

            // 4. 获取设备的管理IP
            PlatformGetNodesResponseDTO.NodeRecord nodeRecord = getNodesResponse.getResult().getRecords().get(0);
            String manageIp = nodeRecord.getManageIp();

            // 5. 调用平台接口更新设备名称，PlatformApiException会被全局异常处理器处理
            PlatformUpdateNodeRequestDTO updateRequest = new PlatformUpdateNodeRequestDTO(
                    platformNodeId, request.getDeviceName(), manageIp);

            platformApiService.updateNode(updateRequest);

            log.info("设备名称更新成功，请求ID：{}，设备ID：{}，新名称：{}", requestId, request.getDeviceId(), request.getDeviceName());

        } catch (NumberFormatException e) {
            throw new ServiceException("VALIDATION_ERROR", "平台设备节点ID格式错误", "设备ID：" + request.getDeviceId());
        }
    }

    @Override
    public QueryDeviceInfoDetailResponseDTO queryDeviceInfoDetail(Long deviceId, String requestId) {
        log.info("开始查询单个设备信息，请求ID：{}，设备ID：{}", requestId, deviceId);

        // 1. 根据设备ID查询设备信息（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);

        if (deviceInfo == null) {
            log.warn("设备不存在，请求ID：{}，设备ID：{}", requestId, deviceId);
            throw new ServiceException("VALIDATION_ERROR", "设备不存在", "设备ID：" + deviceId);
        }

        if (StrUtil.isBlank(deviceInfo.getPlatformNodeId())) {
            throw new ServiceException("VALIDATION_ERROR", "设备未关联平台节点ID", "设备ID：" + deviceId);
        }

        Long platformNodeId;
        try {
            platformNodeId = Long.parseLong(deviceInfo.getPlatformNodeId());
        } catch (NumberFormatException e) {
            throw new ServiceException("VALIDATION_ERROR", "平台设备节点ID格式错误", "设备ID：" + deviceId);
        }

        // 2. 创建响应对象
        QueryDeviceInfoDetailResponseDTO response = QueryDeviceInfoDetailResponseDTO.success(requestId);
        response.setDeviceId(deviceId);

        // 3. 填充定制库数据
        fillCustomData(response, deviceInfo);

        // 4. 查询平台设备基本信息，异常会被全局异常处理器处理
        fillPlatformDeviceData(response, deviceId, platformNodeId);

        log.info("查询单个设备信息查询完成，请求ID：{}，设备ID：{}", requestId, deviceId);
        return response;
    }

    /**
     * 填充定制库数据
     */
    private void fillCustomData(QueryDeviceInfoDetailResponseDTO response, DeviceCustomInfo deviceInfo) {
        response.setDeviceSite(deviceInfo.getDeviceSite());
        response.setDeviceRole(deviceInfo.getDeviceRole());
        response.setIsRR(deviceInfo.getIsRr());
        response.setFirstOnlineTime(deviceInfo.getCreateTime() != null ?
                formatAlarmTime(deviceInfo.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()) : null);
    }

    /**
     * 填充平台设备数据
     */
    private void fillPlatformDeviceData(QueryDeviceInfoDetailResponseDTO response, Long deviceId, Long nodeId) {
        // 查询平台设备基本信息，PlatformApiException会被全局异常处理器处理
        PlatformGetNodesRequestDTO getNodesRequest = new PlatformGetNodesRequestDTO(nodeId);
        PlatformGetNodesResponseDTO getNodesResponse = platformApiService.getNodes(getNodesRequest);

        // 如果平台接口返回空数据，则跳过填充，继续执行后续逻辑
        if (getNodesResponse == null || !getNodesResponse.getSuccessful() ||
                getNodesResponse.getResult() == null ||
                getNodesResponse.getResult().getRecords() == null ||
                getNodesResponse.getResult().getRecords().isEmpty()) {
            throw new ServiceException("PLATFORM_ERROR", "未查询到平台设备信息", "设备ID：" + deviceId);
        }

        PlatformGetNodesResponseDTO.NodeRecord nodeRecord = getNodesResponse.getResult().getRecords().get(0);

        // 填充基本信息
        response.setDeviceName(nodeRecord.getNodeName());
        response.setDeviceManufacturer(mapCompanyToManufacturer(nodeRecord.getCompany()));
        response.setDeviceModel(nodeRecord.getNodeModel());
        // 处理序列号，如果是列表则取第一个，否则直接设置
        if (nodeRecord.getSerialNumbers() != null && !nodeRecord.getSerialNumbers().isEmpty()) {
            response.setDeviceSn(nodeRecord.getSerialNumbers().get(0));
        }
        //response.setDeviceStatus(mapNodeDisplayToStatus(nodeRecord.getNodeDisplay()));
        response.setDeviceStatus(nodeRecord.getNodeDisplay() == 3 ? "off" : "on");// 此处返回on/off，接口返回如果是3就是off，其他都是on
        response.setDeviceIp(nodeRecord.getManageIp());
        //response.setDeviceRole(mapNodeRoleToRole(nodeRecord.getNodeRole()));
        response.setIsMarkDevice(nodeRecord.getIsolationStatus() != null && nodeRecord.getIsolationStatus() == 1 ? 1 : 0);

        if (nodeRecord.getManageIp() == null) {
            throw new ServiceException("PLATFORM_ERROR", "设备管理IP为空", "设备ID：" + deviceId);
        }

        // 查询其他详细信息 - 使用新的API接口，异常会被全局异常处理器处理
        fillDetailedDataWithNewApi(response, nodeRecord.getManageIp());

        // 如果设备在线（nodeDisplay != 3），lastOnlineTime取当前时间，否则取最后一条告警时间，若没有告警数据，则为空
        if (nodeRecord.getNodeDisplay() != 3) {
            response.setLastOnlineTime(formatAlarmTime(LocalDateTime.now()));
        } else if (response.getAlarmInfos() != null && !response.getAlarmInfos().isEmpty()) {
            // 告警列表已在fillAlarmInfo方法中按时间降序排序，直接取第一条告警的时间
            String latestAlarmTimeStr = response.getAlarmInfos().get(0).getAlarmTime();
            response.setLastOnlineTime(latestAlarmTimeStr);
        }
    }

    /**
     * 填充详细数据（网管接口数据）
     */
    /*private void fillDetailedData(QueryDeviceInfoDetailResponseDTO response, String deviceIp) {
        if (deviceIp == null) {
            return;
        }

        try {
            // 查询设备MAC地址、软件版本，并获取devId用于后续性能数据查询
            String devId = fillDeviceDetails(response, deviceIp);

            // 查询硬件版本
            fillHardwareVersion(response, deviceIp);

            // 查询性能数据（使用从设备详情获取的devId）
            fillPerformanceData(response, devId);

            // 查询端口信息
            fillPortInfo(response, deviceIp);

            // 查询告警信息
            fillAlarmInfo(response, deviceIp);

        } catch (Exception e) {
            log.error("填充详细数据异常，设备IP：{}", deviceIp, e);
        }
    }*/

    /**
     * 使用新API填充详细数据
     */
    private void fillDetailedDataWithNewApi(QueryDeviceInfoDetailResponseDTO response, String deviceIp) {
        // 1. 首先通过IP查询资源，获取设备ID
        ResourceQueryRequestDTO resourceRequest = new ResourceQueryRequestDTO(deviceIp);
        Long deviceId = uclinkApiService.getDeviceIdByIp(resourceRequest);

        log.info("通过IP获取到设备ID，设备IP：{}，设备ID：{}", deviceIp, deviceId);

        // 2. 使用设备ID查询设备详情
        fillDeviceDetailsWithNewApi(response, deviceId);

        // 3. 使用设备ID查询硬件版本（使用平台接口）
        fillHardwareVersionWithNewApi(response, deviceId);

        // 4. 使用设备ID查询性能数据
        fillPerformanceDataWithNewApi(response, deviceId);

        // 5. 查询端口信息（保持原有逻辑）
        fillPortInfo(response, deviceIp);

        // 6. 查询告警信息（保持原有逻辑）
        fillAlarmInfo(response, deviceIp);
    }

    /**
     * 使用新API查询设备详细信息
     */
    private void fillDeviceDetailsWithNewApi(QueryDeviceInfoDetailResponseDTO response, Long deviceId) {
        // 调用uclink接口查询设备详情，异常会被全局异常处理器处理
        DeviceDetailNewResponseDTO deviceDetail = uclinkApiService.getDeviceDetailById(deviceId);

        if (deviceDetail != null) {
            log.info("设备详情查询成功，设备ID：{}，设备名称：{}", deviceId, deviceDetail.getDeviceName());

            // 填充响应数据
            response.setDeviceMac(deviceDetail.getMac());
            response.setDeviceSoftwareVersion(deviceDetail.getSysVersion());
        } else {
            log.warn("未查询到设备详情，设备ID：{}，跳过设备详情填充", deviceId);
            // 当查询结果为空时，不抛出异常，继续执行后续逻辑
        }
    }

    /**
     * 使用新API查询性能数据
     */
    private void fillPerformanceDataWithNewApi(QueryDeviceInfoDetailResponseDTO response, Long deviceId) {
        log.info("开始使用新API查询性能数据，设备ID：{}", deviceId);

        // 用于收集多个温度值计算平均值
        List<Float> temperatureValues = new ArrayList<>();
        // 用于收集电源和风扇状态信息
        List<QueryDeviceInfoDetailResponseDTO.PowerInfo> powerInfoList = new ArrayList<>();
        List<QueryDeviceInfoDetailResponseDTO.FanInfo> fanInfoList = new ArrayList<>();

        // 查询不同类型的性能数据：CPU(2)、内存(4)、温度(49)、电源(908)、风扇(902)
        int[] taskIds = {2, 4, 49, 908, 902};
        List<String> failedTasks = new ArrayList<>();

        for (int taskId : taskIds) {
            // 调用uclink接口查询性能数据，接口调用异常会传播到全局异常处理器
            PerformanceDataResponseDTO perfData = uclinkApiService.getPerformanceDataByDeviceIdAndTaskId(deviceId, taskId);

            // 检查接口返回结果
            if (perfData == null) {
                throw new ServiceException("UCLINK_ERROR", "性能数据查询接口返回null", "设备ID：" + deviceId + "，任务ID：" + taskId);
            }

            if (perfData.getErrorCode() != 0) {
                throw new ServiceException("UCLINK_ERROR", "性能数据查询接口返回错误",
                        "设备ID：" + deviceId + "，任务ID：" + taskId + "，错误码：" + perfData.getErrorCode());
            }

            // 只有返回空数据的情况才继续执行
            if (perfData.getData() == null || perfData.getData().isEmpty()) {
                log.warn("性能数据查询返回空数据，设备ID：{}，任务ID：{}，跳过此任务", deviceId, taskId);
                continue;
            }

            // 遍历所有数据实例，处理多个温度传感器、电源模块、风扇等
            for (PerformanceDataResponseDTO.PerfData data : perfData.getData()) {
                log.debug("处理性能数据：taskId={}，instanceId={}，instanceData={}",
                        taskId, data.getInstanceId(), data.getInstanceData());

                switch (taskId) {
                    case 2: // CPU利用率 - 通常只有一个实例，取第一个
                        if (data.getInstanceData() != null && response.getCpuUseRate() == null) {
                            Float cpuValue = parseFloatValue(data.getInstanceData());
                            if (cpuValue != null) {
                                response.setCpuUseRate(Math.round(cpuValue));
                                log.debug("设置CPU利用率：{}%", Math.round(cpuValue));
                            }
                        }
                        break;
                    case 4: // 内存利用率 - 通常只有一个实例，取第一个
                        if (data.getInstanceData() != null && response.getMemUseRate() == null) {
                            Float memValue = parseFloatValue(data.getInstanceData());
                            if (memValue != null) {
                                response.setMemUseRate(Math.round(memValue));
                                log.debug("设置内存利用率：{}%", Math.round(memValue));
                            }
                        }
                        break;
                    case 49: // 温度 - 收集所有温度传感器的值
                        if (data.getInstanceData() != null) {
                            Float tempValue = parseFloatValue(data.getInstanceData());
                            if (tempValue != null) {
                                temperatureValues.add(tempValue);
                                log.debug("收集温度值：{}°C（实例：{}）", tempValue, data.getInstanceId());
                            }
                        }
                        break;
                    case 908: // 电源状态 - 收集所有电源模块的状态
                        if (data.getInstanceData() != null) {
                            QueryDeviceInfoDetailResponseDTO.PowerInfo powerInfo = new QueryDeviceInfoDetailResponseDTO.PowerInfo();
                            try {
                                // 尝试从instanceId中提取数字作为电源ID
                                String instanceId = data.getInstanceId();
                                if (instanceId != null && instanceId.matches(".*\\d+.*")) {
                                    powerInfo.setPowerId(Integer.parseInt(instanceId.replaceAll("\\D+", "")));
                                } else {
                                    powerInfo.setPowerId(powerInfoList.size() + 1); // 使用序号作为ID
                                }
                            } catch (NumberFormatException e) {
                                powerInfo.setPowerId(powerInfoList.size() + 1); // 使用序号作为ID
                            }
                            Float powerValue = parseFloatValue(data.getInstanceData());
                            powerInfo.setPowerStatus(powerValue != null && powerValue == 1.0f ? 1 : 0); // 1表示正常，0表示异常
                            powerInfoList.add(powerInfo);
                            log.debug("收集电源状态：{}（实例：{}，ID：{}）",
                                    powerInfo.getPowerStatus(), data.getInstanceId(), powerInfo.getPowerId());
                        }
                        break;
                    case 902: // 风扇状态 - 收集所有风扇的状态
                        if (data.getInstanceData() != null) {
                            QueryDeviceInfoDetailResponseDTO.FanInfo fanInfo = new QueryDeviceInfoDetailResponseDTO.FanInfo();
                            try {
                                // 尝试从instanceId中提取数字作为风扇ID
                                String instanceId = data.getInstanceId();
                                if (instanceId != null && instanceId.matches(".*\\d+.*")) {
                                    fanInfo.setFanId(Integer.parseInt(instanceId.replaceAll("\\D+", "")));
                                } else {
                                    fanInfo.setFanId(fanInfoList.size() + 1); // 使用序号作为ID
                                }
                            } catch (NumberFormatException e) {
                                fanInfo.setFanId(fanInfoList.size() + 1); // 使用序号作为ID
                            }
                            Float fanValue = parseFloatValue(data.getInstanceData());
                            fanInfo.setFanStatus(fanValue != null && fanValue == 1.0f ? 1 : 0); // 1表示正常，0表示异常
                            fanInfoList.add(fanInfo);
                            log.debug("收集风扇状态：{}（实例：{}，ID：{}）",
                                    fanInfo.getFanStatus(), data.getInstanceId(), fanInfo.getFanId());
                        }
                        break;
                    default:
                        log.warn("未知性能任务ID：{}", taskId);
                }
            }
        }

        // 处理温度平均值
        if (!temperatureValues.isEmpty()) {
            float avgTemperature = (float) temperatureValues.stream()
                    .mapToDouble(Float::doubleValue)
                    .average()
                    .orElse(0.0);
            response.setTemperature(Math.round(avgTemperature));
            log.info("设置平均温度：{}°C（基于{}个传感器）", Math.round(avgTemperature), temperatureValues.size());
        }

        // 设置电源信息列表
        if (!powerInfoList.isEmpty()) {
            response.setPowerInfos(powerInfoList);
            long normalCount = powerInfoList.stream().mapToInt(QueryDeviceInfoDetailResponseDTO.PowerInfo::getPowerStatus).sum();
            log.info("设置电源信息：{}个电源模块，{}个正常", powerInfoList.size(), normalCount);
        }

        // 设置风扇信息列表
        if (!fanInfoList.isEmpty()) {
            response.setFanInfos(fanInfoList);
            long normalCount = fanInfoList.stream().mapToInt(QueryDeviceInfoDetailResponseDTO.FanInfo::getFanStatus).sum();
            log.info("设置风扇信息：{}个风扇，{}个正常", fanInfoList.size(), normalCount);
        }

        log.info("使用新API查询性能数据完成，设备ID：{}", deviceId);
    }

    /**
     * 查询硬件版本（使用平台接口）
     */
    private void fillHardwareVersionWithNewApi(QueryDeviceInfoDetailResponseDTO response, Long deviceId) {
        // 调用平台接口查询硬件版本，PlatformApiException会被全局异常处理器处理
        PlatformInventoryInfoRequestDTO request = new PlatformInventoryInfoRequestDTO(deviceId.toString());
        PlatformInventoryInfoResponseDTO inventoryResponse = platformApiService.getInventoryInfo(request);

        if (inventoryResponse != null && inventoryResponse.getData() != null && !inventoryResponse.getData().isEmpty()) {
            // 按设备ID查询为精确查询，直接取第一条记录的硬件版本信息
            PlatformInventoryInfoResponseDTO.InventoryData data = inventoryResponse.getData().get(0);
            String hardwareVersion = data.getPhysicalHardwareRev();

            response.setDeviceHardwareVersion(hardwareVersion);
            log.info("硬件版本查询成功，设备ID：{}，硬件版本：{}", deviceId, hardwareVersion);
        } else {
            log.warn("未查询到硬件版本信息，设备ID：{}，跳过硬件版本填充", deviceId);
            // 当查询结果为空时，不抛出异常，继续执行后续逻辑
        }
    }

    /**
     * 查询端口信息
     */
    private void fillPortInfo(QueryDeviceInfoDetailResponseDTO response, String deviceIp) {
        // 调用平台接口查询端口信息，PlatformApiException会被全局异常处理器处理
        PlatformInterfaceListRequestDTO request = new PlatformInterfaceListRequestDTO(Collections.singletonList(deviceIp));
        PlatformInterfaceListResponseDTO interfaceResponse = platformApiService.getBatchInterfaceList(request);

        if (interfaceResponse != null && interfaceResponse.getSuccessful() &&
                interfaceResponse.getResult() != null && !interfaceResponse.getResult().isEmpty()) {

            List<QueryDeviceInfoDetailResponseDTO.PortInfo> ports = new ArrayList<>();

            // 遍历每个节点的接口数据
            for (PlatformInterfaceListResponseDTO.NodeInterfaceData nodeData : interfaceResponse.getResult()) {
                if (deviceIp.equals(nodeData.getManageIp()) && nodeData.getInterfaceList() != null) {
                    // 遍历该节点的所有接口
                    for (PlatformInterfaceListResponseDTO.InterfaceData interfaceData : nodeData.getInterfaceList()) {
                        QueryDeviceInfoDetailResponseDTO.PortInfo portInfo = new QueryDeviceInfoDetailResponseDTO.PortInfo();
                        portInfo.setPortName(interfaceData.getIfName());
                        portInfo.setPortStatus(mapInterfaceStatus(interfaceData.getStatus()));
                        portInfo.setPortBandWidth(convertBandwidthToMbps(interfaceData.getBandwidth()));
                        ports.add(portInfo);
                    }
                    break; // 找到匹配的设备IP后退出循环
                }
            }

            response.setPorts(ports);
            log.info("端口信息查询成功，设备IP：{}，端口数量：{}", deviceIp, ports.size());
        } else {
            log.warn("未查询到端口信息，设备IP：{}，跳过端口信息填充", deviceIp);
            // 当查询结果为空时，不抛出异常，继续执行后续逻辑
        }
    }

    /**
     * 查询告警信息
     */
    private void fillAlarmInfo(QueryDeviceInfoDetailResponseDTO response, String deviceIp) {
        // 调用uclink接口查询告警信息，异常会被全局异常处理器处理
        AlarmRequestDTO request = new AlarmRequestDTO(deviceIp);
        List<Fault> alarmList = uclinkApiService.getAlarmInfo(request);

        if (alarmList != null && !alarmList.isEmpty()) {
            // UclinkApiServiceImpl已经进行了精准IP匹配，这里可以直接使用返回结果
            log.info("告警信息查询成功，设备IP：{}，告警数量：{}", deviceIp, alarmList.size());

            // 按告警时间降序排序，最新的告警在前面
            alarmList.sort((f1, f2) -> {
                try {
                    String time1 = f1.getFaultTimeReachString();
                    String time2 = f2.getFaultTimeReachString();
                    if (time1 == null && time2 == null) return 0;
                    if (time1 == null) return 1;
                    if (time2 == null) return -1;

                    LocalDateTime dt1 = parseAlarmTime(time1);
                    LocalDateTime dt2 = parseAlarmTime(time2);
                    if (dt1 == null && dt2 == null) return 0;
                    if (dt1 == null) return 1;
                    if (dt2 == null) return -1;
                    return dt2.compareTo(dt1); // 降序排序
                } catch (Exception e) {
                    log.warn("告警时间排序异常：{}", e.getMessage());
                    return 0;
                }
            });

            // 转换Fault列表为AlarmInfo列表
            List<QueryDeviceInfoDetailResponseDTO.AlarmInfo> alarmInfos = new ArrayList<>();

            for (Fault fault : alarmList) {
                // 打印Fault对象完整信息以确定可用字段
                log.debug("处理告警对象：{}", JSON.toJSONString(fault));

                QueryDeviceInfoDetailResponseDTO.AlarmInfo alarmInfo = new QueryDeviceInfoDetailResponseDTO.AlarmInfo();

                try {
                    // 尝获取告警级别
                    alarmInfo.setAlarmLevel(fault.getSeverity() + "");

                    // 获取告警名称
                    alarmInfo.setAlarmName(fault.getFaultName());

                    // 获取告警时间
                    alarmInfo.setAlarmTime(fault.getFaultTimeReachString());

                    // 获取告警描述
                    alarmInfo.setAlarmDescription(fault.getDescInfo());

                    alarmInfos.add(alarmInfo);
                    log.debug("告警处理完成：级别={}，名称={}，时间={}，描述={}",
                            fault.getSeverity(), fault.getFaultName(), fault.getFaultTimeReachString(), fault.getDescInfo());

                } catch (Exception e) {
                    log.warn("处理告警对象异常，跳过此条告警：{}", e.getMessage());
                }
            }

            response.setAlarmInfos(alarmInfos);
            log.info("告警信息处理完成，设备IP：{}，有效告警数量：{}", deviceIp, alarmInfos.size());

        } else {
            log.info("未查询到告警信息，设备IP：{}，跳过告警信息填充", deviceIp);
            response.setAlarmInfos(new ArrayList<>()); // 设置为空列表
        }
    }

    /**
     * 映射接口状态为on/off
     */
    private String mapInterfaceStatus(Integer status) {
        if (status != null && status == 1) {
            return "on";
        } else {
            return "off";
        }
    }

    /**
     * 转换带宽从kbps到Mbps字符串
     */
    private String convertBandwidthToMbps(Long bandwidthKbps) {
        if (bandwidthKbps == null) {
            return "0";
        }
        double mbps = bandwidthKbps / 1000.0;
        return String.format("%.2f", mbps);
    }

    /**
     * 格式化告警时间
     */
    private String formatAlarmTime(LocalDateTime alarmTime) {
        if (alarmTime == null) {
            return null;
        }
        return alarmTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 解析告警时间字符串为LocalDateTime
     */
    private LocalDateTime parseAlarmTime(String alarmTimeStr) {
        if (alarmTimeStr == null || alarmTimeStr.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析常见的时间格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(alarmTimeStr.trim(), formatter);
        } catch (Exception e) {
            log.warn("解析告警时间失败，时间字符串：{}，错误：{}", alarmTimeStr, e.getMessage());
            return null;
        }
    }

    /**
     * 映射设备厂商
     */
    private String mapCompanyToManufacturer(Integer company) {
        if (company == null) return "Unknown";
        switch (company) {
            case 0:
                return "H3C";
            case 1:
                return "HP";
            case 5:
                return "UNIS";
            default:
                return "Unknown";
        }
    }

    /**
     * 映射设备状态为简化的在线/下线状态（专门用于站点设备查询）
     */
    private String mapNodeDisplayToSimpleStatus(Integer nodeDisplay) {
        if (nodeDisplay == null) return "off";
        switch (nodeDisplay) {
            case 3:
                return "off"; // 灰色，设备下线
            case 1:
            case 2:
            case 4:
            case 5:
                return "on"; // 其他状态都认为是在线
            default:
                return "off"; // 未知状态认为是下线
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchImportDeviceResponseDTO importDeviceTemplate(MultipartFile file, String requestId) {
        log.info("开始批量导入设备模板，请求ID：{}，文件名：{}", requestId, file.getOriginalFilename());

        // 1. 验证文件格式，验证失败抛出ServiceException，由全局异常处理器处理
        if (file.isEmpty()) {
            throw new ServiceException("VALIDATION_ERROR", "上传文件不能为空", "请求ID：" + requestId);
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
            throw new ServiceException("VALIDATION_ERROR", "文件格式错误，请上传Excel文件(.xlsx)", "请求ID：" + requestId);
        }

        // 2. 解析Excel文件，解析失败抛出ServiceException，由全局异常处理器处理
        List<BatchImportDeviceRequestDTO> deviceList;
        try {
            deviceList = ExcelUtil.parseDeviceTemplateExcel(file);
        } catch (Exception e) {
            log.error("解析Excel文件失败，请求ID：{}", requestId, e);
            throw new ServiceException("FILE_PARSE_ERROR", "Excel文件解析失败", "请求ID：" + requestId + "，错误：" + e.getMessage());
        }

        if (deviceList.isEmpty()) {
            throw new ServiceException("VALIDATION_ERROR", "Excel文件中没有有效的设备数据", "请求ID：" + requestId);
        }

        // 3. 统计信息
        int totalNum = deviceList.size();
        int succNum = 0;
        int failNum = 0;
        List<BatchImportDeviceResponseDTO.AddDeviceResult> addDeviceResults = new ArrayList<>();

        // 4. 逐个处理设备，保留内层异常处理以支持批量处理业务逻辑
        for (BatchImportDeviceRequestDTO batchDevice : deviceList) {
            BatchImportDeviceResponseDTO.AddDeviceResult result = new BatchImportDeviceResponseDTO.AddDeviceResult();
            result.setDeviceName(batchDevice.getDeviceName());

            try {
                // 验证必填字段
                String validationError = validateBatchDevice(batchDevice);
                if (validationError != null) {
                    result.setStatus(0); // 失败
                    result.setErrorMsg(validationError);
                    failNum++;
                    addDeviceResults.add(result);
                    continue;
                }

                // 转换为AddDeviceRequestDTO
                AddDeviceRequestDTO addRequest = convertToAddDeviceRequest(batchDevice);

                // 调用单个设备新增逻辑（内部会验证站点是否存在）
                addDevice(addRequest, requestId + "_" + batchDevice.getRowIndex());

                // 如果没有抛出异常，说明成功
                result.setStatus(1); // 成功
                succNum++;

            } catch (PlatformApiException e) {
                // 捕获平台API异常，将平台返回的具体错误信息设置为失败原因
                log.error("调用平台接口失败，行号：{}，设备名称：{}，错误信息：{}",
                        batchDevice.getRowIndex(), batchDevice.getDeviceName(), e.getMessage());
                result.setStatus(0); // 失败
                result.setErrorMsg(e.getMessage()); // 使用平台返回的具体错误信息
                failNum++;
            } catch (ServiceException e) {
                // 捕获Service异常，将业务错误信息设置为失败原因
                log.error("业务逻辑处理失败，行号：{}，设备名称：{}，错误信息：{}",
                        batchDevice.getRowIndex(), batchDevice.getDeviceName(), e.getMessage());
                result.setStatus(0); // 失败
                result.setErrorMsg(e.getMessage()); // 使用业务错误信息
                failNum++;
            } catch (Exception e) {
                log.error("处理设备失败，行号：{}，设备名称：{}", batchDevice.getRowIndex(), batchDevice.getDeviceName(), e);
                result.setStatus(0); // 失败
                result.setErrorMsg("设备处理失败，请检查数据格式或联系管理员");
                failNum++;
            }

            addDeviceResults.add(result);
        }

        // 5. 构造响应结果
        BatchImportDeviceResponseDTO.OptionField optionField = new BatchImportDeviceResponseDTO.OptionField();
        optionField.setTotalNum(totalNum);
        optionField.setSuccNum(succNum);
        optionField.setFailNum(failNum);
        optionField.setAddDeviceResults(addDeviceResults);

        BatchImportDeviceResponseDTO response = BatchImportDeviceResponseDTO.success(requestId);
        response.setOptionField(optionField);

        log.info("批量导入设备完成，请求ID：{}，总数：{}，成功：{}，失败：{}", requestId, totalNum, succNum, failNum);

        return response;
    }
    
    /**
     * 验证批量导入设备数据的必填字段
     */
    private String validateBatchDevice(BatchImportDeviceRequestDTO device) {
        if (device.getDeviceId() == null) {
            return "第" + device.getRowIndex() + "行：设备ID不能为空";
        }
        if (StrUtil.isBlank(device.getDeviceName())) {
            return "第" + device.getRowIndex() + "行：设备名称不能为空";
        }
        if (StrUtil.isBlank(device.getDeviceIp())) {
            return "第" + device.getRowIndex() + "行：管理IP地址不能为空";
        }
        if (StrUtil.isBlank(device.getDeviceRole())) {
            return "第" + device.getRowIndex() + "行：设备角色不能为空";
        }
        if (StrUtil.isBlank(device.getDeviceSite())) {
            return "第" + device.getRowIndex() + "行：所属站点不能为空";
        }
        if (StrUtil.isBlank(device.getIsRR())) {
            return "第" + device.getRowIndex() + "行：是否RR设备不能为空";
        }
        if (StrUtil.isBlank(device.getDeviceManufacturer())) {
            return "第" + device.getRowIndex() + "行：厂商名称不能为空";
        }
        
        return null;
    }

    
    /**
     * 将批量导入DTO转换为新增设备DTO
     */
    private AddDeviceRequestDTO convertToAddDeviceRequest(BatchImportDeviceRequestDTO batchDevice) {
        AddDeviceRequestDTO addRequest = new AddDeviceRequestDTO();
        addRequest.setDeviceId(batchDevice.getDeviceId());
        addRequest.setDeviceName(batchDevice.getDeviceName());
        addRequest.setDeviceIp(batchDevice.getDeviceIp());
        addRequest.setDeviceSn(batchDevice.getDeviceSn());
        addRequest.setDeviceModel(batchDevice.getDeviceModel());
        addRequest.setDeviceRole(batchDevice.getDeviceRole());
        addRequest.setDeviceManufacturer(batchDevice.getDeviceManufacturer());
        addRequest.setDeviceSite(batchDevice.getDeviceSite());
        
        // 处理是否RR设备字段
        if ("是".equals(batchDevice.getIsRR()) || "true".equalsIgnoreCase(batchDevice.getIsRR()) || "1".equals(batchDevice.getIsRR())) {
            addRequest.setIsRR(true);
        } else {
            addRequest.setIsRR(false);
        }
        
        // 处理是否纳管字段
        if ("是".equals(batchDevice.getIsMarkDevice()) || "true".equalsIgnoreCase(batchDevice.getIsMarkDevice()) || "1".equals(batchDevice.getIsMarkDevice())) {
            addRequest.setIsMarkDevice(1);
        } else {
            addRequest.setIsMarkDevice(0);
        }
        
        addRequest.setDeviceIpv6(batchDevice.getDeviceIpv6());
        // 设备分组已废弃
        //addRequest.setDeviceGroup(batchDevice.getDeviceGroup());
        
        return addRequest;
    }
    
    /**
     * 根据站点ID或站点名称获取站点ID
     * 优先按站点ID查询，如果站点ID为null或查询不到数据，再按站点名称查询
     */
    private Integer getSiteId(Integer siteId, String siteName) {
        try {
            // 1. 如果站点ID不为null，先按站点ID查询（@TableLogic自动排除逻辑删除的记录）
            if (siteId != null) {
                LambdaQueryWrapper<SiteCustomInfo> idQueryWrapper = new LambdaQueryWrapper<>();
                idQueryWrapper.eq(SiteCustomInfo::getSiteId, siteId)
                             .select(SiteCustomInfo::getSiteId);
                SiteCustomInfo siteCustomInfoById = siteCustomInfoMapper.selectOne(idQueryWrapper);
                if (siteCustomInfoById != null) {
                    log.debug("根据站点ID查询成功，站点ID：{}", siteId);
                    return siteCustomInfoById.getSiteId();
                }
                log.debug("根据站点ID未找到记录，站点ID：{}，尝试按站点名称查询", siteId);
            }

            // 2. 按站点名称查询（@TableLogic自动排除逻辑删除的记录）
            if (StrUtil.isNotBlank(siteName)) {
                LambdaQueryWrapper<SiteCustomInfo> nameQueryWrapper = new LambdaQueryWrapper<>();
                nameQueryWrapper.eq(SiteCustomInfo::getSiteName, siteName)
                               .select(SiteCustomInfo::getSiteId);
                SiteCustomInfo siteCustomInfoByName = siteCustomInfoMapper.selectOne(nameQueryWrapper);
                if (siteCustomInfoByName != null) {
                    log.debug("根据站点名称查询成功，站点名称：{}，站点ID：{}", siteName, siteCustomInfoByName.getSiteId());
                    return siteCustomInfoByName.getSiteId();
                }
                log.debug("根据站点名称未找到记录，站点名称：{}", siteName);
            }
            
            log.debug("站点查询失败，站点ID：{}，站点名称：{}", siteId, siteName);
            return null;
            
        } catch (Exception e) {
            log.error("查询站点信息异常，站点ID：{}，站点名称：{}", siteId, siteName, e);
            return null;
        }
    }

    @Override
    public QueryDeviceInfoListBySiteIdResponseDTO queryDeviceInfoListBySiteId(Integer siteId, String requestId) {
        log.info("开始查询站点下所有设备信息，请求ID：{}，站点ID：{}", requestId, siteId);

        // 1. 验证站点是否存在（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<SiteCustomInfo> siteQueryWrapper = new LambdaQueryWrapper<>();
        siteQueryWrapper.eq(SiteCustomInfo::getSiteId, siteId);
        SiteCustomInfo siteCustomInfo = siteCustomInfoMapper.selectOne(siteQueryWrapper);
        if (siteCustomInfo == null) {
            log.warn("站点不存在或已被删除，请求ID：{}，站点ID：{}", requestId, siteId);
            throw new ServiceException("VALIDATION_ERROR", "站点不存在或已被删除", "站点ID：" + siteId);
        }

        // 2. 递归获取该站点及所有子站点的ID列表
        Set<Integer> allSiteIds = getAllSubSiteIds(siteId);
        log.info("查询到站点及子站点，请求ID：{}，站点总数：{}，站点列表：{}", requestId, allSiteIds.size(), allSiteIds);

        // 3. 根据站点ID列表查询设备信息
        List<DeviceCustomInfo> deviceList = getDevicesBySiteIds(allSiteIds);
        log.info("查询到设备，请求ID：{}，设备总数：{}", requestId, deviceList.size());

        // 4. 查询每个设备的详细信息，异常会被全局异常处理器处理
        List<QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo> deviceInfoList = new ArrayList<>();
        for (DeviceCustomInfo device : deviceList) {
            QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo deviceInfo = buildDeviceInfoBySiteId(device);
            deviceInfoList.add(deviceInfo);
        }

        log.info("查询站点下设备信息完成，请求ID：{}，站点ID：{}，设备总数：{}", requestId, siteId, deviceInfoList.size());
        return QueryDeviceInfoListBySiteIdResponseDTO.success(requestId, deviceInfoList);
    }

    /**
     * 递归获取站点及其所有子站点的ID列表
     * 
     * @param siteId 根站点ID
     * @return 站点ID集合（包含根站点和所有子站点）
     */
    private Set<Integer> getAllSubSiteIds(Integer siteId) {
        Set<Integer> allSiteIds = new HashSet<>();
        collectSubSiteIds(siteId, allSiteIds);
        return allSiteIds;
    }

    /**
     * 递归收集站点及其所有子站点的ID
     * 
     * @param siteId 当前站点ID
     * @param collectedIds 已收集的站点ID集合
     */
    private void collectSubSiteIds(Integer siteId, Set<Integer> collectedIds) {
        // 防止循环引用
        if (collectedIds.contains(siteId)) {
            return;
        }

        // 添加当前站点ID
        collectedIds.add(siteId);

        // 查询子站点（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteCustomInfo::getParentSiteId, siteId)
                   .select(SiteCustomInfo::getSiteId);
        
        List<SiteCustomInfo> childSites = siteCustomInfoMapper.selectList(queryWrapper);
        
        // 递归处理子站点
        for (SiteCustomInfo childSite : childSites) {
            collectSubSiteIds(childSite.getSiteId(), collectedIds);
        }
    }

    /**
     * 根据站点ID列表查询设备信息
     * 
     * @param siteIds 站点ID集合
     * @return 设备信息列表
     */
    private List<DeviceCustomInfo> getDevicesBySiteIds(Set<Integer> siteIds) {
        if (siteIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(DeviceCustomInfo::getDeviceSiteId, siteIds)
                   .orderByAsc(DeviceCustomInfo::getDeviceId);
        // @TableLogic自动排除逻辑删除的记录
        
        return deviceCustomInfoMapper.selectList(queryWrapper);
    }

    /**
     * 构建设备详细信息
     *
     * @param device 设备自定义信息
     * @return 设备详细信息
     */
    private QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo buildDeviceInfoBySiteId(DeviceCustomInfo device) {
        QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo deviceInfo = new QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo();

        // 填充定制库中的数据
        deviceInfo.setDeviceId(device.getDeviceId());
        deviceInfo.setSiteId(device.getDeviceSiteId());
        deviceInfo.setDeviceSite(device.getDeviceSite());
        deviceInfo.setIsRR(device.getIsRr());
        deviceInfo.setDeviceRole(device.getDeviceRole());

        long platformNodeId;
        try {
            platformNodeId = Long.parseLong(device.getPlatformNodeId());
        } catch (NumberFormatException e) {
            throw new ServiceException("VALIDATION_ERROR", "平台设备节点ID格式错误", "设备ID：" + device.getDeviceId());
        }

        // 如果有平台节点ID，查询平台设备信息，异常会被全局异常处理器处理
        fillPlatformDeviceInfoBySiteId(deviceInfo, platformNodeId);

        deviceInfo.setOptionField(null);
        return deviceInfo;
    }

    /**
     * 从平台查询设备信息并填充到设备详细信息中
     *
     * @param deviceInfo 设备详细信息对象
     * @param nodeId 平台节点ID
     */
    private void fillPlatformDeviceInfoBySiteId(QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo deviceInfo, Long nodeId) {
        // 调用平台接口查询设备信息，PlatformApiException会被全局异常处理器处理
        PlatformGetNodesRequestDTO getNodesRequest = new PlatformGetNodesRequestDTO(nodeId);
        PlatformGetNodesResponseDTO getNodesResponse = platformApiService.getNodes(getNodesRequest);

        // 检查平台接口返回结果
        if (getNodesResponse == null || !getNodesResponse.getSuccessful()) {
            throw new ServiceException("PLATFORM_ERROR", "平台查询设备信息失败", "平台设备节点ID：" + nodeId);
        }

        // 只有返回空数据的情况才填充默认值，继续执行
        if (getNodesResponse.getResult() == null ||
                getNodesResponse.getResult().getRecords() == null ||
                getNodesResponse.getResult().getRecords().isEmpty()) {
            log.warn("平台查询设备信息返回空数据，平台设备节点ID：{}，使用默认值", nodeId);
            fillDefaultDeviceInfoBySiteId(deviceInfo);
            return;
        }

        PlatformGetNodesResponseDTO.NodeRecord nodeRecord = getNodesResponse.getResult().getRecords().get(0);

        // 填充基本信息
        deviceInfo.setDeviceName(nodeRecord.getNodeName());
        deviceInfo.setDeviceManufacturer(mapCompanyToManufacturer(nodeRecord.getCompany()));
        deviceInfo.setDeviceModel(nodeRecord.getNodeModel());
        deviceInfo.setDeviceMac(nodeRecord.getManageMac());

        // 处理序列号，如果是列表则取第一个
        if (nodeRecord.getSerialNumbers() != null && !nodeRecord.getSerialNumbers().isEmpty()) {
            deviceInfo.setDeviceSn(nodeRecord.getSerialNumbers().get(0));
        } else {
            deviceInfo.setDeviceSn("");
        }

        deviceInfo.setDeviceStatus(mapNodeDisplayToSimpleStatus(nodeRecord.getNodeDisplay()));
        deviceInfo.setDeviceIp(nodeRecord.getManageIp());
        deviceInfo.setIsMarkDevice(nodeRecord.getIsolationStatus() != null && nodeRecord.getIsolationStatus() == 1 ? 1 : 0);
    }

    /**
     * 填充默认设备信息
     * 
     * @param deviceInfo 设备详细信息对象
     */
    private void fillDefaultDeviceInfoBySiteId(QueryDeviceInfoListBySiteIdResponseDTO.DeviceInfo deviceInfo) {
        deviceInfo.setDeviceName("未知设备");
        deviceInfo.setDeviceManufacturer("Unknown");
        deviceInfo.setDeviceModel("未知型号");
        deviceInfo.setDeviceMac("");
        deviceInfo.setDeviceSn("");
        deviceInfo.setDeviceStatus("off");
        deviceInfo.setDeviceIp("");
        deviceInfo.setIsMarkDevice(0);
    }



    @Override
    public QueryDeviceModelResponseDTO queryDeviceModel(String requestId) {
        log.info("开始查询设备型号列表，请求ID：{}", requestId);

        // 读取配置文件
        String configFilePath = deviceModelConfig.getConfigFile();
        log.info("读取设备型号配置文件：{}", configFilePath);

        String jsonContent = readConfigFile(configFilePath);
        if (StrUtil.isBlank(jsonContent)) {
            log.warn("设备型号配置文件内容为空，请求ID：{}", requestId);
            return QueryDeviceModelResponseDTO.success(requestId, new ArrayList<>());
        }

        // 解析JSON内容，异常会被全局异常处理器处理
        List<String> deviceModelNames = JSON.parseObject(jsonContent, new TypeReference<List<String>>() {
        });

        // 转换为响应格式
        List<QueryDeviceModelResponseDTO.DeviceModel> deviceModelList = new ArrayList<>();
        for (String modelName : deviceModelNames) {
            QueryDeviceModelResponseDTO.DeviceModel deviceModel = new QueryDeviceModelResponseDTO.DeviceModel();
            deviceModel.setDeviceModel(modelName);
            deviceModel.setOptionField(null); // 暂不使用扩展字段
            deviceModelList.add(deviceModel);
        }

        log.info("查询设备型号列表完成，请求ID：{}，共查询到{}个型号", requestId, deviceModelList.size());
        return QueryDeviceModelResponseDTO.success(requestId, deviceModelList);
    }

    /**
     * 读取配置文件内容
     * 
     * @param configFilePath 配置文件路径
     * @return 文件内容
     */
    private String readConfigFile(String configFilePath) {
        try {
            Resource resource = null;
            
            // 判断是否是绝对路径（外部文件系统路径）
            if (configFilePath.startsWith("/") || configFilePath.matches("^[A-Za-z]:.*")) {
                // Unix绝对路径或Windows绝对路径
                File externalFile = new File(configFilePath);
                if (externalFile.exists() && externalFile.isFile()) {
                    resource = new FileSystemResource(externalFile);
                    log.info("使用外部文件系统配置文件：{}", configFilePath);
                } else {
                    log.warn("外部配置文件不存在：{}，尝试使用classpath路径", configFilePath);
                }
            }
            
            // 如果外部文件不存在，尝试从classpath读取
            if (resource == null) {
                resource = new ClassPathResource(configFilePath);
                if (resource.exists()) {
                    log.info("使用classpath配置文件：{}", configFilePath);
                } else {
                    log.warn("classpath配置文件也不存在：{}", configFilePath);
                    return null;
                }
            }
            
            byte[] bytes = FileCopyUtils.copyToByteArray(resource.getInputStream());
            return new String(bytes, StandardCharsets.UTF_8);
            
        } catch (IOException e) {
            log.error("读取设备型号配置文件异常：{}", configFilePath, e);
            return null;
        }
    }



    @Override
    public QueryDeviceStatusResponseDTO queryDeviceStatus(Integer siteId, String requestId) {
        log.info("开始查询设备状态信息，请求ID：{}，站点ID：{}", requestId, siteId);

        // 1. 验证站点是否存在（@TableLogic自动排除逻辑删除的记录）
        LambdaQueryWrapper<SiteCustomInfo> siteQueryWrapper = new LambdaQueryWrapper<>();
        siteQueryWrapper.eq(SiteCustomInfo::getSiteId, siteId);
        SiteCustomInfo siteCustomInfo = siteCustomInfoMapper.selectOne(siteQueryWrapper);
        if (siteCustomInfo == null) {
            log.warn("站点不存在或已被删除，请求ID：{}，站点ID：{}", requestId, siteId);
            throw new ServiceException("VALIDATION_ERROR", "站点不存在或已被删除", "站点ID：" + siteId);
        }

        // 2. 递归查询站点及所有子站点
        Set<Integer> allSiteIds = getAllSubSiteIds(siteId);
        log.info("查询到站点及子站点总数：{}，站点IDs：{}", allSiteIds.size(), allSiteIds);

        // 3. 查询这些站点下的所有设备
        List<DeviceCustomInfo> devices = getDevicesBySiteIds(allSiteIds);
        log.info("查询到设备总数：{}", devices.size());

        if (devices.isEmpty()) {
            log.warn("未找到任何设备，请求ID：{}，站点ID：{}", requestId, siteId);
            return QueryDeviceStatusResponseDTO.success(requestId, 0, 0, 0);
        }

        // 4. 提取平台节点ID，进行批量查询，为空或格式错误都抛出全局异常
        List<Long> platformNodeIds = new ArrayList<>();
        for (DeviceCustomInfo device : devices) {
            if (StrUtil.isBlank(device.getPlatformNodeId())) {
                throw new ServiceException("VALIDATION_ERROR",
                        "设备ID为 " + device.getDeviceId() + " 的平台节点ID为空",
                        "设备ID：" + device.getDeviceId() + "，平台节点ID为空");
            }

            try {
                platformNodeIds.add(Long.parseLong(device.getPlatformNodeId()));
            } catch (NumberFormatException e) {
                throw new ServiceException("VALIDATION_ERROR",
                        "设备ID为 " + device.getDeviceId() + " 的平台节点ID格式错误：" + device.getPlatformNodeId(),
                        "设备ID：" + device.getDeviceId() + "，平台节点ID：" + device.getPlatformNodeId());
            }
        }

        // 5. 批量调用平台接口查询设备状态
        int total = devices.size();
        int onLine = 0;
        int offLine = 0;

        log.info("开始批量查询{}台设备的状态信息", platformNodeIds.size());

        // 使用批量查询接口，PlatformApiException会被全局异常处理器处理
        PlatformGetNodesRequestDTO getNodesRequest = new PlatformGetNodesRequestDTO(platformNodeIds);
        PlatformGetNodesResponseDTO getNodesResponse = platformApiService.getNodes(getNodesRequest);

        // 检查平台接口返回结果
        if (getNodesResponse == null || !getNodesResponse.getSuccessful()) {
            throw new ServiceException("PLATFORM_ERROR", "平台批量查询设备状态失败", "站点ID：" + siteId + "，设备数量：" + platformNodeIds.size());
        }

        // 检查返回数据是否为空，为空也抛出全局异常
        if (getNodesResponse.getResult() == null || getNodesResponse.getResult().getRecords() == null) {
            throw new ServiceException("PLATFORM_ERROR", "平台批量查询设备状态返回空数据", "站点ID：" + siteId + "，设备数量：" + platformNodeIds.size());
        }

        List<PlatformGetNodesResponseDTO.NodeRecord> records = getNodesResponse.getResult().getRecords();
        log.info("平台返回设备记录数：{}", records.size());

        // 统计在线/离线设备数量
        for (PlatformGetNodesResponseDTO.NodeRecord record : records) {
            if (record.getNodeDisplay() != null && record.getNodeDisplay() == 3) {
                offLine++;  // nodeDisplay=3为离线
            } else {
                onLine++;   // 其他值为在线
            }
        }

        // 对于没有在平台查询到的设备，按离线处理
        int notFoundDevices = platformNodeIds.size() - records.size();
        if (notFoundDevices > 0) {
            offLine += notFoundDevices;
            log.info("有{}台设备在平台中未找到，按离线处理", notFoundDevices);
        }

        log.info("设备状态信息查询完成，请求ID：{}，总数：{}，在线：{}，离线：{}", requestId, total, onLine, offLine);
        return QueryDeviceStatusResponseDTO.success(requestId, total, onLine, offLine);
    }

    /**
     * 解析字符串为Float值
     * 
     * @param value 字符串值
     * @return Float值，解析失败时返回null
     */
    private Float parseFloatValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Float.parseFloat(value.trim());
        } catch (NumberFormatException e) {
            log.warn("无法解析Float值：{}，错误：{}", value, e.getMessage());
            return null;
        }
    }



} 