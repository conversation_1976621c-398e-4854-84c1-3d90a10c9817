package com.h3c.dzkf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.DeviceCustomInfoMapper;
import com.h3c.dzkf.dao.LanCustomInfoMapper;
import com.h3c.dzkf.entity.DeviceCustomInfo;
import com.h3c.dzkf.entity.LanCustomInfo;
import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.LanInterfaceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * LAN口管理Service实现类
 */
@Slf4j
@Service
public class LanInterfaceServiceImpl implements LanInterfaceService {

    @Autowired
    private LanCustomInfoMapper lanCustomInfoMapper;

    @Autowired
    private DeviceCustomInfoMapper deviceCustomInfoMapper;

    @Override
    public AddLanInterfaceResponseDTO addLanInterface(AddLanInterfaceRequestDTO request, String requestId) {
        log.info("开始处理新增LAN口请求，请求ID：{}，设备ID：{}，接口名称：{}",
                requestId, request.getDeviceId(), request.getInterfaceName());

        // 1. 验证设备是否存在，使用LambdaQueryWrapper自动过滤软删除
        LambdaQueryWrapper<DeviceCustomInfo> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(DeviceCustomInfo::getDeviceId, request.getDeviceId());
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(deviceQueryWrapper);
        if (deviceInfo == null) {
            log.warn("设备不存在，请求ID：{}，设备ID：{}", requestId, request.getDeviceId());
            throw new ServiceException("DEVICE_NOT_EXISTS", "设备不存在", "设备ID: " + request.getDeviceId());
        }

        // 2. 验证设备下是否已存在相同接口名称，使用LambdaQueryWrapper自动过滤软删除
        LambdaQueryWrapper<LanCustomInfo> interfaceQueryWrapper = new LambdaQueryWrapper<>();
        interfaceQueryWrapper.eq(LanCustomInfo::getDeviceId, request.getDeviceId())
                .eq(LanCustomInfo::getInterfaceName, request.getInterfaceName());
        LanCustomInfo existingInterface = lanCustomInfoMapper.selectOne(interfaceQueryWrapper);
        if (existingInterface != null) {
            log.warn("设备下已存在相同接口名称，请求ID：{}，设备ID：{}，接口名称：{}",
                    requestId, request.getDeviceId(), request.getInterfaceName());
            throw new ServiceException("INTERFACE_NAME_EXISTS", "设备下已存在相同接口名称",
                    "设备ID: " + request.getDeviceId() + ", 接口名称: " + request.getInterfaceName());
        }

        // 3. 构建LAN口信息
        LanCustomInfo lanCustomInfo = new LanCustomInfo();
        lanCustomInfo.setDeviceId(request.getDeviceId());
        lanCustomInfo.setInterfaceName(request.getInterfaceName());
        lanCustomInfo.setCreateTime(new Date());
        lanCustomInfo.setUpdateTime(new Date());
        lanCustomInfo.setIsDeleted(0); // 显式设置未删除状态

        // 4. 插入数据库，使用MyBatis-Plus的insert方法
        int result = lanCustomInfoMapper.insert(lanCustomInfo);
        if (result <= 0) {
            log.error("新增LAN口失败，请求ID：{}，设备ID：{}", requestId, request.getDeviceId());
            throw new ServiceException("LAN_INSERT_FAILED", "新增LAN口失败",
                    "设备ID: " + request.getDeviceId() + ", 接口名称: " + request.getInterfaceName());
        }

        log.info("新增LAN口成功，请求ID：{}，设备ID：{}，LAN口ID：{}",
                requestId, request.getDeviceId(), lanCustomInfo.getLanId());
        return AddLanInterfaceResponseDTO.success(requestId, lanCustomInfo.getLanId());
    }

    @Override
    public DeleteLanInterfaceResponseDTO deleteLanInterface(Long lanId, String requestId) {
        log.info("开始处理删除LAN口请求，请求ID：{}，LAN口ID：{}", requestId, lanId);

        // 1. 验证LAN口是否存在，使用MyBatis-Plus的查询自动过滤软删除
        LambdaQueryWrapper<LanCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LanCustomInfo::getLanId, lanId);
        LanCustomInfo lanCustomInfo = lanCustomInfoMapper.selectOne(queryWrapper);

        if (lanCustomInfo == null) {
            log.warn("LAN口不存在，请求ID：{}，LAN口ID：{}", requestId, lanId);
            throw new ServiceException("LAN_NOT_EXISTS", "LAN口不存在", "LAN口ID: " + lanId);
        }

        // 2. 删除LAN口信息，使用MyBatis-Plus的deleteById方法（自动软删除）
        int result = lanCustomInfoMapper.deleteById(lanCustomInfo.getLanId());
        if (result <= 0) {
            log.error("删除LAN口失败，请求ID：{}，LAN口ID：{}", requestId, lanId);
            throw new ServiceException("LAN_DELETE_FAILED", "删除LAN口失败", "LAN口ID: " + lanId);
        }

        log.info("删除LAN口成功，请求ID：{}，LAN口ID：{}", requestId, lanId);
        return DeleteLanInterfaceResponseDTO.success(requestId);
    }

    @Override
    public void updateLanInterface(UpdateLanInterfaceRequestDTO request, String requestId) {
        log.info("开始处理修改LAN口请求，请求ID：{}，LAN口ID：{}，设备ID：{}，接口名称：{}",
                requestId, request.getLanId(), request.getDeviceId(), request.getInterfaceName());

        // 1. 验证LAN口是否存在，使用LambdaQueryWrapper自动过滤软删除
        LambdaQueryWrapper<LanCustomInfo> lanQueryWrapper = new LambdaQueryWrapper<>();
        lanQueryWrapper.eq(LanCustomInfo::getLanId, request.getLanId());
        LanCustomInfo existingLan = lanCustomInfoMapper.selectOne(lanQueryWrapper);
        if (existingLan == null) {
            log.warn("LAN口不存在，请求ID：{}，LAN口ID：{}", requestId, request.getLanId());
            throw new ServiceException("LAN_NOT_EXISTS", "LAN口不存在", "LAN口ID: " + request.getLanId());
        }

        // 2. 验证设备是否存在，使用LambdaQueryWrapper自动过滤软删除
        LambdaQueryWrapper<DeviceCustomInfo> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(DeviceCustomInfo::getDeviceId, request.getDeviceId());
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(deviceQueryWrapper);
        if (deviceInfo == null) {
            log.warn("设备不存在，请求ID：{}，设备ID：{}", requestId, request.getDeviceId());
            throw new ServiceException("DEVICE_NOT_EXISTS", "设备不存在", "设备ID: " + request.getDeviceId());
        }

        // 3. 如果修改了设备ID或接口名称，需要验证新的组合是否已存在
        if (!existingLan.getDeviceId().equals(request.getDeviceId()) ||
                !existingLan.getInterfaceName().equals(request.getInterfaceName())) {

            LambdaQueryWrapper<LanCustomInfo> interfaceQueryWrapper = new LambdaQueryWrapper<>();
            interfaceQueryWrapper.eq(LanCustomInfo::getDeviceId, request.getDeviceId())
                    .eq(LanCustomInfo::getInterfaceName, request.getInterfaceName())
                    .ne(LanCustomInfo::getLanId, request.getLanId()); // 排除当前记录
            LanCustomInfo conflictInterface = lanCustomInfoMapper.selectOne(interfaceQueryWrapper);
            if (conflictInterface != null) {
                log.warn("设备下已存在相同接口名称，请求ID：{}，设备ID：{}，接口名称：{}",
                        requestId, request.getDeviceId(), request.getInterfaceName());
                throw new ServiceException("INTERFACE_NAME_EXISTS", "设备下已存在相同接口名称",
                        "设备ID: " + request.getDeviceId() + ", 接口名称: " + request.getInterfaceName());
            }
        }

        // 4. 更新LAN口信息
        existingLan.setDeviceId(request.getDeviceId());
        existingLan.setInterfaceName(request.getInterfaceName());
        existingLan.setUpdateTime(new Date());

        // 5. 执行更新操作，使用MyBatis-Plus的updateById方法
        int result = lanCustomInfoMapper.updateById(existingLan);
        if (result <= 0) {
            log.error("修改LAN口失败，请求ID：{}，LAN口ID：{}", requestId, request.getLanId());
            throw new ServiceException("LAN_UPDATE_FAILED", "修改LAN口失败", "LAN口ID: " + request.getLanId());
        }

        log.info("修改LAN口成功，请求ID：{}，LAN口ID：{}", requestId, request.getLanId());
    }

    @Override
    public QueryLanInterfaceResponseDTO queryLanInterface(String requestId) {
        log.info("开始处理查询LAN口请求，请求ID：{}", requestId);

        // 1. 查询所有LAN口信息，使用MyBatis-Plus自动过滤软删除数据
        LambdaQueryWrapper<LanCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(LanCustomInfo::getLanId); // 按LAN口ID升序排列
        List<LanCustomInfo> lanCustomInfoList = lanCustomInfoMapper.selectList(queryWrapper);

        // 2. 转换为响应DTO格式
        List<QueryLanInterfaceResponseDTO.LanInterfaceInfo> lanList = new ArrayList<>();
        for (LanCustomInfo lanCustomInfo : lanCustomInfoList) {
            QueryLanInterfaceResponseDTO.LanInterfaceInfo lanInfo = new QueryLanInterfaceResponseDTO.LanInterfaceInfo();
            lanInfo.setLanId(lanCustomInfo.getLanId());
            lanInfo.setDeviceId(lanCustomInfo.getDeviceId());
            lanInfo.setInterfaceName(lanCustomInfo.getInterfaceName());
            lanList.add(lanInfo);
        }

        log.info("查询LAN口成功，请求ID：{}，查询到{}条记录", requestId, lanList.size());
        return QueryLanInterfaceResponseDTO.success(requestId, lanList);
    }
} 