package com.h3c.dzkf.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.TeTunnelTemplateCustomInfoMapper;
import com.h3c.dzkf.entity.TeTunnelTemplateCustomInfo;
import com.h3c.dzkf.entity.dto.AddTeTunnelTemplateRequestDTO;
import com.h3c.dzkf.entity.dto.DeleteTeTunnelTemplateRequestDTO;
import com.h3c.dzkf.entity.dto.QueryTeTunnelTemplateResponseDTO;
import com.h3c.dzkf.entity.dto.UpdateTeTunnelTemplateRequestDTO;
import com.h3c.dzkf.service.TeTunnelTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 隧道模板管理Service实现类
 */
@Slf4j
@Service
public class TeTunnelTemplateServiceImpl implements TeTunnelTemplateService {

    @Autowired
    private TeTunnelTemplateCustomInfoMapper teTunnelTemplateCustomInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTeTunnelTemplate(AddTeTunnelTemplateRequestDTO request, String requestId) {
        log.info("开始新增隧道模板，请求ID：{}，请求参数：{}", requestId, JSON.toJSONString(request));

        // 1. 验证必填字段
        String validationError = validateRequest(request);
        if (validationError != null) {
            throw new ServiceException("TUNNEL_TEMPLATE_VALIDATION_ERROR", validationError, "策略ID: " + request.getStrategy().getStrategyId());
        }

        // 2. 检查策略ID是否已存在，使用@TableLogic自动过滤软删除记录
        LambdaQueryWrapper<TeTunnelTemplateCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeTunnelTemplateCustomInfo::getStrategyId, request.getStrategy().getStrategyId());
        TeTunnelTemplateCustomInfo existingTemplate = teTunnelTemplateCustomInfoMapper.selectOne(queryWrapper);
        if (existingTemplate != null) {
            throw new ServiceException("TUNNEL_STRATEGY_ID_EXISTS", "隧道策略ID已存在", "策略ID: " + request.getStrategy().getStrategyId());
        }

        // 3. 构建隧道模板实体
        TeTunnelTemplateCustomInfo teTunnelTemplate = buildTeTunnelTemplate(request);

        // 4. 保存到数据库
        int result = teTunnelTemplateCustomInfoMapper.insert(teTunnelTemplate);
        if (result <= 0) {
            log.error("隧道模板新增失败，数据库插入返回0，请求ID：{}", requestId);
            throw new ServiceException("TUNNEL_TEMPLATE_INSERT_FAILED", "隧道模板新增失败", "策略ID: " + request.getStrategy().getStrategyId());
        }

        log.info("隧道模板新增成功，请求ID：{}，策略ID：{}", requestId, request.getStrategy().getStrategyId());
    }

    /**
     * 验证请求参数
     */
    private String validateRequest(AddTeTunnelTemplateRequestDTO request) {
        // 获取嵌套的strategy对象
        AddTeTunnelTemplateRequestDTO.Strategy strategy = request.getStrategy();
        if (strategy == null) {
            return "隧道策略信息不能为空";
        }

        // 1. 验证保障带宽值
        if (strategy.getBandwidth() != null && strategy.getBandwidth() <= 0) {
            return "保障带宽必须大于0";
        }

        // 4. 验证SLA等级
        if (StrUtil.isNotBlank(strategy.getSlaId())) {
            String[] validSlaIds = {"EF", "AF4", "AF3", "AF2", "AF1", "BE"};
            boolean isValidSla = false;
            for (String validSla : validSlaIds) {
                if (validSla.equals(strategy.getSlaId())) {
                    isValidSla = true;
                    break;
                }
            }
            if (!isValidSla) {
                return "SLA等级必须是EF、AF4、AF3、AF2、AF1、BE中的一个";
            }
        }

        // 5. 验证链路等级
        String[] validPriorities = {"高", "中", "低"};
        if (StrUtil.isBlank(strategy.getUpAllowLinkPriority())) {
            return "正向链路等级不能为空";
        }
        boolean isValidUpPriority = false;
        for (String validPriority : validPriorities) {
            if (validPriority.equals(strategy.getUpAllowLinkPriority())) {
                isValidUpPriority = true;
                break;
            }
        }
        if (!isValidUpPriority) {
            return "正向链路等级必须是高、中、低中的一个";
        }

        if (strategy.getDownAllowLinkPriorityList() == null || strategy.getDownAllowLinkPriorityList().isEmpty()) {
            return "反向链路等级不能为空";
        }
        for (String priority : strategy.getDownAllowLinkPriorityList()) {
            if (StrUtil.isBlank(priority)) {
                return "反向链路等级不能为空";
            }
            boolean isValidDownPriority = false;
            for (String validPriority : validPriorities) {
                if (validPriority.equals(priority)) {
                    isValidDownPriority = true;
                    break;
                }
            }
            if (!isValidDownPriority) {
                return "反向链路等级必须是高、中、低中的一个";
            }
        }

        // 6. 验证丢包率
        if (StrUtil.isBlank(strategy.getPacketLossRate())) {
            return "丢包率不能为空";
        }

        // 7. 验证延迟时间和网络抖动
        if (StrUtil.isBlank(strategy.getDelayTime())) {
            return "延迟时间不能为空";
        }
        if (StrUtil.isBlank(strategy.getNetworkJitter())) {
            return "网络抖动不能为空";
        }

        return null; // 验证通过
    }

    /**
     * 构建隧道模板实体
     */
    private TeTunnelTemplateCustomInfo buildTeTunnelTemplate(AddTeTunnelTemplateRequestDTO request) {
        TeTunnelTemplateCustomInfo teTunnelTemplate = new TeTunnelTemplateCustomInfo();

        // 从嵌套的strategy对象中获取数据
        AddTeTunnelTemplateRequestDTO.Strategy strategy = request.getStrategy();

        teTunnelTemplate.setStrategyId(strategy.getStrategyId());
        teTunnelTemplate.setStrategyName(strategy.getStrategyName());
        teTunnelTemplate.setSlaId(strategy.getSlaId());
        teTunnelTemplate.setBandwidth(strategy.getBandwidth());
        teTunnelTemplate.setUpAllowLinkPriority(strategy.getUpAllowLinkPriority());
        // 将 List<String> 转换为 JSON 字符串存储
        teTunnelTemplate.setDownAllowLinkPriority(JSON.toJSONString(strategy.getDownAllowLinkPriorityList()));
        teTunnelTemplate.setPacketLossRate(strategy.getPacketLossRate());
        teTunnelTemplate.setDelayTime(strategy.getDelayTime());
        teTunnelTemplate.setNetworkJitter(strategy.getNetworkJitter());

        // 设置系统字段
        Date now = new Date();
        teTunnelTemplate.setCreateTime(now);
        teTunnelTemplate.setUpdateTime(now);
        teTunnelTemplate.setIsDeleted(0); // 显式设置未删除状态

        return teTunnelTemplate;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTeTunnelTemplate(DeleteTeTunnelTemplateRequestDTO request, String requestId) {
        log.info("开始删除隧道模板，请求ID：{}，策略ID集合：{}", requestId, JSON.toJSONString(request.getStrategyIds()));

        // 1. 验证参数
        if (request.getStrategyIds() == null || request.getStrategyIds().isEmpty()) {
            throw new ServiceException("TUNNEL_STRATEGY_IDS_EMPTY", "隧道策略ID集合不能为空");
        }

        // 2. 查询要删除的模板是否存在，使用@TableLogic自动过滤软删除记录
        LambdaQueryWrapper<TeTunnelTemplateCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TeTunnelTemplateCustomInfo::getStrategyId, request.getStrategyIds());

        List<TeTunnelTemplateCustomInfo> existingTemplates = teTunnelTemplateCustomInfoMapper.selectList(queryWrapper);

        if (existingTemplates.isEmpty()) {
            log.warn("未找到可删除的隧道模板，请求ID：{}，策略ID集合：{}", requestId, request.getStrategyIds());
            throw new ServiceException("TUNNEL_TEMPLATES_NOT_FOUND", "未找到可删除的隧道模板",
                    "策略ID集合: " + JSON.toJSONString(request.getStrategyIds()));
        }

        // 3. 执行软删除，使用MyBatis-Plus的deleteBatchIds方法自动处理软删除
        List<Long> idsToDelete = new ArrayList<>();
        for (TeTunnelTemplateCustomInfo template : existingTemplates) {
            idsToDelete.add(template.getId());
        }

        int deletedCount = teTunnelTemplateCustomInfoMapper.deleteBatchIds(idsToDelete);

        if (deletedCount <= 0) {
            log.error("隧道模板删除失败，数据库更新返回0，请求ID：{}", requestId);
            throw new ServiceException("TUNNEL_TEMPLATE_DELETE_FAILED", "隧道模板删除失败",
                    "策略ID集合: " + JSON.toJSONString(request.getStrategyIds()));
        }

        log.info("隧道模板删除成功，请求ID：{}，实际删除数量：{}，请求删除策略ID：{}",
                requestId, deletedCount, request.getStrategyIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTeTunnelTemplate(UpdateTeTunnelTemplateRequestDTO request, String requestId) {
        log.info("开始修改隧道模板，请求ID：{}，请求参数：{}", requestId, JSON.toJSONString(request));

        // 1. 验证必填字段
        String validationError = validateUpdateRequest(request);
        if (validationError != null) {
            throw new ServiceException("TUNNEL_TEMPLATE_VALIDATION_ERROR", validationError, "策略ID: " + request.getStrategy().getStrategyId());
        }

        // 2. 根据策略ID查询现有记录，使用@TableLogic自动过滤软删除记录
        LambdaQueryWrapper<TeTunnelTemplateCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TeTunnelTemplateCustomInfo::getStrategyId, request.getStrategy().getStrategyId());
        TeTunnelTemplateCustomInfo existingTemplate = teTunnelTemplateCustomInfoMapper.selectOne(queryWrapper);

        if (existingTemplate == null) {
            log.warn("隧道模板不存在，请求ID：{}，策略ID：{}", requestId, request.getStrategy().getStrategyId());
            throw new ServiceException("TUNNEL_TEMPLATE_NOT_EXISTS", "隧道模板不存在", "策略ID: " + request.getStrategy().getStrategyId());
        }

        // 3. 使用LambdaUpdateWrapper进行更新
        LambdaUpdateWrapper<TeTunnelTemplateCustomInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TeTunnelTemplateCustomInfo::getStrategyId, request.getStrategy().getStrategyId());

        // 更新基本字段
        UpdateTeTunnelTemplateRequestDTO.Strategy strategy = request.getStrategy();
        updateWrapper.set(TeTunnelTemplateCustomInfo::getStrategyName, strategy.getStrategyName())
                .set(TeTunnelTemplateCustomInfo::getSlaId, strategy.getSlaId())
                .set(TeTunnelTemplateCustomInfo::getBandwidth, strategy.getBandwidth())
                .set(TeTunnelTemplateCustomInfo::getUpAllowLinkPriority, strategy.getUpAllowLinkPriority())
                .set(TeTunnelTemplateCustomInfo::getDownAllowLinkPriority, JSON.toJSONString(strategy.getDownAllowLinkPriorityList()))
                .set(TeTunnelTemplateCustomInfo::getPacketLossRate, strategy.getPacketLossRate())
                .set(TeTunnelTemplateCustomInfo::getDelayTime, strategy.getDelayTime())
                .set(TeTunnelTemplateCustomInfo::getNetworkJitter, strategy.getNetworkJitter())
                .set(TeTunnelTemplateCustomInfo::getUpdateTime, new Date());

        // 4. 执行更新
        int result = teTunnelTemplateCustomInfoMapper.update(null, updateWrapper);

        if (result <= 0) {
            log.error("隧道模板修改失败，数据库更新返回0，请求ID：{}", requestId);
            throw new ServiceException("TUNNEL_TEMPLATE_UPDATE_FAILED", "隧道模板修改失败", "策略ID: " + request.getStrategy().getStrategyId());
        }

        log.info("隧道模板修改成功，请求ID：{}，策略ID：{}", requestId, request.getStrategy().getStrategyId());
    }

    /**
     * 验证修改请求参数
     */
    private String validateUpdateRequest(UpdateTeTunnelTemplateRequestDTO request) {
        // 获取嵌套的strategy对象
        UpdateTeTunnelTemplateRequestDTO.Strategy strategy = request.getStrategy();
        if (strategy == null) {
            return "隧道策略信息不能为空";
        }

        // 1. 验证保障带宽值
        if (strategy.getBandwidth() != null && strategy.getBandwidth() <= 0) {
            return "保障带宽必须大于0";
        }

        // 4. 验证SLA等级
        if (StrUtil.isNotBlank(strategy.getSlaId())) {
            String[] validSlaIds = {"EF", "AF4", "AF3", "AF2", "AF1", "BE"};
            boolean isValidSla = false;
            for (String validSla : validSlaIds) {
                if (validSla.equals(strategy.getSlaId())) {
                    isValidSla = true;
                    break;
                }
            }
            if (!isValidSla) {
                return "SLA等级必须是EF、AF4、AF3、AF2、AF1、BE中的一个";
            }
        }

        // 5. 验证链路等级
        String[] validPriorities = {"高", "中", "低"};
        if (StrUtil.isBlank(strategy.getUpAllowLinkPriority())) {
            return "正向链路等级不能为空";
        }
        boolean isValidUpPriority = false;
        for (String validPriority : validPriorities) {
            if (validPriority.equals(strategy.getUpAllowLinkPriority())) {
                isValidUpPriority = true;
                break;
            }
        }
        if (!isValidUpPriority) {
            return "正向链路等级必须是高、中、低中的一个";
        }

        if (strategy.getDownAllowLinkPriorityList() == null || strategy.getDownAllowLinkPriorityList().isEmpty()) {
            return "反向链路等级不能为空";
        }
        for (String priority : strategy.getDownAllowLinkPriorityList()) {
            if (StrUtil.isBlank(priority)) {
                return "反向链路等级不能为空";
            }
            boolean isValidDownPriority = false;
            for (String validPriority : validPriorities) {
                if (validPriority.equals(priority)) {
                    isValidDownPriority = true;
                    break;
                }
            }
            if (!isValidDownPriority) {
                return "反向链路等级必须是高、中、低中的一个";
            }
        }

        // 6. 验证丢包率
        if (StrUtil.isBlank(strategy.getPacketLossRate())) {
            return "丢包率不能为空";
        }

        // 7. 验证延迟时间和网络抖动
        if (StrUtil.isBlank(strategy.getDelayTime())) {
            return "延迟时间不能为空";
        }
        if (StrUtil.isBlank(strategy.getNetworkJitter())) {
            return "网络抖动不能为空";
        }

        return null; // 验证通过
    }

    @Override
    public QueryTeTunnelTemplateResponseDTO queryTeTunnelTemplateList(String requestId) {
        log.info("开始查询隧道模板列表，请求ID：{}", requestId);

        // 查询所有隧道模板，使用@TableLogic自动过滤软删除记录
        LambdaQueryWrapper<TeTunnelTemplateCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(TeTunnelTemplateCustomInfo::getStrategyId); // 按策略ID升序排列
        List<TeTunnelTemplateCustomInfo> templates = teTunnelTemplateCustomInfoMapper.selectList(queryWrapper);

        // 转换为响应DTO
        List<QueryTeTunnelTemplateResponseDTO.TeTunnelTemplateInfo> templateInfoList = new ArrayList<>();
        for (TeTunnelTemplateCustomInfo template : templates) {
            QueryTeTunnelTemplateResponseDTO.TeTunnelTemplateInfo templateInfo = buildTeTunnelTemplateInfo(template);
            templateInfoList.add(templateInfo);
        }

        log.info("查询隧道模板列表成功，请求ID：{}，查询到{}条记录", requestId, templateInfoList.size());
        return QueryTeTunnelTemplateResponseDTO.success(requestId, templateInfoList);
    }

    /**
     * 构建隧道模板信息DTO
     */
    private QueryTeTunnelTemplateResponseDTO.TeTunnelTemplateInfo buildTeTunnelTemplateInfo(TeTunnelTemplateCustomInfo template) {
        QueryTeTunnelTemplateResponseDTO.TeTunnelTemplateInfo templateInfo = new QueryTeTunnelTemplateResponseDTO.TeTunnelTemplateInfo();
        
        templateInfo.setStrategyId(template.getStrategyId());
        templateInfo.setStrategyName(template.getStrategyName());
        templateInfo.setSlaId(template.getSlaId());
        templateInfo.setBandwidth(template.getBandwidth());

        // 设置字符串类型字段
        templateInfo.setUpAllowLinkPriority(template.getUpAllowLinkPriority());

        // 将 JSON 字符串转换为 List<String>
        try {
            List<String> downAllowLinkPriorityList = JSON.parseArray(template.getDownAllowLinkPriority(), String.class);
            templateInfo.setDownAllowLinkPriority(downAllowLinkPriorityList != null ? downAllowLinkPriorityList : new ArrayList<>());
        } catch (Exception e) {
            log.warn("解析反向链路等级失败，策略ID：{}，原始数据：{}", template.getStrategyId(), template.getDownAllowLinkPriority());
            templateInfo.setDownAllowLinkPriority(new ArrayList<>());
        }
        templateInfo.setPacketLossRate(template.getPacketLossRate());
        templateInfo.setDelayTime(template.getDelayTime());
        templateInfo.setNetworkJitter(template.getNetworkJitter());
        
        return templateInfo;
    }
} 