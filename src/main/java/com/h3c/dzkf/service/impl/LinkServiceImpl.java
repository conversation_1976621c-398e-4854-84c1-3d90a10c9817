package com.h3c.dzkf.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.DeviceCustomInfoMapper;
import com.h3c.dzkf.dao.LinkCustomInfoMapper;
import com.h3c.dzkf.dao.SiteCustomInfoMapper;
import com.h3c.dzkf.entity.DeviceCustomInfo;
import com.h3c.dzkf.entity.LinkCustomInfo;
import com.h3c.dzkf.entity.SiteCustomInfo;
import com.h3c.dzkf.entity.dto.AddLinkRequestDTO;
import com.h3c.dzkf.entity.dto.QueryLinkInfoListBySiteIdResponseDTO;
import com.h3c.dzkf.entity.dto.UpdateLinkRequestDTO;
import com.h3c.dzkf.entity.platform.*;
import com.h3c.dzkf.service.LinkService;
import com.h3c.dzkf.service.PlatformApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 链路服务实现类
 */
@Slf4j
@Service
public class LinkServiceImpl implements LinkService {

    @Autowired
    private LinkCustomInfoMapper linkCustomInfoMapper;

    @Autowired
    private DeviceCustomInfoMapper deviceCustomInfoMapper;

    @Autowired
    private PlatformApiService platformApiService;

    @Autowired
    private SiteCustomInfoMapper siteCustomInfoMapper;



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addLink(AddLinkRequestDTO request, String requestId) {
        log.info("开始处理新增链路请求，请求ID：{}，链路名称：{}", requestId, request.getLinkName());

        // 1. 验证源设备和目标设备是否存在，并获取平台节点ID
        String sourcePlatformNodeId = getPlatformNodeId(request.getSourceDeviceId(), "源设备");

        String targetPlatformNodeId = getPlatformNodeId(request.getTargetDeviceId(), "目标设备");

        // 2. 通过设备接口信息查询接口，获取源端口和目标端口的平台信息
        PlatformGetInterfacesResponseDTO.InterfaceVO sourcePortInfo = getPlatformPortInfo(sourcePlatformNodeId, request.getSourceDevicePort(), "源设备端口");

        PlatformGetInterfacesResponseDTO.InterfaceVO targetPortInfo = getPlatformPortInfo(targetPlatformNodeId, request.getTargetDevicePort(), "目标设备端口");

        // 3. 调用平台新增链路接口
        String platformLinkId = callPlatformAddLink(request, sourcePlatformNodeId, targetPlatformNodeId,
                sourcePortInfo, targetPortInfo, requestId);

        if ("0".equals(platformLinkId)) {
            throw new ServiceException("LINK_ADD_005", "平台新增链路失败",
                    String.format("链路名称：%s", request.getLinkName()));
        }

        // 4. 保存链路信息到定制库
        // 从addresses中获取ifId，如果没有则使用dataId
        Long sourceInterfaceId = getInterfaceId(sourcePortInfo);
        Long targetInterfaceId = getInterfaceId(targetPortInfo);
        LinkCustomInfo linkInfo = buildLinkCustomInfo(request, platformLinkId, sourceInterfaceId, targetInterfaceId);

        int result = linkCustomInfoMapper.insert(linkInfo);
        if (result <= 0) {
            log.error("保存链路自定义信息失败，请求ID：{}", requestId);
            throw new ServiceException("LINK_ADD_006", "保存链路信息失败",
                    String.format("链路名称：%s", request.getLinkName()));
        }

        log.info("新增链路成功，请求ID：{}，平台链路ID：{}", requestId, platformLinkId);
        return Long.parseLong(platformLinkId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLink(UpdateLinkRequestDTO request, String requestId) {
        log.info("开始处理更新链路请求，请求ID：{}，链路ID：{}", requestId, request.getLinkId());

        // 1. 查询定制库中的链路信息（过滤软删除的记录）
        LambdaQueryWrapper<LinkCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LinkCustomInfo::getPlatformLinkId, request.getLinkId());
        queryWrapper.eq(LinkCustomInfo::getIsDeleted, 0);

        LinkCustomInfo linkInfo = linkCustomInfoMapper.selectOne(queryWrapper);
        if (linkInfo == null) {
            log.error("链路不存在或已被删除，链路ID：{}", request.getLinkId());
            throw new ServiceException("LINK_UPDATE_001", "链路不存在或已被删除",
                    String.format("链路ID：%d", request.getLinkId()));
        }

        // 2. 分别处理平台字段和定制字段的更新
        List<String> updatedFields = new ArrayList<>();
        boolean hasUpdates = false;

        // 3. 处理平台字段更新（linkName、linkBandWidth）
        boolean needPlatformUpdate = StrUtil.isNotBlank(request.getLinkName()) || StrUtil.isNotBlank(request.getLinkBandWidth());

        if (needPlatformUpdate) {
            // 获取平台现有链路信息
            PlatformGetLinkResponseDTO.GetLinkVo existingLink = getPlatformLinkInfo(request.getLinkId(), requestId);
            if (existingLink == null) {
                throw new ServiceException("LINK_UPDATE_002", "平台中不存在该链路",
                        String.format("链路ID：%d", request.getLinkId()));
            }

            // 基于现有链路信息构建更新请求
            PlatformUpdateLinkRequestDTO platformUpdateRequest = buildPlatformUpdateRequest(existingLink, request);

            // 调用平台更新接口
            boolean platformUpdateResult = platformApiService.updateLink(platformUpdateRequest);
            if (!platformUpdateResult) {
                throw new ServiceException("LINK_UPDATE_003", "平台更新链路失败",
                        String.format("链路ID：%d", request.getLinkId()));
            }

            // 记录更新的平台字段，并同步更新定制库中的备份字段
            if (StrUtil.isNotBlank(request.getLinkName())) {
                linkInfo.setLinkName(request.getLinkName()); // 同步更新定制库中的linkName作为备份
                updatedFields.add("linkName");
                hasUpdates = true;
            }
            if (StrUtil.isNotBlank(request.getLinkBandWidth())) {
                updatedFields.add("linkBandWidth");
                hasUpdates = true;
            }
        }

        // 4. 处理定制字段更新（linkType）
        if (StrUtil.isNotBlank(request.getLinkType())) {
            linkInfo.setLinkType(request.getLinkType());
            updatedFields.add("linkType");
            hasUpdates = true;
        }

        // 5. 检查是否有字段需要更新
        if (!hasUpdates) {
            throw new ServiceException("LINK_UPDATE_004", "没有需要更新的字段",
                    String.format("链路ID：%d", request.getLinkId()));
        }

        // 6. 更新定制库中的链路信息
        linkInfo.setUpdateTime(new Date());
        int result = linkCustomInfoMapper.updateById(linkInfo);
        if (result <= 0) {
            throw new ServiceException("LINK_UPDATE_005", "更新链路定制信息失败",
                    String.format("链路ID：%d", request.getLinkId()));
        }

        log.info("更新链路成功，请求ID：{}，更新字段：{}", requestId, String.join(",", updatedFields));
    }

    /**
     * 获取平台链路信息
     */
    private PlatformGetLinkResponseDTO.GetLinkVo getPlatformLinkInfo(Long linkId, String requestId) {
        PlatformGetLinkRequestDTO getLinkRequest = new PlatformGetLinkRequestDTO();
        getLinkRequest.setLinkId(linkId);

        PlatformGetLinkResponseDTO platformLinkResponse = platformApiService.getLink(getLinkRequest);
        if (platformLinkResponse == null || !Boolean.TRUE.equals(platformLinkResponse.getSuccessful())
                || platformLinkResponse.getResult() == null || platformLinkResponse.getResult().getRecords() == null
                || platformLinkResponse.getResult().getRecords().isEmpty()) {
            log.error("平台中不存在链路，链路ID：{}", linkId);
            throw new ServiceException("PLATFORM_LINK_001", "平台中不存在链路",
                    String.format("链路ID：%d", linkId));
        }

        // 查找匹配的链路
        for (PlatformGetLinkResponseDTO.GetLinkVo linkVo : platformLinkResponse.getResult().getRecords()) {
            if (linkVo.getLinkId() != null && linkVo.getLinkId().equals(linkId)) {
                return linkVo;
            }
        }

        log.error("平台中不存在链路，链路ID：{}", linkId);
        throw new ServiceException("PLATFORM_LINK_002", "平台中不存在链路",
                String.format("链路ID：%d", linkId));
    }

    /**
     * 基于现有链路信息构建平台更新请求
     */
    private PlatformUpdateLinkRequestDTO buildPlatformUpdateRequest(PlatformGetLinkResponseDTO.GetLinkVo existingLink, UpdateLinkRequestDTO request) {
        PlatformUpdateLinkRequestDTO platformUpdateRequest = new PlatformUpdateLinkRequestDTO();
        
        // 设置必要字段
        platformUpdateRequest.setLinkId(request.getLinkId());
        
        // 设置现有的链路属性（完整回填所有19个字段）
        platformUpdateRequest.setLinkName(existingLink.getLinkName());
        platformUpdateRequest.setLinkNameUserSet(existingLink.getLinkNameUserSet());
        platformUpdateRequest.setAttributeFlags(existingLink.getAttributeFlags());
        platformUpdateRequest.setAvailable(existingLink.getAvailable());
        platformUpdateRequest.setAvailableState(existingLink.getAvailableState());
        platformUpdateRequest.setDestNodeId(existingLink.getDestNodeId());
        platformUpdateRequest.setDestTpId(existingLink.getDestTpId());
        platformUpdateRequest.setSrcNodeId(existingLink.getSrcNodeId());
        platformUpdateRequest.setSrcTpId(existingLink.getSrcTpId());
        platformUpdateRequest.setMetric(existingLink.getMetric());
        platformUpdateRequest.setBandwidth(existingLink.getBandwidth());
        platformUpdateRequest.setResvBandwidth(existingLink.getResvBandwidth());
        if (existingLink.getThreshold() != null && existingLink.getThreshold() > 0) {
            platformUpdateRequest.setThreshold(existingLink.getThreshold());
        }
        platformUpdateRequest.setLinkAttrMode(existingLink.getLinkAttrMode());
        platformUpdateRequest.setNextHopAddress(existingLink.getNextHopAddress());
        platformUpdateRequest.setSrlgIdList(existingLink.getSrlgIdList());
        platformUpdateRequest.setIpMtu(existingLink.getIpMtu());
        platformUpdateRequest.setMtu(existingLink.getMtu());
        
        // 覆盖用户指定要更新的字段
        if (StrUtil.isNotBlank(request.getLinkName())) {
            platformUpdateRequest.setLinkName(request.getLinkName());
            platformUpdateRequest.setLinkNameUserSet(1); // 表示用户手动设置了链路名称
        }
        
        if (StrUtil.isNotBlank(request.getLinkBandWidth())) {
            Long bandwidth = parseBandwidth(request.getLinkBandWidth());
            platformUpdateRequest.setBandwidth(bandwidth);
            platformUpdateRequest.setResvBandwidth(bandwidth);
        }
        
        return platformUpdateRequest;
    }

    @Override
    public List<QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO> queryLinkInfoListBySiteId(Integer siteId, String requestId) {
        log.info("开始查询站点下链路信息，请求ID：{}，站点ID：{}", requestId, siteId);

        // 1. 验证站点是否存在
        LambdaQueryWrapper<SiteCustomInfo> siteQueryWrapper = new LambdaQueryWrapper<>();
        siteQueryWrapper.eq(SiteCustomInfo::getSiteId, siteId)
                .eq(SiteCustomInfo::getIsDeleted, 0);
        SiteCustomInfo siteCustomInfo = siteCustomInfoMapper.selectOne(siteQueryWrapper);
        if (siteCustomInfo == null) {
            log.warn("站点不存在或已被删除，请求ID：{}，站点ID：{}", requestId, siteId);
            throw new ServiceException("LINK_QUERY_001", "站点不存在或已被删除",
                    String.format("站点ID：%d", siteId));
        }

        // 2. 查询站点及其下级站点ID列表
        List<Integer> allSiteIds = getAllSubSiteIds(siteId);
        log.info("获取站点及下级站点ID列表：{}", allSiteIds);

        if (allSiteIds.isEmpty()) {
            log.warn("未找到站点或下级站点，站点ID：{}", siteId);
            throw new ServiceException("LINK_QUERY_002", "未找到站点或下级站点",
                    String.format("站点ID：%d", siteId));
        }

        // 3. 获取站点下所有设备的平台节点ID
        List<Long> allPlatformNodeIds = getAllPlatformNodeIdsBySiteIds(allSiteIds);
        log.info("获取站点下所有设备平台节点ID，总数：{}，节点ID列表：{}", allPlatformNodeIds.size(), allPlatformNodeIds);

        if (allPlatformNodeIds.isEmpty()) {
            log.warn("未找到站点下的任何设备，站点ID：{}", siteId);
            throw new ServiceException("LINK_QUERY_003", "未找到站点下的任何设备",
                    String.format("站点ID：%d", siteId));
        }

        // 4. 调用平台接口查询链路信息
        List<PlatformGetLinkResponseDTO.GetLinkVo> platformLinks = queryPlatformLinksByNodeIds(allPlatformNodeIds, requestId);
        log.info("从平台查询到{}条链路信息", platformLinks.size());

        if (platformLinks.isEmpty()) {
            throw new ServiceException("LINK_QUERY_004", "未从平台获取到任何链路数据",
                    String.format("站点ID：%d", siteId));
        }

        // 5. 处理每个链路，构建完整的链路信息
        List<QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO> linkInfoList = new ArrayList<>();

        for (int i = 0; i < platformLinks.size(); i++) {
            PlatformGetLinkResponseDTO.GetLinkVo platformLink = platformLinks.get(i);
            log.info("处理第{}条链路，链路ID：{}，总数：{}", i + 1, platformLink.getLinkId(), platformLinks.size());

            try {
                QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO = buildLinkInfoFromPlatformData(platformLink, allSiteIds, requestId);
                if (linkInfoDTO != null) {
                    linkInfoList.add(linkInfoDTO);
                    log.debug("链路{}处理成功", platformLink.getLinkId());
                }

            } catch (Exception e) {
                log.error("处理链路{}异常，跳过该链路，请求ID：{}", platformLink.getLinkId(), requestId, e);
                // 对于单个链路处理异常，记录日志但不中断整个查询流程
            }
        }

        log.info("成功处理{}条链路信息，请求ID：{}", linkInfoList.size(), requestId);
        return linkInfoList;
    }

    /**
     * 获取单个设备节点数据
     */
    private PlatformGetNodesResponseDTO.NodeRecord getSingleNodeData(String nodeId) {
        log.debug("获取节点{}的详细信息", nodeId);

        PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO(Long.valueOf(nodeId));

        PlatformGetNodesResponseDTO response = platformApiService.getNodes(request);

        if (response != null && response.getSuccessful() != null && response.getSuccessful()
                && response.getResult() != null && response.getResult().getRecords() != null) {

            for (PlatformGetNodesResponseDTO.NodeRecord nodeRecord : response.getResult().getRecords()) {
                if (nodeRecord.getDataId() != null && nodeRecord.getDataId().toString().equals(nodeId)) {
                    log.debug("成功获取节点{}详细信息", nodeId);
                    return nodeRecord;
                }
            }
        }

        log.error("平台接口未找到节点详细信息，节点ID：{}", nodeId);
        throw new ServiceException("PLATFORM_GET_NODE_001", "平台接口未找到节点详细信息",
                String.format("节点ID：%s", nodeId));
    }

    /**
     * 获取链路总览数据（通过接口ID列表查询，返回所有相关链路）
     */
    private List<PlatformLinkOverViewResponseDTO.LinkOverViewInfo> getLinkOverViewDataByInterfaceIds(List<Long> interfaceIds) {
        if (interfaceIds == null || interfaceIds.isEmpty()) {
            log.warn("接口ID列表为空，无法查询链路总览数据");
            return Collections.emptyList();
        }
        
        // 过滤掉null值
        List<Long> validInterfaceIds = new ArrayList<>();
        for (Long interfaceId : interfaceIds) {
            if (interfaceId != null) {
                validInterfaceIds.add(interfaceId);
            }
        }
        
        if (validInterfaceIds.isEmpty()) {
            log.warn("没有有效的接口ID，无法查询链路总览数据");
            return Collections.emptyList();
        }

        log.debug("使用接口ID列表 {}查询链路总览数据", validInterfaceIds);

        PlatformLinkOverViewRequestDTO request = PlatformLinkOverViewRequestDTO.createRealtimeRequest(1, 100);

        // 使用接口ID列表作为查询条件
        request.setSrcTpName(validInterfaceIds);

        PlatformLinkOverViewResponseDTO response = platformApiService.getLinkOverView(request);

        if (response != null && response.getSuccessful() != null && response.getSuccessful()
                && response.getResult() != null && response.getResult().getLinkOverViewInfoList() != null) {

            log.debug("通过接口ID列表 {}查询到{}条链路总览数据", validInterfaceIds, response.getResult().getLinkOverViewInfoList().size());
            return response.getResult().getLinkOverViewInfoList();
        }

        log.error("平台接口未返回链路总览数据，接口ID列表：{}", validInterfaceIds);
        throw new ServiceException("PLATFORM_LINK_OVERVIEW_001", "平台接口未返回链路总览数据",
                String.format("接口ID列表：%s", validInterfaceIds));
    }

    /**
     * 计算带宽利用率
     */
    private void calculateBandwidthUtilization(
            QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO,
            PlatformGetLinkResponseDTO.GetLinkVo linkData,
            PlatformLinkOverViewResponseDTO.LinkOverViewInfo reverseLinkOverView) {
        
        try {
            // sendBwUsedPercent = sendRate / linkBandWidth
            if (linkInfoDTO.getSendRate() != null && linkInfoDTO.getLinkBandWidth() != null) {
                double sendRate = linkInfoDTO.getSendRate();
                double linkBandWidth = linkInfoDTO.getLinkBandWidth();
                if (linkBandWidth > 0) {
                    double sendPercent = (sendRate / linkBandWidth) * 100;
                    linkInfoDTO.setSendBwUsedPercent(sendPercent);  // 直接设置Double值
                }
            }
            
            // receiveBwUsedPercent = receiveRate / linkBandWidth(仅使用反向链路数据)
            if (linkInfoDTO.getReceiveRate() != null && reverseLinkOverView != null && reverseLinkOverView.getBandwidth() != null) {
                double receiveRate = linkInfoDTO.getReceiveRate();
                // reverseLinkBandWidth平台返回kbps，需要转换为bps进行计算
                double reverseLinkBandWidth = reverseLinkOverView.getBandwidth().doubleValue() * 1000;
                if (reverseLinkBandWidth > 0) {
                    double receivePercent = (receiveRate / reverseLinkBandWidth) * 100;
                    linkInfoDTO.setReceiveBwUsedPercent(receivePercent);  // 直接设置Double值
                }
            }
            
        } catch (Exception e) {
            log.error("计算带宽利用率异常", e);
        }
    }

    /**
     * 获取站点及其所有下级站点ID列表
     */
    private List<Integer> getAllSubSiteIds(Integer siteId) {
        List<Integer> allSiteIds = new ArrayList<>();
        allSiteIds.add(siteId);
        addSubSiteIds(siteId, allSiteIds);
        return allSiteIds;
    }

    /**
     * 根据站点ID列表获取所有设备的平台节点ID
     */
    private List<Long> getAllPlatformNodeIdsBySiteIds(List<Integer> siteIds) {
        List<Long> platformNodeIds = new ArrayList<>();

        if (siteIds == null || siteIds.isEmpty()) {
            return platformNodeIds;
        }

        try {
            // 查询站点下的所有设备
            LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DeviceCustomInfo::getDeviceSiteId, siteIds);
            queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0);
            queryWrapper.isNotNull(DeviceCustomInfo::getPlatformNodeId);
            queryWrapper.select(DeviceCustomInfo::getPlatformNodeId);

            List<DeviceCustomInfo> devices = deviceCustomInfoMapper.selectList(queryWrapper);

            for (DeviceCustomInfo device : devices) {
                String platformNodeIdStr = device.getPlatformNodeId();
                if (platformNodeIdStr != null && !platformNodeIdStr.trim().isEmpty()) {
                    try {
                        Long platformNodeId = Long.valueOf(platformNodeIdStr.trim());
                        if (!platformNodeIds.contains(platformNodeId)) {
                            platformNodeIds.add(platformNodeId);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("设备平台节点ID格式不正确，跳过：{}", platformNodeIdStr);
                    }
                }
            }

            log.info("从{}个站点查询到{}个设备，获取到{}个有效的平台节点ID", siteIds.size(), devices.size(), platformNodeIds.size());

        } catch (Exception e) {
            log.error("查询站点下设备平台节点ID异常，站点ID列表：{}", siteIds, e);
        }

        return platformNodeIds;
    }

    /**
     * 递归添加下级站点ID
     */
    private void addSubSiteIds(Integer parentSiteId, List<Integer> allSiteIds) {
        LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteCustomInfo::getParentSiteId, parentSiteId);
        queryWrapper.eq(SiteCustomInfo::getIsDeleted, 0);
        queryWrapper.select(SiteCustomInfo::getSiteId);

        List<SiteCustomInfo> subSites = siteCustomInfoMapper.selectList(queryWrapper);

        for (SiteCustomInfo subSite : subSites) {
            allSiteIds.add(subSite.getSiteId());
            addSubSiteIds(subSite.getSiteId(), allSiteIds);
        }
    }

    /**
     * 根据设备平台节点ID列表查询平台链路信息
     */
    private List<PlatformGetLinkResponseDTO.GetLinkVo> queryPlatformLinksByNodeIds(List<Long> platformNodeIds, String requestId) {
        List<PlatformGetLinkResponseDTO.GetLinkVo> allLinks = new ArrayList<>();

        if (platformNodeIds == null || platformNodeIds.isEmpty()) {
            return allLinks;
        }

        log.info("调用平台接口查询链路信息，设备节点ID数量：{}", platformNodeIds.size());

        // 使用合理的分页大小
        final long pageSize = 50L;
        long currentPage = 1L;
        boolean hasMoreData = true;

        while (hasMoreData) {
            // 构建平台查询请求
            PlatformGetLinkRequestDTO request = new PlatformGetLinkRequestDTO();
            request.setSrcNodeIdList(platformNodeIds);
            request.setDestNodeIdList(platformNodeIds);
            request.setPageNum(currentPage);
            request.setPageSize(pageSize);

            log.debug("查询第{}页链路信息，页面大小：{}", currentPage, pageSize);

            // 调用平台接口
            PlatformGetLinkResponseDTO response = platformApiService.getLink(request);

            if (response == null || !Boolean.TRUE.equals(response.getSuccessful())) {
                log.error("平台接口查询链路信息失败，请求ID：{}，页码：{}", requestId, currentPage);
                throw new ServiceException("PLATFORM_GET_LINKS_001", "平台接口查询链路信息失败",
                        String.format("请求ID：%s，页码：%d", requestId, currentPage));
            }

            if (response.getResult() == null || response.getResult().getRecords() == null) {
                log.error("平台接口返回链路信息为空，请求ID：{}，页码：{}", requestId, currentPage);
                throw new ServiceException("PLATFORM_GET_LINKS_002", "平台接口返回链路信息为空",
                        String.format("请求ID：%s，页码：%d", requestId, currentPage));
            }

            List<PlatformGetLinkResponseDTO.GetLinkVo> currentPageLinks = response.getResult().getRecords();
            allLinks.addAll(currentPageLinks);

            log.debug("第{}页查询成功，获取{}条链路信息，累计{}条",
                    currentPage, currentPageLinks.size(), allLinks.size());

            // 判断是否还有更多数据
            Long totalPage = response.getResult().getTotalPage();
            Long totalItem = response.getResult().getTotalItem();

            if (totalPage != null && currentPage >= totalPage) {
                hasMoreData = false;
                log.info("已查询完所有页面，总页数：{}，总记录数：{}", totalPage, totalItem);
            } else if (currentPageLinks.isEmpty()) {
                // 如果当前页没有数据，也停止查询
                hasMoreData = false;
                log.info("当前页无数据，停止分页查询");
            } else {
                currentPage++;
            }

            // 添加短暂延迟，避免对平台造成过大压力
            if (hasMoreData && currentPage > 1) {
                try {
                    Thread.sleep(100); // 100ms延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("分页查询延迟被中断");
                    break;
                }
            }
        }

        log.info("平台链路查询完成，总共获取{}条链路信息", allLinks.size());

        return allLinks;
    }

    /**
     * 从平台数据构建链路信息
     */
    private QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO buildLinkInfoFromPlatformData(
            PlatformGetLinkResponseDTO.GetLinkVo platformLink, List<Integer> siteIds, String requestId) {

        try {
            log.debug("开始构建链路信息，链路ID：{}", platformLink.getLinkId());

            QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO = new QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO();

            // 1. 设置基本链路信息（来自平台）
            linkInfoDTO.setLinkId(platformLink.getLinkId().toString());
            linkInfoDTO.setLinkName(platformLink.getLinkName() != null ? platformLink.getLinkName() : "链路_" + platformLink.getLinkId());
            linkInfoDTO.setLinkStatus(translateLinkStatusFromPlatform(platformLink.getLinkStatus()));

            // 设置带宽信息（平台返回的是kbps，需要转换为bps）
            if (platformLink.getBandwidth() != null) {
                linkInfoDTO.setLinkBandWidth(platformLink.getBandwidth().doubleValue() * 1000);
            }

            // 2. 查询并设置源设备信息
            if (platformLink.getSrcNodeId() != null) {
                setDeviceInfoFromPlatformNodeId(linkInfoDTO, platformLink.getSrcNodeId(), true, siteIds);
            }

            // 3. 查询并设置目标设备信息
            if (platformLink.getDestNodeId() != null) {
                setDeviceInfoFromPlatformNodeId(linkInfoDTO, platformLink.getDestNodeId(), false, siteIds);
            }

            // 4. 根据平台linkId查询数据库中的定制信息
            LinkCustomInfo linkCustomInfo = getLinkCustomInfoByPlatformLinkId(platformLink.getLinkId().toString());
            if (linkCustomInfo != null) {
                // 如果数据库中有定制信息，使用定制信息
                linkInfoDTO.setLinkType(linkCustomInfo.getLinkType() != null ? linkCustomInfo.getLinkType() : "");
                linkInfoDTO.setLinkLevel(linkCustomInfo.getLinkLevel() != null ? linkCustomInfo.getLinkLevel() : "");

                // 如果数据库中有更详细的设备信息，优先使用
                if (linkCustomInfo.getSourceDeviceId() != null) {
                    linkInfoDTO.setSourceDeviceId(linkCustomInfo.getSourceDeviceId());
                }
                if (linkCustomInfo.getTargetDeviceId() != null) {
                    linkInfoDTO.setTargetDeviceId(linkCustomInfo.getTargetDeviceId());
                }
                if (linkCustomInfo.getSourceDevicePort() != null) {
                    linkInfoDTO.setSourceDevicePort(linkCustomInfo.getSourceDevicePort());
                }
                if (linkCustomInfo.getTargetDevicePort() != null) {
                    linkInfoDTO.setTargetDevicePort(linkCustomInfo.getTargetDevicePort());
                }

                // 设置站点ID（优先使用数据库中的站点信息）
                if (linkCustomInfo.getSiteId() != null) {
                    linkInfoDTO.setSiteId(linkCustomInfo.getSiteId());
                }
            } else {
                // 如果数据库中没有定制信息，设置为空串
                linkInfoDTO.setLinkType("");
                linkInfoDTO.setLinkLevel("");
            }

            // 5. 设置其他默认值
            linkInfoDTO.setCarrierName("");
            linkInfoDTO.setUpBandWidth(null);
            linkInfoDTO.setDownBandWidth(null);
            linkInfoDTO.setIsEncrypt(0);

            // 6. 获取性能数据
            setPlatformLinkPerformanceData(linkInfoDTO, platformLink);

            return linkInfoDTO;

        } catch (Exception e) {
            log.error("构建链路信息异常，链路ID：{}，请求ID：{}", platformLink.getLinkId(), requestId, e);
            return null;
        }
    }

    /**
     * 根据平台节点ID设置设备信息
     */
    private void setDeviceInfoFromPlatformNodeId(QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO,
                                                 Long platformNodeId, boolean isSource, List<Integer> siteIds) {

        try {
            // 1. 查询设备定制信息获取设备ID和端口信息
            DeviceCustomInfo deviceInfo = getDeviceInfoByPlatformNodeId(platformNodeId.toString());
            if (deviceInfo != null) {
                // 检查设备是否属于查询的站点范围
                if (siteIds.contains(deviceInfo.getDeviceSiteId())) {
                    if (isSource) {
                        linkInfoDTO.setSourceDeviceId(deviceInfo.getDeviceId().intValue());
                        linkInfoDTO.setSiteId(deviceInfo.getDeviceSiteId());
                    } else {
                        linkInfoDTO.setTargetDeviceId(deviceInfo.getDeviceId().intValue());
                    }
                }
            }

            // 2. 查询平台设备信息获取设备名称和IP
            try {
                PlatformGetNodesResponseDTO.NodeRecord nodeRecord = getSingleNodeData(platformNodeId.toString());
                if (isSource) {
                    linkInfoDTO.setSourceDeviceName(nodeRecord.getNodeName());
                    linkInfoDTO.setSourceDeviceIp(nodeRecord.getManageIp());
                } else {
                    linkInfoDTO.setTargetDeviceName(nodeRecord.getNodeName());
                    linkInfoDTO.setTargetDeviceIp(nodeRecord.getManageIp());
                }
            } catch (ServiceException e) {
                log.warn("获取平台节点详细信息失败，跳过设备名称和IP设置，平台节点ID：{}，错误：{}",
                        platformNodeId, e.getMessage());
            }

        } catch (Exception e) {
            log.error("设置设备信息异常，平台节点ID：{}", platformNodeId, e);
        }
    }

    /**
     * 根据平台节点ID查询设备定制信息
     */
    private DeviceCustomInfo getDeviceInfoByPlatformNodeId(String platformNodeId) {
        try {
            LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DeviceCustomInfo::getPlatformNodeId, platformNodeId);
            queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0);

            return deviceCustomInfoMapper.selectOne(queryWrapper);

        } catch (Exception e) {
            log.error("查询设备定制信息异常，平台节点ID：{}", platformNodeId, e);
            return null;
        }
    }

    /**
     * 根据平台链路ID查询链路定制信息
     */
    private LinkCustomInfo getLinkCustomInfoByPlatformLinkId(String platformLinkId) {
        try {
            LambdaQueryWrapper<LinkCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LinkCustomInfo::getPlatformLinkId, platformLinkId);
            queryWrapper.eq(LinkCustomInfo::getIsDeleted, 0);

            LinkCustomInfo linkCustomInfo = linkCustomInfoMapper.selectOne(queryWrapper);

            if (linkCustomInfo != null) {
                log.debug("找到链路定制信息，平台链路ID：{}，链路类型：{}，链路级别：{}",
                        platformLinkId, linkCustomInfo.getLinkType(), linkCustomInfo.getLinkLevel());
            } else {
                log.debug("未找到链路定制信息，平台链路ID：{}", platformLinkId);
            }

            return linkCustomInfo;

        } catch (Exception e) {
            log.error("查询链路定制信息异常，平台链路ID：{}", platformLinkId, e);
            return null;
        }
    }

    /**
     * 设置平台链路性能数据
     */
    private void setPlatformLinkPerformanceData(QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO,
                                                PlatformGetLinkResponseDTO.GetLinkVo platformLink) {

        try {
            log.debug("获取链路{}的性能数据", platformLink.getLinkId());

            // 1. 构建接口ID列表（使用平台链路的源和目标接口ID）
            List<Long> interfaceIds = new ArrayList<>();
            if (platformLink.getSrcTpId() != null) {
                interfaceIds.add(platformLink.getSrcTpId());
            }
            if (platformLink.getDestTpId() != null) {
                interfaceIds.add(platformLink.getDestTpId());
            }

            // 如果没有接口ID，设置默认值并返回
            if (interfaceIds.isEmpty()) {
                setDefaultPerformanceData(linkInfoDTO);
                log.debug("链路{}没有接口ID信息，设置默认性能数据", platformLink.getLinkId());
                return;
            }

            // 2. 查询链路总览数据
            List<PlatformLinkOverViewResponseDTO.LinkOverViewInfo> allLinkList;
            try {
                allLinkList = getLinkOverViewDataByInterfaceIds(interfaceIds);
            } catch (ServiceException e) {
                log.warn("获取链路总览数据失败，设置默认性能数据，链路ID：{}，错误：{}",
                        platformLink.getLinkId(), e.getMessage());
                setDefaultPerformanceData(linkInfoDTO);
                return;
            }

            // 3. 从查询结果中区分正向和反向链路
            PlatformLinkOverViewResponseDTO.LinkOverViewInfo forwardLinkInfo = null;
            PlatformLinkOverViewResponseDTO.LinkOverViewInfo reverseLinkInfo = null;

            for (PlatformLinkOverViewResponseDTO.LinkOverViewInfo linkInfo : allLinkList) {
                if (platformLink.getLinkId().toString().equals(linkInfo.getLinkId())) {
                    // 当前链路ID匹配的是正向链路
                    forwardLinkInfo = linkInfo;
                } else {
                    // 其他的就是反向链路
                    reverseLinkInfo = linkInfo;
                }
            }

            // 4. 设置性能数据（delay、jitter、loss都从当前链路获取）
            if (forwardLinkInfo != null) {
                // delay、jitter、loss：发送和接收都使用当前链路的数据
                if (forwardLinkInfo.getDelay() != null && forwardLinkInfo.getDelay() >= 0) {
                    linkInfoDTO.setSendDelay(forwardLinkInfo.getDelay());
                    linkInfoDTO.setReceiveDelay(forwardLinkInfo.getDelay());
                }
                if (forwardLinkInfo.getJitter() != null && forwardLinkInfo.getJitter() >= 0) {
                    linkInfoDTO.setSendJitter(forwardLinkInfo.getJitter().doubleValue());
                    linkInfoDTO.setReceiveJitter(forwardLinkInfo.getJitter().doubleValue());
                }
                if (forwardLinkInfo.getPacketLossRate() != null && forwardLinkInfo.getPacketLossRate() >= 0) {
                    linkInfoDTO.setSendLoss(forwardLinkInfo.getPacketLossRate());
                    linkInfoDTO.setReceiveLoss(forwardLinkInfo.getPacketLossRate());
                }

                // sendRate：使用当前链路带宽，平台返回kbps，转换为bps
                if (forwardLinkInfo.getBandwidth() != null) {
                    linkInfoDTO.setSendRate(forwardLinkInfo.getBandwidth().doubleValue() * 1000);
                }
            } else {
                // 如果没有找到正向链路信息，设置默认值
                setDefaultPerformanceData(linkInfoDTO);
            }

            // 5. 设置receiveRate（仅从反向链路获取，没有则保持为空），平台返回kbps，转换为bps
            if (reverseLinkInfo != null && reverseLinkInfo.getBandwidth() != null) {
                linkInfoDTO.setReceiveRate(reverseLinkInfo.getBandwidth().doubleValue() * 1000);
            }

            // 6. 计算带宽利用率
            calculateBandwidthUtilization(linkInfoDTO, platformLink, reverseLinkInfo);

        } catch (Exception e) {
            log.error("设置链路{}性能数据异常", platformLink.getLinkId(), e);
            // 异常时设置默认值
            setDefaultPerformanceData(linkInfoDTO);
        }
    }

    /**
     * 设置默认性能数据
     */
    private void setDefaultPerformanceData(QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO linkInfoDTO) {
        linkInfoDTO.setSendDelay(null);
        linkInfoDTO.setReceiveDelay(null);
        linkInfoDTO.setSendJitter(null);
        linkInfoDTO.setReceiveJitter(null);
        linkInfoDTO.setSendLoss(null);
        linkInfoDTO.setReceiveLoss(null);
        linkInfoDTO.setSendRate(null);
        linkInfoDTO.setReceiveRate(null);
        linkInfoDTO.setSendBwUsedPercent(null);
        linkInfoDTO.setReceiveBwUsedPercent(null);
    }

    /**
     * 从平台接口翻译链路状态
     */
    private String translateLinkStatusFromPlatform(Integer linkStatus) {
        if (linkStatus == null) {
            return "未知";
        }

        switch (linkStatus) {
            case 0:
                return "离线";
            case 1:
                return "在线";
            default:
                return "未知";
        }
    }

    /**
     * 根据设备ID获取平台节点ID
     */
    private String getPlatformNodeId(Integer deviceId, String deviceType) {
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
        queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0);

        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);
        if (deviceInfo == null) {
            log.error("{}不存在或已被删除，设备ID：{}", deviceType, deviceId);
            throw new ServiceException("DEVICE_NODE_001", deviceType + "不存在或已被删除",
                    String.format("设备ID：%d", deviceId));
        }

        String platformNodeId = deviceInfo.getPlatformNodeId();
        if (platformNodeId == null || platformNodeId.trim().isEmpty()) {
            log.error("{}未关联平台节点ID，设备ID：{}", deviceType, deviceId);
            throw new ServiceException("DEVICE_NODE_002", deviceType + "未关联平台节点ID",
                    String.format("设备ID：%d", deviceId));
        }

        return platformNodeId;
    }

    /**
     * 通过平台接口查询设备端口信息
     */
    private PlatformGetInterfacesResponseDTO.InterfaceVO getPlatformPortInfo(String platformNodeId, String portName, String portType) {
        PlatformGetInterfacesRequestDTO request = new PlatformGetInterfacesRequestDTO();
        request.setNodeId(Long.parseLong(platformNodeId));
        request.setPageNum(1L);
        request.setPageSize(1000L);

        PlatformGetInterfacesResponseDTO response = platformApiService.getInterfaces(request);

        if (response != null && response.getSuccessful() != null && response.getSuccessful()
                && response.getResult() != null && response.getResult().getRecords() != null) {

            for (PlatformGetInterfacesResponseDTO.InterfaceVO interfaceVO : response.getResult().getRecords()) {
                if (interfaceVO.getIfName() != null && interfaceVO.getIfName().equalsIgnoreCase(portName)) {
                    return interfaceVO;
                }
            }
        }

        log.error("未找到匹配的{}，用户端口名称：{}，平台节点ID：{}", portType, portName, platformNodeId);
        throw new ServiceException("PLATFORM_PORT_001", "未找到匹配的" + portType,
                String.format("端口名称：%s，平台节点ID：%s", portName, platformNodeId));
    }

    /**
     * 从InterfaceVO中获取接口ID（优先从addresses中获取ifId，否则使用dataId）
     * 优先使用IPv6地址对应的ifId，如果没有IPv6则使用IPv4
     */
    private Long getInterfaceId(PlatformGetInterfacesResponseDTO.InterfaceVO interfaceVO) {
        if (interfaceVO.getAddresses() != null && !interfaceVO.getAddresses().isEmpty()) {
            // 先找IPv6地址 (ipType = 2)
            for (PlatformGetInterfacesResponseDTO.InterfaceAddressVO addressVO : interfaceVO.getAddresses()) {
                if (addressVO.getIpType() != null && addressVO.getIpType() == 2) {
                    return addressVO.getIfId();
                }
            }
            // 没有IPv6，找IPv4地址 (ipType = 1)
            for (PlatformGetInterfacesResponseDTO.InterfaceAddressVO addressVO : interfaceVO.getAddresses()) {
                if (addressVO.getIpType() != null && addressVO.getIpType() == 1) {
                    return addressVO.getIfId();
                }
            }
            // 如果都没有指定ipType，使用第一条记录的ifId
            return interfaceVO.getAddresses().get(0).getIfId();
        }
        return interfaceVO.getDataId();
    }

    /**
     * 从InterfaceVO中获取接口地址
     * 优先获取IPv6地址，如果没有IPv6则获取IPv4地址
     */
    private String getInterfaceAddress(PlatformGetInterfacesResponseDTO.InterfaceVO interfaceVO) {
        if (interfaceVO.getAddresses() != null && !interfaceVO.getAddresses().isEmpty()) {
            // 先找IPv6地址 (ipType = 2)
            for (PlatformGetInterfacesResponseDTO.InterfaceAddressVO addressVO : interfaceVO.getAddresses()) {
                if (addressVO.getIpType() != null && addressVO.getIpType() == 2) {
                    return addressVO.getAddress();
                }
            }
            // 没有IPv6，找IPv4地址 (ipType = 1)
            for (PlatformGetInterfacesResponseDTO.InterfaceAddressVO addressVO : interfaceVO.getAddresses()) {
                if (addressVO.getIpType() != null && addressVO.getIpType() == 1) {
                    return addressVO.getAddress();
                }
            }
            // 如果都没有指定ipType，使用第一条记录的address
            return interfaceVO.getAddresses().get(0).getAddress();
        }
        return null;
    }

    /**
     * 调用平台新增链路接口
     */
    private String callPlatformAddLink(AddLinkRequestDTO request, String sourcePlatformNodeId,
                                       String targetPlatformNodeId, PlatformGetInterfacesResponseDTO.InterfaceVO sourcePortInfo,
            PlatformGetInterfacesResponseDTO.InterfaceVO targetPortInfo, String requestId) {

        PlatformAddLinkRequestDTO platformRequest = new PlatformAddLinkRequestDTO();

        // 设置必选参数
        platformRequest.setLinkNameUserSet(1);
        platformRequest.setSrcNodeId(Long.parseLong(sourcePlatformNodeId));
        platformRequest.setSrcTpId(getInterfaceId(sourcePortInfo));
        platformRequest.setDestNodeId(Long.parseLong(targetPlatformNodeId));
        platformRequest.setDestTpId(getInterfaceId(targetPortInfo));
        platformRequest.setLinkTypeInt(100);
        platformRequest.setLinkAttrMode("asyn");
        platformRequest.setMetric(1L);

        // 解析和设置带宽
        Long bandwidth = parseBandwidth(request.getLinkBandWidth());
        platformRequest.setBandwidth(bandwidth);
        platformRequest.setResvBandwidth(bandwidth);

        // 设置可选参数
        platformRequest.setLinkName(request.getLinkName());
        // 使用从接口地址中获取的address，如果为空则使用请求中的IP
        String sourceAddress = getInterfaceAddress(sourcePortInfo);
        String targetAddress = getInterfaceAddress(targetPortInfo);
        platformRequest.setSrcAddress(sourceAddress != null ? sourceAddress : request.getSourceDeviceIp());
        platformRequest.setDestAddress(targetAddress != null ? targetAddress : request.getTargetDeviceIp());
        platformRequest.setAvailable(100);
        platformRequest.setAvailableState(1);

        String platformLinkId = platformApiService.addLink(platformRequest);
        log.info("平台新增链路成功，请求ID：{}，平台链路ID：{}", requestId, platformLinkId);
        return platformLinkId;
    }

    /**
     * 解析带宽字符串为数值（单位：kbps）
     */
    private Long parseBandwidth(String bandwidthStr) {
        if (bandwidthStr == null || bandwidthStr.trim().isEmpty()) {
            throw new ServiceException("BANDWIDTH_PARSE_001", "带宽不能为空",
                    "带宽字符串为null或空");
        }

        Pattern pattern = Pattern.compile("^(\\d+(?:\\.\\d+)?)\\s*(bps|Kbps|Mbps|Gbps)$", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(bandwidthStr.trim());

        if (!matcher.matches()) {
            throw new ServiceException("BANDWIDTH_PARSE_002", "带宽格式不正确，应为：数字+单位(bps/Kbps/Mbps/Gbps)，如：1000Mbps",
                    String.format("输入的带宽字符串：%s", bandwidthStr));
        }

        double value = Double.parseDouble(matcher.group(1));
        String unit = matcher.group(2).toLowerCase();

        long bandwidth;
        switch (unit) {
            case "bps":
                bandwidth = (long) (value / 1000);
                break;
            case "kbps":
                bandwidth = (long) value;
                break;
            case "mbps":
                bandwidth = (long) (value * 1000);
                break;
            case "gbps":
                bandwidth = (long) (value * 1000 * 1000);
                break;
            default:
                throw new ServiceException("BANDWIDTH_PARSE_003", "不支持的带宽单位：" + unit,
                        String.format("输入的带宽字符串：%s", bandwidthStr));
        }

        return bandwidth;
    }

    /**
     * 构建链路自定义信息对象
     */
    private LinkCustomInfo buildLinkCustomInfo(AddLinkRequestDTO request, String platformLinkId, 
            Long sourceInterfaceId, Long targetInterfaceId) {
        
        LinkCustomInfo linkInfo = new LinkCustomInfo();
        
        linkInfo.setPlatformLinkId(platformLinkId);
        
        // 设置站点ID（以源设备的站点为准）
        Integer siteId = getDeviceSiteId(request.getSourceDeviceId());
        linkInfo.setSiteId(siteId);
        
        // 设置工行定制字段
        linkInfo.setLinkType(request.getLinkType());
        linkInfo.setLinkLevel(request.getLinkLevel());
        linkInfo.setLinkStatus(request.getLinkStatus());
        linkInfo.setLinkName(request.getLinkName()); // 保存链路名称到定制库作为备份
        
        // 设置源设备信息
        linkInfo.setSourceDeviceId(request.getSourceDeviceId());
        linkInfo.setSourceDeviceName(request.getSourceDeviceName());
        linkInfo.setSourceDevicePort(request.getSourceDevicePort());
        linkInfo.setSourceInterfaceId(sourceInterfaceId);
        
        // 设置目标设备信息
        linkInfo.setTargetDeviceId(request.getTargetDeviceId());
        linkInfo.setTargetDeviceName(request.getTargetDeviceName());
        linkInfo.setTargetDevicePort(request.getTargetDevicePort());
        linkInfo.setTargetInterfaceId(targetInterfaceId);
        
        // 设置审计字段
        linkInfo.setCreateTime(new Date());
        linkInfo.setUpdateTime(new Date());
        linkInfo.setIsDeleted(0);
        
        return linkInfo;
    }

    /**
     * 根据设备ID获取设备所属的站点ID
     */
    private Integer getDeviceSiteId(Integer deviceId) {
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
        queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0);
        queryWrapper.select(DeviceCustomInfo::getDeviceSiteId);
        
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);
        if (deviceInfo != null) {
            return deviceInfo.getDeviceSiteId();
        } else {
            log.warn("设备不存在、已被删除或未设置站点ID，设备ID：{}", deviceId);
            return null;
        }
    }
} 