package com.h3c.dzkf.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.constants.ScheduleConstants;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.*;
import com.h3c.dzkf.entity.*;
import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.entity.platform.*;
import com.h3c.dzkf.service.PlatformApiService;
import com.h3c.dzkf.service.ScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 调度策略服务实现类
 */
@Slf4j
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Autowired
    private ScheduleCustomInfoMapper scheduleCustomInfoMapper;

    @Autowired
    private AppGroupCustomInfoMapper appGroupCustomInfoMapper;

    @Autowired
    private AppCustomInfoMapper appCustomInfoMapper;

    @Autowired
    private TeGroupCustomInfoMapper teGroupCustomInfoMapper;

    @Autowired
    private TeTunnelTemplateCustomInfoMapper teTunnelTemplateCustomInfoMapper;

    @Autowired
    private LanCustomInfoMapper lanCustomInfoMapper;

    @Autowired
    private DeviceCustomInfoMapper deviceCustomInfoMapper;

    @Autowired
    private ScheduleClassifierInfoMapper scheduleClassifierInfoMapper;

    @Autowired
    private ScheduleBehaviorInfoMapper scheduleBehaviorInfoMapper;

    @Autowired
    private SchedulePolicyInfoMapper schedulePolicyInfoMapper;

    @Autowired
    private PlatformApiService platformApiService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSchedule(AddScheduleRequestDTO request, String requestId) {
        log.info("开始处理新增调度策略请求，请求ID：{}，调度策略ID：{}", requestId, request.getAppScheduleId());

        // 1. 参数验证
        String validationError = validateRequest(request);
        if (validationError != null) {
            throw new ServiceException("VALIDATION_ERROR", validationError, "调度策略ID：" + request.getAppScheduleId());
        }

        // 2. 检查调度策略是否已存在
        if (scheduleExists(request.getAppScheduleId())) {
            throw new ServiceException("VALIDATION_ERROR", "调度策略ID已存在", "调度策略ID：" + request.getAppScheduleId());
        }

        // 3. 流分类处理
        Integer classifierId = processClassifier(request);

        // 4. 流行为处理
        Integer behaviorId = processBehavior(request);

        // 5. 流策略处理
        List<DevicePolicyInfoDTO> devicePolicyList = processPolicy(request, classifierId, behaviorId);

        // 6. 策略部署
        boolean deploySuccess = deployPolicies(request, devicePolicyList);
        if (!deploySuccess) {
            log.warn("策略部署失败，但不影响主流程，请求ID：{}", requestId);
        }

        // 7. 保存调度策略信息到数据库
        Map<String, Integer> policyInfo = devicePolicyList.stream()
                .collect(Collectors.toMap(DevicePolicyInfoDTO::getDeviceIp, DevicePolicyInfoDTO::getPolicyId));
        saveScheduleInfo(request, classifierId,
                request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX,
                behaviorId,
                request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX,
                policyInfo);

        // 8. 保存流分类、流行为、流策略详细信息到独立表
        saveScheduleDetailInfo(request, classifierId,
                request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX,
                behaviorId,
                request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX,
                devicePolicyList);

        log.info("新增调度策略成功，请求ID：{}，调度策略ID：{}", requestId, request.getAppScheduleId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifySchedule(ModifyScheduleRequestDTO request, String requestId) {
        log.info("开始处理修改调度策略请求，请求ID：{}，调度策略ID：{}", requestId, request.getAppScheduleId());

        // 1. 检查调度策略是否存在
        LambdaQueryWrapper<ScheduleCustomInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleCustomInfo::getAppScheduleId, request.getAppScheduleId());
        ScheduleCustomInfo existingSchedule = scheduleCustomInfoMapper.selectOne(query);
        if (existingSchedule == null) {
            throw new ServiceException("VALIDATION_ERROR", "调度策略不存在", "调度策略ID：" + request.getAppScheduleId());
        }

        // 2. 修改流分类
        modifyClassifier(request);

        // 3. 修改流行为
        modifyBehavior(request);

        // 4. 修改流策略
        modifyPolicies(request);

        // 5. 更新调度策略主记录
        existingSchedule.setAppScheduleName(request.getAppScheduleName());
        existingSchedule.setAppGroupName(request.getAppGroupName());
        existingSchedule.setDrainageType(request.getDrainageType());
        existingSchedule.setNetworkIds(JSON.toJSONString(request.getNetworkIds()));
        existingSchedule.setVpnId(request.getVpnId());

        // 注意：调度策略名称变化时，不再同步修改流分类和流行为名称

        scheduleCustomInfoMapper.updateById(existingSchedule);

        log.info("修改调度策略成功，请求ID：{}，调度策略ID：{}", requestId, request.getAppScheduleId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSchedule(DeleteScheduleRequestDTO request, String requestId) {
        log.info("开始处理删除调度策略请求，请求ID：{}，调度策略ID：{}", requestId, request.getAppScheduleId());

        // 1. 检查调度策略是否存在
        LambdaQueryWrapper<ScheduleCustomInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleCustomInfo::getAppScheduleId, request.getAppScheduleId());
        ScheduleCustomInfo scheduleInfo = scheduleCustomInfoMapper.selectOne(query);
        if (scheduleInfo == null) {
            throw new ServiceException("VALIDATION_ERROR", "调度策略不存在", "调度策略ID：" + request.getAppScheduleId());
        }

        // 2. 删除流策略
        deletePolicies(request.getAppScheduleId());

        // 休眠1秒，等待流策略异步删除生效
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("SYSTEM_ERROR", "删除流策略等待过程被中断", "调度策略ID：" + request.getAppScheduleId(), e);
        }

        // 3. 删除流行为
        deleteBehavior(request.getAppScheduleId());

        // 4. 删除流分类
        deleteClassifier(request.getAppScheduleId());

        // 5. 软删除调度策略主记录
        scheduleCustomInfoMapper.deleteById(scheduleInfo.getId());

        log.info("删除调度策略成功，请求ID：{}，调度策略ID：{}", requestId, request.getAppScheduleId());
    }

    /**
     * 验证请求参数
     */
    private String validateRequest(AddScheduleRequestDTO request) {
        // 注意：应用组存在性检查已在getAppsByGroupName方法中处理

        // 检查隧道组是否存在
        for (Long networkId : request.getNetworkIds()) {
            LambdaQueryWrapper<TeGroupCustomInfo> teGroupQuery = new LambdaQueryWrapper<>();
            teGroupQuery.eq(TeGroupCustomInfo::getTeGroupId, networkId);
            TeGroupCustomInfo teGroup = teGroupCustomInfoMapper.selectOne(teGroupQuery);
            if (teGroup == null) {
                log.error("隧道组不存在：{}", networkId);
                return "隧道组不存在：" + networkId;
            }
        }

        return null; // 验证通过返回null
    }

    /**
     * 检查调度策略是否已存在
     */
    private boolean scheduleExists(Long appScheduleId) {
        LambdaQueryWrapper<ScheduleCustomInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleCustomInfo::getAppScheduleId, appScheduleId);
        return scheduleCustomInfoMapper.selectCount(query) > 0;
    }

    /**
     * 处理流分类
     */
    private Integer processClassifier(AddScheduleRequestDTO request) {
        String businessContext = "应用组：" + request.getAppGroupName() + "，调度策略：" + request.getAppScheduleName();

        log.info("开始处理流分类，应用组名称：{}", request.getAppGroupName());

        // 1. 根据应用组名称获取应用列表
        List<AppCustomInfo> apps = getAppsByGroupName(request.getAppGroupName());

        // 2. 生成匹配规则
        List<PlatformQosClassifierRequestDTO.MatchRule> matchRules = generateMatchRules(apps);

        // 3. 构建流分类请求
        PlatformQosClassifierRequestDTO classifierRequest = new PlatformQosClassifierRequestDTO();
        classifierRequest.setClassifierName(request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX);
        classifierRequest.setDescription("");
        classifierRequest.setLogic(ScheduleConstants.Classifier.LOGIC_AND);
        classifierRequest.setMatchList(matchRules);

        // 4. 调用平台接口新增流分类
        platformApiService.addQosClassifier(classifierRequest);

        // 5. 查询流分类ID
        Integer classifierId = getClassifierIdByName(request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX);
        if (classifierId == null) {
            throw new ServiceException("CLASSIFIER_ERROR",
                    "流分类创建成功但查询流分类ID失败，流分类名称：" +
                            request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX, businessContext);
        }

        return classifierId;
    }

    /**
     * 根据应用组名称获取应用列表
     */
    private List<AppCustomInfo> getAppsByGroupName(String appGroupName) {
        // 先获取应用组信息
        LambdaQueryWrapper<AppGroupCustomInfo> appGroupQuery = new LambdaQueryWrapper<>();
        appGroupQuery.eq(AppGroupCustomInfo::getAppGroupName, appGroupName);
        AppGroupCustomInfo appGroup = appGroupCustomInfoMapper.selectOne(appGroupQuery);

        if (appGroup == null) {
            log.error("未找到应用组：{}", appGroupName);
            throw new ServiceException("VALIDATION_ERROR", "未找到应用组：" + appGroupName);
        }

        // 根据应用组的app_group_id获取应用列表
        LambdaQueryWrapper<AppCustomInfo> appQuery = new LambdaQueryWrapper<>();
        appQuery.eq(AppCustomInfo::getAppGroupId, appGroup.getAppGroupId());
        List<AppCustomInfo> apps = appCustomInfoMapper.selectList(appQuery);
        if (apps.isEmpty()) {
            log.error("应用组下没有找到应用：{}", appGroupName);
            throw new ServiceException("CLASSIFIER_ERROR", "应用组下没有找到应用：" + appGroupName);
        }

        log.info("应用组{}包含{}个应用", appGroupName, apps.size());
        return apps;
    }

    /**
     * 生成匹配规则
     */
    private List<PlatformQosClassifierRequestDTO.MatchRule> generateMatchRules(List<AppCustomInfo> apps) {
        List<PlatformQosClassifierRequestDTO.MatchRule> matchRules = new ArrayList<>();

        int indexNum = 0;
        for (AppCustomInfo app : apps) {
            // 解析应用的ACL ID列表
            List<Integer> aclIds = parseAclIdList(app.getAclIdList());

            // 为每个ACL ID生成匹配规则
            for (Integer aclId : aclIds) {
                String aclName = getAclNameById(aclId);
                if (aclName != null) {
                    PlatformQosClassifierRequestDTO.MatchRule matchRule = new PlatformQosClassifierRequestDTO.MatchRule();
                    matchRule.setStrValue1(aclName);
                    matchRule.setIntValue2(ScheduleConstants.Classifier.INT_VALUE2_DEFAULT);
//                    matchRule.setIntValue1(ScheduleConstants.Classifier.INT_VALUE1_DEFAULT);
//                    matchRule.setIntValue2(ScheduleConstants.Classifier.INT_VALUE2_DEFAULT);
                    matchRule.setIndexNum(indexNum++);
                    matchRule.setSelectType(ScheduleConstants.Classifier.SELECT_TYPE_DEFAULT);
                    matchRule.setMatchRelation(ScheduleConstants.Classifier.MATCH_RELATION_DEFAULT);

                    matchRules.add(matchRule);
                }
            }
        }

        return matchRules;
    }

    /**
     * 解析ACL ID列表
     */
    private List<Integer> parseAclIdList(String aclIdList) {
        if (aclIdList != null && !aclIdList.trim().isEmpty()) {
            try {
                // aclIdList是JSON数组格式，如："[1,2,3]"
                return JSON.parseArray(aclIdList, Integer.class);
            } catch (Exception e) {
                log.warn("解析ACL ID列表失败：{}，错误：{}", aclIdList, e.getMessage());
            }
        }
        return Collections.emptyList();
    }

    /**
     * 根据ACL ID获取ACL模板名称
     */
    private String getAclNameById(Integer aclId) {
        try {
            // 调用平台查询ACL模板详情接口
            PlatformAclDetailResponseDTO response = platformApiService.getAclDetail(aclId);
            if (response != null && response.getOutput() != null) {
                String aclName = response.getOutput().getAclName();
                log.info("查询到ACL模板名称，ACL ID：{}，ACL名称：{}", aclId, aclName);
                return aclName;
            } else {
                log.warn("未查询到ACL模板详情，ACL ID：{}", aclId);
                return null;
            }
        } catch (Exception e) {
            log.error("查询ACL模板名称失败，ACL ID：{}", aclId, e);
            return null;
        }
    }

    /**
     * 根据名称查询流分类ID（支持分页查询）
     */
    private Integer getClassifierIdByName(String classifierName) {
        int start = 0;
        int size = 15; // 每页查询15条

        while (true) {
            PlatformQosClassifierListRequestDTO request = new PlatformQosClassifierListRequestDTO();
            request.setDesc(true); // 降序排序，新增的在前面
            request.setStart(start);
            request.setSize(size);

            PlatformQosClassifierListResponseDTO response = platformApiService.getQosClassifierList(request);
            if (response == null || response.getOutput() == null ||
                    response.getOutput().getList() == null || response.getOutput().getList().isEmpty()) {
                break;
            }

            // 查找匹配的流分类
            for (PlatformQosClassifierListResponseDTO.ClassifierInfo classifier : response.getOutput().getList()) {
                if (classifierName.equals(classifier.getClassifierName())) {
                    log.info("找到流分类：{}，ID：{}", classifierName, classifier.getClassifierId());
                    return classifier.getClassifierId();
                }
            }

            // 检查是否还有更多数据
            if (response.getOutput().getList().size() < size) {
                break; // 已经是最后一页
            }

            start += size; // 查询下一页
        }

        log.error("未找到流分类：{}", classifierName);
        return null;
    }

    /**
     * 处理流行为
     */
    private Integer processBehavior(AddScheduleRequestDTO request) {
        Long networkId = request.getNetworkIds().get(0);
        String businessContext = "隧道组ID：" + networkId + "，调度策略：" + request.getAppScheduleName();

        log.info("开始处理流行为，隧道组ID列表：{}", request.getNetworkIds());

        // 1. 获取隧道组模板信息
        TeTunnelTemplateCustomInfo template = getTunnelTemplate(networkId);
        if (template == null) {
            log.error("未找到隧道组模板，隧道组ID：{}", networkId);
            throw new ServiceException("BEHAVIOR_ERROR",
                    "未找到隧道组模板，隧道组ID：" + networkId, businessContext);
        }

        // 2. 获取隧道组信息
        TeGroupCustomInfo teGroup = getTeGroupById(networkId);
        if (teGroup == null) {
            log.error("未找到隧道组信息，隧道组ID：{}", networkId);
            throw new ServiceException("BEHAVIOR_ERROR",
                    "未找到隧道组信息，隧道组ID：" + networkId, businessContext);
        }

        // 3. 构建流行为请求
        PlatformQosBehaviorRequestDTO behaviorRequest = new PlatformQosBehaviorRequestDTO();
        behaviorRequest.setBehaviorName(request.getAppScheduleName() + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX);

        List<PlatformQosBehaviorRequestDTO.RemarkInfo> remarkList = generateRemarkList(template, teGroup);
        behaviorRequest.setRemarkList(remarkList);

        // 4. 调用平台接口新增流行为
        PlatformQosBehaviorResponseDTO response = platformApiService.addQosBehavior(behaviorRequest);
        if (response == null || response.getOutput() == null) {
            throw new ServiceException("BEHAVIOR_ERROR",
                    "平台新增流行为接口返回数据为空", businessContext);
        }

        return response.getOutput().getBehaviorId();
    }

    /**
     * 获取隧道组模板信息
     */
    private TeTunnelTemplateCustomInfo getTunnelTemplate(Long networkId) {
        // 先获取隧道组信息
        LambdaQueryWrapper<TeGroupCustomInfo> teGroupQuery = new LambdaQueryWrapper<>();
        teGroupQuery.eq(TeGroupCustomInfo::getTeGroupId, networkId);
        TeGroupCustomInfo teGroup = teGroupCustomInfoMapper.selectOne(teGroupQuery);

        if (teGroup == null || teGroup.getScheduleStrategyId() == null) {
            return null;
        }

        // 根据策略ID获取隧道模板
        LambdaQueryWrapper<TeTunnelTemplateCustomInfo> templateQuery = new LambdaQueryWrapper<>();
        templateQuery.eq(TeTunnelTemplateCustomInfo::getStrategyId, teGroup.getScheduleStrategyId());
        return teTunnelTemplateCustomInfoMapper.selectOne(templateQuery);
    }

    /**
     * 获取隧道组信息
     */
    private TeGroupCustomInfo getTeGroupById(Long networkId) {
        LambdaQueryWrapper<TeGroupCustomInfo> query = new LambdaQueryWrapper<>();
        query.eq(TeGroupCustomInfo::getTeGroupId, networkId);
        return teGroupCustomInfoMapper.selectOne(query);
    }

    /**
     * 生成remark列表
     */
    private List<PlatformQosBehaviorRequestDTO.RemarkInfo> generateRemarkList(
            TeTunnelTemplateCustomInfo template, TeGroupCustomInfo teGroup) {

        List<PlatformQosBehaviorRequestDTO.RemarkInfo> remarkList = new ArrayList<>();

        // remarkType=1，使用隧道模板的sla_id转换
        PlatformQosBehaviorRequestDTO.RemarkInfo remark1 = new PlatformQosBehaviorRequestDTO.RemarkInfo();
        remark1.setRemarkType(ScheduleConstants.RemarkType.SLA_ID_REMARK);
        remark1.setRemarkValue(convertSlaIdToValue(template.getSlaId()));
        remarkList.add(remark1);

        // remarkType=16，使用隧道组的positive_service_class
        PlatformQosBehaviorRequestDTO.RemarkInfo remark16 = new PlatformQosBehaviorRequestDTO.RemarkInfo();
        remark16.setRemarkType(ScheduleConstants.RemarkType.SERVICE_CLASS_REMARK);
        remark16.setRemarkValue(teGroup.getPositiveServiceClass());
        remarkList.add(remark16);

        // remarkType=17，使用隧道模板的sla_id转换
        PlatformQosBehaviorRequestDTO.RemarkInfo remark17 = new PlatformQosBehaviorRequestDTO.RemarkInfo();
        remark17.setRemarkType(ScheduleConstants.RemarkType.SLA_ID_REMARK_2);
        remark17.setRemarkValue(convertSlaIdToValue(template.getSlaId()));
        remarkList.add(remark17);

        return remarkList;
    }

    /**
     * 将SLA ID转换为数值
     * EF-46，AF4-34,AF3-26,AF2-18,AF1-10,BE-0
     */
    private Integer convertSlaIdToValue(String slaId) {
        if (slaId == null) {
            return 0;
        }

        switch (slaId.toUpperCase()) {
            case "EF":
                return ScheduleConstants.SlaMapping.EF_VALUE;
            case "AF4":
                return ScheduleConstants.SlaMapping.AF4_VALUE;
            case "AF3":
                return ScheduleConstants.SlaMapping.AF3_VALUE;
            case "AF2":
                return ScheduleConstants.SlaMapping.AF2_VALUE;
            case "AF1":
                return ScheduleConstants.SlaMapping.AF1_VALUE;
            case "BE":
            default:
                return ScheduleConstants.SlaMapping.BE_VALUE;
        }
    }

    /**
     * 处理流策略
     */
    private List<DevicePolicyInfoDTO> processPolicy(AddScheduleRequestDTO request, Integer classifierId, Integer behaviorId) {
        String businessContext = "调度策略：" + request.getAppScheduleName() + "，流分类ID：" + classifierId + "，流行为ID：" + behaviorId;

        log.info("开始处理流策略，隧道组ID列表：{}", request.getNetworkIds());

        List<DevicePolicyInfoDTO> devicePolicyList = new ArrayList<>();

        // 只处理第一个隧道组
        if (request.getNetworkIds() == null || request.getNetworkIds().isEmpty()) {
            throw new ServiceException("POLICY_ERROR", "隧道组ID列表为空", businessContext);
        }

        Long networkId = request.getNetworkIds().get(0);
        businessContext += "，隧道组ID：" + networkId;

        TeGroupCustomInfo teGroup = getTeGroupById(networkId);
        if (teGroup == null) {
            throw new ServiceException("POLICY_ERROR",
                    "未找到隧道组信息，隧道组ID：" + networkId, businessContext);
        }

        if (teGroup.getTeGroupDcs() == null) {
            throw new ServiceException("POLICY_ERROR",
                    "隧道组作用域信息为空，隧道组ID：" + networkId, businessContext);
        }

        // 解析作用域信息
        List<TeGroupDcsDTO> scopeList;
        try {
            scopeList = JSON.parseArray(teGroup.getTeGroupDcs(), TeGroupDcsDTO.class);
        } catch (Exception e) {
            throw new ServiceException("POLICY_ERROR",
                    "解析隧道组作用域信息失败，隧道组ID：" + networkId + "，错误：" + e.getMessage(),
                    businessContext, e);
        }

        // 为每个设备生成流策略
        Set<Integer> deviceIds = extractDeviceIds(scopeList);
        if (deviceIds.isEmpty()) {
            throw new ServiceException("POLICY_ERROR",
                    "隧道组作用域中没有找到设备，隧道组ID：" + networkId, businessContext);
        }

        List<String> failedDevices = new ArrayList<>();
        for (Integer deviceId : deviceIds) {
            try {
                String platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
                if (platformNodeId == null) {
                    failedDevices.add("设备ID " + deviceId + "：未找到平台节点ID");
                    continue;
                }

                String deviceIp = getDeviceIpByPlatformNodeId(platformNodeId);
                if (deviceIp == null) {
                    failedDevices.add("设备ID " + deviceId + "：未找到设备IP");
                    continue;
                }

                Integer policyId = createOrUpdatePolicy(deviceIp, classifierId, behaviorId, request.getAppScheduleName());
                if (policyId != null) {
                    DevicePolicyInfoDTO devicePolicyInfo = new DevicePolicyInfoDTO(
                            deviceId, platformNodeId, deviceIp, policyId);
                    devicePolicyList.add(devicePolicyInfo);
                    log.info("成功处理设备策略，设备ID：{}，设备IP：{}，策略ID：{}",
                            deviceId, deviceIp, policyId);
                } else {
                    failedDevices.add("设备ID " + deviceId + "（IP：" + deviceIp + "）：创建或更新流策略失败");
                }
            } catch (Exception e) {
                failedDevices.add("设备ID " + deviceId + "：" + e.getMessage());
            }
        }

        if (devicePolicyList.isEmpty()) {
            String errorMsg = "所有设备的流策略处理都失败";
            errorMsg += "，详细错误：" + String.join("；", failedDevices);
            throw new ServiceException("POLICY_ERROR", errorMsg, businessContext);
        }

        if (!failedDevices.isEmpty()) {
            log.warn("部分设备流策略处理失败：{}", String.join("；", failedDevices));
        }

        return devicePolicyList;
    }

    /**
     * 从作用域信息中提取设备ID
     */
    private Set<Integer> extractDeviceIds(List<TeGroupDcsDTO> scopeList) {
        Set<Integer> deviceIds = new HashSet<>();

        for (TeGroupDcsDTO scope : scopeList) {
            // 提取源设备ID
            if (scope.getSrcDeviceIds() != null) {
                deviceIds.addAll(scope.getSrcDeviceIds());
            }

            // 提取目标设备ID
            if (scope.getDstDeviceIds() != null) {
                deviceIds.addAll(scope.getDstDeviceIds());
            }
        }

        return deviceIds;
    }

    /**
     * 根据设备ID获取平台节点ID
     */
    private String getPlatformNodeIdByDeviceId(Integer deviceId) {
        try {
            // 查询device_custom_info表，根据device_id获取platform_node_id
            LambdaQueryWrapper<DeviceCustomInfo> query = new LambdaQueryWrapper<>();
            query.eq(DeviceCustomInfo::getDeviceId, deviceId);
            DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(query);

            if (deviceInfo != null) {
                String platformNodeId = deviceInfo.getPlatformNodeId();
                log.info("查询到平台节点ID，设备ID：{}，平台节点ID：{}", deviceId, platformNodeId);
                return platformNodeId;
            } else {
                log.warn("未找到设备信息，设备ID：{}", deviceId);
                return null;
            }
        } catch (Exception e) {
            log.error("查询平台节点ID失败，设备ID：{}", deviceId, e);
            return null;
        }
    }

    /**
     * 根据平台节点ID获取设备IP
     */
    private String getDeviceIpByPlatformNodeId(String platformNodeId) {
        try {
            // 将platformNodeId转换为Long类型的dataId
            Long dataId = Long.valueOf(platformNodeId);

            // 调用PlatformApiService.getNodes()接口获取设备IP
            PlatformGetNodesRequestDTO request = new PlatformGetNodesRequestDTO(dataId);

            PlatformGetNodesResponseDTO response = platformApiService.getNodes(request);
            if (response != null && response.getSuccessful() != null && response.getSuccessful() &&
                    response.getResult() != null && response.getResult().getRecords() != null &&
                    !response.getResult().getRecords().isEmpty()) {

                PlatformGetNodesResponseDTO.NodeRecord nodeRecord = response.getResult().getRecords().get(0);
                String deviceIp = nodeRecord.getManageIp();
                log.info("查询到设备IP，平台节点ID：{}，设备IP：{}", platformNodeId, deviceIp);
                return deviceIp;
            } else {
                log.warn("未查询到设备信息，平台节点ID：{}", platformNodeId);
                return null;
            }
        } catch (NumberFormatException e) {
            log.error("平台节点ID格式错误，无法转换为Long类型，平台节点ID：{}", platformNodeId, e);
            return null;
        } catch (Exception e) {
            log.error("查询设备IP失败，平台节点ID：{}", platformNodeId, e);
            return null;
        }
    }

    /**
     * 创建或更新流策略
     */
    private Integer createOrUpdatePolicy(String deviceIp, Integer classifierId, Integer behaviorId, String scheduleName) {
        String policyName = ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp;

        int start = 0;
        int size = 15; // 每页查询15条

        while (true) {
            // 先查询是否存在同名策略
            PlatformQosPolicyListRequestDTO queryRequest = new PlatformQosPolicyListRequestDTO();
            queryRequest.setDesc(true);
            queryRequest.setStart(start);
            queryRequest.setSize(size);

            PlatformQosPolicyListResponseDTO queryResponse = platformApiService.getQosPolicyList(queryRequest);
            if (queryResponse == null || queryResponse.getOutput() == null ||
                    queryResponse.getOutput().getList() == null || queryResponse.getOutput().getList().isEmpty()) {
                break;
            }

            // 精准匹配流策略名称
            PlatformQosPolicyListResponseDTO.PolicyInfo existingPolicy = null;
            for (PlatformQosPolicyListResponseDTO.PolicyInfo policy : queryResponse.getOutput().getList()) {
                if (policyName.equals(policy.getPolicyName())) {
                    existingPolicy = policy;
                    break;
                }
            }

            if (existingPolicy != null) {
                // 策略已存在，需要更新（在原策略的cbpairList中新增记录）
                log.info("流策略{}已存在，需要更新，策略ID：{}", policyName, existingPolicy.getPolicyId());
                return updateExistingPolicy(existingPolicy, classifierId, behaviorId, scheduleName);
            } else {
                // 策略不存在，继续下一页查询
                start += size;
            }
        }
        return createNewPolicy(policyName, classifierId, behaviorId, scheduleName);
    }

    /**
     * 创建新的流策略
     */
    private Integer createNewPolicy(String policyName, Integer classifierId, Integer behaviorId, String scheduleName) {
        try {
            PlatformQosPolicyRequestDTO policyRequest = new PlatformQosPolicyRequestDTO();
            policyRequest.setPolicyName(policyName);
            policyRequest.setDescription("");

            // 构建cbpair
            PlatformQosPolicyRequestDTO.CbPair cbPair = new PlatformQosPolicyRequestDTO.CbPair();
            cbPair.setClassifierId(classifierId);
            cbPair.setClassifierName(scheduleName + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX);
            cbPair.setBehaviorId(behaviorId);
            cbPair.setBehaviorName(scheduleName + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX);

            policyRequest.setCbpairList(Collections.singletonList(cbPair));

            // 调用平台接口创建流策略
            PlatformQosPolicyResponseDTO response = platformApiService.addQosPolicy(policyRequest);
            if (response != null && response.getOutput() != null) {
                return response.getOutput().getPolicyId();
            }

            return null;

        } catch (Exception e) {
            log.error("创建新流策略异常，策略名称：{}", policyName, e);
            return null;
        }
    }

    /**
     * 更新已存在的流策略
     */
    private Integer updateExistingPolicy(PlatformQosPolicyListResponseDTO.PolicyInfo existingPolicy,
                                         Integer classifierId, Integer behaviorId, String scheduleName) {
        try {
            // 构建更新请求
            PlatformQosPolicyUpdateRequestDTO updateRequest = new PlatformQosPolicyUpdateRequestDTO();
            updateRequest.setPolicyId(existingPolicy.getPolicyId());
            updateRequest.setPolicyName(existingPolicy.getPolicyName());
            updateRequest.setDescription(existingPolicy.getDescription());

            // 构建新的cbpair列表（包含原有的和新增的）
            List<PlatformQosPolicyRequestDTO.CbPair> cbpairList = new ArrayList<>();

            // 查询现有的CB对详情
            PlatformQosPolicyDetailRuleRequestDTO detailRequest = new PlatformQosPolicyDetailRuleRequestDTO(existingPolicy.getPolicyId());
            PlatformQosPolicyDetailRuleResponseDTO detailResponse = platformApiService.getQosPolicyRuleDetail(detailRequest);

            // 添加原有的cbpair（如果存在）
            if (detailResponse != null && detailResponse.getOutput() != null &&
                    detailResponse.getOutput().getCbList() != null) {
                for (PlatformQosPolicyDetailRuleResponseDTO.CbPair existingCbpair : detailResponse.getOutput().getCbList()) {
                    // 将详情响应DTO的CbPair转换为请求DTO的CbPair
                    PlatformQosPolicyRequestDTO.CbPair requestCbpair = new PlatformQosPolicyRequestDTO.CbPair();
                    requestCbpair.setClassifierId(existingCbpair.getClassifierId());
                    requestCbpair.setClassifierName(existingCbpair.getClassifierName());
                    requestCbpair.setBehaviorId(existingCbpair.getBehaviorId());
                    requestCbpair.setBehaviorName(existingCbpair.getBehaviorName());
                    cbpairList.add(requestCbpair);
                }
            }

            // 添加新的cbpair
            PlatformQosPolicyRequestDTO.CbPair newCbPair = new PlatformQosPolicyRequestDTO.CbPair();
            newCbPair.setClassifierId(classifierId);
            newCbPair.setClassifierName(scheduleName + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX);
            newCbPair.setBehaviorId(behaviorId);
            newCbPair.setBehaviorName(scheduleName + ScheduleConstants.NamingRule.BEHAVIOR_NAME_SUFFIX);
            cbpairList.add(newCbPair);

            updateRequest.setCbpairList(cbpairList);

            // 调用平台接口更新流策略
            boolean updateSuccess = platformApiService.updateQosPolicy(updateRequest);
            if (updateSuccess) {
                log.info("流策略更新成功，策略ID：{}", existingPolicy.getPolicyId());
                return existingPolicy.getPolicyId();
            } else {
                log.error("流策略更新失败，策略ID：{}", existingPolicy.getPolicyId());
                return null;
            }

        } catch (Exception e) {
            log.error("更新流策略异常，策略ID：{}", existingPolicy.getPolicyId(), e);
            return null;
        }
    }

    /**
     * 部署策略到设备接口
     */
    private boolean deployPolicies(AddScheduleRequestDTO request, List<DevicePolicyInfoDTO> devicePolicyList) {
        try {
            log.info("开始部署流策略到设备接口");

            boolean allSuccess = true;

            // 直接遍历处理，无需分组
            for (DevicePolicyInfoDTO devicePolicy : devicePolicyList) {
                Integer policyId = devicePolicy.getPolicyId();
                String deviceIp = devicePolicy.getDeviceIp();
                String policyName = ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp;

                // 收集该设备的所有接口信息
                List<PlatformQosPolicyDeployRequestDTO.DeployInfo> deployInfoList = new ArrayList<>();
                Integer deviceId = devicePolicy.getDeviceId();

                // 1. 查询设备信息
                PlatformQosDeviceListRequestDTO deviceRequest = new PlatformQosDeviceListRequestDTO(deviceIp);
                PlatformQosDeviceListResponseDTO deviceResponse = platformApiService.getQosDeviceList(deviceRequest);

                if (deviceResponse == null || deviceResponse.getOutput() == null ||
                        deviceResponse.getOutput().getList() == null || deviceResponse.getOutput().getList().isEmpty()) {
                    log.warn("未找到设备信息，设备IP：{}", deviceIp);
                    allSuccess = false;
                    continue;
                }

                // 根据IP精准匹配设备信息
                PlatformQosDeviceListResponseDTO.DeviceInfo deviceInfo = null;
                for (PlatformQosDeviceListResponseDTO.DeviceInfo device : deviceResponse.getOutput().getList()) {
                    if (deviceIp.equals(device.getDevIp())) {
                        deviceInfo = device;
                        break;
                    }
                }

                if (deviceInfo == null) {
                    log.warn("未找到匹配IP的设备信息，设备IP：{}", deviceIp);
                    allSuccess = false;
                    continue;
                }

                // 2. 获取设备接口信息（使用定制表设备ID）
                List<String> interfaceNames = getDeviceInterfaceNames(deviceId);
                if (interfaceNames.isEmpty()) {
                    log.warn("未找到设备接口信息，设备ID：{}，设备IP：{}", deviceId, deviceIp);
                    allSuccess = false;
                    continue;
                }

                // 3. 为该设备的每个接口创建部署信息
                for (String interfaceName : interfaceNames) {
                    String ifUuid = getInterfaceUuid(deviceInfo.getDevuuId(), interfaceName);
                    if (ifUuid == null) {
                        log.warn("未找到接口UUID，设备UUID：{}，接口名称：{}", deviceInfo.getDevuuId(), interfaceName);
                        allSuccess = false;
                        continue;
                    }

                    // 创建部署信息
                    PlatformQosPolicyDeployRequestDTO.DeployInfo deployInfo = new PlatformQosPolicyDeployRequestDTO.DeployInfo();
                    deployInfo.setIfUuid(ifUuid);
                    deployInfo.setInterfaceName(interfaceName);
                    deployInfo.setInterfaceDescription(null);
                    deployInfo.setDevId(null);
                    deployInfo.setDevName(deviceInfo.getDevName());
                    deployInfo.setDevUuid(deviceInfo.getDevuuId());
                    deployInfo.setDirection(0);
//                    deployInfo.setDirection(ScheduleConstants.PolicyDeploy.DIRECTION_DEFAULT);
//                    deployInfo.setPreorder(ScheduleConstants.PolicyDeploy.PREORDER_DEFAULT);
//                    deployInfo.setShareMode(ScheduleConstants.PolicyDeploy.SHARE_MODE_DEFAULT);

                    deployInfoList.add(deployInfo);
                }

                // 4. 如果有接口信息，则调用一次部署接口
                if (!deployInfoList.isEmpty()) {
                    boolean deploySuccess = deployPolicyToInterfaces(policyId, policyName, deployInfoList);
                    if (!deploySuccess) {
                        allSuccess = false;
                    }
                } else {
                    log.warn("策略ID：{}没有可部署的接口信息", policyId);
                }
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("部署策略异常", e);
            return false;
        }
    }

    /**
     * 获取设备接口名称列表（根据定制表设备ID查询lan_custom_info表）
     */
    private List<String> getDeviceInterfaceNames(Integer deviceId) {
        try {
            // 从lan_custom_info表中获取设备对应的interface_name
            LambdaQueryWrapper<LanCustomInfo> query = new LambdaQueryWrapper<>();
            query.eq(LanCustomInfo::getDeviceId, deviceId);
            List<LanCustomInfo> lanInfoList = lanCustomInfoMapper.selectList(query);

            if (lanInfoList != null && !lanInfoList.isEmpty()) {
                List<String> interfaceNames = lanInfoList.stream()
                        .map(LanCustomInfo::getInterfaceName)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());

                log.info("查询到设备接口信息，设备ID：{}，接口数量：{}", deviceId, interfaceNames.size());
                return interfaceNames;
            } else {
                log.warn("未找到设备接口信息，设备ID：{}", deviceId);
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("获取设备接口名称异常，设备ID：{}", deviceId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取接口UUID
     */
    private String getInterfaceUuid(String devUuid, String interfaceName) {
        try {
            PlatformQosDeviceInterfaceListRequestDTO request = new PlatformQosDeviceInterfaceListRequestDTO(devUuid, interfaceName);
            PlatformQosDeviceInterfaceListResponseDTO response = platformApiService.getQosDeviceInterfaceList(request);

            if (response != null && response.getOutput() != null &&
                    response.getOutput().getList() != null && !response.getOutput().getList().isEmpty()) {
                return response.getOutput().getList().get(0).getIfuuidId();
            }

            return null;

        } catch (Exception e) {
            log.error("获取接口UUID异常，设备UUID：{}，接口名称：{}", devUuid, interfaceName, e);
            return null;
        }
    }

    /**
     * 部署策略到多个接口（一次调用部署接口，传入多个接口信息）
     */
    private boolean deployPolicyToInterfaces(Integer policyId, String policyName,
                                             List<PlatformQosPolicyDeployRequestDTO.DeployInfo> deployInfoList) {
        try {
            PlatformQosPolicyDeployRequestDTO deployRequest = new PlatformQosPolicyDeployRequestDTO();
            deployRequest.setTemplateId(policyId);
            deployRequest.setTemplateName(policyName);
            deployRequest.setList(deployInfoList);

            log.info("部署策略到接口，策略ID：{}，策略名称：{}，接口数量：{}", policyId, policyName, deployInfoList.size());

            PlatformQosPolicyDeployResponseDTO response = platformApiService.deployQosPolicy(deployRequest);
            boolean success = response != null && response.getOutput() != null;

            if (success) {
                log.info("策略部署成功，策略ID：{}，任务ID：{}", policyId, response.getOutput());
            } else {
                log.error("策略部署失败，策略ID：{}", policyId);
            }

            return success;

        } catch (Exception e) {
            log.error("部署策略到接口异常，策略ID：{}", policyId, e);
            return false;
        }
    }

    /**
     * 保存调度策略信息到数据库
     */
    private void saveScheduleInfo(AddScheduleRequestDTO request, Integer classifierId, String classifierName,
                                  Integer behaviorId, String behaviorName, Map<String, Integer> policyInfo) {
        try {
            ScheduleCustomInfo scheduleInfo = new ScheduleCustomInfo();
            scheduleInfo.setAppScheduleId(request.getAppScheduleId());
            scheduleInfo.setAppScheduleName(request.getAppScheduleName());
            scheduleInfo.setAppGroupName(request.getAppGroupName());
            scheduleInfo.setDrainageType(request.getDrainageType());
            scheduleInfo.setNetworkIds(JSON.toJSONString(request.getNetworkIds()));
            scheduleInfo.setVpnId(request.getVpnId());
            scheduleInfo.setClassifierId(classifierId);
            scheduleInfo.setClassifierName(classifierName);
            scheduleInfo.setBehaviorId(behaviorId);
            scheduleInfo.setBehaviorName(behaviorName);
            scheduleInfo.setPolicyInfo(JSON.toJSONString(policyInfo));
            scheduleInfo.setCreateTime(new Date());
            scheduleInfo.setUpdateTime(new Date());
            scheduleInfo.setIsDeleted(0);

            scheduleCustomInfoMapper.insert(scheduleInfo);
            log.info("调度策略信息保存成功，调度策略ID：{}", request.getAppScheduleId());

        } catch (Exception e) {
            log.error("保存调度策略信息异常", e);
            throw new ServiceException("SAVE_ERROR", "保存调度策略信息失败：" + e.getMessage(),
                    "调度策略ID：" + request.getAppScheduleId(), e);
        }
    }

    /**
     * 保存调度策略详细信息到独立表
     */
    private void saveScheduleDetailInfo(AddScheduleRequestDTO request, Integer classifierId, String classifierName,
                                        Integer behaviorId, String behaviorName, List<DevicePolicyInfoDTO> devicePolicyList) {
        try {
            Date currentTime = new Date();

            // 1. 保存流分类信息
            ScheduleClassifierInfo classifierInfo = new ScheduleClassifierInfo();
            classifierInfo.setAppScheduleId(request.getAppScheduleId());
            classifierInfo.setClassifierId(classifierId);
            classifierInfo.setClassifierName(classifierName);
            classifierInfo.setCreateTime(currentTime);
            classifierInfo.setUpdateTime(currentTime);
            classifierInfo.setIsDeleted(0);
            scheduleClassifierInfoMapper.insert(classifierInfo);

            // 2. 保存流行为信息
            ScheduleBehaviorInfo behaviorInfo = new ScheduleBehaviorInfo();
            behaviorInfo.setAppScheduleId(request.getAppScheduleId());
            behaviorInfo.setBehaviorId(behaviorId);
            behaviorInfo.setBehaviorName(behaviorName);
            behaviorInfo.setCreateTime(currentTime);
            behaviorInfo.setUpdateTime(currentTime);
            behaviorInfo.setIsDeleted(0);
            scheduleBehaviorInfoMapper.insert(behaviorInfo);

            // 3. 保存流策略信息
            for (DevicePolicyInfoDTO devicePolicy : devicePolicyList) {
                SchedulePolicyInfo policyInfo = new SchedulePolicyInfo();
                policyInfo.setAppScheduleId(request.getAppScheduleId());
                policyInfo.setPolicyId(devicePolicy.getPolicyId());
                policyInfo.setPolicyName(ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + devicePolicy.getDeviceIp());
                policyInfo.setDeviceId(Long.valueOf(devicePolicy.getDeviceId()));
                policyInfo.setPlatformNodeId(devicePolicy.getPlatformNodeId());
                policyInfo.setDeviceIp(devicePolicy.getDeviceIp());
                policyInfo.setClassifierId(classifierId);
                policyInfo.setBehaviorId(behaviorId);
                policyInfo.setCreateTime(currentTime);
                policyInfo.setUpdateTime(currentTime);
                policyInfo.setIsDeleted(0);
                schedulePolicyInfoMapper.insert(policyInfo);
            }

            log.info("调度策略详细信息保存成功，调度策略ID：{}，流分类ID：{}，流行为ID：{}，流策略数量：{}",
                    request.getAppScheduleId(), classifierId, behaviorId, devicePolicyList.size());

        } catch (Exception e) {
            log.error("保存调度策略详细信息异常，调度策略ID：{}", request.getAppScheduleId(), e);
            throw new ServiceException("SAVE_ERROR", "保存调度策略详细信息失败：" + e.getMessage(),
                    "调度策略ID：" + request.getAppScheduleId(), e);
        }
    }

    /**
     * 删除流分类
     *
     * @param appScheduleId 调度策略ID
     */
    private void deleteClassifier(Long appScheduleId) {
        // 1. 查询流分类信息
        LambdaQueryWrapper<ScheduleClassifierInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleClassifierInfo::getAppScheduleId, appScheduleId);
        List<ScheduleClassifierInfo> classifierInfos = scheduleClassifierInfoMapper.selectList(query);

        for (ScheduleClassifierInfo classifierInfo : classifierInfos) {
            // 2. 调用平台接口删除流分类（带重试机制）
            deleteClassifierWithRetry(classifierInfo.getClassifierId());

            log.info("平台流分类删除成功，流分类ID：{}", classifierInfo.getClassifierId());

            // 3. 软删除本地记录
            scheduleClassifierInfoMapper.deleteById(classifierInfo.getId());
        }

        log.info("流分类删除完成，调度策略ID：{}，删除数量：{}", appScheduleId, classifierInfos.size());
    }

    /**
     * 删除流行为
     *
     * @param appScheduleId 调度策略ID
     */
    private void deleteBehavior(Long appScheduleId) {
        // 1. 查询流行为信息
        LambdaQueryWrapper<ScheduleBehaviorInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleBehaviorInfo::getAppScheduleId, appScheduleId);
        List<ScheduleBehaviorInfo> behaviorInfos = scheduleBehaviorInfoMapper.selectList(query);

        for (ScheduleBehaviorInfo behaviorInfo : behaviorInfos) {
            // 2. 调用平台接口删除流行为（带重试机制）
            deleteBehaviorWithRetry(behaviorInfo.getBehaviorId());

            log.info("平台流行为删除成功，流行为ID：{}", behaviorInfo.getBehaviorId());

            // 3. 软删除本地记录
            scheduleBehaviorInfoMapper.deleteById(behaviorInfo.getId());
        }

        log.info("流行为删除完成，调度策略ID：{}，删除数量：{}", appScheduleId, behaviorInfos.size());
    }

    /**
     * 删除流行为（带重试机制）
     *
     * @param behaviorId 流行为ID
     */
    private void deleteBehaviorWithRetry(Integer behaviorId) {
        final int maxRetries = 10;
        final long retryIntervalMs = 1000; // 1秒
        final String targetErrorCode = "2131";

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("尝试删除流行为，流行为ID：{}，第{}次尝试", behaviorId, attempt);

                PlatformDeleteResult result = platformApiService.deleteQosBehaviorWithResult(behaviorId);

                if (result.isSuccess()) {
                    log.info("流行为删除成功，流行为ID：{}，尝试次数：{}", behaviorId, attempt);
                    return; // 成功
                }

                // 检查是否是需要重试的错误
                boolean shouldRetry = false;
                if (result.getStatusCode() == 500 && targetErrorCode.equals(result.getErrorCode())) {
                    // 原有的重试逻辑：流行为被引用
                    shouldRetry = true;
                } else if (result.getStatusCode() == 500 && "SERVER_BUSY".equals(result.getErrorCode())) {
                    // 新增的重试逻辑：服务器忙
                    shouldRetry = true;
                }

                if (shouldRetry) {
                    log.warn("流行为删除失败，错误码：{}，错误信息：{}，流行为ID：{}，第{}次尝试，将在{}ms后重试",
                            result.getErrorCode(), result.getErrorMessage(), behaviorId, attempt, retryIntervalMs);

                    if (attempt < maxRetries) {
                        Thread.sleep(retryIntervalMs);
                        continue;
                    } else {
                        // 达到最大重试次数
                        throw new ServiceException("BEHAVIOR_DELETE_ERROR",
                                String.format("重试%d次后仍失败，错误码：%s，错误信息：%s", maxRetries, result.getErrorCode(), result.getErrorMessage()),
                                "流行为ID：" + behaviorId);
                    }
                } else {
                    // 其他错误，不重试
                    log.error("流行为删除失败，不符合重试条件，状态码：{}，错误码：{}，错误信息：{}，流行为ID：{}",
                            result.getStatusCode(), result.getErrorCode(), result.getErrorMessage(), behaviorId);
                    throw new ServiceException("BEHAVIOR_DELETE_ERROR",
                            String.format("状态码：%d，错误码：%s，错误信息：%s", result.getStatusCode(), result.getErrorCode(), result.getErrorMessage()),
                            "流行为ID：" + behaviorId);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("删除流行为重试被中断，流行为ID：{}", behaviorId, e);
                throw new ServiceException("BEHAVIOR_DELETE_ERROR", "删除操作被中断", "流行为ID：" + behaviorId, e);
            } catch (ServiceException e) {
                // 重新抛出ServiceException
                throw e;
            } catch (Exception e) {
                log.error("删除流行为异常，流行为ID：{}，第{}次尝试", behaviorId, attempt, e);
                if (attempt >= maxRetries) {
                    throw new ServiceException("BEHAVIOR_DELETE_ERROR", "系统异常：" + e.getMessage(), "流行为ID：" + behaviorId, e);
                }
            }
        }

        log.error("流行为删除失败，已达到最大重试次数{}，流行为ID：{}", maxRetries, behaviorId);
        throw new ServiceException("BEHAVIOR_DELETE_ERROR",
                String.format("已达到最大重试次数%d，删除失败", maxRetries), "流行为ID：" + behaviorId);
    }

    /**
     * 删除流分类（带重试机制）
     *
     * @param classifierId 流分类ID
     */
    private void deleteClassifierWithRetry(Integer classifierId) {
        final int maxRetries = 10;
        final long retryIntervalMs = 1000; // 1秒
        final String targetErrorCode = "2318";

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("尝试删除流分类，流分类ID：{}，第{}次尝试", classifierId, attempt);

                PlatformDeleteResult result = platformApiService.deleteQosClassifierWithResult(classifierId);

                if (result.isSuccess()) {
                    log.info("流分类删除成功，流分类ID：{}，尝试次数：{}", classifierId, attempt);
                    return; // 成功
                }

                // 检查是否是需要重试的错误
                boolean shouldRetry = false;
                if (result.getStatusCode() == 500 && targetErrorCode.equals(result.getErrorCode())) {
                    // 原有的重试逻辑：流分类被引用
                    shouldRetry = true;
                } else if (result.getStatusCode() == 500 && "SERVER_BUSY".equals(result.getErrorCode())) {
                    // 新增的重试逻辑：服务器忙
                    shouldRetry = true;
                }

                if (shouldRetry) {
                    log.warn("流分类删除失败，错误码：{}，错误信息：{}，流分类ID：{}，第{}次尝试，将在{}ms后重试",
                            result.getErrorCode(), result.getErrorMessage(), classifierId, attempt, retryIntervalMs);

                    if (attempt < maxRetries) {
                        Thread.sleep(retryIntervalMs);
                        continue;
                    } else {
                        // 达到最大重试次数
                        throw new ServiceException("CLASSIFIER_DELETE_ERROR",
                                String.format("重试%d次后仍失败，错误码：%s，错误信息：%s", maxRetries, result.getErrorCode(), result.getErrorMessage()),
                                "流分类ID：" + classifierId);
                    }
                } else {
                    // 其他错误，不重试
                    log.error("流分类删除失败，不符合重试条件，状态码：{}，错误码：{}，错误信息：{}，流分类ID：{}",
                            result.getStatusCode(), result.getErrorCode(), result.getErrorMessage(), classifierId);
                    throw new ServiceException("CLASSIFIER_DELETE_ERROR",
                            String.format("状态码：%d，错误码：%s，错误信息：%s", result.getStatusCode(), result.getErrorCode(), result.getErrorMessage()),
                            "流分类ID：" + classifierId);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("删除流分类重试被中断，流分类ID：{}", classifierId, e);
                throw new ServiceException("CLASSIFIER_DELETE_ERROR", "删除操作被中断", "流分类ID：" + classifierId, e);
            } catch (ServiceException e) {
                // 重新抛出ServiceException
                throw e;
            } catch (Exception e) {
                log.error("删除流分类异常，流分类ID：{}，第{}次尝试", classifierId, attempt, e);
                if (attempt >= maxRetries) {
                    throw new ServiceException("CLASSIFIER_DELETE_ERROR", "系统异常：" + e.getMessage(), "流分类ID：" + classifierId, e);
                }
            }
        }

        log.error("流分类删除失败，已达到最大重试次数{}，流分类ID：{}", maxRetries, classifierId);
        throw new ServiceException("CLASSIFIER_DELETE_ERROR",
                String.format("已达到最大重试次数%d，删除失败", maxRetries), "流分类ID：" + classifierId);
    }

    /**
     * 删除流策略
     *
     * @param appScheduleId 调度策略ID
     */
    private void deletePolicies(Long appScheduleId) {
        // 1. 查询流策略信息
        LambdaQueryWrapper<SchedulePolicyInfo> query = new LambdaQueryWrapper<>();
        query.eq(SchedulePolicyInfo::getAppScheduleId, appScheduleId);
        List<SchedulePolicyInfo> policyInfos = schedulePolicyInfoMapper.selectList(query);

        // 2. 直接遍历处理，无需分组
        for (SchedulePolicyInfo policyInfo : policyInfos) {
            // 3. 查询流策略CB对详情
            PlatformQosPolicyDetailRuleRequestDTO detailRequest = new PlatformQosPolicyDetailRuleRequestDTO(policyInfo.getPolicyId());
            PlatformQosPolicyDetailRuleResponseDTO detailResponse = platformApiService.getQosPolicyRuleDetail(detailRequest);

            if (detailResponse != null && detailResponse.getOutput() != null &&
                    detailResponse.getOutput().getCbList() != null) {

                List<PlatformQosPolicyDetailRuleResponseDTO.CbPair> existingCbPairs = detailResponse.getOutput().getCbList();

                // 4. 过滤出剩余的cbpair（移除当前调度策略的cbpair）
                List<PlatformQosPolicyRequestDTO.CbPair> remainingCbPairs = existingCbPairs.stream()
                        .filter(cbPair -> !shouldRemoveCbPair(cbPair, policyInfo.getClassifierId(), policyInfo.getBehaviorId()))
                        .map(this::convertDetailCbPair)
                        .collect(Collectors.toList());

                // 5. 根据剩余cbpair数量决定操作
                if (remainingCbPairs.isEmpty()) {
                    // 删除整个流策略
                    platformApiService.deleteQosPolicy(policyInfo.getPolicyId());
                    log.info("平台流策略删除成功，策略ID：{}", policyInfo.getPolicyId());
                } else {
                    // 更新流策略，移除对应的cbpair
                    // 从策略名称中提取设备IP
                    String deviceIp = extractIpFromPolicyName(policyInfo.getPolicyName());
                    if (deviceIp == null || deviceIp.isEmpty()) {
                        log.warn("无法从策略名称中提取设备IP，策略名称：{}，使用默认值", policyInfo.getPolicyName());
                        deviceIp = "unknown";
                    }

                    updatePolicyWithRemainingCbPairs(policyInfo.getPolicyId(), remainingCbPairs, deviceIp);
                }
            } else {
                log.warn("未查询到流策略CB对详情，策略ID：{}", policyInfo.getPolicyId());
            }

            // 6. 软删除本地记录
            schedulePolicyInfoMapper.deleteById(policyInfo.getId());
        }

        log.info("流策略删除完成，调度策略ID：{}，删除数量：{}", appScheduleId, policyInfos.size());
    }

    /**
     * 判断cbpair是否应该被删除
     */
    private boolean shouldRemoveCbPair(PlatformQosPolicyDetailRuleResponseDTO.CbPair cbPair,
                                       Integer classifierId, Integer behaviorId) {
        // 只有当classifier_id和behavior_id都匹配时才删除
        // 因为一个调度策略对应一个特定的classifier和behavior组合
        return cbPair.getClassifierId().equals(classifierId) &&
                cbPair.getBehaviorId().equals(behaviorId);
    }

    /**
     * 转换CbPair对象（从详情响应转换为请求对象）
     */
    private PlatformQosPolicyRequestDTO.CbPair convertDetailCbPair(PlatformQosPolicyDetailRuleResponseDTO.CbPair sourceCbPair) {
        PlatformQosPolicyRequestDTO.CbPair targetCbPair = new PlatformQosPolicyRequestDTO.CbPair();
        targetCbPair.setClassifierId(sourceCbPair.getClassifierId());
        targetCbPair.setClassifierName(sourceCbPair.getClassifierName());
        targetCbPair.setBehaviorId(sourceCbPair.getBehaviorId());
        targetCbPair.setBehaviorName(sourceCbPair.getBehaviorName());
        return targetCbPair;
    }

    /**
     * 更新流策略，保留剩余的cbpair
     *
     * @param policyId         策略ID
     * @param remainingCbPairs 剩余的cbpair列表
     * @param deviceIp         设备IP，用于生成正确的策略名称
     */
    private void updatePolicyWithRemainingCbPairs(Integer policyId,
                                                  List<PlatformQosPolicyRequestDTO.CbPair> remainingCbPairs,
                                                  String deviceIp) {
        // 使用正确的策略名称：policy_ + 设备IP
        String policyName = ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp;
        String description = "";

        PlatformQosPolicyUpdateRequestDTO updateRequest = new PlatformQosPolicyUpdateRequestDTO();
        updateRequest.setPolicyId(policyId);
        updateRequest.setPolicyName(policyName);
        updateRequest.setDescription(description);
        updateRequest.setCbpairList(remainingCbPairs);

        platformApiService.updateQosPolicy(updateRequest);
        log.info("平台流策略更新成功，策略ID：{}，策略名称：{}，剩余cbpair数量：{}",
                policyId, policyName, remainingCbPairs.size());
    }

    @Override
    public QueryScheduleListResponseDTO queryScheduleList(String requestId) {
        log.info("开始查询调度策略列表，请求ID：{}", requestId);

        // 1. 查询调度策略基本信息
        LambdaQueryWrapper<ScheduleCustomInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleCustomInfo::getIsDeleted, 0);
        List<ScheduleCustomInfo> scheduleInfoList = scheduleCustomInfoMapper.selectList(query);

        if (scheduleInfoList.isEmpty()) {
            log.info("未查询到调度策略数据，请求ID：{}", requestId);
            return QueryScheduleListResponseDTO.success(requestId, 0, new ArrayList<>());
        }

        List<QueryScheduleListResponseDTO.ScheduleInfo> scheduleList = new ArrayList<>();

        // 2. 处理每个调度策略
        for (ScheduleCustomInfo scheduleInfo : scheduleInfoList) {
            QueryScheduleListResponseDTO.ScheduleInfo scheduleItem = processScheduleInfo(scheduleInfo);
            scheduleList.add(scheduleItem);
        }

        log.info("查询调度策略列表成功，请求ID：{}，总数：{}", requestId, scheduleList.size());
        return QueryScheduleListResponseDTO.success(requestId, scheduleList.size(), scheduleList);
    }

    /**
     * 处理单个调度策略信息
     */
    private QueryScheduleListResponseDTO.ScheduleInfo processScheduleInfo(ScheduleCustomInfo scheduleInfo) {
        QueryScheduleListResponseDTO.ScheduleInfo scheduleItem = new QueryScheduleListResponseDTO.ScheduleInfo();
        scheduleItem.setAppScheduleId(scheduleInfo.getAppScheduleId());
        scheduleItem.setAppScheduleName(scheduleInfo.getAppScheduleName());
        scheduleItem.setAppGroupName(scheduleInfo.getAppGroupName());
        scheduleItem.setDrainageType(scheduleInfo.getDrainageType());
        scheduleItem.setVpnId(scheduleInfo.getVpnId());

        // 解析隧道组ID列表
        if (scheduleInfo.getNetworkIds() != null && !scheduleInfo.getNetworkIds().isEmpty()) {
            try {
                List<Long> networkIds = JSON.parseArray(scheduleInfo.getNetworkIds(), Long.class);
                scheduleItem.setNetworkIds(networkIds);
            } catch (Exception e) {
                log.error("解析隧道组ID列表异常，策略ID：{}，原始数据：{}", scheduleInfo.getAppScheduleId(), scheduleInfo.getNetworkIds(), e);
                throw new ServiceException("DATA_PARSE_ERROR", "解析隧道组ID列表失败：" + e.getMessage(),
                        "调度策略ID：" + scheduleInfo.getAppScheduleId(), e);
            }
        }

        // 查询关联的流策略信息
        LambdaQueryWrapper<SchedulePolicyInfo> policyQuery = new LambdaQueryWrapper<>();
        policyQuery.eq(SchedulePolicyInfo::getAppScheduleId, scheduleInfo.getAppScheduleId())
                .eq(SchedulePolicyInfo::getIsDeleted, 0);
        List<SchedulePolicyInfo> policyInfoList = schedulePolicyInfoMapper.selectList(policyQuery);

        // 生成扩展信息和计算策略状态
        List<QueryScheduleListResponseDTO.OptionField> optionFields = new ArrayList<>();
        int successCount = 0;
        int totalCount = policyInfoList.size();

        for (SchedulePolicyInfo policyInfo : policyInfoList) {
            try {
                QueryScheduleListResponseDTO.OptionField optionField = processOptionField(policyInfo);
                optionFields.add(optionField);
                if (optionField.getResult() != null && optionField.getResult() == 1) {
                    successCount++;
                }
            } catch (Exception e) {
                // 单个策略信息处理失败不影响整体查询，记录日志并跳过
                log.error("处理策略扩展信息失败，策略ID：{}，设备IP：{}，跳过该项",
                        policyInfo.getPolicyId(), policyInfo.getDeviceIp(), e);

                // 创建一个默认的失败项
                QueryScheduleListResponseDTO.OptionField failedOptionField = new QueryScheduleListResponseDTO.OptionField();
                failedOptionField.setDeviceIp(policyInfo.getDeviceIp());
                failedOptionField.setResult(0);
                failedOptionField.setFaultReason("处理策略信息异常：" + e.getMessage());
                optionFields.add(failedOptionField);
            }
        }

        scheduleItem.setOptionField(optionFields);

        // 计算策略状态
        Integer scheduleStatus = calculateScheduleStatus(successCount, totalCount);
        scheduleItem.setScheduleStatus(scheduleStatus);

        return scheduleItem;
    }

    /**
     * 处理扩展信息字段
     */
    private QueryScheduleListResponseDTO.OptionField processOptionField(SchedulePolicyInfo policyInfo) {
        QueryScheduleListResponseDTO.OptionField optionField = new QueryScheduleListResponseDTO.OptionField();
        optionField.setDeviceIp(policyInfo.getDeviceIp());

        // 查询设备信息
        LambdaQueryWrapper<DeviceCustomInfo> deviceQuery = new LambdaQueryWrapper<>();
        deviceQuery.eq(DeviceCustomInfo::getDeviceId, policyInfo.getDeviceId())
                .eq(DeviceCustomInfo::getIsDeleted, 0);
        DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(deviceQuery);

        if (deviceInfo != null) {
            optionField.setSiteId(deviceInfo.getDeviceSiteId());
            optionField.setSiteName(deviceInfo.getDeviceSite());

            // 从平台查询设备SN
            String deviceSn = getDeviceSnByPlatformNodeId(deviceInfo.getPlatformNodeId());
            optionField.setDeviceSn(deviceSn != null ? deviceSn : "");
        }

        // 查询流策略部署历史，获取最新部署结果
        PlatformQosPolicyDeployHistoryRequestDTO historyRequest = new PlatformQosPolicyDeployHistoryRequestDTO();
        historyRequest.setPolicyId(policyInfo.getPolicyId());
        historyRequest.setDesc(true); // 降序排列，获取最新记录
        historyRequest.setStart(0);
        historyRequest.setSize(1); // 只取最新一条

        PlatformQosPolicyDeployHistoryResponseDTO historyResponse =
                platformApiService.getQosPolicyDeployHistory(historyRequest);

        if (historyResponse != null && historyResponse.getOutput() != null &&
                historyResponse.getOutput().getList() != null && !historyResponse.getOutput().getList().isEmpty()) {

            PlatformQosPolicyDeployHistoryResponseDTO.DeployHistoryInfo latestHistory =
                    historyResponse.getOutput().getList().get(0);

            // 设置部署结果
            optionField.setResult(latestHistory.getResult());
            optionField.setFaultReason(latestHistory.getReason());
        } else {
            // 未查询到部署历史，默认为失败
            optionField.setResult(0);
            optionField.setFaultReason("未查询到部署历史");
        }

        return optionField;
    }

    /**
     * 计算调度策略状态
     * 0未启用、1未生效、2部分生效、3全部生效、4生效中、9异常
     */
    private Integer calculateScheduleStatus(int successCount, int totalCount) {
        if (totalCount == 0) {
            return 1; // 未生效
        }

        if (successCount == 0) {
            return 1; // 未生效
        } else if (successCount == totalCount) {
            return 3; // 全部生效
        } else {
            return 2; // 部分生效
        }
    }

    /**
     * 根据平台节点ID获取设备序列号
     * 参考DeviceServiceImpl.fillPlatformDeviceInfoBySiteId方法实现
     */
    private String getDeviceSnByPlatformNodeId(String platformNodeId) {
        try {
            if (platformNodeId == null || platformNodeId.isEmpty()) {
                log.warn("平台节点ID为空，无法查询设备序列号");
                return "";
            }

            Long nodeId = Long.parseLong(platformNodeId);
            PlatformGetNodesRequestDTO getNodesRequest = new PlatformGetNodesRequestDTO(nodeId);
            PlatformGetNodesResponseDTO getNodesResponse = platformApiService.getNodes(getNodesRequest);

            if (getNodesResponse != null && getNodesResponse.getSuccessful() &&
                    getNodesResponse.getResult() != null &&
                    getNodesResponse.getResult().getRecords() != null &&
                    !getNodesResponse.getResult().getRecords().isEmpty()) {

                PlatformGetNodesResponseDTO.NodeRecord nodeRecord = getNodesResponse.getResult().getRecords().get(0);

                // 处理序列号，如果是列表则取第一个
                if (nodeRecord.getSerialNumbers() != null && !nodeRecord.getSerialNumbers().isEmpty()) {
                    String deviceSn = nodeRecord.getSerialNumbers().get(0);
                    log.debug("查询到设备序列号，平台节点ID：{}，设备SN：{}", platformNodeId, deviceSn);
                    return deviceSn;
                } else {
                    log.warn("设备序列号列表为空，平台节点ID：{}", platformNodeId);
                    return "";
                }

            } else {
                log.warn("平台查询设备信息失败或返回空结果，平台节点ID：{}", platformNodeId);
                return "";
            }

        } catch (NumberFormatException e) {
            log.error("平台节点ID格式错误，无法转换为Long类型，平台节点ID：{}", platformNodeId, e);
            return "";
        } catch (Exception e) {
            log.error("查询平台设备序列号异常，平台节点ID：{}", platformNodeId, e);
            return "";
        }
    }

    // ==================== 修改调度策略相关方法 ====================

    /**
     * 修改流分类
     */
    private void modifyClassifier(ModifyScheduleRequestDTO request) {
        log.info("开始修改流分类，调度策略ID：{}", request.getAppScheduleId());

        // 1. 查询现有流分类信息
        LambdaQueryWrapper<ScheduleClassifierInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleClassifierInfo::getAppScheduleId, request.getAppScheduleId());
        ScheduleClassifierInfo classifierInfo = scheduleClassifierInfoMapper.selectOne(query);

        if (classifierInfo == null) {
            log.error("未找到流分类信息，调度策略ID：{}", request.getAppScheduleId());
            throw new ServiceException("VALIDATION_ERROR", "未找到流分类信息", "调度策略ID：" + request.getAppScheduleId());
        }

        // 2. 使用查询接口获取现有流分类详情（包含现有的matchList）
            /*String classifierName = request.getAppScheduleName() + ScheduleConstants.NamingRule.CLASSIFIER_NAME_SUFFIX;
            PlatformQosClassifierListRequestDTO listRequest = new PlatformQosClassifierListRequestDTO(classifierName);
            listRequest.setIsWithRule(true); // 查询规则详情
            PlatformQosClassifierListResponseDTO listResponse = platformApiService.getQosClassifierList(listRequest);

            if (listResponse == null || listResponse.getOutput() == null ||
                listResponse.getOutput().getList() == null || listResponse.getOutput().getList().isEmpty()) {
                log.warn("未查询到现有流分类详情，将使用默认配置");
            }*/

        // 3. 根据应用组名称查询应用组信息
        LambdaQueryWrapper<AppGroupCustomInfo> appGroupQuery = new LambdaQueryWrapper<>();
        appGroupQuery.eq(AppGroupCustomInfo::getAppGroupName, request.getAppGroupName());
        AppGroupCustomInfo appGroupInfo = appGroupCustomInfoMapper.selectOne(appGroupQuery);

        if (appGroupInfo == null) {
            log.error("未找到应用组信息，应用组名称：{}", request.getAppGroupName());
            throw new ServiceException("VALIDATION_ERROR", "未找到应用组信息，应用组名称：" + request.getAppGroupName(),
                    "调度策略ID：" + request.getAppScheduleId());
        }

        // 4. 查询应用组下的所有应用
        LambdaQueryWrapper<AppCustomInfo> appQuery = new LambdaQueryWrapper<>();
        appQuery.eq(AppCustomInfo::getAppGroupId, appGroupInfo.getAppGroupId());
        List<AppCustomInfo> appList = appCustomInfoMapper.selectList(appQuery);

        // 5. 重新生成matchList（删除原有matchList，重新生成）
        List<PlatformQosClassifierUpdateRequestDTO.MatchRule> matchList = new ArrayList<>();
        int indexNum = 0;

        for (AppCustomInfo app : appList) {
            List<Integer> aclIdList = JSON.parseArray(app.getAclIdList(), Integer.class);
            if (aclIdList != null) {
                for (Integer aclId : aclIdList) {
                    // 查询ACL详情
                    PlatformAclDetailResponseDTO aclDetail = platformApiService.getAclDetail(aclId);
                    if (aclDetail != null && aclDetail.getOutput() != null) {
                        PlatformQosClassifierUpdateRequestDTO.MatchRule matchRule = new PlatformQosClassifierUpdateRequestDTO.MatchRule();
                        matchRule.setStrValue1(aclDetail.getOutput().getAclName());
                        matchRule.setIntValue2(ScheduleConstants.Classifier.INT_VALUE2_DEFAULT);
                        matchRule.setIndexNum(indexNum++);
                        matchRule.setSelectType(ScheduleConstants.Classifier.SELECT_TYPE_DEFAULT);
                        matchRule.setMatchRelation(ScheduleConstants.Classifier.MATCH_RELATION_DEFAULT);

                        matchList.add(matchRule);
                    }
                }
            }
        }

        // 6. 构建修改流分类请求（保持原有名称不变）
        PlatformQosClassifierUpdateRequestDTO updateRequest = new PlatformQosClassifierUpdateRequestDTO();
        updateRequest.setClassifierId(classifierInfo.getClassifierId());
        updateRequest.setClassifierName(classifierInfo.getClassifierName()); // 保持原有名称不变
        updateRequest.setDescription("");
        updateRequest.setLogic(ScheduleConstants.Classifier.LOGIC_AND);
        updateRequest.setMatchList(matchList);

        // 7. 调用平台接口修改流分类
        platformApiService.updateQosClassifier(updateRequest);
        log.info("修改流分类成功，调度策略ID：{}，流分类ID：{}，名称保持不变：{}，新matchList数量：{}",
                request.getAppScheduleId(), classifierInfo.getClassifierId(), classifierInfo.getClassifierName(), matchList.size());
    }

    /**
     * 修改流行为
     */
    private void modifyBehavior(ModifyScheduleRequestDTO request) {
        log.info("开始修改流行为，调度策略ID：{}", request.getAppScheduleId());

        // 1. 查询现有流行为信息
        LambdaQueryWrapper<ScheduleBehaviorInfo> query = new LambdaQueryWrapper<>();
        query.eq(ScheduleBehaviorInfo::getAppScheduleId, request.getAppScheduleId());
        ScheduleBehaviorInfo behaviorInfo = scheduleBehaviorInfoMapper.selectOne(query);

        if (behaviorInfo == null) {
            log.error("未找到流行为信息，调度策略ID：{}", request.getAppScheduleId());
            throw new ServiceException("VALIDATION_ERROR", "未找到流行为信息", "调度策略ID：" + request.getAppScheduleId());
        }

        // 2. 使用查询接口获取现有流行为详情
        PlatformQosBehaviorDetailRequestDTO detailRequest = new PlatformQosBehaviorDetailRequestDTO(behaviorInfo.getBehaviorId());
        PlatformQosBehaviorDetailResponseDTO detailResponse = platformApiService.getQosBehaviorDetail(detailRequest);

        if (detailResponse == null || detailResponse.getOutput() == null) {
            log.error("查询流行为详情失败，流行为ID：{}", behaviorInfo.getBehaviorId());
            throw new ServiceException("BEHAVIOR_ERROR", "查询流行为详情失败，流行为ID：" + behaviorInfo.getBehaviorId(),
                    "调度策略ID：" + request.getAppScheduleId());
        }

        // 3. 根据隧道组ID重新获取service class和slaId数据，只处理第一个隧道组
        if (request.getNetworkIds() == null || request.getNetworkIds().isEmpty()) {
            log.warn("隧道组ID列表为空");
            throw new ServiceException("VALIDATION_ERROR", "隧道组ID列表为空", "调度策略ID：" + request.getAppScheduleId());
        }

        // 只处理第一个隧道组，参考processPolicy()方法的实现
        Long networkId = request.getNetworkIds().get(0);
        log.info("处理隧道组ID：{}（只处理第一个）", networkId);

        // 查询隧道组信息
        TeGroupCustomInfo teGroupInfo = getTeGroupById(networkId);
        if (teGroupInfo == null) {
            log.error("未找到隧道组信息，隧道组ID：{}", networkId);
            throw new ServiceException("VALIDATION_ERROR", "未找到隧道组信息，隧道组ID：" + networkId,
                    "调度策略ID：" + request.getAppScheduleId());
        }

        // 查询隧道模板信息
        TeTunnelTemplateCustomInfo templateInfo = getTunnelTemplate(networkId);
        if (templateInfo == null) {
            log.error("未找到隧道模板信息，模板ID：{}", teGroupInfo.getScheduleStrategyId());
            throw new ServiceException("VALIDATION_ERROR", "未找到隧道模板信息，模板ID：" + teGroupInfo.getScheduleStrategyId(),
                    "调度策略ID：" + request.getAppScheduleId());
        }

        // 构建remark信息，参考processBehavior方法的generateRemarkList逻辑
        List<PlatformQosBehaviorUpdateRequestDTO.RemarkInfo> remarkList
                = generateRemarkListForUpdate(templateInfo, teGroupInfo, behaviorInfo.getBehaviorId());

        // 4. 基于查询到的现有配置构建修改请求，保持原有配置不变，只修改remarkList（名称保持不变）
        PlatformQosBehaviorUpdateRequestDTO updateRequest = buildBehaviorUpdateRequest(detailResponse.getOutput(), remarkList, behaviorInfo.getBehaviorName());

        // 5. 调用平台接口修改流行为
        platformApiService.updateQosBehavior(updateRequest);
        log.info("修改流行为成功，调度策略ID：{}，流行为ID：{}，名称保持不变：{}",
                request.getAppScheduleId(), behaviorInfo.getBehaviorId(), behaviorInfo.getBehaviorName());
    }

    /**
     * 修改流策略
     */
    private void modifyPolicies(ModifyScheduleRequestDTO request) {
        log.info("开始修改流策略，调度策略ID：{}", request.getAppScheduleId());

        // 1. 查询现有流策略信息
        LambdaQueryWrapper<SchedulePolicyInfo> query = new LambdaQueryWrapper<>();
        query.eq(SchedulePolicyInfo::getAppScheduleId, request.getAppScheduleId());
        List<SchedulePolicyInfo> existingPolicies = schedulePolicyInfoMapper.selectList(query);

        // 2. 获取新的设备完整信息列表（只处理第一个隧道组ID，参考processPolicy()方法）
        if (request.getNetworkIds() == null || request.getNetworkIds().isEmpty()) {
            log.warn("隧道组ID列表为空");
            throw new ServiceException("VALIDATION_ERROR", "隧道组ID列表为空", "调度策略ID：" + request.getAppScheduleId());
        }

        Long networkId = request.getNetworkIds().get(0);
        log.info("处理隧道组ID：{}（只处理第一个）", networkId);

        // 一次性获取完整的设备信息，避免重复查询
        List<DeviceInfo> newDeviceInfos = getDeviceInfosFromSingleNetworkId(networkId);
        Set<String> newDeviceIps = newDeviceInfos.stream()
                .map(DeviceInfo::getDeviceIp)
                .collect(Collectors.toSet());

        // 3. 获取现有设备列表（从策略名称中提取IP）
        Set<String> existingDeviceIps = existingPolicies.stream()
                .map(policy -> extractIpFromPolicyName(policy.getPolicyName()))
                .filter(ip -> ip != null && !ip.isEmpty())
                .collect(Collectors.toSet());

        // 4. 计算设备变化
        Set<String> devicesToRemove = new HashSet<>(existingDeviceIps);
        devicesToRemove.removeAll(newDeviceIps);

        Set<String> devicesToAdd = new HashSet<>(newDeviceIps);
        devicesToAdd.removeAll(existingDeviceIps);

        Set<String> devicesToKeep = new HashSet<>(existingDeviceIps);
        devicesToKeep.retainAll(newDeviceIps);

        log.info("设备变化分析 - 删除：{}，新增：{}，保持：{}",
                devicesToRemove.size(), devicesToAdd.size(), devicesToKeep.size());

        // 5. 处理需要删除的设备策略
        for (String deviceIp : devicesToRemove) {
            handleDevicePolicyRemoval(request.getAppScheduleId(), deviceIp);
        }

        // 6. 处理需要新增的设备策略
        for (String deviceIp : devicesToAdd) {
            // 从完整设备信息中找到对应的设备信息
            DeviceInfo deviceInfo = newDeviceInfos.stream()
                    .filter(info -> deviceIp.equals(info.getDeviceIp()))
                    .findFirst()
                    .orElse(null);
            handleDevicePolicyAddition(request, deviceInfo);
        }

        // 7. 保持不变的设备策略无需处理

        log.info("修改流策略完成，调度策略ID：{}", request.getAppScheduleId());
    }

    /**
     * 从策略名称中提取IP地址
     */
    private String extractIpFromPolicyName(String policyName) {
        if (policyName == null || !policyName.startsWith(ScheduleConstants.NamingRule.POLICY_NAME_PREFIX)) {
            return null;
        }

        return policyName.substring(ScheduleConstants.NamingRule.POLICY_NAME_PREFIX.length());
    }

    /**
     * 处理设备策略删除
     */
    private void handleDevicePolicyRemoval(Long appScheduleId, String deviceIp) {
        log.info("处理设备策略删除，调度策略ID：{}，设备IP：{}", appScheduleId, deviceIp);

        // 查找对应的策略信息
        String policyName = ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp;
        LambdaQueryWrapper<SchedulePolicyInfo> query = new LambdaQueryWrapper<>();
        query.eq(SchedulePolicyInfo::getAppScheduleId, appScheduleId)
                .eq(SchedulePolicyInfo::getPolicyName, policyName);
        SchedulePolicyInfo policyInfo = schedulePolicyInfoMapper.selectOne(query);

        if (policyInfo == null) {
            log.warn("未找到对应的策略信息，调度策略ID：{}，设备IP：{}", appScheduleId, deviceIp);
            return;
        }

        // 查询流策略CB对详情
        PlatformQosPolicyDetailRuleRequestDTO detailRequest = new PlatformQosPolicyDetailRuleRequestDTO(policyInfo.getPolicyId());
        PlatformQosPolicyDetailRuleResponseDTO detailResponse = platformApiService.getQosPolicyRuleDetail(detailRequest);

        if (detailResponse != null && detailResponse.getOutput() != null &&
                detailResponse.getOutput().getCbList() != null) {

            List<PlatformQosPolicyDetailRuleResponseDTO.CbPair> existingCbPairs = detailResponse.getOutput().getCbList();

            // 过滤出剩余的cbpair（移除当前调度策略的cbpair）
            List<PlatformQosPolicyRequestDTO.CbPair> remainingCbPairs = existingCbPairs.stream()
                    .filter(cbPair -> !shouldRemoveCbPair(cbPair, policyInfo.getClassifierId(), policyInfo.getBehaviorId()))
                    .map(this::convertDetailCbPair)
                    .collect(Collectors.toList());

            // 根据剩余cbpair数量决定操作
            if (remainingCbPairs.isEmpty()) {
                // 删除整个流策略
                platformApiService.deleteQosPolicy(policyInfo.getPolicyId());
                log.info("删除流策略成功，策略ID：{}", policyInfo.getPolicyId());
            } else {
                // 更新流策略，移除对应的cbpair
                updatePolicyWithRemainingCbPairs(policyInfo.getPolicyId(), remainingCbPairs, deviceIp);
            }
        }

        // 删除本地记录
        schedulePolicyInfoMapper.deleteById(policyInfo.getId());
    }

    /**
     * 处理设备策略新增
     */
    private void handleDevicePolicyAddition(ModifyScheduleRequestDTO request, DeviceInfo deviceInfo) {
        if (deviceInfo == null) {
            log.error("设备信息为空，无法处理设备策略新增");
            throw new ServiceException("VALIDATION_ERROR", "设备信息为空", "调度策略ID：" + request.getAppScheduleId());
        }

        String deviceIp = deviceInfo.getDeviceIp();
        log.info("处理设备策略新增，调度策略ID：{}，设备IP：{}，设备ID：{}，平台节点ID：{}",
                request.getAppScheduleId(), deviceIp, deviceInfo.getDeviceId(), deviceInfo.getPlatformNodeId());

        // 查询流分类和流行为信息
        LambdaQueryWrapper<ScheduleClassifierInfo> classifierQuery = new LambdaQueryWrapper<>();
        classifierQuery.eq(ScheduleClassifierInfo::getAppScheduleId, request.getAppScheduleId());
        ScheduleClassifierInfo classifierInfo = scheduleClassifierInfoMapper.selectOne(classifierQuery);

        LambdaQueryWrapper<ScheduleBehaviorInfo> behaviorQuery = new LambdaQueryWrapper<>();
        behaviorQuery.eq(ScheduleBehaviorInfo::getAppScheduleId, request.getAppScheduleId());
        ScheduleBehaviorInfo behaviorInfo = scheduleBehaviorInfoMapper.selectOne(behaviorQuery);

        if (classifierInfo == null || behaviorInfo == null) {
            log.error("未找到流分类或流行为信息，调度策略ID：{}", request.getAppScheduleId());
            throw new ServiceException("VALIDATION_ERROR", "未找到流分类或流行为信息",
                    "调度策略ID：" + request.getAppScheduleId() + "，设备IP：" + deviceIp);
        }

        // 检查是否已存在该设备的策略
        String policyName = ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp;

        Integer policyId = null;

        int start = 0;
        int size = 15; // 每页查询15条

        while (true) {
            PlatformQosPolicyListRequestDTO queryRequest = new PlatformQosPolicyListRequestDTO();
            queryRequest.setDesc(true);
            queryRequest.setStart(start);
            queryRequest.setSize(size);

            PlatformQosPolicyListResponseDTO queryResponse = platformApiService.getQosPolicyList(queryRequest);
            if (queryResponse == null || queryResponse.getOutput() == null ||
                    queryResponse.getOutput().getList() == null || queryResponse.getOutput().getList().isEmpty()) {
                break;
            }

            // 精准匹配流策略名称
            PlatformQosPolicyListResponseDTO.PolicyInfo existingPolicy = null;
            for (PlatformQosPolicyListResponseDTO.PolicyInfo policy : queryResponse.getOutput().getList()) {
                if (policyName.equals(policy.getPolicyName())) {
                    existingPolicy = policy;
                    break;
                }
            }

            if (existingPolicy != null) {
                // 策略已存在，需要更新（在原策略的cbpairList中新增记录）
                log.info("流策略{}已存在，需要更新，策略ID：{}", policyName, existingPolicy.getPolicyId());

                policyId = existingPolicy.getPolicyId();
                addCbPairToExistingPolicy(policyId, classifierInfo, behaviorInfo, deviceIp);
                break;
            } else {
                start += size;
            }
        }

        if (policyId == null) {
            log.info("流策略{}不存在，需要创建新策略", policyName);
            policyId = createNewPolicyForDevice(deviceIp, classifierInfo, behaviorInfo, request.getAppScheduleId());
        }

        // 统一保存本地记录
        // 直接使用传入的设备信息，避免重复查询
        Integer deviceId = deviceInfo.getDeviceId();
        String platformNodeId = deviceInfo.getPlatformNodeId();

        log.debug("使用已获取的设备信息，设备IP：{}，设备ID：{}，平台节点ID：{}", deviceIp, deviceId, platformNodeId);

        // 保存策略信息到本地数据库
        saveSchedulePolicyInfo(policyId, classifierInfo, behaviorInfo, request.getAppScheduleId(), deviceId, platformNodeId, deviceIp);

        // 查询是否已存在该策略
        /*PlatformQosPolicyListRequestDTO listRequest = new PlatformQosPolicyListRequestDTO();
        listRequest.setPolicyName(policyName);
        PlatformQosPolicyListResponseDTO listResponse = platformApiService.getQosPolicyList(listRequest);

        Integer policyId;
        if (listResponse != null && listResponse.getOutput() != null &&
                listResponse.getOutput().getList() != null && !listResponse.getOutput().getList().isEmpty()) {

            // 策略已存在，更新策略添加新的cbpair
            policyId = listResponse.getOutput().getList().get(0).getPolicyId();
            addCbPairToExistingPolicy(policyId, classifierInfo, behaviorInfo, deviceIp);

        } else {
            // 策略不存在，创建新策略
            policyId = createNewPolicyForDevice(deviceIp, classifierInfo, behaviorInfo, request.getAppScheduleId());
        }

        // 统一保存本地记录
        // 直接使用传入的设备信息，避免重复查询
        Integer deviceId = deviceInfo.getDeviceId();
        String platformNodeId = deviceInfo.getPlatformNodeId();

        log.debug("使用已获取的设备信息，设备IP：{}，设备ID：{}，平台节点ID：{}", deviceIp, deviceId, platformNodeId);

        // 保存策略信息到本地数据库
        saveSchedulePolicyInfo(policyId, classifierInfo, behaviorInfo, request.getAppScheduleId(), deviceId, platformNodeId, deviceIp);*/
    }

    /**
     * 向现有策略添加cbpair
     */
    private void addCbPairToExistingPolicy(Integer policyId, ScheduleClassifierInfo classifierInfo,
                                           ScheduleBehaviorInfo behaviorInfo, String deviceIp) {
        // 查询现有策略的cbpair
        PlatformQosPolicyDetailRuleRequestDTO detailRequest = new PlatformQosPolicyDetailRuleRequestDTO(policyId);
        PlatformQosPolicyDetailRuleResponseDTO detailResponse = platformApiService.getQosPolicyRuleDetail(detailRequest);

        List<PlatformQosPolicyRequestDTO.CbPair> cbpairList = new ArrayList<>();

        // 添加现有的cbpair
        if (detailResponse != null && detailResponse.getOutput() != null &&
                detailResponse.getOutput().getCbList() != null) {
            cbpairList.addAll(detailResponse.getOutput().getCbList().stream()
                    .map(this::convertDetailCbPair)
                    .collect(Collectors.toList()));
        }

        // 添加新的cbpair
        PlatformQosPolicyRequestDTO.CbPair newCbPair = new PlatformQosPolicyRequestDTO.CbPair();
        newCbPair.setClassifierId(classifierInfo.getClassifierId());
        newCbPair.setClassifierName(classifierInfo.getClassifierName());
        newCbPair.setBehaviorId(behaviorInfo.getBehaviorId());
        newCbPair.setBehaviorName(behaviorInfo.getBehaviorName());
        cbpairList.add(newCbPair);

        // 更新策略
        PlatformQosPolicyUpdateRequestDTO updateRequest = new PlatformQosPolicyUpdateRequestDTO();
        updateRequest.setPolicyId(policyId);
        updateRequest.setPolicyName(ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp);
        updateRequest.setDescription("");
        updateRequest.setCbpairList(cbpairList);

        platformApiService.updateQosPolicy(updateRequest);
        log.info("向现有策略添加cbpair成功，策略ID：{}，设备IP：{}", policyId, deviceIp);
    }

    /**
     * 为设备创建新策略
     * @return 创建的策略ID
     */
    private Integer createNewPolicyForDevice(String deviceIp, ScheduleClassifierInfo classifierInfo,
                                             ScheduleBehaviorInfo behaviorInfo, Long appScheduleId) {
        String policyName = ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp;

        // 构建cbpair
        PlatformQosPolicyRequestDTO.CbPair cbPair = new PlatformQosPolicyRequestDTO.CbPair();
        cbPair.setClassifierId(classifierInfo.getClassifierId());
        cbPair.setClassifierName(classifierInfo.getClassifierName());
        cbPair.setBehaviorId(behaviorInfo.getBehaviorId());
        cbPair.setBehaviorName(behaviorInfo.getBehaviorName());

        // 创建策略请求
        PlatformQosPolicyRequestDTO policyRequest = new PlatformQosPolicyRequestDTO();
        policyRequest.setPolicyName(policyName);
        policyRequest.setDescription("");
        policyRequest.setCbpairList(Collections.singletonList(cbPair));

        // 调用平台接口创建策略
        PlatformQosPolicyResponseDTO policyResponse = platformApiService.addQosPolicy(policyRequest);
        if (policyResponse != null && policyResponse.getOutput() != null) {
            Integer policyId = policyResponse.getOutput().getPolicyId();
            log.info("为设备创建新策略成功，设备IP：{}，策略ID：{}", deviceIp, policyId);
            return policyId; // 返回策略ID，供调用方保存本地记录
        } else {
            log.error("为设备创建新策略失败，设备IP：{}", deviceIp);
            throw new ServiceException("POLICY_ERROR", "为设备创建新策略失败，平台接口返回数据为空",
                    "设备IP：" + deviceIp + "，调度策略ID：" + appScheduleId);
        }
    }

    /**
     * 保存策略信息到本地数据库
     */
    private void saveSchedulePolicyInfo(Integer policyId, ScheduleClassifierInfo classifierInfo,
                                        ScheduleBehaviorInfo behaviorInfo, Long appScheduleId,
                                        Integer deviceId, String platformNodeId, String deviceIp) {
        Date currentTime = new Date();

        SchedulePolicyInfo policyInfo = new SchedulePolicyInfo();
        policyInfo.setAppScheduleId(appScheduleId);
        policyInfo.setPolicyId(policyId);
        policyInfo.setPolicyName(ScheduleConstants.NamingRule.POLICY_NAME_PREFIX + deviceIp);
        policyInfo.setDeviceId(Long.valueOf(deviceId));
        policyInfo.setPlatformNodeId(platformNodeId);
        policyInfo.setDeviceIp(deviceIp);
        policyInfo.setClassifierId(classifierInfo.getClassifierId());
        policyInfo.setBehaviorId(behaviorInfo.getBehaviorId());
        policyInfo.setCreateTime(currentTime);
        policyInfo.setUpdateTime(currentTime);
        policyInfo.setIsDeleted(0);

        schedulePolicyInfoMapper.insert(policyInfo);
        log.debug("保存策略信息成功，策略ID：{}，设备IP：{}，策略名称：{}", policyId, deviceIp, policyInfo.getPolicyName());
    }

    /**
     * 基于现有配置构建流行为更新请求
     *
     * @param existingBehavior 现有流行为配置
     * @param newRemarkList    新的remark列表
     * @param behaviorName     流行为名称（通常保持原有名称不变）
     */
    private PlatformQosBehaviorUpdateRequestDTO buildBehaviorUpdateRequest(
            PlatformQosBehaviorDetailResponseDTO.Output existingBehavior,
            List<PlatformQosBehaviorUpdateRequestDTO.RemarkInfo> newRemarkList,
            String behaviorName) {

        PlatformQosBehaviorUpdateRequestDTO updateRequest = new PlatformQosBehaviorUpdateRequestDTO();
        updateRequest.setBehaviorId(existingBehavior.getBehaviorId());
        updateRequest.setBehaviorName(behaviorName);  // 使用传入的流行为名称
        updateRequest.setDescription(existingBehavior.getDescription());

        // 保持原有配置，转换数据结构
//        updateRequest.setCar(convertCarConfig(existingBehavior.getCar()));
//        updateRequest.setGts(convertGtsConfig(existingBehavior.getGts()));
//        updateRequest.setQueue(convertQueueConfig(existingBehavior.getQueue()));
//        updateRequest.setFilter(convertFilterConfig(existingBehavior.getFilter()));
//        updateRequest.setAccount(convertAccountConfig(existingBehavior.getAccount()));
//        updateRequest.setRedirect(convertRedirectConfig(existingBehavior.getRedirect()));
//        updateRequest.setMirror(convertMirrorConfig(existingBehavior.getMirror()));
//        updateRequest.setPolicy(convertPolicyConfig(existingBehavior.getPolicy()));

        // 使用新的remarkList
        updateRequest.setRemarkList(newRemarkList);

        return updateRequest;
    }

    /**
     * 转换CAR配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.CarConfig convertCarConfig(
            PlatformQosBehaviorDetailResponseDTO.CarConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.CarConfig();
        }
        // 这里可以根据需要进行字段转换，目前两个类结构相同，可以直接使用
        return new PlatformQosBehaviorUpdateRequestDTO.CarConfig();
    }

    /**
     * 转换GTS配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.GtsConfig convertGtsConfig(
            PlatformQosBehaviorDetailResponseDTO.GtsConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.GtsConfig();
        }
        return new PlatformQosBehaviorUpdateRequestDTO.GtsConfig();
    }

    /**
     * 转换队列配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.QueueConfig convertQueueConfig(
            PlatformQosBehaviorDetailResponseDTO.QueueConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.QueueConfig();
        }

        PlatformQosBehaviorUpdateRequestDTO.QueueConfig target = new PlatformQosBehaviorUpdateRequestDTO.QueueConfig();
        target.setBehaviorId(source.getBehaviorId());
        target.setQueueType(source.getQueueType());
        target.setQueueBandWidthUnit(source.getQueueBandWidthUnit());
        target.setQueueBandWidthValue(source.getQueueBandWidthValue());
        target.setQueueCbs(source.getQueueCbs());
        target.setQueuePir(source.getQueuePir());
        target.setQueuePct(source.getQueuePct());
        target.setRemainPct(source.getRemainPct());
        target.setQueueCbsRatio(source.getQueueCbsRatio());
        target.setQueueLength(source.getQueueLength());
        target.setWeight(source.getWeight());

        return target;
    }

    /**
     * 转换过滤器配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.FilterConfig convertFilterConfig(
            PlatformQosBehaviorDetailResponseDTO.FilterConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.FilterConfig();
        }
        return new PlatformQosBehaviorUpdateRequestDTO.FilterConfig();
    }

    /**
     * 转换账户配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.AccountConfig convertAccountConfig(
            PlatformQosBehaviorDetailResponseDTO.AccountConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.AccountConfig();
        }

        PlatformQosBehaviorUpdateRequestDTO.AccountConfig target = new PlatformQosBehaviorUpdateRequestDTO.AccountConfig();
        target.setBehaviorId(source.getBehaviorId());
        target.setByptes(source.getByptes());
        target.setPackets(source.getPackets());

        return target;
    }

    /**
     * 转换重定向配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.RedirectConfig convertRedirectConfig(
            PlatformQosBehaviorDetailResponseDTO.RedirectConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.RedirectConfig();
        }

        PlatformQosBehaviorUpdateRequestDTO.RedirectConfig target = new PlatformQosBehaviorUpdateRequestDTO.RedirectConfig();
        target.setBehaviorId(source.getBehaviorId());
        target.setRedirectType(source.getRedirectType());
        target.setInterfaceName(source.getInterfaceName());
        target.setVlanId(source.getVlanId());
        target.setVsiName(source.getVsiName());
        target.setVrfName(source.getVrfName());
        target.setIpAddress1(source.getIpAddress1());
        target.setIpAddress2(source.getIpAddress2());
        target.setEndPointAddress(source.getEndPointAddress());
        target.setColor(source.getColor());
        target.setPublicSid(source.getPublicSid());
        target.setPrivateSid(source.getPrivateSid());
        target.setAccessVpn(source.getAccessVpn());

        return target;
    }

    /**
     * 转换镜像配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.MirrorConfig convertMirrorConfig(
            PlatformQosBehaviorDetailResponseDTO.MirrorConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.MirrorConfig();
        }

        PlatformQosBehaviorUpdateRequestDTO.MirrorConfig target = new PlatformQosBehaviorUpdateRequestDTO.MirrorConfig();
        target.setBehaviorId(source.getBehaviorId());
        target.setMirrorType(source.getMirrorType());
        target.setInterfaceName(source.getInterfaceName());
        target.setVlanId(source.getVlanId());

        return target;
    }

    /**
     * 转换策略配置
     */
    private PlatformQosBehaviorUpdateRequestDTO.PolicyConfig convertPolicyConfig(
            PlatformQosBehaviorDetailResponseDTO.PolicyConfig source) {
        if (source == null) {
            return new PlatformQosBehaviorUpdateRequestDTO.PolicyConfig();
        }

        PlatformQosBehaviorUpdateRequestDTO.PolicyConfig target = new PlatformQosBehaviorUpdateRequestDTO.PolicyConfig();
        target.setBehaviorId(source.getBehaviorId());
        target.setPolicyName(source.getPolicyName());

        return target;
    }

    /**
     * 生成修改流行为的remark列表，参考processBehavior方法的generateRemarkList逻辑
     */
    private List<PlatformQosBehaviorUpdateRequestDTO.RemarkInfo> generateRemarkListForUpdate(
            TeTunnelTemplateCustomInfo template, TeGroupCustomInfo teGroup, Integer behaviorId) {

        List<PlatformQosBehaviorUpdateRequestDTO.RemarkInfo> remarkList = new ArrayList<>();

        // remarkType=1，使用隧道模板的sla_id转换，参考generateRemarkList方法
        PlatformQosBehaviorUpdateRequestDTO.RemarkInfo remark1 = new PlatformQosBehaviorUpdateRequestDTO.RemarkInfo();
        remark1.setBehaviorId(behaviorId);
        remark1.setRemarkType(ScheduleConstants.RemarkType.SLA_ID_REMARK);
        remark1.setRemarkValue(convertSlaIdToValue(template.getSlaId()));
        remark1.setRemarkColor(null);
        remark1.setRemarkAddress(null);
        remark1.setApnInstance(null);
        remarkList.add(remark1);

        // remarkType=16，使用隧道组的positive_service_class，参考generateRemarkList方法
        PlatformQosBehaviorUpdateRequestDTO.RemarkInfo remark16 = new PlatformQosBehaviorUpdateRequestDTO.RemarkInfo();
        remark16.setBehaviorId(behaviorId);
        remark16.setRemarkType(ScheduleConstants.RemarkType.SERVICE_CLASS_REMARK);
        remark16.setRemarkValue(teGroup.getPositiveServiceClass());
        remark16.setRemarkColor(null);
        remark16.setRemarkAddress(null);
        remark16.setApnInstance(null);
        remarkList.add(remark16);

        // remarkType=17，使用隧道模板的sla_id转换，参考generateRemarkList方法
        PlatformQosBehaviorUpdateRequestDTO.RemarkInfo remark17 = new PlatformQosBehaviorUpdateRequestDTO.RemarkInfo();
        remark17.setBehaviorId(behaviorId);
        remark17.setRemarkType(ScheduleConstants.RemarkType.SLA_ID_REMARK_2);
        remark17.setRemarkValue(convertSlaIdToValue(template.getSlaId()));
        remark17.setRemarkColor(null);
        remark17.setRemarkAddress(null);
        remark17.setApnInstance(null);
        remarkList.add(remark17);

        return remarkList;
    }

    /**
     * 设备信息内部类
     */
    private static class DeviceInfo {
        private Integer deviceId;
        private String platformNodeId;
        private String deviceIp;

        public DeviceInfo(Integer deviceId, String platformNodeId, String deviceIp) {
            this.deviceId = deviceId;
            this.platformNodeId = platformNodeId;
            this.deviceIp = deviceIp;
        }

        public Integer getDeviceId() {
            return deviceId;
        }

        public String getPlatformNodeId() {
            return platformNodeId;
        }

        public String getDeviceIp() {
            return deviceIp;
        }
    }

    /**
     * 根据隧道组ID获取完整的设备信息列表
     * 改造自getDeviceIpsFromSingleNetworkId方法
     */
    private List<DeviceInfo> getDeviceInfosFromSingleNetworkId(Long networkId) {
        List<DeviceInfo> deviceInfos = new ArrayList<>();

        // 使用getTeGroupById方法获取隧道组信息
        TeGroupCustomInfo teGroup = getTeGroupById(networkId);
        if (teGroup == null || teGroup.getTeGroupDcs() == null) {
            log.warn("隧道组{}的作用域信息为空", networkId);
            return deviceInfos;
        }

        try {
            // 解析作用域信息，参考processPolicy()方法
            List<TeGroupDcsDTO> scopeList = JSON.parseArray(teGroup.getTeGroupDcs(), TeGroupDcsDTO.class);

            // 提取所有设备ID
            Set<Integer> deviceIds = extractDeviceIds(scopeList);

            // 为每个设备获取完整信息
            for (Integer deviceId : deviceIds) {
                String platformNodeId = getPlatformNodeIdByDeviceId(deviceId);
                if (platformNodeId != null) {
                    // 使用平台接口获取设备IP，参考processPolicy()方法
                    String deviceIp = getDeviceIpByPlatformNodeId(platformNodeId);
                    if (deviceIp != null) {
                        DeviceInfo deviceInfo = new DeviceInfo(deviceId, platformNodeId, deviceIp);
                        deviceInfos.add(deviceInfo);
                        log.debug("成功获取设备完整信息，设备ID：{}，平台节点ID：{}，设备IP：{}",
                                deviceId, platformNodeId, deviceIp);
                    }
                }
            }

        } catch (Exception e) {
            log.error("解析隧道组作用域数据异常，隧道组ID：{}", networkId, e);
        }

        log.info("从隧道组ID：{}获取到{}个设备信息", networkId, deviceInfos.size());
        return deviceInfos;
    }

}
