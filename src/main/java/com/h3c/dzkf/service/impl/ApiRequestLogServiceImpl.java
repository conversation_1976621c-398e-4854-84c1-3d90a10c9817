package com.h3c.dzkf.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.ApiRequestLogMapper;
import com.h3c.dzkf.entity.ApiRequestLog;
import com.h3c.dzkf.entity.dto.QueryApiRequestLogResponseDTO;
import com.h3c.dzkf.service.ApiRequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

@Slf4j
@Service
public class ApiRequestLogServiceImpl implements ApiRequestLogService {

    @Autowired
    private ApiRequestLogMapper apiRequestLogMapper;

    @Override
    public void saveLog(ApiRequestLog log) {
        apiRequestLogMapper.insert(log);
    }

    @Override
    public QueryApiRequestLogResponseDTO queryApiRequestLogByRequestId(String requestId, String currentRequestId) {
        log.info("开始查询API请求日志，当前请求ID：{}，查询的请求ID：{}", currentRequestId, requestId);

        // 根据requestId查询日志记录
        LambdaQueryWrapper<ApiRequestLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiRequestLog::getRequestId, requestId);
        ApiRequestLog apiRequestLog = apiRequestLogMapper.selectOne(queryWrapper);

        if (apiRequestLog == null) {
            log.warn("API请求日志不存在，当前请求ID：{}，查询的请求ID：{}", currentRequestId, requestId);
            throw new ServiceException("VALIDATION_ERROR", "API请求日志不存在", "请求ID：" + requestId);
        }

        // 构建响应对象
        QueryApiRequestLogResponseDTO response = QueryApiRequestLogResponseDTO.success(currentRequestId);
        response.setId(apiRequestLog.getId());
        response.setRequestId(apiRequestLog.getRequestId());
        response.setRequestUri(apiRequestLog.getRequestUri());
        response.setRequestMethod(apiRequestLog.getRequestMethod());
        response.setRequestBody(apiRequestLog.getRequestBody());
        response.setResponseBody(apiRequestLog.getResponseBody());
        response.setDurationMs(apiRequestLog.getDurationMs());
        response.setClientIp(apiRequestLog.getClientIp());

        // 格式化时间字段
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (apiRequestLog.getRequestTime() != null) {
            response.setRequestTime(dateFormat.format(apiRequestLog.getRequestTime()));
        }
        if (apiRequestLog.getCreateTime() != null) {
            response.setCreateTime(dateFormat.format(apiRequestLog.getCreateTime()));
        }

        log.info("API请求日志查询完成，当前请求ID：{}，查询的请求ID：{}", currentRequestId, requestId);
        return response;
    }
    
    @Override
    public int cleanupOldLogs(int keepDays) {
        try {
            int deletedCount = apiRequestLogMapper.deleteOldLogs(keepDays);
            log.info("API请求日志清理完成，删除了{}条记录，保留近{}天的数据", deletedCount, keepDays);
            return deletedCount;
        } catch (Exception e) {
            log.error("清理API请求日志异常", e);
            throw new RuntimeException("清理API请求日志失败", e);
        }
    }
    
    @Override
    public int cleanupOldLogsOptimized(int keepDays, int batchSize, long maxDeleteCount, long batchDelayMs) {
        long startTime = System.currentTimeMillis();
        int totalDeleted = 0;
        
        try {
            // 先统计需要删除的总数量
            long totalCount = apiRequestLogMapper.countOldLogs(keepDays);
            log.info("开始分批删除API请求日志，共{}条记录需要删除，批次大小：{}，最大删除限制：{}", 
                    totalCount, batchSize, maxDeleteCount);
            
            // 如果超过最大删除限制，则只删除限制数量
            if (totalCount > maxDeleteCount) {
                log.warn("需要删除的记录数({})超过最大限制({})，本次仅删除{}条记录，剩余记录将在下次执行时处理", 
                        totalCount, maxDeleteCount, maxDeleteCount);
                totalCount = maxDeleteCount;
            }
            
            // 如果没有数据需要删除
            if (totalCount == 0) {
                log.info("没有找到需要删除的API请求日志记录");
                return 0;
            }
            
            // 分批删除
            int batchCount = 0;
            while (totalDeleted < totalCount) {
                batchCount++;
                
                // 计算本批次要删除的数量
                int currentBatchSize = (int) Math.min(batchSize, totalCount - totalDeleted);
                
                // 执行删除
                int deleted = apiRequestLogMapper.deleteOldLogsBatch(keepDays, currentBatchSize);
                totalDeleted += deleted;
                
                log.debug("第{}批删除完成，本批删除{}条，累计删除{}条", batchCount, deleted, totalDeleted);
                
                // 如果本批次没有删除任何记录，说明已经删除完毕
                if (deleted == 0) {
                    break;
                }
                
                // 批次间延迟，避免对数据库造成持续压力
                if (batchDelayMs > 0 && totalDeleted < totalCount) {
                    try {
                        Thread.sleep(batchDelayMs);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("批次间延迟被中断");
                        break;
                    }
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("API请求日志分批删除完成，共删除{}条记录，分{}批执行，总耗时{}ms，保留近{}天的数据", 
                    totalDeleted, batchCount, (endTime - startTime), keepDays);
            
            return totalDeleted;
            
        } catch (Exception e) {
            log.error("分批清理API请求日志异常，已删除{}条记录", totalDeleted, e);
            throw new RuntimeException("分批清理API请求日志失败", e);
        }
    }
} 