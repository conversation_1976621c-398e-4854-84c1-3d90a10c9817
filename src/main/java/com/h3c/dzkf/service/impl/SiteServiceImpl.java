package com.h3c.dzkf.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.DeviceCustomInfoMapper;
import com.h3c.dzkf.dao.SiteCustomInfoMapper;
import com.h3c.dzkf.entity.DeviceCustomInfo;
import com.h3c.dzkf.entity.SiteCustomInfo;
import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.service.SiteService;
import com.h3c.dzkf.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 站点服务实现类
 */
@Slf4j
@Service
public class SiteServiceImpl implements SiteService {

    @Autowired
    private SiteCustomInfoMapper siteCustomInfoMapper;

    @Autowired
    private DeviceCustomInfoMapper deviceCustomInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSite(AddSiteRequestDTO request, String requestId) {
        log.info("开始新增站点，请求ID：{}，站点ID：{}，站点名称：{}", requestId, request.getSiteId(), request.getSiteName());

        // 1. 检查站点ID是否已存在
        LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteCustomInfo::getSiteId, request.getSiteId());
        SiteCustomInfo existingSite = siteCustomInfoMapper.selectOne(queryWrapper);
        if (existingSite != null) {
            log.warn("站点ID已存在，请求ID：{}，站点ID：{}", requestId, request.getSiteId());
            throw new ServiceException("SITE_ID_EXISTS", "站点ID已存在", "站点ID: " + request.getSiteId());
        }

        // 2. 如果有父级站点ID，检查父级站点是否存在
        if (request.getParentSite() != null) {
            LambdaQueryWrapper<SiteCustomInfo> parentQueryWrapper = new LambdaQueryWrapper<>();
            parentQueryWrapper.eq(SiteCustomInfo::getSiteId, request.getParentSite());
            SiteCustomInfo parentSite = siteCustomInfoMapper.selectOne(parentQueryWrapper);
            if (parentSite == null) {
                log.warn("父级站点不存在，请求ID：{}，父级站点ID：{}", requestId, request.getParentSite());
                throw new ServiceException("PARENT_SITE_NOT_EXISTS", "父级站点不存在", "父级站点ID: " + request.getParentSite());
            }
        }

        // 3. 构建站点信息对象
        SiteCustomInfo siteCustomInfo = new SiteCustomInfo();
        siteCustomInfo.setSiteId(request.getSiteId());
        siteCustomInfo.setSiteName(request.getSiteName());
        siteCustomInfo.setParentSiteId(request.getParentSite());
        siteCustomInfo.setLocation(request.getLocation());
        siteCustomInfo.setSiteType(request.getSiteType());
        siteCustomInfo.setCreateTime(new Date());
        siteCustomInfo.setUpdateTime(new Date());
        siteCustomInfo.setIsDeleted(0);

        // 4. 保存到数据库
        int result = siteCustomInfoMapper.insert(siteCustomInfo);
        if (result <= 0) {
            log.error("新增站点失败，请求ID：{}，站点ID：{}", requestId, request.getSiteId());
            throw new ServiceException("SITE_INSERT_FAILED", "站点新增失败", "站点ID: " + request.getSiteId());
        }

        log.info("新增站点成功，请求ID：{}，站点ID：{}，数据库ID：{}", requestId, request.getSiteId(), siteCustomInfo.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSite(Integer siteId, String requestId) {
        log.info("开始删除站点，请求ID：{}，站点ID：{}", requestId, siteId);

        // 1. 检查站点是否存在且未被删除
        LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteCustomInfo::getSiteId, siteId);
        SiteCustomInfo existingSite = siteCustomInfoMapper.selectOne(queryWrapper);
        if (existingSite == null) {
            log.warn("站点不存在或已被删除，请求ID：{}，站点ID：{}", requestId, siteId);
            throw new ServiceException("SITE_NOT_EXISTS", "站点不存在或已被删除", "站点ID: " + siteId);
        }

        // 2. 检查是否有未删除的设备关联到该站点
        LambdaQueryWrapper<DeviceCustomInfo> deviceQueryWrapper = new LambdaQueryWrapper<>();
        deviceQueryWrapper.eq(DeviceCustomInfo::getDeviceSiteId, siteId);
        List<DeviceCustomInfo> relatedDevices = deviceCustomInfoMapper.selectList(deviceQueryWrapper);

        if (!relatedDevices.isEmpty()) {
            log.warn("站点下存在关联设备，无法删除，请求ID：{}，站点ID：{}，关联设备数量：{}",
                    requestId, siteId, relatedDevices.size());
            throw new ServiceException("SITE_HAS_DEVICES", "站点下存在关联设备，无法删除",
                    "站点ID: " + siteId + ", 关联设备数量: " + relatedDevices.size());
        }

        // 3. 检查是否有未删除的子站点关联到该站点
        LambdaQueryWrapper<SiteCustomInfo> childSiteQueryWrapper = new LambdaQueryWrapper<>();
        childSiteQueryWrapper.eq(SiteCustomInfo::getParentSiteId, siteId);
        List<SiteCustomInfo> childSites = siteCustomInfoMapper.selectList(childSiteQueryWrapper);

        if (!childSites.isEmpty()) {
            log.warn("站点下存在子站点，无法删除，请求ID：{}，站点ID：{}，子站点数量：{}",
                    requestId, siteId, childSites.size());
            throw new ServiceException("SITE_HAS_CHILD_SITES", "站点下存在子站点，无法删除",
                    "站点ID: " + siteId + ", 子站点数量: " + childSites.size());
        }

        // 4. 执行软删除
        int result = siteCustomInfoMapper.deleteById(existingSite.getId());
        if (result <= 0) {
            log.error("删除站点失败，请求ID：{}，站点ID：{}", requestId, siteId);
            throw new ServiceException("SITE_DELETE_FAILED", "站点删除失败", "站点ID: " + siteId);
        }

        log.info("删除站点成功，请求ID：{}，站点ID：{}，数据库ID：{}", requestId, siteId, existingSite.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSite(UpdateSiteRequestDTO request, String requestId) {
        log.info("开始修改站点，请求ID：{}，站点ID：{}，站点名称：{}", requestId, request.getSiteId(), request.getSiteName());

        // 1. 检查站点是否存在且未被删除
        LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SiteCustomInfo::getSiteId, request.getSiteId());
        SiteCustomInfo existingSite = siteCustomInfoMapper.selectOne(queryWrapper);
        if (existingSite == null) {
            log.warn("站点不存在或已被删除，请求ID：{}，站点ID：{}", requestId, request.getSiteId());
            throw new ServiceException("SITE_NOT_EXISTS", "站点不存在或已被删除", "站点ID: " + request.getSiteId());
        }

        // 获取原站点名称，用于判断是否需要同步更新设备表
        String originalSiteName = existingSite.getSiteName();
        boolean siteNameChanged = !request.getSiteName().equals(originalSiteName);

        // 2. 如果有父级站点ID，检查父级站点是否存在且不是自己
        if (request.getParentSite() != null) {
            // 检查是否设置自己为父级站点
            if (request.getParentSite().equals(request.getSiteId())) {
                log.warn("不能设置自己为父级站点，请求ID：{}，站点ID：{}", requestId, request.getSiteId());
                throw new ServiceException("SITE_SELF_PARENT", "不能设置自己为父级站点", "站点ID: " + request.getSiteId());
            }

            // 检查父级站点是否存在
            LambdaQueryWrapper<SiteCustomInfo> parentQueryWrapper = new LambdaQueryWrapper<>();
            parentQueryWrapper.eq(SiteCustomInfo::getSiteId, request.getParentSite());
            SiteCustomInfo parentSite = siteCustomInfoMapper.selectOne(parentQueryWrapper);
            if (parentSite == null) {
                log.warn("父级站点不存在或已被删除，请求ID：{}，父级站点ID：{}", requestId, request.getParentSite());
                throw new ServiceException("PARENT_SITE_NOT_EXISTS", "父级站点不存在或已被删除", "父级站点ID: " + request.getParentSite());
            }

            // 检查循环引用：确保父级站点的父级不是当前站点
            if (parentSite.getParentSiteId() != null && parentSite.getParentSiteId().equals(request.getSiteId())) {
                log.warn("检测到循环引用，请求ID：{}，站点ID：{}，父级站点ID：{}", requestId, request.getSiteId(), request.getParentSite());
                throw new ServiceException("SITE_CIRCULAR_REFERENCE", "不能设置循环引用的父级站点",
                        "站点ID: " + request.getSiteId() + ", 父级站点ID: " + request.getParentSite());
            }
        }

        // 3. 更新站点信息
        existingSite.setSiteName(request.getSiteName());
        existingSite.setParentSiteId(request.getParentSite());
        existingSite.setLocation(request.getLocation());
        existingSite.setSiteType(request.getSiteType());
        existingSite.setUpdateTime(new Date());

        // 4. 保存到数据库
        int result = siteCustomInfoMapper.updateById(existingSite);
        if (result <= 0) {
            log.error("修改站点失败，请求ID：{}，站点ID：{}", requestId, request.getSiteId());
            throw new ServiceException("SITE_UPDATE_FAILED", "站点修改失败", "站点ID: " + request.getSiteId());
        }

        log.info("修改站点成功，请求ID：{}，站点ID：{}，数据库ID：{}", requestId, request.getSiteId(), existingSite.getId());

        // 5. 如果站点名称发生变化，同步更新设备定制表中的站点名称
        if (siteNameChanged) {
            updateDeviceSiteName(request.getSiteId(), request.getSiteName(), requestId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchImportSiteResponseDTO importSiteTemplate(MultipartFile file, String requestId) {
        log.info("开始批量导入站点模板，请求ID：{}，文件名：{}", requestId, file.getOriginalFilename());

        // 1. 验证文件格式
        if (file.isEmpty()) {
            throw new ServiceException("FILE_EMPTY", "上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
            throw new ServiceException("FILE_FORMAT_ERROR", "文件格式错误，请上传Excel文件(.xlsx)");
        }

        // 2. 解析Excel文件
        List<BatchImportSiteRequestDTO> siteList;
        try {
            siteList = ExcelUtil.parseSiteTemplateExcel(file);
            log.info("Excel解析成功，共解析到{}条站点数据", siteList.size());
        } catch (Exception e) {
            log.error("解析Excel文件失败，请求ID：{}", requestId, e);
            throw new ServiceException("EXCEL_PARSE_ERROR", "Excel文件解析失败：" + e.getMessage(), e);
        }

        if (siteList.isEmpty()) {
            throw new ServiceException("EXCEL_NO_DATA", "Excel文件中没有有效的站点数据");
        }

        // 3. 统计信息
        int totalNum = siteList.size();
        int succNum = 0;
        int failNum = 0;
        List<BatchImportSiteResponseDTO.AddSiteResult> addSiteResults = new ArrayList<>();

        // 4. 逐个处理站点
        for (BatchImportSiteRequestDTO batchSite : siteList) {
            BatchImportSiteResponseDTO.AddSiteResult result = new BatchImportSiteResponseDTO.AddSiteResult();
            result.setSiteName(batchSite.getSiteName());

            try {
                // 验证必填字段
                String validationError = validateBatchSite(batchSite);
                if (validationError != null) {
                    result.setStatus(0); // 失败
                    result.setErrorMsg(validationError);
                    failNum++;
                    addSiteResults.add(result);
                    continue;
                }

                // 转换为AddSiteRequestDTO
                AddSiteRequestDTO addRequest = convertToAddSiteRequest(batchSite);

                // 验证父级站点转换结果：如果所属站点字段不为空但转换后为null，说明所属站点不存在
                if (StrUtil.isNotBlank(batchSite.getParentSite()) && addRequest.getParentSite() == null) {
                    result.setStatus(0); // 失败
                    result.setErrorMsg("所属站点不存在");
                    failNum++;
                    addSiteResults.add(result);
                    continue;
                }

                // 调用单个站点新增逻辑
                addSite(addRequest, requestId + "_" + batchSite.getRowIndex());
                result.setStatus(1); // 成功
                succNum++;

            } catch (Exception e) {
                log.error("处理站点失败，行号：{}，站点名称：{}", batchSite.getRowIndex(), batchSite.getSiteName(), e);
                result.setStatus(0); // 失败
                result.setErrorMsg("站点处理失败：" + e.getMessage());
                failNum++;
            }

            addSiteResults.add(result);
        }

        // 5. 构造响应结果
        BatchImportSiteResponseDTO.OptionField optionField = new BatchImportSiteResponseDTO.OptionField();
        optionField.setTotalNum(totalNum);
        optionField.setSuccNum(succNum);
        optionField.setFailNum(failNum);
        optionField.setAddSiteResults(addSiteResults);

        log.info("批量导入站点完成，请求ID：{}，总数：{}，成功：{}，失败：{}", requestId, totalNum, succNum, failNum);

        return BatchImportSiteResponseDTO.success(requestId, optionField);
    }
    

    
    /**
     * 验证批量导入站点数据
     */
    private String validateBatchSite(BatchImportSiteRequestDTO site) {
        if (site.getSiteId() == null) {
            return "第" + site.getRowIndex() + "行：站点ID不能为空";
        }
        if (StrUtil.isBlank(site.getSiteName())) {
            return "第" + site.getRowIndex() + "行：站点名称不能为空";
        }
        if (StrUtil.isBlank(site.getLocation())) {
            return "第" + site.getRowIndex() + "行：站点位置不能为空";
        }
        if (StrUtil.isBlank(site.getSiteType())) {
            return "第" + site.getRowIndex() + "行：站点类型不能为空";
        }
        return null;
    }
    
    /**
     * 将批量导入DTO转换为新增站点DTO
     */
    private AddSiteRequestDTO convertToAddSiteRequest(BatchImportSiteRequestDTO batchSite) {
        AddSiteRequestDTO addRequest = new AddSiteRequestDTO();
        addRequest.setSiteId(batchSite.getSiteId());
        addRequest.setSiteName(batchSite.getSiteName());
        addRequest.setLocation(batchSite.getLocation());
        addRequest.setSiteType(batchSite.getSiteType());
        
        // 处理父级站点信息
        if (StrUtil.isNotBlank(batchSite.getParentSite())) {
            String parentSiteName = batchSite.getParentSite().trim();
            
            // 根据站点名称查询站点ID
            LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SiteCustomInfo::getSiteName, parentSiteName)
                       .select(SiteCustomInfo::getSiteId);
            
            SiteCustomInfo parentSiteCustomInfo = siteCustomInfoMapper.selectOne(queryWrapper);
            Integer parentSiteId = parentSiteCustomInfo != null ? parentSiteCustomInfo.getSiteId() : null;
            
            if (parentSiteId != null) {
                addRequest.setParentSite(parentSiteId);
                log.info("第{}行：根据站点名称'{}' 查询到父级站点ID：{}", batchSite.getRowIndex(), parentSiteName, parentSiteId);
            } else {
                log.warn("第{}行：未找到名为'{}' 的父级站点", batchSite.getRowIndex(), parentSiteName);
                addRequest.setParentSite(null);
            }
        }
        
        return addRequest;
    }

    @Override
    public QuerySiteInfoListResponseDTO querySiteInfoList(String requestId) {
        log.info("开始查询站点信息列表，请求ID：{}", requestId);

        // 查询所有未删除的站点
        LambdaQueryWrapper<SiteCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(SiteCustomInfo::getSiteId); // 按站点ID升序排列

        List<SiteCustomInfo> siteCustomInfoList = siteCustomInfoMapper.selectList(queryWrapper);

        // 转换为响应DTO
        List<QuerySiteInfoListResponseDTO.SiteInfo> siteList = new ArrayList<>();
        for (SiteCustomInfo siteCustomInfo : siteCustomInfoList) {
            QuerySiteInfoListResponseDTO.SiteInfo siteInfoDTO = new QuerySiteInfoListResponseDTO.SiteInfo();
            siteInfoDTO.setSiteId(siteCustomInfo.getSiteId());
            siteInfoDTO.setSiteName(siteCustomInfo.getSiteName());
            siteInfoDTO.setParentSite(siteCustomInfo.getParentSiteId());
            siteInfoDTO.setLocation(siteCustomInfo.getLocation());
            siteInfoDTO.setSiteType(siteCustomInfo.getSiteType());
            siteList.add(siteInfoDTO);
        }

        log.info("查询站点信息列表完成，请求ID：{}，共查询到{}个站点", requestId, siteList.size());
        return QuerySiteInfoListResponseDTO.success(requestId, siteList);
    }

    /**
     * 同步更新设备定制表中的站点名称
     *
     * @param siteId 站点ID
     * @param newSiteName 新的站点名称
     * @param requestId 请求ID
     */
    private void updateDeviceSiteName(Integer siteId, String newSiteName, String requestId) {
        log.info("开始同步更新设备表中的站点名称，请求ID：{}，站点ID：{}，新站点名称：{}", requestId, siteId, newSiteName);

        // 查询该站点下的所有未删除设备
        LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceCustomInfo::getDeviceSiteId, siteId);
        List<DeviceCustomInfo> devices = deviceCustomInfoMapper.selectList(queryWrapper);

        if (devices.isEmpty()) {
            log.info("站点下没有关联设备，无需更新，请求ID：{}，站点ID：{}", requestId, siteId);
            return;
        }

        // 批量更新设备表中的站点名称
        LambdaUpdateWrapper<DeviceCustomInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DeviceCustomInfo::getDeviceSiteId, siteId)
                .set(DeviceCustomInfo::getDeviceSite, newSiteName)
                .set(DeviceCustomInfo::getUpdateTime, new Date());

        int updatedCount = deviceCustomInfoMapper.update(null, updateWrapper);
        if (updatedCount <= 0) {
            log.error("同步更新设备表站点名称失败，请求ID：{}，站点ID：{}，新站点名称：{}", requestId, siteId, newSiteName);
            throw new ServiceException("DEVICE_SITE_NAME_UPDATE_FAILED", "同步更新设备表站点名称失败",
                    "站点ID: " + siteId + ", 新站点名称: " + newSiteName);
        }

        log.info("同步更新设备表站点名称完成，请求ID：{}，站点ID：{}，更新设备数量：{}", requestId, siteId, updatedCount);
    }

} 