package com.h3c.dzkf.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.h3c.dzkf.common.exceptions.ServiceException;
import com.h3c.dzkf.dao.AppAclRelationMapper;
import com.h3c.dzkf.dao.AppCustomInfoMapper;
import com.h3c.dzkf.dao.AppGroupCustomInfoMapper;
import com.h3c.dzkf.entity.AppAclRelation;
import com.h3c.dzkf.entity.AppCustomInfo;
import com.h3c.dzkf.entity.AppGroupCustomInfo;
import com.h3c.dzkf.entity.dto.*;
import com.h3c.dzkf.entity.platform.PlatformAclRequestDTO;
import com.h3c.dzkf.entity.platform.PlatformAclResponseDTO;
import com.h3c.dzkf.entity.platform.QueryAclTemplateRequestDTO;
import com.h3c.dzkf.entity.platform.QueryAclTemplateResponseDTO;
import com.h3c.dzkf.service.AppService;
import com.h3c.dzkf.service.PlatformApiService;
import com.h3c.dzkf.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用服务实现类
 */
@Slf4j
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private AppCustomInfoMapper appCustomInfoMapper;

    @Autowired
    private AppGroupCustomInfoMapper appGroupCustomInfoMapper;

    @Autowired
    private AppAclRelationMapper appAclRelationMapper;

    @Autowired
    private PlatformApiService platformApiService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addApp(AddAppRequestDTO request, String requestId) {
        log.info("开始处理新增应用请求，请求ID：{}，应用ID：{}", requestId, request.getAppId());

        // 1. 参数验证
        validateRequest(request, requestId);

        // 2. 检查应用ID是否已存在
        LambdaQueryWrapper<AppCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCustomInfo::getAppId, request.getAppId());
        AppCustomInfo existingApp = appCustomInfoMapper.selectOne(queryWrapper);
        if (existingApp != null) {
            log.warn("应用ID已存在，请求ID：{}，应用ID：{}", requestId, request.getAppId());
            throw new ServiceException("VALIDATION_ERROR", "应用ID已存在", "应用ID：" + request.getAppId());
        }

        // 3. 查询应用组信息
        LambdaQueryWrapper<AppGroupCustomInfo> groupQueryWrapper = new LambdaQueryWrapper<>();
        groupQueryWrapper.eq(AppGroupCustomInfo::getAppGroupName, request.getAppGroupName());
        AppGroupCustomInfo appGroup = appGroupCustomInfoMapper.selectOne(groupQueryWrapper);
        if (appGroup == null) {
            log.warn("应用分组不存在，请求ID：{}，应用分组：{}", requestId, request.getAppGroupName());
            throw new ServiceException("VALIDATION_ERROR", "指定的应用分组不存在", "应用分组：" + request.getAppGroupName());
        }

        // 4. 先保存应用信息到数据库，获得主键ID
        AppCustomInfo appCustomInfo = buildAppCustomInfo(request, Long.valueOf(appGroup.getAppGroupId()), new ArrayList<>());
        int insertResult = appCustomInfoMapper.insert(appCustomInfo);
        if (insertResult <= 0) {
            log.error("保存应用信息失败，请求ID：{}，应用ID：{}", requestId, request.getAppId());
            throw new ServiceException("DATABASE_ERROR", "保存应用信息失败", "应用ID：" + request.getAppId());
        }

        // 5. 使用生成的主键创建ACL模板
        List<Integer> aclIdList = createAclTemplates(request, requestId, appCustomInfo.getId());
        if (aclIdList.isEmpty()) {
            log.error("创建ACL模板失败，请求ID：{}，应用ID：{}", requestId, request.getAppId());
            // 回滚：删除已保存的应用信息
            appCustomInfoMapper.deleteById(appCustomInfo.getId());
            throw new ServiceException("PLATFORM_ERROR", "创建ACL模板失败", "应用ID：" + request.getAppId());
        }

        // 6. 更新应用信息中的ACL ID列表
        appCustomInfo.setAclIdList(JSON.toJSONString(aclIdList));
        appCustomInfo.setUpdateTime(new Date());
        appCustomInfoMapper.updateById(appCustomInfo);

        log.info("新增应用成功，请求ID：{}，应用ID：{}，生成的ACL模板ID列表：{}",
                requestId, request.getAppId(), aclIdList);
    }

    /**
     * 验证请求参数
     */
    private void validateRequest(AddAppRequestDTO request, String requestId) {
        // 验证应用类型
        if (!"五元组".equals(request.getAppType())) {
            log.warn("应用类型不支持，请求ID：{}，应用类型：{}", requestId, request.getAppType());
            throw new ServiceException("VALIDATION_ERROR", "应用类型仅支持'五元组'", "应用类型：" + request.getAppType());
        }

        // 验证协议类型
        if (!isValidProtocol(request.getAgreementType())) {
            log.warn("协议类型不支持，请求ID：{}，协议类型：{}", requestId, request.getAgreementType());
            throw new ServiceException("VALIDATION_ERROR", "不支持的协议类型", "协议类型：" + request.getAgreementType());
        }

        // 验证IPv4和IPv6参数的二选一逻辑
        validateIpParameters(request, requestId);
    }

    /**
     * 验证IPv4和IPv6参数的二选一逻辑（新增应用）
     */
    private void validateIpParameters(AddAppRequestDTO request, String requestId) {
        validateIpParametersCommon(
                request.getSourceIP(), request.getSourcePort(), request.getDestinationIPList(),
                request.getSourceIPv6(), request.getSourcePortIPv6(), request.getDestinationIPv6List(),
                requestId
        );
    }

    /**
     * 验证IPv4和IPv6参数的二选一逻辑（修改应用）
     */
    private void validateIpParameters(ModifyAppRequestDTO request, String requestId) {
        validateIpParametersCommon(
                request.getSourceIP(), request.getSourcePort(), request.getDestinationIPList(),
                request.getSourceIPv6(), request.getSourcePortIPv6(), request.getDestinationIPv6List(),
                requestId
        );
    }

    /**
     * 验证IPv4和IPv6参数的二选一逻辑（通用方法）
     */
    private void validateIpParametersCommon(
            String sourceIP, String sourcePort, List<String> destinationIPList,
            String sourceIPv6, String sourcePortIPv6, List<String> destinationIPv6List,
            String requestId) {
        // 检查IPv4参数是否完整
        boolean hasIpv4 = StringUtils.hasText(sourceIP) ||
                StringUtils.hasText(sourcePort) ||
                (destinationIPList != null && !destinationIPList.isEmpty());

        // 检查IPv6参数是否完整
        boolean hasIpv6 = StringUtils.hasText(sourceIPv6) ||
                StringUtils.hasText(sourcePortIPv6) ||
                (destinationIPv6List != null && !destinationIPv6List.isEmpty());

        // 必须至少提供一组参数
        if (!hasIpv4 && !hasIpv6) {
            log.warn("IPv4和IPv6参数都未提供，请求ID：{}", requestId);
            throw new ServiceException("VALIDATION_ERROR", "必须提供IPv4参数或IPv6参数中的至少一组", "请求ID：" + requestId);
        }

        // 验证IPv4参数的完整性
        if (hasIpv4) {
            if (!StringUtils.hasText(sourceIP)) {
                log.warn("IPv4源IP地址不能为空，请求ID：{}", requestId);
                throw new ServiceException("VALIDATION_ERROR", "提供IPv4参数时，源IP地址不能为空", "请求ID：" + requestId);
            }
            if (!StringUtils.hasText(sourcePort)) {
                log.warn("IPv4源端口不能为空，请求ID：{}", requestId);
                throw new ServiceException("VALIDATION_ERROR", "提供IPv4参数时，源端口不能为空", "请求ID：" + requestId);
            }
            if (destinationIPList == null || destinationIPList.isEmpty()) {
                log.warn("IPv4目的IP及端口列表不能为空，请求ID：{}", requestId);
                throw new ServiceException("VALIDATION_ERROR", "提供IPv4参数时，目的IP及端口列表不能为空", "请求ID：" + requestId);
            }

            // 验证IPv4目的IP列表格式（支持ip、ip&、ip&port三种格式）
            for (String destIpPort : destinationIPList) {
                if (StringUtils.isEmpty(destIpPort.trim())) {
                    log.warn("IPv4目的IP及端口不能为空，请求ID：{}，格式：{}", requestId, destIpPort);
                    throw new ServiceException("VALIDATION_ERROR", "IPv4目的IP及端口不能为空", "格式：" + destIpPort);
                }
            }
        }

        // 验证IPv6参数的完整性
        if (hasIpv6) {
            if (!StringUtils.hasText(sourceIPv6)) {
                log.warn("IPv6源IP地址不能为空，请求ID：{}", requestId);
                throw new ServiceException("VALIDATION_ERROR", "提供IPv6参数时，源IP地址不能为空", "请求ID：" + requestId);
            }
            if (destinationIPv6List == null || destinationIPv6List.isEmpty()) {
                log.warn("IPv6目的IP及端口列表不能为空，请求ID：{}", requestId);
                throw new ServiceException("VALIDATION_ERROR", "提供IPv6参数时，目的IP及端口列表不能为空", "请求ID：" + requestId);
            }

            // 验证IPv6目的IP列表格式（支持ip、ip&、ip&port三种格式）
            for (String destIpPort : destinationIPv6List) {
                if (StringUtils.isEmpty(destIpPort.trim())) {
                    log.warn("IPv6目的IP及端口不能为空，请求ID：{}，格式：{}", requestId, destIpPort);
                    throw new ServiceException("VALIDATION_ERROR", "IPv6目的IP及端口不能为空", "格式：" + destIpPort);
                }
            }
        }
    }

    /**
     * 验证协议类型是否有效
     */
    private boolean isValidProtocol(String protocol) {
        Set<String> validProtocols = new HashSet<>(Arrays.asList("TCP", "UDP", "ICMP", "IGMP", "IPINIP", "GRE", "OSPF", "IP"));
        return validProtocols.contains(protocol.toUpperCase());
    }

    /**
     * 创建ACL模板
     */
    private List<Integer> createAclTemplates(AddAppRequestDTO request, String requestId, Long appCustomId) {
        List<Integer> aclIdList = new ArrayList<>();

        // IPv4 ACL模板
        if (StringUtils.hasText(request.getSourceIP()) && request.getDestinationIPList() != null && !request.getDestinationIPList().isEmpty()) {
            List<Integer> ipv4AclIds = createAclTemplatesForIpVersion(request, requestId, 4, appCustomId);
            aclIdList.addAll(ipv4AclIds);
        }

        // IPv6 ACL模板
        if (StringUtils.hasText(request.getSourceIPv6()) && request.getDestinationIPv6List() != null && !request.getDestinationIPv6List().isEmpty()) {
            List<Integer> ipv6AclIds = createAclTemplatesForIpVersion(request, requestId, 6, appCustomId);
            aclIdList.addAll(ipv6AclIds);
        }

        return aclIdList;
    }

    /**
     * 为指定IP版本创建ACL模板
     */
    private List<Integer> createAclTemplatesForIpVersion(AddAppRequestDTO request, String requestId, int ipVersion, Long appCustomId) {
        List<Integer> aclIds = new ArrayList<>();
        
        String sourceIp = ipVersion == 4 ? request.getSourceIP() : request.getSourceIPv6();
        String sourcePort = ipVersion == 4 ? request.getSourcePort() : request.getSourcePortIPv6();
        List<String> destinationList = ipVersion == 4 ? request.getDestinationIPList() : request.getDestinationIPv6List();

        // 构建ACL模板请求（一个IP版本对应一个ACL模板，包含多个目标规则）
        PlatformAclRequestDTO aclRequest = buildAclRequestForVersion(request, sourceIp, sourcePort, destinationList, ipVersion);
        
        // 调用平台接口创建ACL模板
        PlatformAclResponseDTO response = platformApiService.addAclTemplate(aclRequest);

        log.info("平台ACL接口返回结果：{}", JSON.toJSONString(response));

        if (response != null && response.getSuccessful() != null && response.getSuccessful()) {
            // ACL模板创建成功，通过名称查询获取ACL ID
            String aclName = aclRequest.getAclName();
            Integer aclId = getAclIdByName(aclName);

            if (aclId != null) {
                aclIds.add(aclId);
                log.info("成功创建ACL模板，请求ID：{}，ACL ID：{}，ACL名称：{}，IP版本：{}，目标IP数量：{}",
                        requestId, aclId, aclName, ipVersion, destinationList.size());

                // 保存ACL与目标IP的关系记录（直接使用生成的appCustomId）
                saveAclRelationRecords(request.getAppId(), appCustomId, aclId, ipVersion, destinationList);

            } else {
                log.error("ACL模板创建成功但无法获取ACL ID，请求ID：{}，ACL名称：{}", requestId, aclName);
            }
        } else {
            log.error("创建ACL模板失败，请求ID：{}，响应：{}", requestId, JSON.toJSONString(response));
        }

        return aclIds;
    }

    /**
     * 构建指定IP版本的ACL模板请求（包含多个目标规则）
     */
    private PlatformAclRequestDTO buildAclRequestForVersion(AddAppRequestDTO request, String sourceIp, String sourcePort, 
                                                           List<String> destinationList, int ipVersion) {
        PlatformAclRequestDTO aclRequest = new PlatformAclRequestDTO();
        
        // ACL基本信息 - 根据IP版本添加对应后缀
        String aclName = "acl_" + request.getAppId() + (ipVersion == 4 ? "_ipv4" : "_ipv6");
        aclRequest.setAclName(aclName);
        aclRequest.setGroupType(ipVersion == 4 ? 1 : 2); // 1：IPv4ACL 2：IPv6ACL
        aclRequest.setIdType(1); // 1：名称标志 2：数字标识，默认1
        aclRequest.setIdValue(generateRandomIdValue()); // ACL标识值 默认应用分组名称
        aclRequest.setMatchOrder(1); // 匹配次序，1：配置 2：自动，默认1
        aclRequest.setStep(5); // 步长，默认为5

        // 构建匹配规则列表（每个目标IP一个规则）
        List<PlatformAclRequestDTO.AclMatchRule> matchRules = new ArrayList<>();
        int ruleId = 0;
        
        for (String destIpPort : destinationList) {
            String destIp;
            String destPort = null;

            // 解析目标IP和端口
            if (destIpPort.contains("&")) {
                String[] parts = destIpPort.split("&");
                destIp = parts[0];
                // 如果&后面有内容且不为空，则设置端口
                if (parts.length > 1 && !parts[1].trim().isEmpty()) {
                    destPort = parts[1];
                }
            } else {
                // 没有&符号，整个字符串就是IP
                destIp = destIpPort;
            }

            PlatformAclRequestDTO.AclMatchRule matchRule = new PlatformAclRequestDTO.AclMatchRule();
            matchRule.setAction(2); // 动作 1：Deny、2：Permit,默认2
            matchRule.setProtocol(getProtocolNumber(request.getAgreementType()));
            matchRule.setSourceIpMask(sourceIp);
            matchRule.setSourcePort(sourcePort);
            matchRule.setDestIpMask(destIp);
            // 只有当destPort不为null时才设置DestPort
            if (destPort != null) {
                matchRule.setDestPort(destPort);
            }
            matchRule.setRuleId(ruleId++); // 规则号(0-65534，从0开始，循环使用)
            
            matchRules.add(matchRule);
        }
        
        aclRequest.setMatchList(matchRules);
        return aclRequest;
    }

    /**
     * 通过ACL名称查询ACL ID（直接调用queryAclTemplates进行分页查询和名称匹配）
     */
    private Integer getAclIdByName(String aclName) {
        log.info("开始通过名称查询ACL ID，ACL名称：{}", aclName);

        try {
            int pageSize = 15; // 每页大小
            int start = 0; // 起始位置

            while (true) {
                // 构建查询请求
                QueryAclTemplateRequestDTO request = new QueryAclTemplateRequestDTO();
                request.setDesc(true); // 降序排序，新增的在前面
                request.setStart(start);
                request.setSize(pageSize);

                // 直接调用queryAclTemplates进行查询
                QueryAclTemplateResponseDTO response = platformApiService.queryAclTemplates(request);
                if (response == null || response.getOutput() == null ||
                        response.getOutput().getList() == null || response.getOutput().getList().isEmpty()) {
                    log.warn("查询ACL模板返回空结果，ACL名称：{}，起始位置：{}", aclName, start);
                    break;
                }

                // 在当前页中查找匹配的ACL名称
                for (QueryAclTemplateResponseDTO.AclTemplate template : response.getOutput().getList()) {
                    if (aclName.equals(template.getAclName())) {
                        log.info("找到匹配的ACL模板，ACL ID：{}，ACL名称：{}", template.getAclId(), aclName);
                        return template.getAclId();
                    }
                }

                // 检查是否还有更多数据
                if (response.getOutput().getList().size() < pageSize) {
                    // 当前页数据不足pageSize，说明已经是最后一页
                    log.info("已查询完所有ACL模板，未找到匹配的ACL名称：{}", aclName);
                    break;
                }

                // 查询下一页
                start += pageSize;
                log.debug("继续查询下一页，ACL名称：{}，起始位置：{}", aclName, start);

                // 添加安全检查，避免无限循环
                if (start > 10000) { // 最多查询10000条记录
                    log.warn("查询ACL模板超过最大限制，停止查询，ACL名称：{}", aclName);
                    break;
                }
            }

            log.warn("未找到匹配的ACL模板，ACL名称：{}", aclName);
            return null;

        } catch (Exception e) {
            log.error("通过名称查询ACL ID异常，ACL名称：{}", aclName, e);
            throw new ServiceException("PLATFORM_ERROR", "通过名称查询ACL ID异常", "ACL名称：" + aclName);
        }
    }

    /**
     * 生成随机英文字母+数字组合，长度不超过20，必须以英文字母开头
     */
    private String generateRandomIdValue() {
        int length = 20;
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder(length);
        Random random = new Random();

        // 第一个字符必须是英文字母
        sb.append(letters.charAt(random.nextInt(letters.length())));

        // 剩余字符可以是字母或数字
        for (int i = 1; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 保存ACL关系记录
     */
    private void saveAclRelationRecords(Integer appId, Long appCustomId, Integer aclId, int ipVersion, List<String> destinationList) {
        for (String destIpPort : destinationList) {
            String destIp;
            String destPort = null;

            // 解析目标IP和端口
            if (destIpPort.contains("&")) {
                String[] parts = destIpPort.split("&");
                destIp = parts[0];
                // 如果&后面有内容且不为空，则设置端口
                if (parts.length > 1 && !parts[1].trim().isEmpty()) {
                    destPort = parts[1];
                }
            } else {
                // 没有&符号，整个字符串就是IP
                destIp = destIpPort;
            }

            AppAclRelation relation = new AppAclRelation();
            relation.setAppCustomId(appCustomId); // 可能为null，后续更新
            relation.setAppId(appId);
            relation.setAclId(aclId);
            relation.setIpVersion(ipVersion);
            relation.setDestinationIp(destIp);
            // 设置目标端口，如果为null则设置为空字符串（数据库字段不能为null）
            relation.setDestinationPort(destPort != null ? destPort : "");
            relation.setCreateTime(new Date());
            relation.setUpdateTime(new Date());
            relation.setIsDeleted(0);

            int result = appAclRelationMapper.insert(relation);
            if (result > 0) {
                log.info("保存ACL关系记录成功，应用ID：{}，ACL ID：{}，目标IP：{}，端口：{}",
                        appId, aclId, destIp, destPort != null ? destPort : "无端口限制");
            } else {
                log.error("保存ACL关系记录失败，应用ID：{}，ACL ID：{}，目标IP：{}，端口：{}",
                        appId, aclId, destIp, destPort != null ? destPort : "无端口限制");
            }
        }
    }



    /**
     * 获取协议编号
     */
    private Integer getProtocolNumber(String protocol) {
        switch (protocol.toUpperCase()) {
            case "ICMP": return 1;
            case "IGMP": return 2;
            case "IPINIP": return 4;
            case "TCP": return 6;
            case "UDP": return 17;
            case "GRE": return 47;
            case "OSPF": return 89;
            case "IP": return 256;
            default: return 6; // 默认TCP
        }
    }

    /**
     * 构建应用定制信息对象
     */
    private AppCustomInfo buildAppCustomInfo(AddAppRequestDTO request, Long appGroupId, List<Integer> aclIdList) {
        AppCustomInfo appCustomInfo = new AppCustomInfo();
        
        appCustomInfo.setAppId(request.getAppId());
        appCustomInfo.setAppName(request.getAppName());
        appCustomInfo.setAppType(request.getAppType());
        appCustomInfo.setInternetAppDSCP(request.getInternetAppDSCP());
        appCustomInfo.setAgreementType(request.getAgreementType());
        appCustomInfo.setSourceIP(request.getSourceIP());
        appCustomInfo.setSourcePort(request.getSourcePort());
        if (request.getDestinationIPList() != null) {
            appCustomInfo.setDestinationIPList(JSON.toJSONString(request.getDestinationIPList()));
        }
        appCustomInfo.setSourceIPv6(request.getSourceIPv6());
        appCustomInfo.setSourcePortIPv6(request.getSourcePortIPv6());
        if (request.getDestinationIPv6List() != null) {
            appCustomInfo.setDestinationIPv6List(JSON.toJSONString(request.getDestinationIPv6List()));
        }
        appCustomInfo.setAppGroupName(request.getAppGroupName());
        appCustomInfo.setAppGroupId(appGroupId);
        appCustomInfo.setAclIdList(JSON.toJSONString(aclIdList));
        appCustomInfo.setCreateTime(new Date());
        appCustomInfo.setUpdateTime(new Date());
        appCustomInfo.setIsDeleted(0);

        return appCustomInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteApp(Integer appId, String requestId) {
        log.info("开始处理删除应用请求，请求ID：{}，应用ID：{}", requestId, appId);

        // 1. 查询应用是否存在
        LambdaQueryWrapper<AppCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCustomInfo::getAppId, appId);
        AppCustomInfo appCustomInfo = appCustomInfoMapper.selectOne(queryWrapper);

        if (appCustomInfo == null) {
            log.warn("应用不存在，请求ID：{}，应用ID：{}", requestId, appId);
            throw new ServiceException("VALIDATION_ERROR", "应用不存在", "应用ID：" + appId);
        }

        // 2. 查询关联的ACL信息，按IP版本分组
        Map<Integer, List<AppAclRelation>> aclByIpVersion = getAppAclsByIpVersion(appId);
        log.info("查询到应用关联的ACL模板，请求ID：{}，应用ID：{}，IPv4数量：{}，IPv6数量：{}",
                requestId, appId,
                aclByIpVersion.getOrDefault(4, Collections.emptyList()).size(),
                aclByIpVersion.getOrDefault(6, Collections.emptyList()).size());

        if (aclByIpVersion.isEmpty()) {
            log.info("应用没有关联的ACL模板，直接删除应用记录，请求ID：{}，应用ID：{}", requestId, appId);
            deleteAppRecord(appCustomInfo, requestId);
            return;
        }

        // 3. 按照IPv6优先、IPv4次序删除ACL模板
        List<String> failureReasons = new ArrayList<>();
        List<Integer> successAclIds = new ArrayList<>();
        List<Integer> failedAclIds = new ArrayList<>();

        // 先删除IPv6 ACL
        if (aclByIpVersion.containsKey(6)) {
            deleteAclsByIpVersion(aclByIpVersion.get(6), 6, requestId, successAclIds, failedAclIds, failureReasons);
        }

        // 再删除IPv4 ACL
        if (aclByIpVersion.containsKey(4)) {
            deleteAclsByIpVersion(aclByIpVersion.get(4), 4, requestId, successAclIds, failedAclIds, failureReasons);
        }

        // 4. 处理删除结果
        if (!failedAclIds.isEmpty()) {
            // 有ACL删除失败，只更新成功删除的ACL记录，不删除应用
            updateAppAfterPartialAclDeletion(appCustomInfo, successAclIds, requestId);

            String errorMessage = String.format("部分ACL模板删除失败，失败ACL ID：%s，失败原因：%s",
                    failedAclIds.toString(), String.join("; ", failureReasons));
            log.error("删除应用失败，请求ID：{}，应用ID：{}，错误信息：{}", requestId, appId, errorMessage);

            throw new ServiceException("PLATFORM_ERROR", errorMessage, "应用ID：" + appId);
        } else {
            // 所有ACL删除成功，删除应用记录
            log.info("所有ACL模板删除成功，删除应用记录，请求ID：{}，应用ID：{}", requestId, appId);
            deleteAppRecord(appCustomInfo, requestId);
        }
    }

    /**
     * 获取应用关联的ACL信息，按IP版本分组
     */
    private Map<Integer, List<AppAclRelation>> getAppAclsByIpVersion(Integer appId) {
        // 从ACL关系表中查询所有关联的ACL记录
        LambdaQueryWrapper<AppAclRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppAclRelation::getAppId, appId)
                   .eq(AppAclRelation::getIsDeleted, 0);
        
        List<AppAclRelation> relations = appAclRelationMapper.selectList(queryWrapper);
        
        // 按IP版本分组
        Map<Integer, List<AppAclRelation>> aclByIpVersion = new HashMap<>();
        for (AppAclRelation relation : relations) {
            aclByIpVersion.computeIfAbsent(relation.getIpVersion(), k -> new ArrayList<>()).add(relation);
        }
        
        return aclByIpVersion;
    }

    /**
     * 按IP版本删除ACL模板
     */
    private void deleteAclsByIpVersion(List<AppAclRelation> aclRelations, int ipVersion, String requestId,
                                      List<Integer> successAclIds, List<Integer> failedAclIds, List<String> failureReasons) {
        // 按ACL ID分组，避免重复删除
        Map<Integer, List<AppAclRelation>> relationsByAclId = aclRelations.stream()
                .collect(Collectors.groupingBy(AppAclRelation::getAclId));
        
        String ipVersionStr = ipVersion == 4 ? "IPv4" : "IPv6";
        log.info("开始删除{}ACL模板，请求ID：{}，ACL数量：{}", ipVersionStr, requestId, relationsByAclId.size());
        
        for (Map.Entry<Integer, List<AppAclRelation>> entry : relationsByAclId.entrySet()) {
            Integer aclId = entry.getKey();
            try {
                boolean deleteResult = platformApiService.deleteAclTemplate(aclId, requestId);
                if (deleteResult) {
                    successAclIds.add(aclId);
                    log.info("删除{}ACL模板成功，请求ID：{}，ACL ID：{}", ipVersionStr, requestId, aclId);
                } else {
                    failedAclIds.add(aclId);
                    String reason = String.format("%s ACL模板(ID:%d)删除失败", ipVersionStr, aclId);
                    failureReasons.add(reason);
                    log.error("删除{}ACL模板失败，请求ID：{}，ACL ID：{}", ipVersionStr, requestId, aclId);
                }
            } catch (Exception e) {
                failedAclIds.add(aclId);
                String reason = String.format("%s ACL模板(ID:%d)删除异常: %s", ipVersionStr, aclId, e.getMessage());
                failureReasons.add(reason);
                log.error("删除{}ACL模板异常，请求ID：{}，ACL ID：{}", ipVersionStr, requestId, aclId, e);
            }
        }
    }

    /**
     * 部分ACL删除后更新应用信息
     */
    private void updateAppAfterPartialAclDeletion(AppCustomInfo appCustomInfo, List<Integer> successAclIds, String requestId) {
        try {
            // 1. 软删除成功删除的ACL关系记录
            for (Integer aclId : successAclIds) {
                LambdaQueryWrapper<AppAclRelation> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AppAclRelation::getAppId, appCustomInfo.getAppId())
                           .eq(AppAclRelation::getAclId, aclId)
                           .eq(AppAclRelation::getIsDeleted, 0);
                
                List<AppAclRelation> relations = appAclRelationMapper.selectList(queryWrapper);
                for (AppAclRelation relation : relations) {
                    relation.setIsDeleted(1);
                    relation.setUpdateTime(new Date());
                    appAclRelationMapper.updateById(relation);
                }
            }
            
            // 2. 更新应用的ACL ID列表，移除成功删除的ACL
            String aclIdListStr = appCustomInfo.getAclIdList();
            if (StringUtils.hasText(aclIdListStr)) {
                List<Integer> currentAclIds = JSON.parseArray(aclIdListStr, Integer.class);
                List<Integer> remainingAclIds = currentAclIds.stream()
                        .filter(id -> !successAclIds.contains(id))
                        .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
                
                appCustomInfo.setAclIdList(JSON.toJSONString(remainingAclIds));
                appCustomInfo.setUpdateTime(new Date());
                appCustomInfoMapper.updateById(appCustomInfo);
                
                log.info("更新应用ACL ID列表，请求ID：{}，应用ID：{}，移除ACL：{}，剩余ACL：{}", 
                    requestId, appCustomInfo.getAppId(), successAclIds, remainingAclIds);
            }
            
        } catch (Exception e) {
            log.error("更新应用信息失败，请求ID：{}，应用ID：{}", requestId, appCustomInfo.getAppId(), e);
        }
    }

    /**
     * 删除应用记录（软删除）
     */
    private void deleteAppRecord(AppCustomInfo appCustomInfo, String requestId) {
        // 删除所有ACL关系数据（软删除）
        deleteAppAclRelations(appCustomInfo.getAppId(), requestId);

        // 删除应用信息（软删除）
        int updateResult = appCustomInfoMapper.deleteById(appCustomInfo);

        if (updateResult <= 0) {
            log.error("删除应用信息失败，请求ID：{}，应用ID：{}", requestId, appCustomInfo.getAppId());
            throw new ServiceException("DATABASE_ERROR", "删除应用信息失败", "应用ID：" + appCustomInfo.getAppId());
        }

        log.info("删除应用成功，请求ID：{}，应用ID：{}", requestId, appCustomInfo.getAppId());
    }

    /**
     * 删除应用ACL关系数据（软删除）
     */
    private void deleteAppAclRelations(Integer appId, String requestId) {
        LambdaQueryWrapper<AppAclRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppAclRelation::getAppId, appId)
                   .eq(AppAclRelation::getIsDeleted, 0);
        
        List<AppAclRelation> relations = appAclRelationMapper.selectList(queryWrapper);
        
        for (AppAclRelation relation : relations) {
            int result = appAclRelationMapper.deleteById(relation);
            if (result > 0) {
                log.info("删除ACL关系记录成功，请求ID：{}，关系ID：{}", requestId, relation.getId());
            } else {
                log.error("删除ACL关系记录失败，请求ID：{}，关系ID：{}", requestId, relation.getId());
            }
        }
        
        log.info("批量删除ACL关系记录完成，请求ID：{}，应用ID：{}，删除数量：{}", 
            requestId, appId, relations.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyApp(ModifyAppRequestDTO request, String requestId) {
        log.info("开始处理修改应用请求，请求ID：{}，应用ID：{}", requestId, request.getAppId());

        // 1. 参数验证
        validateModifyRequest(request, requestId);

        // 2. 查询应用是否存在
        LambdaQueryWrapper<AppCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCustomInfo::getAppId, request.getAppId());
        AppCustomInfo existingApp = appCustomInfoMapper.selectOne(queryWrapper);
        if (existingApp == null) {
            log.warn("应用不存在，请求ID：{}，应用ID：{}", requestId, request.getAppId());
            throw new ServiceException("VALIDATION_ERROR", "应用不存在", "应用ID：" + request.getAppId());
        }

        // 3. 查询应用组信息
        LambdaQueryWrapper<AppGroupCustomInfo> groupQueryWrapper = new LambdaQueryWrapper<>();
        groupQueryWrapper.eq(AppGroupCustomInfo::getAppGroupName, request.getAppGroupName());
        AppGroupCustomInfo appGroup = appGroupCustomInfoMapper.selectOne(groupQueryWrapper);
        if (appGroup == null) {
            log.warn("应用分组不存在，请求ID：{}，应用分组：{}", requestId, request.getAppGroupName());
            throw new ServiceException("VALIDATION_ERROR", "指定的应用分组不存在", "应用分组：" + request.getAppGroupName());
        }

        // 4. 分析字段变化类型
        boolean needUpdateAcl = isNeedUpdateAcl(existingApp, request);
        boolean needRecreateAcl = isNeedRecreateAcl(existingApp, request);
        log.info("修改应用变化分析，请求ID：{}，应用ID：{}，是否需要更新ACL：{}，是否需要重建ACL：{}",
                requestId, request.getAppId(), needUpdateAcl, needRecreateAcl);

        // 5. 根据变化类型处理
        if (needUpdateAcl) {
            if (needRecreateAcl) {
                // 需要删除现有ACL并重新创建
                handleModifyAppWithRecreateAcl(existingApp, request, appGroup, requestId);
            } else {
                // 仅需要调用平台修改ACL接口并更新定制库
                handleModifyAppWithUpdateAcl(existingApp, request, appGroup, requestId);
            }
        } else {
            // 仅更新定制库
            handleModifyAppOnly(existingApp, request, appGroup, requestId);
        }
    }

    /**
     * 验证修改应用请求参数
     */
    private void validateModifyRequest(ModifyAppRequestDTO request, String requestId) {
        // 验证应用类型
        if (!"五元组".equals(request.getAppType())) {
            log.warn("应用类型不支持，请求ID：{}，应用类型：{}", requestId, request.getAppType());
            throw new ServiceException("VALIDATION_ERROR", "应用类型仅支持'五元组'", "应用类型：" + request.getAppType());
        }

        // 验证协议类型
        if (!isValidProtocol(request.getAgreementType())) {
            log.warn("协议类型不支持，请求ID：{}，协议类型：{}", requestId, request.getAgreementType());
            throw new ServiceException("VALIDATION_ERROR", "不支持的协议类型", "协议类型：" + request.getAgreementType());
        }

        // 验证IPv4和IPv6参数的二选一逻辑
        validateIpParameters(request, requestId);
    }

    /**
     * 判断是否需要更新ACL模板
     * 当appType、internetAppDSCP变化时，仅更新定制库
     * 当其他字段变化时，需要调用平台修改ACL接口
     */
    private boolean isNeedUpdateAcl(AppCustomInfo existingApp, ModifyAppRequestDTO request) {
        // 检查影响ACL的关键字段是否变化
        boolean agreementTypeChanged = !Objects.equals(existingApp.getAgreementType(), request.getAgreementType());
        boolean sourceIPChanged = !Objects.equals(existingApp.getSourceIP(), request.getSourceIP());
        boolean sourcePortChanged = !Objects.equals(existingApp.getSourcePort(), request.getSourcePort());
        boolean sourceIPv6Changed = !Objects.equals(existingApp.getSourceIPv6(), request.getSourceIPv6());
        boolean sourcePortIPv6Changed = !Objects.equals(existingApp.getSourcePortIPv6(), request.getSourcePortIPv6());

        // 检查目的IP列表是否变化（不考虑顺序）
        boolean destinationIPListChanged = isDestinationListChanged(
            existingApp.getDestinationIPList(), 
            JSON.toJSONString(request.getDestinationIPList())
        );
        
        boolean destinationIPv6ListChanged = isDestinationListChanged(
            existingApp.getDestinationIPv6List(), 
            request.getDestinationIPv6List() != null ? JSON.toJSONString(request.getDestinationIPv6List()) : null
        );

        boolean needUpdateAcl = agreementTypeChanged || sourceIPChanged || 
                               sourcePortChanged || sourceIPv6Changed || sourcePortIPv6Changed || 
                               destinationIPListChanged || destinationIPv6ListChanged;

        log.info("ACL更新需求分析，应用ID：{}，agreementType：{}，sourceIP：{}，sourcePort：{}，" +
                "sourceIPv6：{}，sourcePortIPv6：{}，destinationIPList：{}，destinationIPv6List：{}",
            existingApp.getAppId(), agreementTypeChanged, sourceIPChanged, sourcePortChanged,
            sourceIPv6Changed, sourcePortIPv6Changed, destinationIPListChanged, destinationIPv6ListChanged);

        return needUpdateAcl;
    }

    /**
     * 判断是否需要删除并重新创建ACL模板
     * 当appName、agreementType和appGroupName发生变化时，需要删除现有ACL模板并重新创建
     */
    private boolean isNeedRecreateAcl(AppCustomInfo existingApp, ModifyAppRequestDTO request) {
        boolean appNameChanged = !Objects.equals(existingApp.getAppName(), request.getAppName());
        boolean appGroupNameChanged = !Objects.equals(existingApp.getAppGroupName(), request.getAppGroupName());

        boolean needRecreate = appNameChanged || appGroupNameChanged;

        log.info("ACL重建需求分析，应用ID：{}，appName：{}，appGroupName：{}，需要重建：{}",
            existingApp.getAppId(), appNameChanged, appGroupNameChanged, needRecreate);

        return needRecreate;
    }

    /**
     * 检查目的IP列表是否变化（忽略元素顺序）
     */
    private boolean isDestinationListChanged(String existingListStr, String newListStr) {
        if (Objects.equals(existingListStr, newListStr)) {
            return false;
        }
        
        if (existingListStr == null || newListStr == null) {
            return true;
        }

        try {
            List<String> existingList = JSON.parseArray(existingListStr, String.class);
            List<String> newList = JSON.parseArray(newListStr, String.class);
            
            if (existingList == null || newList == null) {
                return true;
            }
            
            // 转换为Set进行比较，忽略顺序
            Set<String> existingSet = new HashSet<>(existingList);
            Set<String> newSet = new HashSet<>(newList);
            
            return !existingSet.equals(newSet);
        } catch (Exception e) {
            log.warn("解析目的IP列表失败", e);
            return true;
        }
    }

    /**
     * 处理需要删除并重新创建ACL的修改应用请求
     */
    private void handleModifyAppWithRecreateAcl(AppCustomInfo existingApp, ModifyAppRequestDTO request,
                                                         AppGroupCustomInfo appGroup, String requestId) {
        log.info("开始处理需要重建ACL的修改应用，请求ID：{}，应用ID：{}", requestId, request.getAppId());

        // 1. 查询现有的ACL关系
        Map<Integer, List<AppAclRelation>> existingAclsByVersion = getAppAclsByIpVersion(request.getAppId());

        // 2. 删除现有的ACL模板
        List<Integer> deletedAclIds = new ArrayList<>();
        List<Integer> failedDeleteAclIds = new ArrayList<>();

        for (Map.Entry<Integer, List<AppAclRelation>> entry : existingAclsByVersion.entrySet()) {
            int ipVersion = entry.getKey();
            List<AppAclRelation> relations = entry.getValue();

            // 按ACL ID分组，避免重复删除
            Map<Integer, List<AppAclRelation>> relationsByAclId = relations.stream()
                    .collect(Collectors.groupingBy(AppAclRelation::getAclId));

            for (Integer aclId : relationsByAclId.keySet()) {
                try {
                    boolean deleteResult = platformApiService.deleteAclTemplate(aclId, requestId);
                    if (deleteResult) {
                        deletedAclIds.add(aclId);
                        log.info("删除ACL模板成功，请求ID：{}，ACL ID：{}，IP版本：{}", requestId, aclId, ipVersion);
                    } else {
                        failedDeleteAclIds.add(aclId);
                        log.error("删除ACL模板失败，请求ID：{}，ACL ID：{}，IP版本：{}", requestId, aclId, ipVersion);
                    }
                } catch (Exception e) {
                    failedDeleteAclIds.add(aclId);
                    log.error("删除ACL模板异常，请求ID：{}，ACL ID：{}，IP版本：{}", requestId, aclId, ipVersion, e);
                }
            }
        }

        // 3. 检查删除结果
        if (!failedDeleteAclIds.isEmpty()) {
            log.error("部分ACL模板删除失败，请求ID：{}，应用ID：{}，失败ACL ID：{}",
                    requestId, request.getAppId(), failedDeleteAclIds);
            throw new ServiceException("PLATFORM_ERROR", "删除现有ACL模板失败，无法继续修改", "失败ACL ID：" + failedDeleteAclIds);
        }

        // 4. 清理ACL关系记录
        cleanupAclRelationRecords(request.getAppId(), requestId);

        // 5. 重新创建ACL模板
        List<Integer> newAclIdList = createAclTemplatesForModify(request, requestId, existingApp.getId());
        if (newAclIdList.isEmpty()) {
            log.error("重新创建ACL模板失败，请求ID：{}，应用ID：{}", requestId, request.getAppId());
            throw new ServiceException("PLATFORM_ERROR", "重新创建ACL模板失败", "应用ID：" + request.getAppId());
        }

        // 6. 更新应用定制信息
        updateAppCustomInfoWithRecreate(existingApp, request, appGroup, newAclIdList, requestId);

        log.info("修改应用成功（重建ACL），请求ID：{}，应用ID：{}，新ACL ID列表：{}",
                requestId, request.getAppId(), newAclIdList);
    }

    /**
     * 处理需要更新ACL的修改应用请求
     */
    private void handleModifyAppWithUpdateAcl(AppCustomInfo existingApp, ModifyAppRequestDTO request,
                                                        AppGroupCustomInfo appGroup, String requestId) {
        log.info("开始处理需要更新ACL的修改应用，请求ID：{}，应用ID：{}", requestId, request.getAppId());

        // 1. 查询现有的ACL关系
        Map<Integer, List<AppAclRelation>> existingAclsByVersion = getAppAclsByIpVersion(request.getAppId());

        // 2. 根据新参数创建ACL模板请求
        List<PlatformAclRequestDTO> newAclRequests = buildModifyAclRequests(request, existingAclsByVersion);

        // 3. 调用平台接口修改ACL模板
        Map<Integer, Integer> versionToAclIdMap = new HashMap<>();
        for (PlatformAclRequestDTO aclRequest : newAclRequests) {
            PlatformAclResponseDTO response = platformApiService.updateAclTemplate(aclRequest);

            if (response == null || !Boolean.TRUE.equals(response.getSuccessful())) {
                log.error("修改ACL模板失败，请求ID：{}，应用ID：{}，ACL请求：{}",
                        requestId, request.getAppId(), JSON.toJSONString(aclRequest));
                throw new ServiceException("PLATFORM_ERROR", "修改ACL模板失败", "应用ID：" + request.getAppId());
            }

            // 记录版本对应的ACL ID
            if (aclRequest.getGroupType() == 1) { // IPv4
                versionToAclIdMap.put(4, aclRequest.getAclId());
            } else if (aclRequest.getGroupType() == 2) { // IPv6
                versionToAclIdMap.put(6, aclRequest.getAclId());
            }

            log.info("修改ACL模板成功，请求ID：{}，ACL ID：{}", requestId, aclRequest.getAclId());
        }

        // 4. 更新ACL关系记录
        updateAclRelationRecords(request, versionToAclIdMap, existingApp.getId(), requestId);

        // 5. 更新应用定制信息
        updateAppCustomInfo(existingApp, request, appGroup, versionToAclIdMap, requestId);

        log.info("修改应用成功（包含ACL更新），请求ID：{}，应用ID：{}", requestId, request.getAppId());
    }

    /**
     * 处理仅更新定制库的修改应用请求
     */
    private void handleModifyAppOnly(AppCustomInfo existingApp, ModifyAppRequestDTO request,
                                              AppGroupCustomInfo appGroup, String requestId) {
        log.info("开始处理仅更新定制库的修改应用，请求ID：{}，应用ID：{}", requestId, request.getAppId());

        // 直接更新应用定制信息，不修改ACL
        updateAppCustomInfoOnly(existingApp, request, appGroup, requestId);

        log.info("修改应用成功（仅更新定制库），请求ID：{}，应用ID：{}", requestId, request.getAppId());
    }

    /**
     * 构建修改ACL的请求列表
     */
    private List<PlatformAclRequestDTO> buildModifyAclRequests(ModifyAppRequestDTO request, 
                                                              Map<Integer, List<AppAclRelation>> existingAclsByVersion) {
        List<PlatformAclRequestDTO> aclRequests = new ArrayList<>();

        // IPv4 ACL
        if (StringUtils.hasText(request.getSourceIP()) && request.getDestinationIPList() != null && !request.getDestinationIPList().isEmpty()) {
            List<AppAclRelation> ipv4Relations = existingAclsByVersion.getOrDefault(4, Collections.emptyList());
            if (!ipv4Relations.isEmpty()) {
                Integer aclId = ipv4Relations.get(0).getAclId(); // 获取现有的ACL ID
                PlatformAclRequestDTO aclRequest = buildAclRequestForVersionWithId(
                    request, request.getSourceIP(), request.getSourcePort(), 
                    request.getDestinationIPList(), 4, aclId);
                aclRequests.add(aclRequest);
            }
        }

        // IPv6 ACL
        if (StringUtils.hasText(request.getSourceIPv6()) && request.getDestinationIPv6List() != null && !request.getDestinationIPv6List().isEmpty()) {
            List<AppAclRelation> ipv6Relations = existingAclsByVersion.getOrDefault(6, Collections.emptyList());
            if (!ipv6Relations.isEmpty()) {
                Integer aclId = ipv6Relations.get(0).getAclId(); // 获取现有的ACL ID
                PlatformAclRequestDTO aclRequest = buildAclRequestForVersionWithId(
                    request, request.getSourceIPv6(), request.getSourcePortIPv6(), 
                    request.getDestinationIPv6List(), 6, aclId);
                aclRequests.add(aclRequest);
            }
        }

        return aclRequests;
    }

    /**
     * 为指定IP版本构建带ACL ID的ACL请求（用于修改）
     */
    private PlatformAclRequestDTO buildAclRequestForVersionWithId(ModifyAppRequestDTO request, String sourceIp, 
                                                                 String sourcePort, List<String> destinationList, 
                                                                 int ipVersion, Integer aclId) {
        PlatformAclRequestDTO aclRequest = new PlatformAclRequestDTO();
        
        // 设置必填的ACL ID
        aclRequest.setAclId(aclId);
        
        // 设置ACL基本信息
        String aclName = "acl_" + request.getAppId() + (ipVersion == 4 ? "_ipv4" : "_ipv6");
        aclRequest.setAclName(aclName);
        //aclRequest.setDescription("修改的应用ACL模板 - " + request.getAppName());
        aclRequest.setGroupType(ipVersion == 4 ? 1 : 2); // 1=IPv4ACL, 2=IPv6ACL
        aclRequest.setIdType(1); // 1：名称标志 2：数字标识，默认1
        aclRequest.setIdValue(generateRandomIdValue());
        aclRequest.setMatchOrder(1); // 匹配次序，1：配置 2：自动，默认1
        aclRequest.setStep(5);

        // 构建匹配规则列表（每个目标IP一个规则）
        List<PlatformAclRequestDTO.AclMatchRule> matchRules = new ArrayList<>();
        int ruleId = 0;
        
        for (String destIpPort : destinationList) {
            String destIp;
            String destPort = null;

            // 解析目标IP和端口
            if (destIpPort.contains("&")) {
                String[] parts = destIpPort.split("&");
                destIp = parts[0];
                // 如果&后面有内容且不为空，则设置端口
                if (parts.length > 1 && !parts[1].trim().isEmpty()) {
                    destPort = parts[1];
                }
            } else {
                // 没有&符号，整个字符串就是IP
                destIp = destIpPort;
            }

            PlatformAclRequestDTO.AclMatchRule matchRule = new PlatformAclRequestDTO.AclMatchRule();
            matchRule.setAction(2); // 动作 1：Deny、2：Permit,默认2
            matchRule.setProtocol(getProtocolNumber(request.getAgreementType()));
            matchRule.setSourceIpMask(sourceIp);
            matchRule.setSourcePort(sourcePort);
            matchRule.setDestIpMask(destIp);
            // 只有当destPort不为null时才设置DestPort
            if (destPort != null) {
                matchRule.setDestPort(destPort);
            }
            matchRule.setRuleId(ruleId++); // 规则号(0-65534，从0开始，循环使用)
            
            matchRules.add(matchRule);
        }

        aclRequest.setMatchList(matchRules);
        return aclRequest;
    }

    /**
     * 更新ACL关系记录
     */
    private void updateAclRelationRecords(ModifyAppRequestDTO request, Map<Integer, Integer> versionToAclIdMap, 
                                         Long appCustomId, String requestId) {
        // 1. 删除现有的ACL关系记录
        LambdaQueryWrapper<AppAclRelation> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AppAclRelation::getAppId, request.getAppId())
                    .eq(AppAclRelation::getIsDeleted, 0);
        
        List<AppAclRelation> existingRelations = appAclRelationMapper.selectList(deleteWrapper);
        for (AppAclRelation relation : existingRelations) {
            appAclRelationMapper.deleteById(relation);
        }

        // 2. 创建新的ACL关系记录
        if (versionToAclIdMap.containsKey(4) && request.getDestinationIPList() != null) {
            saveAclRelationRecords(request.getAppId(), appCustomId, versionToAclIdMap.get(4), 4, request.getDestinationIPList());
        }
        
        if (versionToAclIdMap.containsKey(6) && request.getDestinationIPv6List() != null) {
            saveAclRelationRecords(request.getAppId(), appCustomId, versionToAclIdMap.get(6), 6, request.getDestinationIPv6List());
        }

        log.info("更新ACL关系记录完成，请求ID：{}，应用ID：{}", requestId, request.getAppId());
    }

    /**
     * 更新应用定制信息（包含ACL ID更新）
     */
    private void updateAppCustomInfo(AppCustomInfo existingApp, ModifyAppRequestDTO request, 
                                    AppGroupCustomInfo appGroup, Map<Integer, Integer> versionToAclIdMap, String requestId) {
        // 更新应用信息
        existingApp.setAppName(request.getAppName());
        existingApp.setAppType(request.getAppType());
        existingApp.setInternetAppDSCP(request.getInternetAppDSCP());
        existingApp.setAgreementType(request.getAgreementType());
        existingApp.setSourceIP(request.getSourceIP());
        existingApp.setSourcePort(request.getSourcePort());
        existingApp.setDestinationIPList(JSON.toJSONString(request.getDestinationIPList()));
        existingApp.setSourceIPv6(request.getSourceIPv6());
        existingApp.setSourcePortIPv6(request.getSourcePortIPv6());
        if (request.getDestinationIPv6List() != null) {
            existingApp.setDestinationIPv6List(JSON.toJSONString(request.getDestinationIPv6List()));
        } else {
            existingApp.setDestinationIPv6List(null);
        }
        existingApp.setAppGroupName(request.getAppGroupName());
        existingApp.setAppGroupId(appGroup.getId());

        // 更新ACL ID列表
        List<Integer> aclIdList = new ArrayList<>(versionToAclIdMap.values());
        existingApp.setAclIdList(JSON.toJSONString(aclIdList));
        existingApp.setUpdateTime(new Date());

        int updateResult = appCustomInfoMapper.updateById(existingApp);
        if (updateResult <= 0) {
            throw new ServiceException("DATABASE_ERROR", "更新应用信息失败", "应用ID：" + existingApp.getAppId());
        }

        log.info("更新应用定制信息成功，请求ID：{}，应用ID：{}", requestId, request.getAppId());
    }

    /**
     * 仅更新应用定制信息（不更新ACL）
     */
    private void updateAppCustomInfoOnly(AppCustomInfo existingApp, ModifyAppRequestDTO request, 
                                        AppGroupCustomInfo appGroup, String requestId) {
        // 仅更新允许独立修改的字段
        existingApp.setAppType(request.getAppType());
        existingApp.setInternetAppDSCP(request.getInternetAppDSCP());
        existingApp.setUpdateTime(new Date());

        int updateResult = appCustomInfoMapper.updateById(existingApp);
        if (updateResult <= 0) {
            throw new ServiceException("DATABASE_ERROR", "更新应用信息失败", "应用ID：" + existingApp.getAppId());
        }

        log.info("仅更新应用定制信息成功，请求ID：{}，应用ID：{}", requestId, request.getAppId());
    }

    /**
     * 清理ACL关系记录
     */
    private void cleanupAclRelationRecords(Integer appId, String requestId) {
        LambdaQueryWrapper<AppAclRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppAclRelation::getAppId, appId)
                   .eq(AppAclRelation::getIsDeleted, 0);
        
        List<AppAclRelation> relations = appAclRelationMapper.selectList(queryWrapper);
        
        for (AppAclRelation relation : relations) {
            int result = appAclRelationMapper.deleteById(relation);
            if (result > 0) {
                log.info("清理ACL关系记录成功，请求ID：{}，关系ID：{}", requestId, relation.getId());
            } else {
                log.error("清理ACL关系记录失败，请求ID：{}，关系ID：{}", requestId, relation.getId());
            }
        }
        
        log.info("清理ACL关系记录完成，请求ID：{}，应用ID：{}，清理数量：{}", 
            requestId, appId, relations.size());
    }

    /**
     * 为修改应用创建ACL模板
     */
    private List<Integer> createAclTemplatesForModify(ModifyAppRequestDTO request, String requestId, Long appCustomId) {
        List<Integer> aclIdList = new ArrayList<>();

        // IPv4 ACL模板
        if (StringUtils.hasText(request.getSourceIP()) && request.getDestinationIPList() != null && !request.getDestinationIPList().isEmpty()) {
            List<Integer> ipv4AclIds = createAclTemplatesForIpVersionForModify(request, requestId, 4, appCustomId);
            aclIdList.addAll(ipv4AclIds);
        }

        // IPv6 ACL模板
        if (StringUtils.hasText(request.getSourceIPv6()) && request.getDestinationIPv6List() != null && !request.getDestinationIPv6List().isEmpty()) {
            List<Integer> ipv6AclIds = createAclTemplatesForIpVersionForModify(request, requestId, 6, appCustomId);
            aclIdList.addAll(ipv6AclIds);
        }

        return aclIdList;
    }

    /**
     * 为指定IP版本创建ACL模板（修改应用场景）
     */
    private List<Integer> createAclTemplatesForIpVersionForModify(ModifyAppRequestDTO request, String requestId, int ipVersion, Long appCustomId) {
        List<Integer> aclIds = new ArrayList<>();
        
        String sourceIp = ipVersion == 4 ? request.getSourceIP() : request.getSourceIPv6();
        String sourcePort = ipVersion == 4 ? request.getSourcePort() : request.getSourcePortIPv6();
        List<String> destinationList = ipVersion == 4 ? request.getDestinationIPList() : request.getDestinationIPv6List();

        // 构建ACL模板请求（一个IP版本对应一个ACL模板，包含多个目标规则）
        PlatformAclRequestDTO aclRequest = buildAclRequestForVersionForModify(request, sourceIp, sourcePort, destinationList, ipVersion);
        
        // 调用平台接口创建ACL模板
        PlatformAclResponseDTO response = platformApiService.addAclTemplate(aclRequest);

        log.info("平台ACL接口返回结果：{}", JSON.toJSONString(response));

        if (response != null && response.getSuccessful() != null && response.getSuccessful()) {
            // ACL模板创建成功，通过名称查询获取ACL ID
            String aclName = aclRequest.getAclName();
            Integer aclId = getAclIdByName(aclName);

            if (aclId != null) {
                aclIds.add(aclId);
                log.info("成功创建ACL模板，请求ID：{}，ACL ID：{}，ACL名称：{}，IP版本：{}，目标IP数量：{}",
                        requestId, aclId, aclName, ipVersion, destinationList.size());

                // 保存ACL与目标IP的关系记录
                saveAclRelationRecords(request.getAppId(), appCustomId, aclId, ipVersion, destinationList);

            } else {
                log.error("ACL模板创建成功但无法获取ACL ID，请求ID：{}，ACL名称：{}", requestId, aclName);
            }
        } else {
            log.error("创建ACL模板失败，请求ID：{}，响应：{}", requestId, JSON.toJSONString(response));
        }

        return aclIds;
    }

    /**
     * 构建指定IP版本的ACL模板请求（修改应用场景）
     */
    private PlatformAclRequestDTO buildAclRequestForVersionForModify(ModifyAppRequestDTO request, String sourceIp, String sourcePort, 
                                                                    List<String> destinationList, int ipVersion) {
        PlatformAclRequestDTO aclRequest = new PlatformAclRequestDTO();
        
        // ACL基本信息 - 根据IP版本添加对应后缀
        String aclName = "acl_" + request.getAppId() + (ipVersion == 4 ? "_ipv4" : "_ipv6");
        aclRequest.setAclName(aclName);
        aclRequest.setGroupType(ipVersion == 4 ? 1 : 2); // 1：IPv4ACL 2：IPv6ACL
        aclRequest.setIdType(1); // 1：名称标志 2：数字标识，默认1
        aclRequest.setIdValue(generateRandomIdValue()); // ACL标识值 默认应用分组名称
        aclRequest.setMatchOrder(1); // 匹配次序，1：配置 2：自动，默认1
        aclRequest.setStep(5); // 步长，默认为5

        // 构建匹配规则列表（每个目标IP一个规则）
        List<PlatformAclRequestDTO.AclMatchRule> matchRules = new ArrayList<>();
        int ruleId = 0;
        
        for (String destIpPort : destinationList) {
            String destIp;
            String destPort = null;

            // 解析目标IP和端口
            if (destIpPort.contains("&")) {
                String[] parts = destIpPort.split("&");
                destIp = parts[0];
                // 如果&后面有内容且不为空，则设置端口
                if (parts.length > 1 && !parts[1].trim().isEmpty()) {
                    destPort = parts[1];
                }
            } else {
                // 没有&符号，整个字符串就是IP
                destIp = destIpPort;
            }

            PlatformAclRequestDTO.AclMatchRule matchRule = new PlatformAclRequestDTO.AclMatchRule();
            matchRule.setAction(2); // 动作 1：Deny、2：Permit,默认2
            matchRule.setProtocol(getProtocolNumber(request.getAgreementType()));
            matchRule.setSourceIpMask(sourceIp);
            matchRule.setSourcePort(sourcePort);
            matchRule.setDestIpMask(destIp);
            // 只有当destPort不为null时才设置DestPort
            if (destPort != null) {
                matchRule.setDestPort(destPort);
            }
            matchRule.setRuleId(ruleId++); // 规则号(0-65534，从0开始，循环使用)
            
            matchRules.add(matchRule);
        }
        
        aclRequest.setMatchList(matchRules);
        return aclRequest;
    }

    /**
     * 更新应用定制信息（重建ACL场景）
     */
    private void updateAppCustomInfoWithRecreate(AppCustomInfo existingApp, ModifyAppRequestDTO request, 
                                                AppGroupCustomInfo appGroup, List<Integer> newAclIdList, String requestId) {
        // 更新应用信息
        existingApp.setAppName(request.getAppName());
        existingApp.setAppType(request.getAppType());
        existingApp.setInternetAppDSCP(request.getInternetAppDSCP());
        existingApp.setAgreementType(request.getAgreementType());
        existingApp.setSourceIP(request.getSourceIP());
        existingApp.setSourcePort(request.getSourcePort());
        if (request.getDestinationIPList() != null) {
            existingApp.setDestinationIPList(JSON.toJSONString(request.getDestinationIPList()));
        } else {
            existingApp.setDestinationIPList(null);
        }
        existingApp.setSourceIPv6(request.getSourceIPv6());
        existingApp.setSourcePortIPv6(request.getSourcePortIPv6());
        if (request.getDestinationIPv6List() != null) {
            existingApp.setDestinationIPv6List(JSON.toJSONString(request.getDestinationIPv6List()));
        } else {
            existingApp.setDestinationIPv6List(null);
        }
        existingApp.setAppGroupName(request.getAppGroupName());
        existingApp.setAppGroupId(appGroup.getId());

        // 更新ACL ID列表
        existingApp.setAclIdList(JSON.toJSONString(newAclIdList));
        existingApp.setUpdateTime(new Date());

        int updateResult = appCustomInfoMapper.updateById(existingApp);
        if (updateResult <= 0) {
            throw new ServiceException("DATABASE_ERROR", "更新应用信息失败", "应用ID：" + existingApp.getAppId());
        }

        log.info("更新应用定制信息成功（重建场景），请求ID：{}，应用ID：{}，新ACL ID列表：{}", 
            requestId, request.getAppId(), newAclIdList);
    }

    @Override
    public QueryAppInfoListResponseDTO queryAppInfoList(String requestId) {
        log.info("开始处理查询应用列表请求，请求ID：{}", requestId);

        // 查询所有未删除的应用信息
        LambdaQueryWrapper<AppCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCustomInfo::getIsDeleted, 0)
                .orderByAsc(AppCustomInfo::getAppId);

        List<AppCustomInfo> appCustomInfoList = appCustomInfoMapper.selectList(queryWrapper);

        // 转换为响应DTO
        List<QueryAppInfoListResponseDTO.AppInfo> appInfoList = new ArrayList<>();
        for (AppCustomInfo appCustomInfo : appCustomInfoList) {
            QueryAppInfoListResponseDTO.AppInfo appInfo = convertToAppInfo(appCustomInfo);
            appInfoList.add(appInfo);
        }

        log.info("查询应用列表成功，请求ID：{}，应用数量：{}", requestId, appInfoList.size());
        return QueryAppInfoListResponseDTO.success(requestId, appInfoList);
    }

    /**
     * 将AppCustomInfo转换为AppInfo
     */
    private QueryAppInfoListResponseDTO.AppInfo convertToAppInfo(AppCustomInfo appCustomInfo) {
        QueryAppInfoListResponseDTO.AppInfo appInfo = new QueryAppInfoListResponseDTO.AppInfo();
        
        appInfo.setAppId(appCustomInfo.getAppId());
        appInfo.setAppName(appCustomInfo.getAppName());
        appInfo.setAppType(appCustomInfo.getAppType());
        appInfo.setInternetAppDSCP(appCustomInfo.getInternetAppDSCP());
        appInfo.setAgreementType(appCustomInfo.getAgreementType());
        appInfo.setSourceIP(appCustomInfo.getSourceIP());
        appInfo.setSourcePort(appCustomInfo.getSourcePort());
        appInfo.setSourceIPv6(appCustomInfo.getSourceIPv6());
        appInfo.setSourcePortIPv6(appCustomInfo.getSourcePortIPv6());
        appInfo.setAppGroupName(appCustomInfo.getAppGroupName());
        
        // 处理目的IP列表（JSON字符串转换为List）
        if (StringUtils.hasText(appCustomInfo.getDestinationIPList())) {
            try {
                List<String> destinationIPList = JSON.parseArray(appCustomInfo.getDestinationIPList(), String.class);
                appInfo.setDestinationIPList(destinationIPList);
            } catch (Exception e) {
                log.warn("解析目的IP列表失败，应用ID：{}，原始数据：{}", appCustomInfo.getAppId(), appCustomInfo.getDestinationIPList());
                appInfo.setDestinationIPList(new ArrayList<>());
            }
        } else {
            appInfo.setDestinationIPList(new ArrayList<>());
        }
        
        // 处理IPv6目的IP列表（JSON字符串转换为List）
        if (StringUtils.hasText(appCustomInfo.getDestinationIPv6List())) {
            try {
                List<String> destinationIPv6List = JSON.parseArray(appCustomInfo.getDestinationIPv6List(), String.class);
                appInfo.setDestinationIPv6List(destinationIPv6List);
            } catch (Exception e) {
                log.warn("解析IPv6目的IP列表失败，应用ID：{}，原始数据：{}", appCustomInfo.getAppId(), appCustomInfo.getDestinationIPv6List());
                appInfo.setDestinationIPv6List(new ArrayList<>());
            }
        } else {
            appInfo.setDestinationIPv6List(new ArrayList<>());
        }
        
        // 暂时将optionField设置为空对象，可根据需要扩展
        appInfo.setOptionField(new HashMap<>());
        
        return appInfo;
    }

    @Override
    public QueryAppInfoByAppIdResponseDTO queryAppInfoByAppId(Integer appId, String requestId) {
        log.info("开始处理查询单个应用请求，请求ID：{}，应用ID：{}", requestId, appId);

        // 参数验证
        if (appId == null) {
            log.warn("应用ID不能为空，请求ID：{}", requestId);
            throw new ServiceException("VALIDATION_ERROR", "应用ID不能为空", "请求ID：" + requestId);
        }

        // 查询指定应用ID的应用信息
        LambdaQueryWrapper<AppCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppCustomInfo::getAppId, appId)
                .eq(AppCustomInfo::getIsDeleted, 0);

        AppCustomInfo appCustomInfo = appCustomInfoMapper.selectOne(queryWrapper);

        if (appCustomInfo == null) {
            log.warn("未找到指定的应用，请求ID：{}，应用ID：{}", requestId, appId);
            throw new ServiceException("VALIDATION_ERROR", "未找到指定的应用", "应用ID：" + appId);
        }

        // 转换为响应DTO
        QueryAppInfoByAppIdResponseDTO.AppInfo appInfo = convertToSingleAppInfo(appCustomInfo);

        log.info("查询单个应用成功，请求ID：{}，应用ID：{}", requestId, appId);
        return QueryAppInfoByAppIdResponseDTO.success(requestId, appInfo);
    }

    /**
     * 将AppCustomInfo转换为单个应用查询的AppInfo
     */
    private QueryAppInfoByAppIdResponseDTO.AppInfo convertToSingleAppInfo(AppCustomInfo appCustomInfo) {
        QueryAppInfoByAppIdResponseDTO.AppInfo appInfo = new QueryAppInfoByAppIdResponseDTO.AppInfo();
        
        appInfo.setAppId(appCustomInfo.getAppId());
        appInfo.setAppName(appCustomInfo.getAppName());
        appInfo.setAppType(appCustomInfo.getAppType());
        appInfo.setInternetAppDSCP(appCustomInfo.getInternetAppDSCP());
        appInfo.setAgreementType(appCustomInfo.getAgreementType());
        appInfo.setSourceIP(appCustomInfo.getSourceIP());
        appInfo.setSourcePort(appCustomInfo.getSourcePort());
        appInfo.setSourceIPv6(appCustomInfo.getSourceIPv6());
        appInfo.setSourcePortIPv6(appCustomInfo.getSourcePortIPv6());
        appInfo.setAppGroupName(appCustomInfo.getAppGroupName());
        
        // 处理目的IP列表（JSON字符串转换为List）
        if (StringUtils.hasText(appCustomInfo.getDestinationIPList())) {
            try {
                List<String> destinationIPList = JSON.parseArray(appCustomInfo.getDestinationIPList(), String.class);
                appInfo.setDestinationIPList(destinationIPList);
            } catch (Exception e) {
                log.warn("解析目的IP列表失败，应用ID：{}，原始数据：{}", appCustomInfo.getAppId(), appCustomInfo.getDestinationIPList());
                appInfo.setDestinationIPList(new ArrayList<>());
            }
        } else {
            appInfo.setDestinationIPList(new ArrayList<>());
        }
        
        // 处理IPv6目的IP列表（JSON字符串转换为List）
        if (StringUtils.hasText(appCustomInfo.getDestinationIPv6List())) {
            try {
                List<String> destinationIPv6List = JSON.parseArray(appCustomInfo.getDestinationIPv6List(), String.class);
                appInfo.setDestinationIPv6List(destinationIPv6List);
            } catch (Exception e) {
                log.warn("解析IPv6目的IP列表失败，应用ID：{}，原始数据：{}", appCustomInfo.getAppId(), appCustomInfo.getDestinationIPv6List());
                appInfo.setDestinationIPv6List(new ArrayList<>());
            }
        } else {
            appInfo.setDestinationIPv6List(new ArrayList<>());
        }
        
        // 暂时将optionField设置为空对象，可根据需要扩展
        appInfo.setOptionField(new HashMap<>());
        
        return appInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchImportAppResponseDTO importAppTemplate(MultipartFile file, String requestId) {
        log.info("开始批量导入应用模板，请求ID：{}，文件名：{}", requestId, file.getOriginalFilename());

        // 1. 验证文件格式，验证失败抛出ServiceException，由全局异常处理器处理
        if (file.isEmpty()) {
            throw new ServiceException("VALIDATION_ERROR", "上传文件不能为空", "请求ID：" + requestId);
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".xlsx")) {
            throw new ServiceException("VALIDATION_ERROR", "文件格式错误，请上传Excel文件(.xlsx)", "请求ID：" + requestId);
        }

        // 2. 解析Excel文件
        List<BatchImportAppDataDTO> appList;
        try {
            appList = ExcelUtil.parseAppTemplateExcel(file);
            log.info("Excel解析成功，共解析到{}条应用数据", appList.size());
        } catch (Exception e) {
            log.error("解析Excel文件失败，请求ID：{}", requestId, e);
            throw new ServiceException("EXCEL_PARSE_ERROR", "Excel文件解析失败：" + e.getMessage(), e);
        }

        if (appList.isEmpty()) {
            throw new ServiceException("EXCEL_NO_DATA", "Excel文件中没有有效的应用数据");
        }

        // 3. 统计信息
        int totalNum = appList.size();
        int succNum = 0;
        int failNum = 0;
        List<BatchImportAppResponseDTO.AddAppResult> addAppResults = new ArrayList<>();

        // 4. 逐个处理应用数据
        for (BatchImportAppDataDTO appData : appList) {
            BatchImportAppResponseDTO.AddAppResult result = new BatchImportAppResponseDTO.AddAppResult();
            result.setAppName(appData.getAppName());

            // 验证必填字段
            String validationError = validateBatchApp(appData);
            if (validationError != null) {
                result.setStatus(0);
                result.setErrorMsg(validationError);
                failNum++;
            } else {
                try {
                    // 转换为AddAppRequestDTO并调用现有的addApp方法
                    AddAppRequestDTO addRequest = convertToAddAppRequest(appData);
                    addApp(addRequest, requestId);

                    result.setStatus(1);
                    succNum++;
                } catch (ServiceException e) {
                    // ServiceException包含具体的业务错误信息，记录并继续处理下一个
                    log.warn("处理第{}行应用数据失败，应用名称：{}，错误：{}",
                            appData.getRowIndex(), appData.getAppName(), e.getMessage());
                    result.setStatus(0);
                    result.setErrorMsg(e.getMessage());
                    failNum++;
                } catch (Exception e) {
                    // 其他未预期的异常，记录并继续处理
                    log.warn("处理第{}行应用数据发生未预期异常，应用名称：{}，错误：{}",
                            appData.getRowIndex(), appData.getAppName(), e.getMessage());
                    result.setStatus(0);
                    result.setErrorMsg("系统异常：" + e.getMessage());
                    failNum++;
                }
            }

            addAppResults.add(result);
        }

        // 5. 构造响应结果
        BatchImportAppResponseDTO.OptionField optionFieldData = new BatchImportAppResponseDTO.OptionField();
        optionFieldData.setTotalNum(totalNum);
        optionFieldData.setSuccNum(succNum);
        optionFieldData.setFailNum(failNum);
        optionFieldData.setAddAppResults(addAppResults);

        BatchImportAppResponseDTO response = BatchImportAppResponseDTO.success(requestId);
        response.setOptionField(optionFieldData);

        log.info("批量导入应用完成，请求ID：{}，总数：{}，成功：{}，失败：{}", requestId, totalNum, succNum, failNum);

        return response;
    }

    /**
     * 验证批量导入应用数据的必填字段
     */
    private String validateBatchApp(BatchImportAppDataDTO appData) {
        if (StrUtil.isBlank(appData.getAppName())) {
            return "第" + appData.getRowIndex() + "行：应用名称不能为空";
        }
        if (StrUtil.isBlank(appData.getAppGroupName())) {
            return "第" + appData.getRowIndex() + "行：应用分组不能为空";
        }
        if (StrUtil.isBlank(appData.getAppType())) {
            return "第" + appData.getRowIndex() + "行：类型不能为空";
        }

        // 当类型为五元组时，协议是必填的
        if ("五元组".equals(appData.getAppType()) && StrUtil.isBlank(appData.getAgreementType())) {
            return "第" + appData.getRowIndex() + "行：类型选择五元组时协议不能为空";
        }

        // 当类型为DSCP时，DSCP值是必填的
        if ("DSCP".equals(appData.getAppType()) && StrUtil.isBlank(appData.getInternetAppDSCP())) {
            return "第" + appData.getRowIndex() + "行：类型选择DSCP时DSCP值不能为空";
        }

        return null;
    }

    /**
     * 将批量导入DTO转换为新增应用DTO
     */
    private AddAppRequestDTO convertToAddAppRequest(BatchImportAppDataDTO appData) {
        AddAppRequestDTO addRequest = new AddAppRequestDTO();

        // 基本信息
        addRequest.setAppId(appData.getAppId());
        addRequest.setAppName(appData.getAppName());
        addRequest.setAppType(appData.getAppType());
        addRequest.setAgreementType(appData.getAgreementType());
        addRequest.setInternetAppDSCP(appData.getInternetAppDSCP());
        addRequest.setAppGroupName(appData.getAppGroupName());

        // IPv4信息
        addRequest.setSourceIP(appData.getSourceIP());
        addRequest.setSourcePort(appData.getSourcePort());

        // 构建IPv4目的IP列表
        List<String> destinationIPList = new ArrayList<>();
        addDestinationIP(destinationIPList, appData.getDestinationIP1(), appData.getDestinationPort1());
        addDestinationIP(destinationIPList, appData.getDestinationIP2(), appData.getDestinationPort2());
        addDestinationIP(destinationIPList, appData.getDestinationIP3(), appData.getDestinationPort3());
        addDestinationIP(destinationIPList, appData.getDestinationIP4(), appData.getDestinationPort4());
        addDestinationIP(destinationIPList, appData.getDestinationIP5(), appData.getDestinationPort5());
        addDestinationIP(destinationIPList, appData.getDestinationIP6(), appData.getDestinationPort6());
        addDestinationIP(destinationIPList, appData.getDestinationIP7(), appData.getDestinationPort7());
        addDestinationIP(destinationIPList, appData.getDestinationIP8(), appData.getDestinationPort8());
        addDestinationIP(destinationIPList, appData.getDestinationIP9(), appData.getDestinationPort9());
        addDestinationIP(destinationIPList, appData.getDestinationIP10(), appData.getDestinationPort10());
        addRequest.setDestinationIPList(destinationIPList);

        // IPv6信息
        addRequest.setSourceIPv6(appData.getSourceIPv6());
        addRequest.setSourcePortIPv6(appData.getSourcePortIPv6());

        // 构建IPv6目的IP列表
        List<String> destinationIPv6List = new ArrayList<>();
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_1(), appData.getDestinationPortIPv6_1());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_2(), appData.getDestinationPortIPv6_2());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_3(), appData.getDestinationPortIPv6_3());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_4(), appData.getDestinationPortIPv6_4());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_5(), appData.getDestinationPortIPv6_5());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_6(), appData.getDestinationPortIPv6_6());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_7(), appData.getDestinationPortIPv6_7());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_8(), appData.getDestinationPortIPv6_8());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_9(), appData.getDestinationPortIPv6_9());
        addDestinationIP(destinationIPv6List, appData.getDestinationIPv6_10(), appData.getDestinationPortIPv6_10());
        addRequest.setDestinationIPv6List(destinationIPv6List);

        return addRequest;
    }

    /**
     * 添加目的IP和端口到列表中
     */
    private void addDestinationIP(List<String> destinationList, String ip, String port) {
        if (StrUtil.isNotBlank(ip)) {
            if (StrUtil.isNotBlank(port)) {
                destinationList.add(ip + "&" + port);
            } else {
                destinationList.add(ip);
            }
        }
    }
}