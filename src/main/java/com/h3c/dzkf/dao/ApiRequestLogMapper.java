package com.h3c.dzkf.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.h3c.dzkf.entity.ApiRequestLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ApiRequestLogMapper extends BaseMapper<ApiRequestLog> {
    
    /**
     * 删除指定天数前的日志数据
     * @param days 保留天数
     * @return 删除的记录数
     */
    int deleteOldLogs(@Param("days") int days);
    
    /**
     * 统计指定天数前的日志数量
     * @param days 保留天数
     * @return 符合条件的记录数
     */
    long countOldLogs(@Param("days") int days);
    
    /**
     * 分批删除指定天数前的日志数据
     * @param days 保留天数
     * @param batchSize 批次大小
     * @return 删除的记录数
     */
    int deleteOldLogsBatch(@Param("days") int days, @Param("batchSize") int batchSize);
} 