package com.h3c.dzkf.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.h3c.dzkf.entity.ServiceClassPool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * ServiceClass池管理Mapper接口
 */
@Mapper
public interface ServiceClassPoolMapper extends BaseMapper<ServiceClassPool> {

    /**
     * 获取下一个可用的ServiceClass（优先使用已回收的）
     * 
     * @return 可用的ServiceClass值
     */
    @Select("SELECT service_class FROM service_class_pool WHERE is_used = 0 ORDER BY service_class LIMIT 1")
    Integer getNextAvailableServiceClass();

    /**
     * 分配ServiceClass
     *
     * @param serviceClass ServiceClass值
     * @param teGroupId 隧道组ID
     * @param policyDirection Policy方向
     * @return 更新的行数
     */
    int allocateServiceClass(@Param("serviceClass") Integer serviceClass,
                             @Param("teGroupId") Long teGroupId,
                           @Param("policyDirection") String policyDirection);

    /**
     * 回收ServiceClass
     *
     * @param teGroupId 隧道组ID
     * @return 更新的行数
     */
    int releaseServiceClassByTeGroupId(@Param("teGroupId") Long teGroupId);
} 