server:
  port: 28000
  servlet:
    context-path: /gszh-srv6-api

spring:
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher
  datasource:
    url: *****************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 60000
      connection-timeout: 30000

mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  type-aliases-package: com.h3c.dzkf.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 数据清理配置（生产环境）
cleanup:
  api-log:
    # 定时清理API请求日志的cron表达式（每天0点10分执行）
    cron: 0 10 0 * * ?
    # 保留的日志天数（仅保留近3天的数据，不含执行当天）
    keep-days: 3
    # 性能优化配置（生产环境建议启用）
    performance-optimization: true  # 是否启用性能优化模式（分批删除）
    batch-size: 2000               # 分批删除的批次大小（生产环境可适当增大）
    max-delete-count: 100000       # 单次任务最大删除数量限制（生产环境适当增大）
    batch-delay-ms: 200            # 批次间延迟时间（毫秒，生产环境适当增大）

# UC配置
uclinker:
  rest:
    connectTimeout: 120
    readTimeout: 120
    writeTimeout: 120
    filterSpecialCharacters: on
    netLog: true
    netLogStatisticsPeriod: 3600
    netLogStatisticsDetail: false
    #token刷新频率间隔，单位：秒
    tokenRefresh: 600

    ## Main Server ,账号密码加密使用 CryUtil.aesEncrypt(String before)
    host: ***********
    port: 30000
    protocol: http
    username: admin
    password: Pwd@12345

# ADWAN平台配置
adwan:
  platform:
    # 平台基础URL
    baseUrl: http://***********:30000
    # 新增设备接口路径
    addDeviceUrl: /adwan/proxy/nfm/physicalNetwork/node/addNode
    # 删除设备接口路径
    deleteDeviceUrl: /adwan/proxy/nfm/physicalNetwork/node/deleteMaintainedNodes
    # 查询设备接口路径
    getNodesUrl: /adwan/proxy/nfm/physicalNetwork/node/getNodes
    # 更新设备接口路径
    updateNodeUrl: /adwan/proxy/nfm/physicalNetwork/node/updateNode
    # 查询接口信息路径
    getBatchInterfaceListUrl: /adwan/proxy/nfm/physicalNetwork/node/getBatchInterfaceList
    # 新增链路接口路径
    addLinkUrl: /adwan/proxy/nfm/physicalNetwork/link/addLink
    # 更新链路接口路径
    updateLinkUrl: /adwan/proxy/nfm/physicalNetwork/link/updateLink
    # 查询链路接口路径
    getLinkUrl: /adwan/proxy/nfm/physicalNetwork/link/getLink
    # 查询链路概览接口路径
    getLinkOverViewUrl: /adwan/proxy/oam/linkOverView/getLinkOverView
    # 查询设备接口信息路径
    getInterfacesUrl: /adwan/proxy/nfm/physicalNetwork/interface/getInterfaces
    # 查询NETCONF模板信息路径
    getNetconfTemplateUrl: /adwan/proxy/nfm/template/getNetconfTemplate
    # 查询SNMP模板信息路径
    getSnmpTemplateUrl: /adwan/proxy/nfm/template/getSnmpTemplate
    # 维护设备接口路径
    maintainDeviceUrl: /adwan/proxy/nfm/template/node/maintain
    # 新增ACL模板接口路径
    addAclTemplateUrl: /qostrs/qos/acl
    # 删除ACL模板接口路径
    deleteAclTemplateUrl: /qostrs/qos/acl/all
    # 查询ACL模板接口路径
    queryAclTemplatesUrl: /qostrs/qos/acl
    # 更新ACL模板接口路径
    updateAclTemplateUrl: /qostrs/qos/acl/all
    # 新增SRv6 Policy应用组接口路径
    addSrv6PolicyGroupUrl: /adwan/proxy/vas/srv6PolicyGroup/addSrv6PolicyGroup
    # 更新SRv6 Policy应用组作用域接口路径
    updateSrv6PolicyGroupNetworkUrl: /adwan/proxy/vas/srv6PolicyGroup/updateSrv6PolicyGroupNetwork
    # 查询SRv6 Policy应用组接口路径
    getSrv6PolicyGroupUrl: /adwan/proxy/vas/srv6PolicyGroup/getSrv6PolicyGroup
    # 删除SRv6 Policy应用组接口路径
    deleteSrv6PolicyGroupUrl: /adwan/proxy/vas/srv6PolicyGroup/deleteSrv6PolicyGroup
    # 查询SRv6 Policy应用组详情接口路径
    getSrv6PolicyGroupDetailUrl: /adwan/proxy/vas/srv6PolicyGroup/getSrv6PolicyGroupDetail
    # 更新SRv6 Policy应用组接口路径
    updateSrv6PolicyGroupUrl: /adwan/proxy/vas/srv6PolicyGroup/updateSrv6PolicyGroup
    # 规划应用组路径接口路径
    startPlanSrv6PolicyGroupUrl: /adwan/proxy/vas/srv6PolicyGroup/startPlanSrv6PolicyGroup
    # 部署应用组配置接口路径
    deploySrv6PolicyGroupUrl: /adwan/proxy/vas/srv6PolicyGroup/deploySrv6PolicyGroup
    # 查询BFD模板接口路径
    getBfdTemplateUrl: /adwan/proxy/vas/bfd/template/get
    # 查询设备硬件版本信息接口路径
    getInventoryInfoUrl: /inventoryInfo
    # 获取自定义网络作用域接口路径
    getCustomNetworkScopeUrl: /adwan/proxy/vas/srv6PolicyGroupNetwork/getCustomNetworkScope
    # 获取SRv6策略轨迹接口路径
    getSrv6PolicyTrailUrl: /adwan/proxy/vas/srv6PolicyTrail/getSrv6PolicyTrail
    # 获取SRv6策略轨迹部署详情接口路径
    getSrv6PolicyTrailDeployDetailUrl: /adwan/proxy/vas/srv6PolicyTrail/getSrv6PolicyTrailDeployDetail
    # QoS相关接口路径
    # 新增流分类接口路径
    addQosClassifierUrl: /qostrs/qos/classifier
    # 修改流分类接口路径
    updateQosClassifierUrl: /qostrs/qos/classifier/all
    # 删除流分类和设备模板
    deleteQosClassifierUrl: /qostrs/qos/classifier/all
    # 查询流分类列表接口路径
    getQosClassifierListUrl: /qostrs/qos/classifier
    # 新增流行为接口路径
    addQosBehaviorUrl: /qostrs/qos/behavior
    # 查询流行为详情接口路径
    getQosBehaviorDetailUrl: /qostrs/qos/behavior/detail
    # 修改流行为接口路径
    updateQosBehaviorUrl: /qostrs/qos/behavior/all
    # 修改流行为和设备模板
    deleteQosBehaviorUrl: /qostrs/qos/behavior/all
    # 新增流策略接口路径
    addQosPolicyUrl: /qostrs/qos/policy
    # 删除流策略和设备引用
    deleteQosPolicyUrl: /qostrs/qos/policy/all
    # 修改流策略接口路径
    updateQosPolicyUrl: /qostrs/qos/policy/all
    # 查询流策略列表接口路径
    getQosPolicyListUrl: /qostrs/qos/policy
    # 查询流策略CB对
    getQosPolicyDetailRuleUrl: /qostrs/qos/policy/detail/rule
    # 部署流策略到设备接口路径
    deployQosPolicyUrl: /qostrs/qos/policy/deploy/devif
    # 查询QoS设备列表接口路径
    getQosDeviceListUrl: /qostrs/qos/device
    # 查询设备接口列表接口路径
    getQosDeviceInterfaceListUrl: /qostrs/qos/device/if
    # 查询ACL模板详情接口路径
    getAclDetailUrl: /qostrs/qos/acl/detail
    # 查询流策略部署历史接口路径
    getQosPolicyDeployHistoryUrl: /qostrs/qos/policy/deploy/devif

# 测试模式配置（生产环境关闭）
test:
  mode:
    # 是否启用测试模式
    enabled: false
    # 是否跳过token验证
    skipTokenValidation: false
    # 是否模拟ADWAN平台调用
    mockAdwanPlatform: false
    # 模拟返回的设备ID范围
    mockDeviceIdRange:
      min: 100000000
      max: 999999999

# 设备型号配置
device:
  model:
    # 生产环境使用外部文件系统路径（当前项目外部目录）
    config-file: D:\IdeaProjects\other\h3c\gongshangzonghang\gszh-api\external-config\device-models.json
  # 设备API配置
  api:
    # 基础URL - 生产环境配置
    base-url: http://************:30000
    # 资源查询接口路径
    resource-query-url: /ucpresource/v2/resources
    # 设备详情查询接口路径
    device-detail-url: /resrs/device
    # 性能数据查询接口路径
    performance-data-url: /perfrs/data/queryTaskPerfData

# OAuth配置
oauth:
  token:
    # DES加密配置（用于获取Token接口）
    des:
      # DES加密密钥，必须是8字节长度
      # 生产环境使用更安全的密钥，用于OAuth获取Token接口的参数加密
      key: Prod@Key 