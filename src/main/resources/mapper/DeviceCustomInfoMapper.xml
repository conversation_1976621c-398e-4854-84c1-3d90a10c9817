<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.DeviceCustomInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.DeviceCustomInfo">
        <id column="id" property="id" />
        <result column="device_id" property="deviceId" />
        <result column="platform_node_id" property="platformNodeId" />
        <result column="device_site_id" property="deviceSiteId" />
        <result column="device_site" property="deviceSite" />
        <result column="device_role" property="deviceRole" />
        <result column="is_rr" property="isRr" />
        <result column="device_plane_id" property="devicePlaneId" />
        <result column="device_ipv6" property="deviceIpv6" />
        <result column="device_group" property="deviceGroup" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_id, platform_node_id, device_site_id, device_site, device_role, is_rr, device_plane_id, device_ipv6, device_group, create_time, update_time, is_deleted
    </sql>

</mapper> 