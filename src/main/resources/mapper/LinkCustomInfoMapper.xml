<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.LinkCustomInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.LinkCustomInfo">
        <id column="id" property="id" />
        <result column="platform_link_id" property="platformLinkId" />
        <result column="site_id" property="siteId" />
        <result column="link_type" property="linkType" />
        <result column="link_level" property="linkLevel" />
        <result column="link_status" property="linkStatus" />
        <result column="link_name" property="linkName" />
        <result column="source_device_id" property="sourceDeviceId" />
        <result column="source_device_name" property="sourceDeviceName" />
        <result column="source_device_port" property="sourceDevicePort" />
        <result column="source_interface_id" property="sourceInterfaceId" />
        <result column="target_device_id" property="targetDeviceId" />
        <result column="target_device_name" property="targetDeviceName" />
        <result column="target_device_port" property="targetDevicePort" />
        <result column="target_interface_id" property="targetInterfaceId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, platform_link_id, site_id, link_type, link_level, link_status, link_name, 
        source_device_id, source_device_name, source_device_port, source_interface_id, 
        target_device_id, target_device_name, target_device_port, target_interface_id, 
        create_time, update_time, is_deleted
    </sql>

</mapper> 