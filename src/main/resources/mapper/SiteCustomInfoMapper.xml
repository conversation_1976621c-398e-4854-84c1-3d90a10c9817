<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.SiteCustomInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.SiteCustomInfo">
        <id column="id" property="id" />
        <result column="site_id" property="siteId" />
        <result column="site_name" property="siteName" />
        <result column="parent_site_id" property="parentSiteId" />
        <result column="location" property="location" />
        <result column="site_type" property="siteType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, site_id, site_name, parent_site_id, location, site_type, create_time, update_time, is_deleted
    </sql>

</mapper> 