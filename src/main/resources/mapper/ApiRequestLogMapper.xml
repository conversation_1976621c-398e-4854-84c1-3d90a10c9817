<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.ApiRequestLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.ApiRequestLog">
        <id column="id" property="id" />
        <result column="request_id" property="requestId" />
        <result column="request_uri" property="requestUri" />
        <result column="request_method" property="requestMethod" />
        <result column="request_body" property="requestBody" />
        <result column="response_body" property="responseBody" />
        <result column="request_time" property="requestTime" />
        <result column="duration_ms" property="durationMs" />
        <result column="client_ip" property="clientIp" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, request_id, request_uri, request_method, request_body, response_body, request_time, duration_ms, client_ip, create_time
    </sql>

    <!-- 删除指定天数前的日志数据 -->
    <delete id="deleteOldLogs">
        DELETE FROM api_request_log 
        WHERE create_time &lt; DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
    </delete>

    <!-- 统计指定天数前的日志数量 -->
    <select id="countOldLogs" resultType="long">
        SELECT COUNT(*)
        FROM api_request_log 
        WHERE create_time &lt; DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
    </select>

    <!-- 分批删除指定天数前的日志数据 -->
    <delete id="deleteOldLogsBatch">
        DELETE FROM api_request_log 
        WHERE create_time &lt; DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        LIMIT #{batchSize}
    </delete>

</mapper> 