<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.h3c.dzkf.dao.AppCustomInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.AppCustomInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="INTEGER"/>
        <result column="app_name" property="appName" jdbcType="VARCHAR"/>
        <result column="app_type" property="appType" jdbcType="VARCHAR"/>
        <result column="internet_app_dscp" property="internetAppDSCP" jdbcType="VARCHAR"/>
        <result column="agreement_type" property="agreementType" jdbcType="VARCHAR"/>
        <result column="source_ip" property="sourceIP" jdbcType="VARCHAR"/>
        <result column="source_port" property="sourcePort" jdbcType="VARCHAR"/>
        <result column="destination_ip_list" property="destinationIPList" jdbcType="LONGVARCHAR"/>
        <result column="source_ipv6" property="sourceIPv6" jdbcType="VARCHAR"/>
        <result column="source_port_ipv6" property="sourcePortIPv6" jdbcType="VARCHAR"/>
        <result column="destination_ipv6_list" property="destinationIPv6List" jdbcType="LONGVARCHAR"/>
        <result column="app_group_name" property="appGroupName" jdbcType="VARCHAR"/>
        <result column="app_group_id" property="appGroupId" jdbcType="BIGINT"/>
        <result column="acl_id_list" property="aclIdList" jdbcType="LONGVARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列名 -->
    <sql id="Base_Column_List">
        id, app_id, app_name, app_type, internet_app_dscp, agreement_type,
        source_ip, source_port, destination_ip_list, source_ipv6, source_port_ipv6,
        destination_ipv6_list, app_group_name, app_group_id, acl_id_list,
        create_time, update_time, is_deleted
    </sql>

</mapper> 