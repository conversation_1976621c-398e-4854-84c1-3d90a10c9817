<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.ScheduleCustomInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.ScheduleCustomInfo">
        <id column="id" property="id"/>
        <result column="app_schedule_id" property="appScheduleId"/>
        <result column="app_schedule_name" property="appScheduleName"/>
        <result column="app_group_name" property="appGroupName"/>
        <result column="drainage_type" property="drainageType"/>
        <result column="network_ids" property="networkIds"/>
        <result column="vpn_id" property="vpnId"/>
        <result column="classifier_id" property="classifierId"/>
        <result column="classifier_name" property="classifierName"/>
        <result column="behavior_id" property="behaviorId"/>
        <result column="behavior_name" property="behaviorName"/>
        <result column="policy_info" property="policyInfo"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , app_schedule_id, app_schedule_name, app_group_name, drainage_type, network_ids, vpn_id,
        classifier_id, classifier_name, behavior_id, behavior_name, policy_info,
        create_time, update_time, is_deleted
    </sql>

</mapper>
