<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.TeTunnelTemplateCustomInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.TeTunnelTemplateCustomInfo">
        <id column="id" property="id" />
        <result column="strategy_id" property="strategyId" />
        <result column="strategy_name" property="strategyName" />
        <result column="sla_id" property="slaId" />
        <result column="bandwidth" property="bandwidth" />
        <result column="up_allow_link_priority" property="upAllowLinkPriority" />
        <result column="down_allow_link_priority" property="downAllowLinkPriority" />
        <result column="packet_loss_rate" property="packetLossRate" />
        <result column="delay_time" property="delayTime" />
        <result column="network_jitter" property="networkJitter" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , strategy_id, strategy_name, sla_id, bandwidth,
        up_allow_link_priority, down_allow_link_priority, packet_loss_rate, 
        delay_time, network_jitter, create_time, update_time, is_deleted
    </sql>

</mapper> 