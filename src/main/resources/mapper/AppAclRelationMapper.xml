<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.h3c.dzkf.dao.AppAclRelationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.AppAclRelation">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="app_custom_id" property="appCustomId" jdbcType="BIGINT"/>
        <result column="app_id" property="appId" jdbcType="INTEGER"/>
        <result column="acl_id" property="aclId" jdbcType="INTEGER"/>
        <result column="ip_version" property="ipVersion" jdbcType="TINYINT"/>
        <result column="destination_ip" property="destinationIp" jdbcType="VARCHAR"/>
        <result column="destination_port" property="destinationPort" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础列名 -->
    <sql id="Base_Column_List">
        id, app_custom_id, app_id, acl_id, ip_version, destination_ip, destination_port,
        create_time, update_time, is_deleted
    </sql>

</mapper> 