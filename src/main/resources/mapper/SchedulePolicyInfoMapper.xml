<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.SchedulePolicyInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.h3c.dzkf.entity.SchedulePolicyInfo">
        <id column="id" property="id"/>
        <result column="app_schedule_id" property="appScheduleId"/>
        <result column="policy_id" property="policyId"/>
        <result column="policy_name" property="policyName"/>
        <result column="device_id" property="deviceId"/>
        <result column="platform_node_id" property="platformNodeId"/>
        <result column="device_ip" property="deviceIp"/>
        <result column="classifier_id" property="classifierId"/>
        <result column="behavior_id" property="behaviorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , app_schedule_id, policy_id, policy_name, device_id, platform_node_id, device_ip,
        classifier_id, behavior_id, create_time, update_time, is_deleted
    </sql>

</mapper>
