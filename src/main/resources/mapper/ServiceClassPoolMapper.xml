<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.h3c.dzkf.dao.ServiceClassPoolMapper">

    <!-- 分配ServiceClass -->
    <update id="allocateServiceClass">
        UPDATE service_class_pool 
        SET is_used = 1,
            te_group_id = #{teGroupId},
            policy_direction = #{policyDirection},
            allocated_time = NOW(),
            update_time = NOW()
        WHERE service_class = #{serviceClass} AND is_used = 0
    </update>

    <!-- 回收ServiceClass -->
    <update id="releaseServiceClassByTeGroupId">
        UPDATE service_class_pool 
        SET is_used = 0,
            te_group_id = NULL,
            policy_direction = NULL,
            allocated_time = NULL,
            update_time = NOW()
        WHERE te_group_id = #{teGroupId}
    </update>

</mapper> 