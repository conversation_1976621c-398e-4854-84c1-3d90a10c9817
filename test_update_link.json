{"description": "工商总行修改链路接口测试", "url": "http://localhost:8080/srv6/updateLink", "method": "PUT", "headers": {"Content-Type": "application/json;charset=UTF-8", "X-Access-Token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJnc3poLWFwaSIsImlhdCI6MTczNzYwMDAwMCwiZXhwIjoxNzM3Njg2NDAwfQ.mockToken"}, "request_body": {"linkId": "279062257336323", "linkName": "更新后的链路名称", "linkBandWidth": "10Gbps", "linkType": "BACKUP", "linkLevel": "2", "linkStatus": "ACTIVE"}, "expected_response": {"requestId": "20241223001", "linkId": "279062257336323", "failReason": null, "optionField": "link<PERSON><PERSON>,linkBandWidth,linkType"}, "test_scenarios": [{"name": "仅更新链路名称", "request": {"linkId": "279062257336323", "linkName": "新的链路名称"}}, {"name": "仅更新带宽", "request": {"linkId": "279062257336323", "linkBandWidth": "5Gbps"}}, {"name": "更新定制字段", "request": {"linkId": "279062257336323", "linkType": "PRIMARY", "linkLevel": "1", "linkStatus": "MAINTENANCE"}}, {"name": "全量更新", "request": {"linkId": "279062257336323", "linkName": "完整更新的链路", "linkBandWidth": "100Gbps", "linkType": "BACKUP", "linkLevel": "3", "linkStatus": "ACTIVE"}}], "curl_example": "curl -X PUT 'http://localhost:8080/srv6/updateLink' \\\n  -H 'Content-Type: application/json;charset=UTF-8' \\\n  -H 'X-Access-Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJnc3poLWFwaSIsImlhdCI6MTczNzYwMDAwMCwiZXhwIjoxNzM3Njg2NDAwfQ.mockToken' \\\n  -d '{\n    \"linkId\": \"279062257336323\",\n    \"linkName\": \"更新后的链路名称\",\n    \"linkBandWidth\": \"10Gbps\",\n    \"linkType\": \"BACKUP\"\n  }'"}