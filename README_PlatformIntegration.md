# 平台接口集成说明

## 概述

本文档详细介绍了系统与ADWAN平台链路总览接口的集成实现，包括获取实时性能数据、反向链路查询、错误处理等核心功能。

## 核心功能

### 1. 平台链路总览集成

#### 接口信息
- **平台接口**: `POST /oam/linkOverView/getLinkOverView`
- **调用方式**: HTTP POST
- **数据格式**: JSON
- **超时时间**: 30秒

#### 查询策略 🚀
- **精准接口查询（新增）**: 基于源设备接口ID (srcTpName) 实现精准匹配
- **批量查询**: 一次性查询所有链路的实时性能数据
- **按设备过滤**: 支持按设备名称进行精确查询
- **分页支持**: 自动处理大量数据的分页查询
- **失败降级**: 平台接口失败时仍返回定制库基本数据

#### 精准查询优势 ⭐
1. **减少数据传输**: 只查询相关接口的链路，避免全量数据传输
2. **提高查询精度**: 基于接口ID精确匹配，消除模糊查询的干扰
3. **提升性能**: 减少不必要的网络传输和数据处理
4. **智能降级**: 接口信息缺失时自动回退到设备级查询

#### 查询流程
```
定制库链路信息 → 收集设备接口映射 → 按接口精准查询平台 → 数据合并处理
     ↓                    ↓                      ↓              ↓
[LinkCustomInfo]  [Device-Interface Map]  [Platform API]  [Complete DTO]
```

### 2. 反向链路查询

#### 实现逻辑
1. **链路匹配规则**：
   - 源设备和目标设备互换
   - 源接口和目标接口互换
   - 精确字符串匹配（区分大小写）

2. **性能数据分离**：
   - **发送数据**: 使用正向链路的性能指标
   - **接收数据**: 使用反向链路的性能指标
   - **降级处理**: 无反向链路时复用正向数据

#### 代码示例
```java
// 查找反向链路
PlatformLinkOverViewResponseDTO.LinkOverViewInfo reverseLinkInfo = findReverseLinkInfo(
    platformLinkInfo, platformLinkMap, linkCustomInfo);

// 设置发送性能数据（正向链路）
linkInfoDTO.setSendDelay(platformLinkInfo.getDelay().toString());
linkInfoDTO.setSendJitter(platformLinkInfo.getJitter().toString());
linkInfoDTO.setSendLoss(platformLinkInfo.getPacketLossRate().toString());

// 设置接收性能数据（反向链路）
if (reverseLinkInfo != null) {
    linkInfoDTO.setReceiveDelay(reverseLinkInfo.getDelay().toString());
    linkInfoDTO.setReceiveJitter(reverseLinkInfo.getJitter().toString());
    linkInfoDTO.setReceiveLoss(reverseLinkInfo.getPacketLossRate().toString());
}
```

### 3. 数据映射和转换

#### 链路状态翻译
| 平台状态值 | 中文描述 |
|-----------|---------|
| 0 | 离线 |
| 1 | 在线 |
| null | 未知 |

#### 性能数据处理
- **延时**: 毫秒单位，-1表示无数据
- **抖动**: 毫秒单位，-1表示无数据  
- **丢包率**: 百分比，-1表示无数据
- **带宽**: bps单位
- **带宽利用率**: 百分比，保留两位小数

### 4. 性能优化策略

#### 精准接口查询优化（新增）
```java
// 1. 收集设备接口映射关系
Map<String, List<String>> deviceInterfaceMap = collectDeviceInterfaceMap(linkCustomInfos);

// 示例映射结果:
// {
//   "**************": ["GigabitEthernet1/2/0", "GigabitEthernet1/1/0"],
//   "**************": ["GigabitEthernet1/2/0", "GigabitEthernet1/3/0"]
// }

// 2. 按设备和接口精准查询
for (Map.Entry<String, List<String>> entry : deviceInterfaceMap.entrySet()) {
    String deviceName = entry.getKey();
    List<String> interfaceNames = entry.getValue();
    
    // 构建精准查询请求
    PlatformLinkOverViewRequestDTO request = createRealtimeRequest(1, 100);
    request.setSrcDeviceName(Arrays.asList(deviceName));
    request.setSrcTpName(interfaceNames);  // 关键：接口级精准匹配
    
    queryLinksByDeviceInterfaces(deviceName, interfaceNames, platformLinkMap);
}
```

#### 批量查询优化
```java
// 收集所有相关设备
Set<String> deviceNames = linkCustomInfos.stream()
    .flatMap(link -> Stream.of(link.getSourceDeviceName(), link.getTargetDeviceName()))
    .filter(Objects::nonNull)
    .collect(Collectors.toSet());

// 分批查询减少API调用
int batchSize = 10;
for (int i = 0; i < deviceList.size(); i += batchSize) {
    queryLinksByDeviceBatch(deviceList.subList(i, Math.min(i + batchSize, deviceList.size())));
}
```

#### 查询性能对比
| 查询方式 | 数据传输量 | 查询精度 | 网络开销 | 处理时间 |
|---------|-----------|---------|---------|---------|
| 全量查询 | 100% | 低 | 高 | 慢 |
| 设备级查询 | 30-50% | 中 | 中 | 中等 |
| **接口级精准查询** | **10-20%** | **高** | **低** | **快** |

#### 缓存机制
- **内存缓存**: 单次请求内复用平台数据
- **失败容错**: 平台接口异常不影响定制库数据返回
- **性能监控**: 记录平台接口调用时间和成功率
- **智能降级**: 精准查询失败时自动回退到基础查询

## 错误处理

### 1. 平台接口异常
```java
try {
    PlatformLinkOverViewResponseDTO response = platformApiService.getLinkOverView(request);
    // 处理响应
} catch (Exception e) {
    log.error("调用平台链路总览接口异常", e);
    // 继续使用定制库数据，不抛异常
}
```

### 2. 数据异常处理
- **空值检查**: 所有平台数据字段进行空值验证
- **负数过滤**: 性能数据为负数时视为无效数据
- **类型转换**: 安全的数据类型转换和格式化

### 3. 降级策略
1. **平台接口失败**: 返回定制库基本数据
2. **反向链路缺失**: 使用正向链路数据
3. **性能数据异常**: 跳过异常字段，保留有效数据

## 配置和测试

### 1. 测试模式配置
```properties
# 启用测试模式，使用Mock数据
test.mock-adwan-platform=true

# 跳过Token验证
test.skip-token-validation=true
```

### 2. Mock数据说明
测试模式下返回标准的模拟数据：
- 链路ID: "283986525421569"
- 链路名称: "************** To ************** Link1 IPv6"
- 带宽: 58 bps
- 带宽利用率: 5.8%
- 延时/抖动/丢包率: -1（表示无数据）

### 3. 测试用例
提供8个测试用例覆盖：
- 基本查询功能
- 异常场景处理
- 性能数据验证
- 多级站点查询
- Token验证机制

## 监控指标

### 1. 关键性能指标
- **平台接口调用成功率**: >95%
- **平台接口响应时间**: <2秒
- **数据完整性**: 定制库+平台数据匹配率
- **反向链路匹配率**: 正向链路的反向匹配成功率

### 2. 日志监控
```java
// 平台接口调用日志
log.info("开始调用平台链路总览接口，查询{}条链路的性能数据", linkCount);
log.info("成功获取{}条平台链路总览数据", platformLinkCount);

// 反向链路查询日志  
log.debug("找到反向链路性能数据，链路ID：{}，反向链路ID：{}", linkId, reverseLinkId);
log.debug("未找到反向链路，使用正向链路数据，链路ID：{}", linkId);
```

## 版本演进

### 当前版本 (v1.1)
- ✅ 基础平台接口集成
- ✅ 实时数据查询
- ✅ 反向链路查询逻辑
- ✅ 错误处理和降级
- ✅ Mock测试支持
- ✅ **精准接口查询功能**（基于srcTpName的精准匹配）
- ✅ **智能查询降级**（精准查询失败时自动回退）

### 下一版本 (v1.2)
- 🔄 带宽使用率精确计算
- 🔄 历史数据查询支持
- 🔄 性能数据缓存机制
- 🔄 平台接口监控告警
- 🔄 **多路径链路查询**（支持复杂网络拓扑）

### 未来规划 (v2.0)
- 📋 多平台接口集成
- 📋 实时数据推送
- 📋 性能趋势分析
- 📋 智能故障诊断
- 📋 **AI智能查询优化**（基于历史查询模式优化）

## 注意事项

1. **网络依赖**: 确保与ADWAN平台网络连通
2. **认证配置**: 配置正确的平台访问凭证
3. **数据一致性**: 定期验证定制库与平台数据一致性
4. **性能影响**: 大量链路查询时注意接口响应时间
5. **错误监控**: 关注平台接口调用失败率和异常日志 