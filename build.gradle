plugins {
    id 'java'
    id 'org.springframework.boot' version '2.7.6'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = "com.h3c.dzkf"
version = "2.0.0"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(8)
    }
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

configurations.configureEach {
    resolutionStrategy {
        force 'log4j:log4j:1.2.17'
    }
}

repositories {
    mavenCentral()
    maven { url = uri("https://maven.aliyun.com/repository/public") }
}

dependencies {
    implementation fileTree(dir: "src/main/resources/lib", include: ["*.jar"])

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    runtimeOnly 'com.mysql:mysql-connector-j:8.2.0'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    implementation 'io.springfox:springfox-swagger2:2.6.1'
    implementation 'io.springfox:springfox-swagger-ui:2.6.1'

    implementation 'cn.hutool:hutool-all:5.3.3'
    implementation 'cn.hutool:hutool-json:5.8.2'
    implementation 'org.apache.httpcomponents:httpclient:4.5.13'
    implementation 'com.fasterxml.jackson.jaxrs:jackson-jaxrs-xml-provider:2.9.6'
    implementation 'com.squareup.okhttp3:okhttp:3.14.+'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.alibaba:fastjson:1.2.73'

    implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.5'
    implementation 'com.zaxxer:HikariCP:3.4.5'

    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.aspectj:aspectjweaver:1.9.19'

    implementation 'com.alibaba.fastjson2:fastjson2:2.0.42'

    implementation 'org.bouncycastle:bcprov-jdk16:1.46'
    
    // Apache POI for Excel processing
    implementation 'org.apache.poi:poi:5.2.4'
    implementation 'org.apache.poi:poi-ooxml:5.2.4'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
//    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
}

tasks.named('test') {
    useJUnitPlatform()
}
