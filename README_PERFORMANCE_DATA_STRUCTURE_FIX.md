# 性能数据接口结构修正说明

## 问题描述

在实际测试中发现，性能数据接口返回的JSON结构与原始DTO定义不匹配：

### 原始DTO定义问题
1. `data`字段定义为单个`PerfData`对象，但实际返回的是`PerfData`数组
2. `instanceData`字段定义为`Float`类型，但实际返回的是字符串类型

### 实际API响应格式
```json
{
  "errorCode": 0,
  "errorMessage": "成功",
  "success": null,
  "fail": null,
  "data": [
    {
      "taskId": 2,
      "instanceId": "[Entity:MPU]",
      "instanceName": "[Entity:MPU]",
      "devId": 4166665348641788,
      "instanceData": "23",
      "instanceDataUnit": "%",
      "collectTime": 1750738258
    }
  ]
}
```

## 修正内容

### 1. PerformanceDataResponseDTO结构修正

#### data字段类型修改
```java
// 修改前
private PerfData data;

// 修改后
private java.util.List<PerfData> data;
```

#### instanceData字段类型修改
```java
// 修改前
private Float instanceData;

// 修改后
private String instanceData;
```

### 2. DeviceServiceImpl处理逻辑修正

#### 数组遍历修改
```java
// 修改前
PerformanceDataResponseDTO.PerfData data = perfData.getData();

// 修改后
for (PerformanceDataResponseDTO.PerfData data : perfData.getData()) {
    // 处理每个实例的数据
}
```

#### 字符串转换处理
```java
// 修改前
response.setCpuUseRate(Math.round(data.getInstanceData()));

// 修改后
Float cpuValue = parseFloatValue(data.getInstanceData());
if (cpuValue != null) {
    response.setCpuUseRate(Math.round(cpuValue));
}
```

### 3. 新增工具方法

添加了`parseFloatValue(String value)`方法来安全地将字符串转换为Float：

```java
private Float parseFloatValue(String value) {
    if (value == null || value.trim().isEmpty()) {
        return null;
    }
    try {
        return Float.parseFloat(value.trim());
    } catch (NumberFormatException e) {
        log.warn("无法解析Float值：{}，错误：{}", value, e.getMessage());
        return null;
    }
}
```

### 4. 模拟数据修正

#### instanceData类型修正
```java
// 修改前
perfData.setInstanceData(23.0f);

// 修改后
perfData.setInstanceData("23");
```

#### data数组格式修正
```java
// 修改前
mockResponse.setData(perfData);

// 修改后
java.util.List<PerformanceDataResponseDTO.PerfData> dataList = new java.util.ArrayList<>();
dataList.add(perfData);
mockResponse.setData(dataList);
```

## 处理逻辑

1. **多实例处理**：遍历整个data数组，处理所有实例数据
   - CPU/内存：通常只有一个实例，取第一个有效值
   - 温度：收集所有温度传感器的值，计算平均值
   - 电源：收集所有电源模块的状态
   - 风扇：收集所有风扇的状态
2. **类型安全**：增加字符串到数字的安全转换
3. **ID提取**：智能提取instanceId中的数字作为设备ID
4. **错误处理**：转换失败时记录警告并继续处理
5. **向后兼容**：保持对外接口不变，内部处理透明

## 影响范围

- ✅ 正常性能数据查询不受影响
- ✅ 测试模式兼容性保持
- ✅ 错误处理机制完善
- ✅ 日志记录优化

## 测试建议

1. 验证CPU、内存数据的正确解析（单实例）
2. 验证温度数据的多实例收集和平均值计算
3. 验证电源、风扇状态的多实例收集
4. 验证instanceId中数字提取的正确性
5. 验证异常数据格式的错误处理
6. 验证测试模式下的多实例模拟数据

## 总结

此次修正确保了性能数据接口能够正确解析实际API返回的JSON格式，提高了系统的健壮性和数据处理的准确性。 