# 修改应用接口 - ACL重建逻辑说明（V3版本）

## 更新说明

本次更新在修改应用接口中新增了ACL重建逻辑，当特定字段发生变化时，系统会先删除现有ACL模板，然后重新创建新的ACL模板。

## 业务规则

### 1. 字段变化类型分类

修改应用时，根据字段变化类型采用不同的处理策略：

#### 1.1 仅更新定制库
- **触发条件**：仅 `appType`、`internetAppDSCP` 字段变化
- **处理方式**：只更新本地定制库，不调用平台接口

#### 1.2 重建ACL（删除+重新创建）
- **触发条件**：`appName`、`agreementType`、`appGroupName` 任一字段变化
- **处理方式**：
  1. 删除现有ACL模板
  2. 清理ACL关系记录
  3. 重新创建ACL模板
  4. 更新定制库

#### 1.3 更新ACL（直接修改）
- **触发条件**：其他ACL相关字段变化（`sourceIP`、`sourcePort`、`destinationIPList`、`sourceIPv6`、`sourcePortIPv6`、`destinationIPv6List`）
- **处理方式**：
  1. 调用平台更新ACL接口
  2. 更新ACL关系记录
  3. 更新定制库

### 2. 重建ACL流程详解

#### 2.1 删除现有ACL
```java
// 查询现有ACL关系
Map<Integer, List<AppAclRelation>> existingAclsByVersion = getAppAclsByIpVersion(appId);

// 按版本删除ACL模板
for (Integer aclId : aclIds) {
    boolean deleteResult = platformApiService.deleteAclTemplate(aclId, requestId);
}
```

#### 2.2 清理关系记录
```java
// 清理本地ACL关系记录
cleanupAclRelationRecords(appId, requestId);
```

#### 2.3 重新创建ACL
```java
// 使用新参数创建ACL模板
List<Integer> newAclIdList = createAclTemplatesForModify(request, requestId, appCustomId);

// 更新应用信息
updateAppCustomInfoWithRecreate(existingApp, request, appGroup, newAclIdList, requestId);
```

## 关键实现细节

### 1. 重建判断逻辑
```java
private boolean isNeedRecreateAcl(AppCustomInfo existingApp, ModifyAppRequestDTO request) {
    boolean appNameChanged = !Objects.equals(existingApp.getAppName(), request.getAppName());
    boolean agreementTypeChanged = !Objects.equals(existingApp.getAgreementType(), request.getAgreementType());
    boolean appGroupNameChanged = !Objects.equals(existingApp.getAppGroupName(), request.getAppGroupName());
    
    return appNameChanged || agreementTypeChanged || appGroupNameChanged;
}
```

### 2. 分支处理逻辑
```java
if (needUpdateAcl) {
    if (needRecreateAcl) {
        // 删除并重新创建ACL
        return handleModifyAppWithRecreateAcl(existingApp, request, appGroup, requestId);
    } else {
        // 仅更新ACL
        return handleModifyAppWithUpdateAcl(existingApp, request, appGroup, requestId);
    }
} else {
    // 仅更新定制库
    return handleModifyAppOnly(existingApp, request, appGroup, requestId);
}
```

### 3. 错误处理
- **删除失败**：如果任一ACL模板删除失败，终止修改流程
- **创建失败**：如果重新创建ACL失败，返回错误信息
- **事务性**：确保删除和创建的原子性操作

## 测试场景

### 重建ACL场景
1. **测试场景10**：仅修改应用名称（触发重建）
2. **测试场景11**：仅修改协议类型（触发重建）
3. **测试场景12**：仅修改应用分组（触发重建）
4. **测试场景13**：同时修改多个重建字段

### 更新ACL场景
1. **测试场景14**：仅修改源IP（不触发重建）
2. **测试场景15**：仅修改目的IP列表（不触发重建）

## 日志记录

系统会记录详细的操作日志：

```
ACL重建需求分析，应用ID：{}，appName：{}，agreementType：{}，appGroupName：{}，需要重建：{}
开始处理需要重建ACL的修改应用，请求ID：{}，应用ID：{}
删除ACL模板成功，请求ID：{}，ACL ID：{}，IP版本：{}
修改应用成功（重建ACL），请求ID：{}，应用ID：{}，新ACL ID列表：{}
```

## 接口变更

### 新增方法
- `isNeedRecreateAcl()` - 判断是否需要重建ACL
- `handleModifyAppWithRecreateAcl()` - 处理重建ACL流程
- `cleanupAclRelationRecords()` - 清理ACL关系记录
- `createAclTemplatesForModify()` - 为修改创建ACL模板
- `updateAppCustomInfoWithRecreate()` - 更新应用信息（重建场景）

### 修改方法
- `modifyApp()` - 新增重建逻辑分支
- `handleModifyAppWithAcl()` - 重命名为 `handleModifyAppWithUpdateAcl()`

## 性能考虑

1. **批量删除**：按ACL ID分组，避免重复删除操作
2. **异常回滚**：删除失败时及时终止，避免数据不一致
3. **日志优化**：记录关键操作节点，便于问题排查

## 兼容性

- 保持原有接口参数不变
- 保持响应格式不变
- 新增逻辑对现有功能无影响
- 测试模式下支持模拟操作

---
**更新时间**：2024-12-19  
**版本**：V3.0  
**作者**：系统开发团队 