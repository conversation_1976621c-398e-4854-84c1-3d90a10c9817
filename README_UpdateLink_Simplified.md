# 工商总行链路更新接口（简化版）

## 概述
链路更新接口采用简化的工作模式：使用`getLink`接口仅验证链路存在性，然后按需更新用户指定的字段。

## 工作流程

### 简化的更新流程
1. **验证链路存在性**：从定制库查询链路信息
2. **验证平台存在性**：调用平台 `getLink` 接口验证链路在平台是否存在
3. **按需更新字段**：仅设置用户指定的字段（`linkName`、`linkBandWidth`）
4. **提交更新**：调用平台 `updateLink` 接口提交修改
5. **更新定制库**：更新定制库中的工行定制字段

## 关键特点

### 1. 最小化更新原则
```java
// 构建平台更新请求，只设置需要更新的字段
PlatformUpdateLinkRequestDTO platformUpdateRequest = new PlatformUpdateLinkRequestDTO();
platformUpdateRequest.setLinkId(Long.valueOf(request.getLinkId()));

// 仅设置用户指定的字段
if (StrUtil.isNotBlank(request.getLinkName())) {
    platformUpdateRequest.setLinkName(request.getLinkName());
    platformUpdateRequest.setLinkNameUserSet(1);
}

if (StrUtil.isNotBlank(request.getLinkBandWidth())) {
    Long bandwidth = parseBandwidth(request.getLinkBandWidth());
    platformUpdateRequest.setBandwidth(bandwidth);
    platformUpdateRequest.setResvBandwidth(bandwidth);
}
```

### 2. 存在性验证
```java
// getLink仅用于验证链路是否存在
PlatformGetLinkRequestDTO getLinkRequest = new PlatformGetLinkRequestDTO();
getLinkRequest.setLinkId(Long.valueOf(request.getLinkId()));

PlatformGetLinkResponseDTO platformLinkResponse = platformApiService.getLink(getLinkRequest);
if (platformLinkResponse == null || !Boolean.TRUE.equals(platformLinkResponse.getSuccessful()) 
    || platformLinkResponse.getResult() == null || platformLinkResponse.getResult().getRecords() == null 
    || platformLinkResponse.getResult().getRecords().isEmpty()) {
    return UpdateLinkResponseDTO.fail(requestId, "平台中不存在该链路");
}
```

## 接口详细信息

### 请求参数
- **linkId** (String, 必选) - 平台链路ID
- **linkName** (String, 可选) - 新的链路名称
- **linkBandWidth** (String, 可选) - 新的带宽（支持：100Mbps, 1Gbps等格式）
- **linkType** (String, 可选) - 工行定制：链路类型
- **linkLevel** (String, 可选) - 工行定制：链路等级
- **linkStatus** (String, 可选) - 工行定制：链路状态

### 响应参数
- **requestId** (String) - 请求ID
- **linkId** (String) - 链路ID
- **failReason** (String) - 失败原因（成功时为null）
- **optionField** (String) - 已更新的字段列表

## 测试场景

### 场景1：仅更新链路名称
```json
{
  "linkId": "279062257336323",
  "linkName": "新的链路名称"
}
```

**实际行为**：
1. 验证链路在平台是否存在
2. 仅设置 `linkName` 和 `linkNameUserSet` 字段
3. 不涉及其他平台字段

### 场景2：仅更新带宽
```json
{
  "linkId": "279062257336323",
  "linkBandWidth": "10Gbps"
}
```

**实际行为**：
1. 验证链路在平台是否存在
2. 解析带宽：10Gbps → 10,000,000,000 bps
3. 仅设置 `bandwidth` 和 `resvBandwidth` 字段

### 场景3：仅更新定制字段
```json
{
  "linkId": "279062257336323",
  "linkType": "BACKUP"
}
```

**实际行为**：
1. 不调用平台接口（因为无平台字段需要更新）
2. 仅更新定制库中的 `linkType` 字段

### 场景4：同时更新平台和定制字段
```json
{
  "linkId": "279062257336323",
  "linkName": "完整更新的链路",
  "linkBandWidth": "100Gbps",
  "linkType": "BACKUP"
}
```

**实际行为**：
1. 验证链路在平台是否存在
2. 更新平台字段：链路名称和带宽
3. 更新定制库字段：链路类型

## 错误处理

### 1. 链路不存在（定制库）
- **检查点**：定制库查询
- **错误信息**：`"链路不存在"`

### 2. 链路不存在（平台）
- **检查点**：调用平台 `getLink` 接口
- **错误信息**：`"平台中不存在该链路"`

### 3. 平台更新失败
- **检查点**：调用平台 `updateLink` 接口
- **错误信息**：`"平台更新链路失败"`

### 4. 无更新字段
- **检查点**：所有字段验证后
- **错误信息**：`"没有需要更新的字段"`

### 5. 带宽格式错误
- **检查点**：带宽解析
- **错误信息**：`"带宽格式不正确，应为：数字+单位(bps/Kbps/Mbps/Gbps)，如：1000Mbps"`

## 关键日志

### 重要日志输出
1. **开始处理**：`"开始处理更新链路请求，请求ID：{}, 链路ID：{}"`
2. **平台验证**：`"需要更新平台字段，先验证链路在平台是否存在，链路ID：{}"`
3. **存在确认**：`"链路在平台中存在，开始构建更新请求，链路ID：{}"`
4. **字段设置**：`"设置更新链路名称：{}"` / `"设置更新链路带宽：{} -> {} bps"`
5. **更新成功**：`"平台更新链路成功，请求ID：{}"`

## 性能优化

### 1. 条件性平台调用
- 只有当 `linkName` 或 `linkBandWidth` 不为空时才调用平台接口
- 仅更新定制字段时不涉及平台调用

### 2. 最小化数据传输
- 平台更新请求只包含必要的字段
- 不获取和传输不需要的数据

### 3. 快速失败机制
- 在每个验证点快速返回错误
- 避免不必要的后续处理

## 测试命令

### cURL测试
```bash
curl -X PUT 'http://localhost:8080/srv6/updateLink' \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H 'X-Access-Token: your-token-here' \
  -d '{
    "linkId": "279062257336323",
    "linkName": "测试更新链路名称",
    "linkBandWidth": "5Gbps"
  }'
```

### 预期响应
```json
{
  "requestId": "20241223001",
  "linkId": "279062257336323",
  "failReason": null,
  "optionField": "linkName,linkBandWidth"
}
```

## 优势总结

1. **简化逻辑**：不需要获取和回填所有平台字段
2. **性能优化**：减少不必要的数据传输
3. **操作安全**：仅修改用户指定的字段
4. **快速响应**：条件性平台调用，提高响应速度
5. **易于维护**：逻辑清晰，代码简洁

这种简化的实现方式既满足了业务需求，又保持了良好的性能和可维护性。 