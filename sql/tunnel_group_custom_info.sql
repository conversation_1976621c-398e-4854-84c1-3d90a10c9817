-- 隧道组定制信息表
CREATE TABLE `tunnel_group_custom_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `te_group_id` int(11) NOT NULL COMMENT '隧道组ID（工商总行定义）',
  `name` varchar(255) NOT NULL COMMENT '隧道组名称',
  `platform_group_id` varchar(64) DEFAULT NULL COMMENT '平台应用组ID（与平台创建的应用组关联）',
  `is_loose` tinyint(1) DEFAULT NULL COMMENT '选路方式（true:智能选路, false:自定义）',
  `balance_mode` int(11) NOT NULL COMMENT '路径模式 1-主备模式 2-负载模式',
  `balance_proportion` varchar(100) NOT NULL COMMENT '负载模式下比例 例如1:2或1:2:3',
  `schedule_strategy_id` int(11) NOT NULL COMMENT '隧道模板ID',
  `color` int(11) NOT NULL COMMENT '隧道组的color值',
  `path_size` int(11) NOT NULL COMMENT '路径条数',
  `plane_routing_type` int(11) NOT NULL COMMENT '平面选路类型（1-同平面, 2-跨平面）',
  `is_virtual_net` tinyint(1) NOT NULL COMMENT '配置方式，是否按业务网络（true:按业务网络, false:按设备）',
  `virtual_net_id` varchar(64) DEFAULT NULL COMMENT '业务网络ID',
  `hop_limit` int(11) DEFAULT NULL COMMENT '最大跳数，取值范围1~**********',
  `positive_service_class` int(11) DEFAULT NULL COMMENT '正向Policy的ServiceClass',
  `negative_service_class` int(11) DEFAULT NULL COMMENT '反向Policy的ServiceClass',
  `te_group_dcs` json DEFAULT NULL COMMENT '隧道组设备配置信息（JSON格式）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_te_group_id` (`te_group_id`) COMMENT '隧道组ID唯一索引',
  KEY `idx_platform_group_id` (`platform_group_id`) COMMENT '平台应用组ID索引',
  KEY `idx_schedule_strategy_id` (`schedule_strategy_id`) COMMENT '隧道模板ID索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='隧道组定制信息表'; 