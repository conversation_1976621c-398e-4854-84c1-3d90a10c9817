-- 数据库迁移脚本：将隧道组ID从String类型改为Integer类型
-- 执行时间：2024-07-03
-- 说明：将隧道组相关表的ID字段从varchar类型改为int类型

-- 1. 修改隧道组自定义信息表
ALTER TABLE `tunnel_group_custom_info` 
MODIFY COLUMN `te_group_id` int(11) NOT NULL COMMENT '隧道组ID（工商总行定义）';

-- 2. 修改ServiceClass池表
ALTER TABLE `service_class_pool` 
MODIFY COLUMN `te_group_id` int(11) DEFAULT NULL COMMENT '使用该ServiceClass的隧道组ID';

-- 注意事项：
-- 1. 执行此脚本前请备份数据库
-- 2. 如果表中已有数据，请确保te_group_id字段的值都是有效的整数
-- 3. 如果有外键约束，需要先删除外键约束，修改字段类型后再重新添加
-- 4. 建议在维护窗口期间执行此脚本

-- 验证修改结果
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('tunnel_group_custom_info', 'service_class_pool')
    AND COLUMN_NAME = 'te_group_id'
ORDER BY 
    TABLE_NAME, ORDINAL_POSITION;
