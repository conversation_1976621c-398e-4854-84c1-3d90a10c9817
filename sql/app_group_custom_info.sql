-- 应用组定制信息表
CREATE TABLE `app_group_custom_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_group_id` int NOT NULL COMMENT '应用组唯一标识ID（工行控制器设定）',
    `app_group_name` varchar(255) NOT NULL COMMENT '应用组名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_app_group_id_not_deleted` (`app_group_id`, `is_deleted`),
    KEY `idx_app_group_name` (`app_group_name`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用组定制信息表'; 