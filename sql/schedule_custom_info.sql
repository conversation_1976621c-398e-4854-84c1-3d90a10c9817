-- 调度策略定制信息表
CREATE TABLE `schedule_custom_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_schedule_id` int(11) NOT NULL COMMENT '调度策略唯一标识ID（工行控制器设定）',
    `app_schedule_name` varchar(255) NOT NULL COMMENT '策略名称',
    `app_group_name` varchar(255) NOT NULL COMMENT '应用组名称',
    `drainage_type` int(11) DEFAULT NULL COMMENT '引流类型 0:五元组、1:dscp、4:Vpn',
    `network_ids` text NOT NULL COMMENT '隧道组ID列表（JSON格式存储）',
    `vpn_id` int(11) DEFAULT NULL COMMENT 'VPN ID',
    `classifier_id` int(11) DEFAULT NULL COMMENT '流分类ID',
    `classifier_name` varchar(255) DEFAULT NULL COMMENT '流分类名称',
    `behavior_id` int(11) DEFAULT NULL COMMENT '流行为ID',
    `behavior_name` varchar(255) DEFAULT NULL COMMENT '流行为名称',
    `policy_info` text DEFAULT NULL COMMENT '流策略信息（JSON格式存储，包含设备IP和对应的策略ID）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_app_schedule_id_not_deleted` (`app_schedule_id`, `is_deleted`),
    KEY `idx_app_schedule_name` (`app_schedule_name`),
    KEY `idx_app_group_name` (`app_group_name`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度策略定制信息表';
