-- 应用ACL关系表
CREATE TABLE `app_acl_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_custom_id` bigint(20) NOT NULL COMMENT '关联的应用定制信息ID（app_custom_info表主键）',
    `app_id` int NOT NULL COMMENT '应用ID（便于直接查询）',
    `acl_id` int NOT NULL COMMENT 'ACL模板ID',
    `ip_version` tinyint(1) NOT NULL COMMENT 'IP版本：4-IPv4，6-IPv6',
    `destination_ip` varchar(255) NOT NULL COMMENT '目标IP地址（拆分后的单个IP）',
    `destination_port` varchar(50) NOT NULL COMMENT '目标端口（拆分后的单个端口）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_app_custom_id` (`app_custom_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_acl_id` (`acl_id`),
    KEY `idx_ip_version` (`ip_version`),
    KEY `idx_destination_ip` (`destination_ip`),
    KEY `idx_destination_port` (`destination_port`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用ACL关系表'; 