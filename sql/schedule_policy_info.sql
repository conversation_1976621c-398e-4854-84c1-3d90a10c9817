-- 调度策略流策略信息表
CREATE TABLE `schedule_policy_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_schedule_id` int(11) NOT NULL COMMENT '调度策略ID',
    `policy_id` int(11) NOT NULL COMMENT '平台流策略ID',
    `policy_name` varchar(255) NOT NULL COMMENT '流策略名称',
    `device_id` bigint(20) NOT NULL COMMENT '设备ID',
    `platform_node_id` varchar(50) NOT NULL COMMENT '平台节点ID',
    `device_ip` varchar(50) NOT NULL COMMENT '设备IP',
    `classifier_id` int(11) NOT NULL COMMENT '关联的流分类ID',
    `behavior_id` int(11) NOT NULL COMMENT '关联的流行为ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_app_schedule_id` (`app_schedule_id`),
    KEY `idx_policy_id` (`policy_id`),
    KEY `idx_policy_name` (`policy_name`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_device_ip` (`device_ip`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度策略流策略信息表';
