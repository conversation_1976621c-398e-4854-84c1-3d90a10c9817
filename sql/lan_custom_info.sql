-- LAN口定制信息表
CREATE TABLE `lan_custom_info` (
  `lan_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'LAN口ID，主键，自增',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID，关联device_custom_info表的device_id',
  `interface_name` varchar(255) NOT NULL COMMENT '接口名称',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`lan_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_interface_name` (`interface_name`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LAN口定制信息表'; 