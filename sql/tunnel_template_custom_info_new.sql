-- 隧道模板定制信息表
-- 删除原表（如果存在）
DROP TABLE IF EXISTS `tunnel_template_custom_info`;

-- 创建新表
CREATE TABLE `tunnel_template_custom_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_id` int(11) NOT NULL COMMENT '隧道策略ID',
  `strategy_name` varchar(255) NOT NULL COMMENT '策略名称',
  `sla_id` varchar(64) DEFAULT NULL COMMENT 'SLA等级：EF、AF4、AF3、AF2、AF1、BE',
  `bandwidth` bigint(20) DEFAULT NULL COMMENT '保障带宽，单位为bps',
  `up_allow_link_priority` varchar(32) NOT NULL COMMENT '正向链路等级，优选链路等级，可选值：高级、中级、低级',
  `down_allow_link_priority` json NOT NULL COMMENT '反向链路等级，可选链路等级列表，JSON格式存储，可选值：高级、中级、低级',
  `packet_loss_rate` varchar(64) NOT NULL COMMENT '丢包率，字符串格式',
  `delay_time` varchar(64) NOT NULL COMMENT '延迟时间，字符串格式，单位为毫秒',
  `network_jitter` varchar(64) NOT NULL COMMENT '网络抖动，字符串格式，单位为毫秒',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_id` (`strategy_id`) COMMENT '策略ID唯一索引',
  KEY `idx_strategy_name` (`strategy_name`) COMMENT '策略名称索引',
  KEY `idx_sla_id` (`sla_id`) COMMENT 'SLA等级索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引',
  KEY `idx_is_deleted` (`is_deleted`) COMMENT '删除标记索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='隧道模板定制信息表';

-- 插入示例数据
INSERT INTO `tunnel_template_custom_info` (
  `strategy_id`, 
  `strategy_name`, 
  `sla_id`, 
  `bandwidth`, 
  `up_allow_link_priority`, 
  `down_allow_link_priority`, 
  `packet_loss_rate`, 
  `delay_time`, 
  `network_jitter`
) VALUES 
(1, '高优先级隧道策略', 'EF', 1000000000, '高级', '["高级", "中级"]', '0.01', '100', '10'),
(2, '中优先级隧道策略', 'AF4', 500000000, '中级', '["中级", "低级"]', '0.05', '200', '20'),
(3, '低优先级隧道策略', 'BE', 100000000, '低级', '["低级"]', '0.1', '500', '50');

-- 表结构说明
/*
字段变更说明：
1. 删除了 bandwidth_percent 字段
2. up_allow_link_priority 改为 varchar(32)，存储中文值：高级、中级、低级
3. down_allow_link_priority 改为 json 类型，存储字符串数组：["高级", "中级", "低级"]
4. packet_loss_rate 改为 varchar(64)，存储字符串格式
5. delay_time 改为 varchar(64)，存储字符串格式
6. network_jitter 改为 varchar(64)，存储字符串格式

索引说明：
1. uk_strategy_id: 策略ID唯一索引，确保策略ID不重复
2. idx_strategy_name: 策略名称索引，提高按名称查询的性能
3. idx_sla_id: SLA等级索引，提高按SLA等级查询的性能
4. idx_create_time: 创建时间索引，提高按时间排序查询的性能
5. idx_is_deleted: 删除标记索引，提高软删除查询的性能

数据类型说明：
1. bandwidth: bigint(20) - 保障带宽，单位为bps，支持大数值
2. up_allow_link_priority: varchar(32) - 正向链路等级，存储中文字符串
3. down_allow_link_priority: json - 反向链路等级列表，使用JSON格式存储字符串数组
4. packet_loss_rate: varchar(64) - 丢包率，字符串格式，支持各种格式的数值
5. delay_time: varchar(64) - 延迟时间，字符串格式，支持各种格式的数值
6. network_jitter: varchar(64) - 网络抖动，字符串格式，支持各种格式的数值

约束说明：
1. strategy_id 必须唯一，不能重复
2. up_allow_link_priority 和 down_allow_link_priority 只能是：高级、中级、低级
3. 所有必填字段都设置了 NOT NULL 约束
4. 使用软删除机制，通过 is_deleted 字段标记删除状态
*/
