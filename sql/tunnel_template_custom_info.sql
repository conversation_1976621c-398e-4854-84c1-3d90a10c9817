-- 隧道模板定制信息表
CREATE TABLE `tunnel_template_custom_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `strategy_id` int(11) NOT NULL COMMENT '隧道策略ID',
  `strategy_name` varchar(255) NOT NULL COMMENT '策略名称',
  `sla_id` varchar(64) DEFAULT NULL COMMENT 'SLA等级：EF、AF4、AF3、AF2、AF1、BE',
  `bandwidth_percent` decimal(5,2) DEFAULT NULL COMMENT '保障带宽百分比',
  `bandwidth` bigint(20) DEFAULT NULL COMMENT '保障带宽',
  `up_allow_link_priority` int(11) NOT NULL COMMENT '正向链路等级，优选链路等级',
  `down_allow_link_priority` int(11) NOT NULL COMMENT '反向链路等级，可选链路等级',
  `packet_loss_rate` decimal(8,4) NOT NULL COMMENT '丢包率',
  `delay_time` bigint(20) NOT NULL COMMENT '延迟时间',
  `network_jitter` bigint(20) NOT NULL COMMENT '网络抖动',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_id` (`strategy_id`, `is_deleted`) COMMENT '策略ID唯一索引（排除删除）',
  KEY `idx_strategy_name` (`strategy_name`) COMMENT '策略名称索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='隧道模板定制信息表'; 