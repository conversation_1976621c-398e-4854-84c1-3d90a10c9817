-- 调度策略流分类信息表
CREATE TABLE `schedule_classifier_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_schedule_id` int(11) NOT NULL COMMENT '调度策略ID',
    `classifier_id` int(11) NOT NULL COMMENT '平台流分类ID',
    `classifier_name` varchar(255) NOT NULL COMMENT '流分类名称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_app_schedule_id` (`app_schedule_id`),
    KEY `idx_classifier_id` (`classifier_id`),
    KEY `idx_classifier_name` (`classifier_name`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调度策略流分类信息表';
