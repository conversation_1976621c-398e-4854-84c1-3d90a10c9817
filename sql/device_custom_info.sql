-- 设备自定义信息表
CREATE TABLE `device_custom_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` int NOT NULL COMMENT '设备ID，工行控制器统一设定唯一标识',
  `platform_node_id` varchar(64) DEFAULT NULL COMMENT '平台设备节点ID，关联平台设备',
  `device_site_id` int DEFAULT NULL COMMENT '设备所属站点ID',
  `device_site` varchar(255) DEFAULT NULL COMMENT '所属站点名称',
  `device_role` varchar(50) DEFAULT NULL COMMENT '设备角色（hub/spoke/agg）',
  `is_rr` tinyint(1) DEFAULT 0 COMMENT '是否为RR设备：0-否，1-是',
  `device_plane_id` int DEFAULT NULL COMMENT '所属平面ID',
  `device_ipv6` varchar(255) DEFAULT NULL COMMENT 'IPv6地址',
  `device_group` varchar(255) DEFAULT NULL COMMENT '设备组',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（首次上线时间）',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_id_deleted` (`device_id`, `is_deleted`),
  KEY `idx_device_site_id` (`device_site_id`),
  KEY `idx_device_plane_id` (`device_plane_id`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备自定义信息表'; 