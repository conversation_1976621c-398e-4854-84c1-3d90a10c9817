-- 链路自定义信息表（仅存储工行定制字段）
CREATE TABLE `link_custom_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `platform_link_id` varchar(64) DEFAULT NULL COMMENT '平台生成的链路ID（用于关联平台链路）',
  `site_id` int DEFAULT NULL COMMENT '站点ID（以源设备的站点为准，用于站点级链路查询）',
  `link_type` varchar(50) NOT NULL COMMENT '链路类型（工行定制字段）',
  `link_level` varchar(50) NOT NULL COMMENT '链路等级（工行定制字段）',
  `link_status` varchar(50) DEFAULT NULL COMMENT '链路状态（工行定制字段）',
  `source_device_id` int NOT NULL COMMENT '源设备ID（工行控制器统一设定）',
  `source_device_name` varchar(255) NOT NULL COMMENT '源设备名称（工行定制字段）',
  `source_device_port` varchar(255) NOT NULL COMMENT '源设备端口（工行定制字段）',
  `target_device_id` int NOT NULL COMMENT '目标设备ID（工行控制器统一设定）',
  `target_device_name` varchar(255) NOT NULL COMMENT '目标设备名称（工行定制字段）',
  `target_device_port` varchar(255) NOT NULL COMMENT '目标设备端口（工行定制字段）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_platform_link_id` (`platform_link_id`),
  KEY `idx_site_id` (`site_id`),
  KEY `idx_link_type` (`link_type`),
  KEY `idx_source_device_id` (`source_device_id`),
  KEY `idx_target_device_id` (`target_device_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='链路自定义信息表（仅存储工行定制字段）'; 