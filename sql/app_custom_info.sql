-- 应用定制信息表
CREATE TABLE `app_custom_info` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `app_id` int NOT NULL COMMENT '应用ID',
    `app_name` varchar(255) NOT NULL COMMENT '应用名称',
    `app_type` varchar(50) NOT NULL COMMENT '应用类型，仅支持五元组',
    `internet_app_dscp` varchar(50) DEFAULT NULL COMMENT '网络应用DSCP',
    `agreement_type` varchar(20) NOT NULL COMMENT '协议类型（如TCP/UDP）',
    `source_ip` varchar(255) NOT NULL COMMENT '源IP地址及掩码（格式：*******/32）',
    `source_port` varchar(50) NOT NULL COMMENT '源端口(如80）',
    `destination_ip_list` text NOT NULL COMMENT '目的IP及端口列表（格式：ip&port，如["***********/32&443"]）存储JSON格式',
    `source_ipv6` varchar(255) DEFAULT NULL COMMENT 'IPv6源IP地址及掩码',
    `source_port_ipv6` varchar(50) DEFAULT NULL COMMENT 'IPv6源端口（非必须）',
    `destination_ipv6_list` text DEFAULT NULL COMMENT 'IPv6目的IP及端口列表（非必须）（格式：ip&port，如["1111:2222::ffff/32&8080"]）存储JSON格式',
    `app_group_name` varchar(255) NOT NULL COMMENT '所属应用分组',
    `app_group_id` bigint(20) DEFAULT NULL COMMENT '关联的应用组ID（app_group_custom_info表主键）',
    `acl_id_list` text DEFAULT NULL COMMENT '关联的ACL模板ID列表，JSON格式存储，因为一个应用可能对应多个ACL模板',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_app_name` (`app_name`),
    KEY `idx_app_group_name` (`app_group_name`),
    KEY `idx_app_group_id` (`app_group_id`),
    KEY `idx_is_deleted` (`is_deleted`),
    KEY `idx_app_id_is_deleted` (`app_id`, `is_deleted`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用定制信息表'; 