-- ServiceClass池管理表
CREATE TABLE `service_class_pool` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_class` int(11) NOT NULL COMMENT 'ServiceClass值，范围1-127',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被使用：0-空闲，1-使用中',
  `te_group_id` int(11) DEFAULT NULL COMMENT '使用该ServiceClass的隧道组ID',
  `policy_direction` varchar(10) DEFAULT NULL COMMENT 'Policy方向：POSITIVE-正向，NEGATIVE-反向（按顺序递增分配）',
  `allocated_time` datetime DEFAULT NULL COMMENT '分配时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_service_class` (`service_class`) COMMENT 'ServiceClass唯一索引',
  KEY `idx_is_used` (`is_used`) COMMENT '使用状态索引',
  KEY `idx_te_group_id` (`te_group_id`) COMMENT '隧道组ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ServiceClass池管理表';

-- 初始化ServiceClass池（1-127）
-- 分配策略：按顺序递增，正向反向连续分配
-- 示例：隧道组1(正向=1,反向=2)，隧道组2(正向=3,反向=4)，以此类推
INSERT INTO `service_class_pool` (`service_class`) 
SELECT number FROM (
  SELECT 1 + a.N + b.N * 10 + c.N * 100 as number
  FROM 
    (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
    (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
    (SELECT 0 as N UNION SELECT 1) c
) numbers 
WHERE number BETWEEN 1 AND 127
ORDER BY number; 