### 查询隧道详情接口测试
### 注意：
### 1. deployState参数会在平台层面进行过滤，提高查询效率
### 2. srcNodeId和dstNodeId为Long类型，通过device_custom_info表转换为上游设备ID

### 1. 基本查询 - 查询指定隧道组的详情
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002]
}

### 2. 根据设备名称过滤查询
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002, 1003],
  "devName": "Device-1001"
}

### 3. 根据部署状态过滤查询 - 查询部署成功的隧道
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002, 1003],
  "deployState": 3
}

### 4. 综合条件查询 - 设备名称 + 部署状态
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002, 1003],
  "devName": "Device-1001",
  "deployState": 3
}

### 5. 查询部署失败的隧道
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002, 1003],
  "deployState": 4
}

### 6. 查询未部署的隧道
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002, 1003],
  "deployState": 1
}

### 7. 查询待删除的隧道
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [1001, 1002, 1003],
  "deployState": 5
}

### 8. 错误测试 - 空的隧道组ID列表
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": []
}

### 9. 测试不存在的隧道组ID - 验证业务数据过滤
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": [9999, 8888]
}

### 10. 错误测试 - 空的隧道组ID列表
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "teGroupIds": []
}

### 11. 错误测试 - 缺少必填参数
POST http://localhost:8080/srv6/getTeGroupDetails
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
  "devName": "Device-1001"
}
