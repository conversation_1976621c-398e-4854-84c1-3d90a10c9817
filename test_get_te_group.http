### 查询隧道组接口测试

POST http://localhost:8080/srv6/getTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

### 说明
# 该接口用于查询所有隧道组信息，包含部署状态
# 接口地址：POST /srv6/getTeGroup
# 无需请求参数，直接查询所有隧道组
# 
# 响应字段说明：
# - requestId: 请求ID，用于溯源
# - result: 状态，0:失败，1：成功
# - failReason: 异常状态下的异常原因
# - teGroups: 隧道组集合
#   - id: 隧道组ID
#   - name: 隧道组名称
#   - isLoose: 选路方式（true:智能选路, false:自定义）
#   - balanceMode: 路径模式 1-主备模式 2-负载模式
#   - balanceProportion: 负载模式下比例
#   - scheduleStrategyId: 隧道模板ID
#   - color: 隧道组的color值
#   - pathSize: 路径条数
#   - planeRoutingType: 平面选路类型（1-同平面, 2-跨平面）
#   - isVirtualNet: 配置方式（true:按业务网络, false:按设备）
#   - virtualNetId: 业务网络ID
#   - teGroupDcs: 隧道组源目的信息（按设备配置时）
#     - srcDeviceIds: 源设备ID列表
#     - srcDeviceNames: 源设备名称列表
#     - dstDeviceIds: 目的设备ID列表
#     - dstDeviceNames: 目的设备名称列表
#   - mandatoryNodeIds: 必选设备ID集合
#   - excludeNodeIds: 排除设备ID集合
#   - mandatoryLinkIds: 必经链路ID集合
#   - excludeLinkIds: 排除链路ID集合
#   - teTunnelManualList: 指定路径（废弃）
#   - hopLimit: 最大跳数
#   - deployState: 部署状态（1-未部署，2-修改未部署，3-部署成功，4-部署失败）
#
# 部署状态映射规则：
# - TO_BE_DEPLOY -> 1 (未部署)
# - DEPLOY_SUCCESS -> 3 (部署成功)
# - DEPLOY_FAILED -> 4 (部署失败)
# - TO_BE_DELETE -> 5 (待删除)
