# UpdateLinkResponseDTO optionField 字段支持增强

## 概述
`UpdateLinkResponseDTO` 现在支持选择性返回 `optionField` 字段，提供了更灵活的响应控制。

## 实现原理

### JSON序列化控制
通过 `@JsonInclude(JsonInclude.Include.NON_NULL)` 注解，当 `optionField` 字段为 `null` 时，该字段不会出现在JSON响应中。

```java
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateLinkResponseDTO {
    // ...
    private String optionField;
    // ...
}
```

## 方法重载支持

### 1. 带 optionField 的成功响应
```java
/**
 * 成功响应（带optionField）
 */
public static UpdateLinkResponseDTO success(String requestId, String optionField)
```

**使用场景**: 需要告知客户端具体更新了哪些字段时使用。

**示例**:
```java
return UpdateLinkResponseDTO.success(requestId, "linkName,linkBandWidth");
```

**JSON响应**:
```json
{
  "requestId": "20241223001",
  "result": 1,
  "optionField": "linkName,linkBandWidth"
}
```

### 2. 不返回 optionField 的成功响应
```java
/**
 * 成功响应（不返回optionField）
 */
public static UpdateLinkResponseDTO success(String requestId)
```

**使用场景**: 客户端不需要知道具体更新字段，或简化响应结构时使用。

**示例**:
```java
return UpdateLinkResponseDTO.success(requestId);
```

**JSON响应**:
```json
{
  "requestId": "20241223001",
  "result": 1
}
```

### 3. 失败响应（保持不变）
```java
/**
 * 失败响应
 */
public static UpdateLinkResponseDTO fail(String requestId, String failReason)
```

**JSON响应**:
```json
{
  "requestId": "20241223001",
  "result": 0,
  "failReason": "链路不存在或已被删除"
}
```

## 使用建议

### 何时返回 optionField
- ✅ **调试和开发阶段**: 帮助开发者了解具体更新了哪些字段
- ✅ **审计需求**: 需要记录具体操作内容的场景
- ✅ **详细日志**: 客户端需要展示详细操作信息

### 何时不返回 optionField
- ✅ **生产环境**: 简化响应，减少网络传输
- ✅ **移动端应用**: 减少数据传输量
- ✅ **简化场景**: 客户端只关心成功与否，不关心具体字段

## 代码示例

### 业务服务中的使用

```java
@Override
public UpdateLinkResponseDTO updateLink(UpdateLinkRequestDTO request, String requestId) {
    try {
        // ... 业务逻辑 ...
        
        if (someCondition) {
            // 需要返回详细字段信息
            String optionField = String.join(",", updatedFields);
            return UpdateLinkResponseDTO.success(requestId, optionField);
        } else {
            // 简化响应，不返回字段信息
            return UpdateLinkResponseDTO.success(requestId);
        }
        
    } catch (Exception e) {
        return UpdateLinkResponseDTO.fail(requestId, "系统异常：" + e.getMessage());
    }
}
```

### 控制器中的条件响应

```java
@PutMapping("/updateLink")
public ResponseEntity<UpdateLinkResponseDTO> updateLink(
        @RequestParam(required = false, defaultValue = "false") boolean includeFields,
        @Valid @RequestBody UpdateLinkRequestDTO request) {
    
    String requestId = generateRequestId();
    UpdateLinkResponseDTO response = linkService.updateLink(request, requestId);
    
    // 根据参数决定是否包含字段信息
    if (!includeFields && response.getResult() == 1) {
        return ResponseEntity.ok(UpdateLinkResponseDTO.success(requestId));
    }
    
    return ResponseEntity.ok(response);
}
```

## 向后兼容性

### ✅ 完全向后兼容
- 现有调用 `success(requestId, optionField)` 的代码无需修改
- 现有调用 `fail(requestId, failReason)` 的代码无需修改
- JSON响应格式保持一致

### 新功能
- 新增 `success(requestId)` 重载方法
- 支持选择性返回 `optionField` 字段

## 测试用例

### 测试场景1: 带 optionField 的成功响应
```java
@Test
public void testSuccessWithOptionField() {
    UpdateLinkResponseDTO response = UpdateLinkResponseDTO.success("req001", "linkName");
    
    assertEquals("req001", response.getRequestId());
    assertEquals(Integer.valueOf(1), response.getResult());
    assertEquals("linkName", response.getOptionField());
    assertNull(response.getFailReason());
}
```

### 测试场景2: 不带 optionField 的成功响应
```java
@Test
public void testSuccessWithoutOptionField() {
    UpdateLinkResponseDTO response = UpdateLinkResponseDTO.success("req001");
    
    assertEquals("req001", response.getRequestId());
    assertEquals(Integer.valueOf(1), response.getResult());
    assertNull(response.getOptionField());
    assertNull(response.getFailReason());
}
```

### 测试场景3: JSON序列化验证
```java
@Test
public void testJsonSerialization() {
    // 带optionField的响应
    UpdateLinkResponseDTO responseWithField = UpdateLinkResponseDTO.success("req001", "linkName");
    String jsonWithField = objectMapper.writeValueAsString(responseWithField);
    assertTrue(jsonWithField.contains("optionField"));
    
    // 不带optionField的响应
    UpdateLinkResponseDTO responseWithoutField = UpdateLinkResponseDTO.success("req001");
    String jsonWithoutField = objectMapper.writeValueAsString(responseWithoutField);
    assertFalse(jsonWithoutField.contains("optionField"));
}
```

## 总结

这个增强提供了更灵活的响应控制机制：

1. **灵活性**: 可以根据业务需求选择是否返回 `optionField`
2. **性能优化**: 减少不必要的数据传输
3. **向后兼容**: 不影响现有代码
4. **易于使用**: 简单的方法重载，使用直观

通过这个增强，开发者可以根据具体场景选择最合适的响应格式，既能满足详细信息需求，也能实现简洁响应。 