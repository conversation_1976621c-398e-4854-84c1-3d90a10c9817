# 工商总行链路管理系统软删除支持

## 概述
系统已全面支持软删除机制，在所有数据库查询操作中都会自动过滤已被软删除的记录，确保业务逻辑的正确性和数据的一致性。

## 软删除字段说明

### 字段定义
- **字段名称**: `is_deleted`
- **数据类型**: `INTEGER` 
- **取值含义**: 
  - `0` - 正常记录（未删除）
  - `1` - 已软删除记录

### 涉及的数据表
1. **link_custom_info** - 链路自定义信息表
2. **device_custom_info** - 设备自定义信息表

## 实现的软删除过滤

### 1. 链路更新操作 (`updateLink`)

**位置**: `LinkServiceImpl.updateLink()` 方法

**实现**:
```java
// 1. 查询定制库中的链路信息（过滤软删除的记录）
LambdaQueryWrapper<LinkCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(LinkCustomInfo::getPlatformLinkId, request.getLinkId());
queryWrapper.eq(LinkCustomInfo::getIsDeleted, 0); // 过滤软删除的记录

LinkCustomInfo linkInfo = linkCustomInfoMapper.selectOne(queryWrapper);
if (linkInfo == null) {
    log.error("链路不存在或已被删除，链路ID：{}", request.getLinkId());
    return UpdateLinkResponseDTO.fail(requestId, "链路不存在或已被删除");
}
```

**效果**: 只能更新未被软删除的链路记录，已软删除的链路会返回"链路不存在或已被删除"错误。

### 2. 设备平台节点ID查询 (`getPlatformNodeId`)

**位置**: `LinkServiceImpl.getPlatformNodeId()` 方法

**实现**:
```java
private String getPlatformNodeId(Integer deviceId, String deviceType) {
    LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
    queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0); // 过滤软删除的设备
    
    DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);
    if (deviceInfo == null) {
        log.error("{}不存在或已被删除，设备ID：{}", deviceType, deviceId);
        return null;
    }
    // ...
}
```

**效果**: 新增链路时，只能使用未被软删除的设备，已软删除的设备会导致链路创建失败。

### 3. 设备站点ID查询 (`getDeviceSiteId`)

**位置**: `LinkServiceImpl.getDeviceSiteId()` 方法

**实现**:
```java
private Integer getDeviceSiteId(Integer deviceId) {
    LambdaQueryWrapper<DeviceCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(DeviceCustomInfo::getDeviceId, deviceId);
    queryWrapper.eq(DeviceCustomInfo::getIsDeleted, 0); // 过滤软删除的设备
    queryWrapper.select(DeviceCustomInfo::getDeviceSiteId);
    
    DeviceCustomInfo deviceInfo = deviceCustomInfoMapper.selectOne(queryWrapper);
    if (deviceInfo != null) {
        log.info("获取设备站点ID成功，设备ID：{}，站点ID：{}", deviceId, deviceInfo.getDeviceSiteId());
        return deviceInfo.getDeviceSiteId();
    } else {
        log.warn("设备不存在、已被删除或未设置站点ID，设备ID：{}", deviceId);
        return null;
    }
}
```

**效果**: 获取设备站点信息时自动过滤软删除的设备记录。

## 业务场景影响

### 1. 链路新增 (`addLink`)
- ✅ **源设备检查**: 验证源设备未被软删除
- ✅ **目标设备检查**: 验证目标设备未被软删除  
- ✅ **站点信息**: 只从有效设备获取站点ID
- ❌ **失败场景**: 如果源设备或目标设备被软删除，链路创建失败

### 2. 链路更新 (`updateLink`)
- ✅ **链路存在性**: 验证链路未被软删除
- ✅ **平台验证**: 通过平台接口验证链路存在性
- ❌ **失败场景**: 如果链路已被软删除，更新操作失败

### 3. 错误信息优化
原有错误信息进行了优化，明确指出软删除状态：
- `"链路不存在"` → `"链路不存在或已被删除"`
- `"设备不存在"` → `"设备不存在或已被删除"`
- `"设备不存在或未设置站点ID"` → `"设备不存在、已被删除或未设置站点ID"`

## 数据一致性保障

### 1. 查询一致性
所有数据库查询操作都包含 `is_deleted = 0` 条件，确保：
- 不会返回已删除的记录
- 不会基于已删除的记录进行业务操作
- 保持数据的逻辑一致性

### 2. 业务逻辑保护
- **新增操作**: 依赖的设备必须是有效的（未软删除）
- **更新操作**: 只能操作有效的链路记录
- **关联查询**: 所有关联的设备和链路都经过软删除过滤

### 3. 错误处理完善
- 提供明确的错误信息区分"不存在"和"已删除"
- 详细的日志记录便于问题排查
- 统一的错误响应格式

## 测试建议

### 1. 正常场景测试
- 使用有效的设备和链路进行新增、更新操作
- 验证操作正常完成

### 2. 软删除场景测试
- 对设备进行软删除，验证相关链路操作是否被正确阻止
- 对链路进行软删除，验证更新操作是否返回正确错误信息

### 3. 边界情况测试
- 部分设备软删除情况下的链路操作
- 混合有效和无效记录的批量操作

## 维护注意事项

### 1. 新增查询方法
在添加新的数据库查询方法时，请确保：
- 包含 `queryWrapper.eq(Entity::getIsDeleted, 0)` 条件
- 错误信息明确指出软删除状态
- 添加相应的单元测试

### 2. 业务逻辑扩展
- 所有涉及数据库记录的业务逻辑都需要考虑软删除
- 保持错误信息的一致性
- 维护详细的操作日志

### 3. 数据库操作
- 删除操作应使用软删除而非物理删除
- 定期清理长期软删除的数据（如有需要）
- 监控软删除记录的数量和分布

这种软删除机制确保了数据的安全性和业务逻辑的完整性，同时保持了良好的用户体验和错误提示。 