# 链路管理接口全局异常处理优化

## 概述

本次优化将链路管理接口（LinkController）改为使用全局异常处理模式，参考设备管理接口的实现方式，移除了Controller和Service层的try-catch块，让异常直接传播到GlobalExceptionHandler进行统一处理。

## 修改内容

### 1. LinkController.java 修改

**文件**: `src/main/java/com/h3c/dzkf/controller/LinkController.java`

#### 修改前的问题：
- 每个接口方法都有try-catch块
- 异常处理逻辑重复
- 返回错误响应的代码冗余

#### 修改后的改进：
- 移除所有try-catch块
- 保留token设置和清理逻辑
- 让异常直接传播到GlobalExceptionHandler

#### 具体修改：

**addLink方法**:
```java
// 修改前
try {
    TokenContext.setToken(accessToken);
    AddLinkResponseDTO response = linkService.addLink(request, requestId);
    return ResponseEntity.ok(response);
} catch (Exception e) {
    log.error("新增链路接口异常，请求ID：{}", requestId, e);
    return ResponseEntity.ok(AddLinkResponseDTO.fail(requestId, "系统异常"));
} finally {
    TokenContext.clearToken();
}

// 修改后
try {
    TokenContext.setToken(accessToken);
    Long linkId = linkService.addLink(request, requestId);
    return ResponseEntity.ok(AddLinkResponseDTO.success(requestId, linkId));
} finally {
    TokenContext.clearToken();
}
```

**updateLink方法**:
```java
// 修改前
try {
    TokenContext.setToken(accessToken);
    ApiResponseDTO response = linkService.updateLink(request, requestId);
    return ResponseEntity.ok(response);
} catch (Exception e) {
    log.error("更新链路接口异常，请求ID：{}", requestId, e);
    return ResponseEntity.ok(ApiResponseDTO.fail(requestId, "系统异常"));
} finally {
    TokenContext.clearToken();
}

// 修改后
try {
    TokenContext.setToken(accessToken);
    linkService.updateLink(request, requestId);
    return ResponseEntity.ok(ApiResponseDTO.success(requestId));
} finally {
    TokenContext.clearToken();
}
```

**queryLinkInfoListBySiteId方法**:
```java
// 修改前
try {
    TokenContext.setToken(accessToken);
    QueryLinkInfoListBySiteIdResponseDTO response = linkService.queryLinkInfoListBySiteId(siteId, requestId);
    return ResponseEntity.ok(response);
} catch (Exception e) {
    log.error("查询站点下链路信息接口异常，请求ID：{}", requestId, e);
    return ResponseEntity.ok(QueryLinkInfoListBySiteIdResponseDTO.fail(requestId, "系统异常"));
} finally {
    TokenContext.clearToken();
}

// 修改后
try {
    TokenContext.setToken(accessToken);
    List<QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO> linkInfoList = linkService.queryLinkInfoListBySiteId(siteId, requestId);
    return ResponseEntity.ok(QueryLinkInfoListBySiteIdResponseDTO.success(requestId, linkInfoList));
} finally {
    TokenContext.clearToken();
}
```

### 2. LinkService.java 接口修改

**文件**: `src/main/java/com/h3c/dzkf/service/LinkService.java`

#### 方法签名调整：

```java
// 修改前
AddLinkResponseDTO addLink(AddLinkRequestDTO request, String requestId);
ApiResponseDTO updateLink(UpdateLinkRequestDTO request, String requestId);
QueryLinkInfoListBySiteIdResponseDTO queryLinkInfoListBySiteId(Integer siteId, String requestId);

// 修改后
Long addLink(AddLinkRequestDTO request, String requestId);
void updateLink(UpdateLinkRequestDTO request, String requestId);
List<QueryLinkInfoListBySiteIdResponseDTO.LinkInfoDTO> queryLinkInfoListBySiteId(Integer siteId, String requestId);
```

#### 新增异常声明：
- 所有方法都声明抛出 `com.h3c.dzkf.common.exceptions.ServiceException`

### 3. LinkServiceImpl.java 实现修改

**文件**: `src/main/java/com/h3c/dzkf/service/impl/LinkServiceImpl.java`

#### 主要修改：

1. **添加ServiceException导入**
2. **移除所有try-catch块**
3. **将错误返回改为抛出ServiceException**
4. **修改私有方法的异常处理**

#### 具体修改示例：

**addLink方法**:
```java
// 修改前
try {
    // 业务逻辑
    return AddLinkResponseDTO.success(requestId, linkId);
} catch (Exception e) {
    log.error("新增链路异常，请求ID：{}", requestId, e);
    return AddLinkResponseDTO.fail(requestId, "系统异常：" + e.getMessage());
}

// 修改后
// 直接执行业务逻辑，异常自动传播
// 验证失败时抛出ServiceException
if (sourcePortInfo == null) {
    throw new ServiceException("LINK_ADD_003", "未找到源设备端口对应的平台端口信息", 
        String.format("设备ID：%d，端口：%s", request.getSourceDeviceId(), request.getSourceDevicePort()));
}
return linkId;
```

**updateLink方法**:
```java
// 修改前
try {
    // 业务逻辑
    return ApiResponseDTO.success(requestId);
} catch (Exception e) {
    log.error("更新链路异常，请求ID：{}", requestId, e);
    return ApiResponseDTO.fail(requestId, "系统异常：" + e.getMessage());
}

// 修改后
// 直接执行业务逻辑，异常自动传播
if (linkInfo == null) {
    throw new ServiceException("LINK_UPDATE_001", "链路不存在或已被删除", 
        String.format("链路ID：%d", request.getLinkId()));
}
// 方法结束，无返回值
```

**queryLinkInfoListBySiteId方法**:
```java
// 修改前
try {
    // 业务逻辑
    return QueryLinkInfoListBySiteIdResponseDTO.success(requestId, linkInfoList);
} catch (Exception e) {
    log.error("查询站点下链路信息异常，请求ID：{}", requestId, e);
    return QueryLinkInfoListBySiteIdResponseDTO.fail(requestId, "系统异常：" + e.getMessage());
}

// 修改后
// 直接执行业务逻辑，异常自动传播
if (siteCustomInfo == null) {
    throw new ServiceException("LINK_QUERY_001", "站点不存在或已被删除", 
        String.format("站点ID：%d", siteId));
}
return linkInfoList;
```

#### 私有方法修改：

**getPlatformLinkInfo方法**:
```java
// 修改前
try {
    // 查询逻辑
    return linkVo;
} catch (Exception e) {
    log.error("获取平台链路信息异常", e);
    return null;
}

// 修改后
// 直接执行查询逻辑
if (platformLinkResponse == null || ...) {
    throw new ServiceException("PLATFORM_LINK_001", "平台中不存在链路", 
        String.format("链路ID：%d", linkId));
}
return linkVo;
```

**getPlatformNodeId方法**:
```java
// 修改前
if (deviceInfo == null) {
    log.error("{}不存在或已被删除，设备ID：{}", deviceType, deviceId);
    return null;
}

// 修改后
if (deviceInfo == null) {
    throw new ServiceException("DEVICE_NODE_001", deviceType + "不存在或已被删除", 
        String.format("设备ID：%d", deviceId));
}
```

**callPlatformAddLink方法**:
```java
// 修改前
try {
    // 调用平台接口
    return platformLinkId;
} catch (Exception e) {
    log.error("调用平台新增链路接口异常", e);
    return null;
}

// 修改后
// 直接调用平台接口
if (platformLinkId != null) {
    return platformLinkId;
} else {
    throw new ServiceException("PLATFORM_ADD_LINK_001", "平台新增链路失败", 
        String.format("链路名称：%s", request.getLinkName()));
}
```

**getPlatformPortInfo方法**:
```java
// 修改前
try {
    // 查询端口信息
    return interfaceVO;
} catch (Exception e) {
    log.error("查询端口信息异常", e);
    return null;
}

// 修改后
// 直接查询端口信息
if (找不到匹配端口) {
    throw new ServiceException("PLATFORM_PORT_001", "未找到匹配的" + portType, 
        String.format("端口名称：%s，平台节点ID：%s", portName, platformNodeId));
}
```

**parseBandwidth方法**:
```java
// 修改前
if (bandwidthStr == null || bandwidthStr.trim().isEmpty()) {
    throw new IllegalArgumentException("带宽不能为空");
}
if (!matcher.matches()) {
    throw new IllegalArgumentException("带宽格式不正确，应为：数字+单位(bps/Kbps/Mbps/Gbps)，如：1000Mbps");
}
default:
    throw new IllegalArgumentException("不支持的带宽单位：" + unit);

// 修改后
if (bandwidthStr == null || bandwidthStr.trim().isEmpty()) {
    throw new ServiceException("BANDWIDTH_PARSE_001", "带宽不能为空",
        "带宽字符串为null或空");
}
if (!matcher.matches()) {
    throw new ServiceException("BANDWIDTH_PARSE_002", "带宽格式不正确，应为：数字+单位(bps/Kbps/Mbps/Gbps)，如：1000Mbps",
        String.format("输入的带宽字符串：%s", bandwidthStr));
}
default:
    throw new ServiceException("BANDWIDTH_PARSE_003", "不支持的带宽单位：" + unit,
        String.format("输入的带宽字符串：%s", bandwidthStr));
```

**getSingleLinkData方法**:
```java
// 修改前
try {
    // 查询链路基本信息
    return linkVo;
} catch (Exception e) {
    log.error("获取链路基本信息异常", e);
    return null;
}

// 修改后
// 直接查询链路基本信息
if (未找到链路) {
    throw new ServiceException("PLATFORM_GET_LINK_001", "平台接口未找到链路基本信息",
        String.format("链路ID：%s", linkId));
}
```

**getSingleNodeData方法**:
```java
// 修改前
try {
    // 查询节点详细信息
    return nodeRecord;
} catch (Exception e) {
    log.error("获取节点详细信息异常", e);
    return null;
}

// 修改后
// 直接查询节点详细信息
if (未找到节点) {
    throw new ServiceException("PLATFORM_GET_NODE_001", "平台接口未找到节点详细信息",
        String.format("节点ID：%s", nodeId));
}
```

**getLinkOverViewDataByInterfaceIds方法**:
```java
// 修改前
try {
    // 查询链路总览数据
    return linkOverViewInfoList;
} catch (Exception e) {
    log.error("查询链路总览数据异常", e);
    return Collections.emptyList();
}

// 修改后
// 直接查询链路总览数据
if (未返回数据) {
    throw new ServiceException("PLATFORM_LINK_OVERVIEW_001", "平台接口未返回链路总览数据",
        String.format("接口ID列表：%s", interfaceIds));
}
```

**queryPlatformLinksByNodeIds方法**:
```java
// 修改前
try {
    // 查询链路信息
    if (response == null || !response.getSuccessful()) {
        log.warn("查询失败");
        hasMoreData = false;
    }
} catch (Exception e) {
    log.error("调用平台接口查询链路信息异常", e);
}

// 修改后
// 直接查询链路信息
if (response == null || !Boolean.TRUE.equals(response.getSuccessful())) {
    throw new ServiceException("PLATFORM_GET_LINKS_001", "平台接口查询链路信息失败",
        String.format("请求ID：%s，页码：%d", requestId, currentPage));
}
if (response.getResult() == null || response.getResult().getRecords() == null) {
    throw new ServiceException("PLATFORM_GET_LINKS_002", "平台接口返回链路信息为空",
        String.format("请求ID：%s，页码：%d", requestId, currentPage));
}
```

## 异常错误码规范

### 链路新增相关 (LINK_ADD_xxx)
- `LINK_ADD_001`: 源设备不存在或未找到对应的平台节点ID
- `LINK_ADD_002`: 目标设备不存在或未找到对应的平台节点ID
- `LINK_ADD_003`: 未找到源设备端口对应的平台端口信息
- `LINK_ADD_004`: 未找到目标设备端口对应的平台端口信息
- `LINK_ADD_005`: 平台新增链路失败
- `LINK_ADD_006`: 保存链路信息失败

### 链路更新相关 (LINK_UPDATE_xxx)
- `LINK_UPDATE_001`: 链路不存在或已被删除
- `LINK_UPDATE_002`: 平台中不存在该链路
- `LINK_UPDATE_003`: 平台更新链路失败
- `LINK_UPDATE_004`: 没有需要更新的字段
- `LINK_UPDATE_005`: 更新链路定制信息失败

### 链路查询相关 (LINK_QUERY_xxx)
- `LINK_QUERY_001`: 站点不存在或已被删除
- `LINK_QUERY_002`: 未找到站点或下级站点
- `LINK_QUERY_003`: 未找到站点下的任何设备
- `LINK_QUERY_004`: 未从平台获取到任何链路数据

### 平台接口相关 (PLATFORM_xxx)
- `PLATFORM_LINK_001`: 平台中不存在链路
- `PLATFORM_LINK_002`: 平台中不存在链路（查找匹配失败）
- `PLATFORM_ADD_LINK_001`: 平台新增链路失败
- `PLATFORM_PORT_001`: 未找到匹配的端口

### 设备节点相关 (DEVICE_NODE_xxx)
- `DEVICE_NODE_001`: 设备不存在或已被删除
- `DEVICE_NODE_002`: 设备未关联平台节点ID

### 设备站点相关 (DEVICE_SITE_xxx)
- `DEVICE_SITE_001`: 设备不存在或已被删除
- `DEVICE_SITE_002`: 设备未设置站点ID

### 带宽解析相关 (BANDWIDTH_PARSE_xxx)
- `BANDWIDTH_PARSE_001`: 带宽不能为空
- `BANDWIDTH_PARSE_002`: 带宽格式不正确
- `BANDWIDTH_PARSE_003`: 不支持的带宽单位

### 平台接口调用相关 (PLATFORM_xxx)
- `PLATFORM_GET_LINK_001`: 平台接口未找到链路基本信息
- `PLATFORM_GET_NODE_001`: 平台接口未找到节点详细信息
- `PLATFORM_LINK_OVERVIEW_001`: 平台接口未返回链路总览数据
- `PLATFORM_GET_LINKS_001`: 平台接口查询链路信息失败
- `PLATFORM_GET_LINKS_002`: 平台接口返回链路信息为空

## 优势

### 1. 代码简化
- 移除了大量重复的try-catch代码
- Controller层代码更加简洁
- Service层专注于业务逻辑

### 2. 异常处理统一
- 所有异常都由GlobalExceptionHandler统一处理
- 错误响应格式一致
- 日志记录统一

### 3. 错误信息详细
- ServiceException包含错误代码、错误消息和业务上下文
- 便于问题定位和调试
- 支持国际化扩展

### 4. 事务回滚
- ServiceException继承自RuntimeException
- 自动触发Spring事务回滚
- 保证数据一致性

## 向后兼容性

- ✅ API接口路径和参数保持不变
- ✅ 响应格式保持不变
- ✅ 错误响应结构保持不变
- ✅ 现有客户端代码无需修改

## 测试建议

1. **功能测试**: 验证所有链路管理接口的正常功能
2. **异常测试**: 验证各种异常情况下的错误响应
3. **事务测试**: 验证异常情况下的事务回滚
4. **性能测试**: 确认异常处理不影响性能

## 平台接口异常处理原则

根据用户要求，**原则上平台接口返回的错误都要按全局异常方式处理**。我们对以下平台接口调用进行了全局异常处理改造：

### 1. 关键业务流程中的平台接口
这些接口失败时必须抛出ServiceException，不能降级处理：
- `getSingleLinkData` - 获取链路基本信息
- `getSingleNodeData` - 获取节点详细信息
- `getLinkOverViewDataByInterfaceIds` - 获取链路总览数据
- `queryPlatformLinksByNodeIds` - 查询平台链路信息

### 2. 容错处理策略
对于调用这些平台接口的方法，采用以下容错策略：
- **主要业务流程**: 平台接口失败时抛出ServiceException，中断流程
- **辅助数据获取**: 在调用方捕获ServiceException，使用默认值或跳过处理
- **批量处理**: 单个项目的平台接口失败不影响其他项目的处理

### 3. 修改的调用方法
为了保持系统健壮性，以下调用方法增加了ServiceException的捕获处理：
- `buildSingleLinkInfoWithAllData` - 捕获链路基本信息获取异常，使用默认值
- `setSingleDeviceInfo` - 捕获节点信息获取异常，跳过设备信息设置
- `setDeviceInfoFromPlatformNodeId` - 捕获节点信息获取异常，跳过设备信息设置
- `setSingleLinkPerformanceData` - 捕获链路总览数据获取异常，跳过性能数据设置
- `setPlatformLinkPerformanceData` - 捕获链路总览数据获取异常，设置默认性能数据

## 总结

通过本次优化，链路管理接口实现了与设备管理接口一致的全局异常处理模式，特别是**严格按照平台接口错误全局异常处理的原则**进行了改造，提高了代码质量和维护性，同时保持了完全的向后兼容性。
