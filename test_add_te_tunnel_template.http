### 隧道模板管理接口测试

### 1. 新增隧道模板 - 使用保障带宽百分比
POST http://localhost:8080/srv6/addTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy001",
  "strategyName": "高优先级隧道策略",
  "slaId": "EF",
  "bandwidthPrecent": 80.5,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 2,
  "packetLossRate": 0.001,
  "delayTime": 10,
  "networkJitter": 5
}

### 2. 新增隧道模板 - 使用保障带宽值
POST http://localhost:8080/srv6/addTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy002",
  "strategyName": "中等优先级隧道策略",
  "slaId": "AF4",
  "bandwidth": 1000000,
  "upAllowLinkPriority": 2,
  "downAllowLinkPriority": 3,
  "packetLossRate": 0.005,
  "delayTime": 20,
  "networkJitter": 10
}

### 3. 新增隧道模板 - 最小配置（不填可选字段）
POST http://localhost:8080/srv6/addTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy003",
  "strategyName": "低优先级隧道策略",
  "bandwidthPrecent": 50.0,
  "upAllowLinkPriority": 3,
  "downAllowLinkPriority": 4,
  "packetLossRate": 0.01,
  "delayTime": 50,
  "networkJitter": 20
}

### 4. 错误测试 - 策略ID重复
POST http://localhost:8080/srv6/addTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy001",
  "strategyName": "重复策略ID测试",
  "bandwidthPrecent": 60.0,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 1,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}

### 5. 错误测试 - 缺少必填字段
POST http://localhost:8080/srv6/addTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyName": "缺少策略ID测试",
  "bandwidthPrecent": 60.0,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 1,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}

### 6. 错误测试 - 带宽字段都填写
POST http://localhost:8080/srv6/addTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy006",
  "strategyName": "带宽字段冲突测试",
  "bandwidthPrecent": 60.0,
  "bandwidth": 500000,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 1,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}

### ============ 删除隧道模板接口测试 ============

### 7. 删除隧道模板 - 单个策略ID
POST http://localhost:8080/srv6/deleteTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyIds": ["strategy001"]
}

### 8. 删除隧道模板 - 多个策略ID
POST http://localhost:8080/srv6/deleteTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyIds": ["strategy002", "strategy003"]
}

### 9. 错误测试 - 空的策略ID集合
POST http://localhost:8080/srv6/deleteTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyIds": []
}

### 10. 错误测试 - 不存在的策略ID
POST http://localhost:8080/srv6/deleteTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyIds": ["nonexistent_strategy"]
}

### 11. 错误测试 - 缺少策略ID字段
POST http://localhost:8080/srv6/deleteTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{}

### ============ 修改隧道模板接口测试 ============

### 12. 修改隧道模板 - 使用保障带宽百分比
POST http://localhost:8080/srv6/updateTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy001",
  "strategyName": "修改后的高优先级隧道策略",
  "slaId": "AF4",
  "bandwidthPrecent": 90.0,
  "upAllowLinkPriority": 2,
  "downAllowLinkPriority": 3,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}

### 13. 修改隧道模板 - 使用保障带宽值
POST http://localhost:8080/srv6/updateTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy002",
  "strategyName": "修改后的中等优先级隧道策略",
  "slaId": "AF3",
  "bandwidth": 2000000,
  "upAllowLinkPriority": 3,
  "downAllowLinkPriority": 4,
  "packetLossRate": 0.003,
  "delayTime": 25,
  "networkJitter": 12
}

### 14. 修改隧道模板 - 最小配置（不填可选字段）
POST http://localhost:8080/srv6/updateTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy003",
  "strategyName": "修改后的低优先级隧道策略",
  "bandwidthPrecent": 70.0,
  "upAllowLinkPriority": 4,
  "downAllowLinkPriority": 5,
  "packetLossRate": 0.015,
  "delayTime": 60,
  "networkJitter": 25
}

### 15. 错误测试 - 修改不存在的策略ID
POST http://localhost:8080/srv6/updateTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "nonexistent_strategy",
  "strategyName": "不存在的策略测试",
  "bandwidthPrecent": 60.0,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 1,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}

### 16. 错误测试 - 带宽字段都填写
POST http://localhost:8080/srv6/updateTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy001",
  "strategyName": "带宽字段冲突测试",
  "bandwidthPrecent": 60.0,
  "bandwidth": 500000,
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 1,
  "packetLossRate": 0.002,
  "delayTime": 15,
  "networkJitter": 8
}

### 17. 错误测试 - 缺少必填字段
POST http://localhost:8080/srv6/updateTeTunnelTemplate
X-Param-Length: {{$dotenv CONTENT_LENGTH}}
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

{
  "strategyId": "strategy001",
  "strategyName": "缺少必填字段测试",
  "upAllowLinkPriority": 1,
  "downAllowLinkPriority": 1,
  "delayTime": 15,
  "networkJitter": 8
}

### ============ 查询隧道模板列表接口测试 ============

### 18. 查询隧道模板列表 - 正常查询
GET http://localhost:8080/srv6/getAllTeTunnelTemplate
X-Param-Length: 0
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{$dotenv ACCESS_TOKEN}}

### 19. 错误测试 - 无效Token
GET http://localhost:8080/srv6/getAllTeTunnelTemplate
X-Param-Length: 0
Content-Type: application/json;charset=UTF-8
X-Access-Token: invalid-token

### 20. 错误测试 - 缺少Token
GET http://localhost:8080/srv6/getAllTeTunnelTemplate
X-Param-Length: 0
Content-Type: application/json;charset=UTF-8 