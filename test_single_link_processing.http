###
# 测试逐条处理链路信息查询
# 新的实现方式：对每条链路逐条获取相关数据
###

### 1. 测试单个站点的链路查询（逐条处理）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json
X-Token: test-token

### 2. 测试多链路站点的查询（观察逐条处理日志）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=2
Content-Type: application/json
X-Token: test-token

### 3. 测试大站点的查询（验证性能和稳定性）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=3
Content-Type: application/json
X-Token: test-token

### 4. 测试不存在的站点
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=99999
Content-Type: application/json
X-Token: test-token

### 5. 测试空参数
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId
Content-Type: application/json
X-Token: test-token

###
# 逐条处理的优势：
# 1. 逻辑更清晰 - 每条链路的处理是独立的
# 2. 错误隔离 - 单个链路异常不影响其他链路
# 3. 实时反馈 - 可以看到每条链路的处理进度
# 4. 易于调试 - 可以精确定位问题链路
# 5. 易于扩展 - 可以针对单个链路添加特殊处理逻辑
###

### 逐条处理流程说明：
# 对于每一条链路：
# 1. 获取链路基本信息（linkStatus, linkName, linkBandWidth）
# 2. 获取源设备信息（sourceDeviceName, sourceDeviceIp）
# 3. 获取目标设备信息（targetDeviceName, targetDeviceIp）
# 4. 获取链路性能数据（delay, jitter, loss, rate）
# 5. 查找反向链路并计算带宽利用率
# 6. 组装完整的链路信息DTO
### 