# 批量更新剩余方法的计划

基于代码检索结果，以下方法仍需要修改为抛出PlatformApiException：

## 需要修改的方法列表

### ACL相关方法
1. `addAclTemplate` - 新增ACL模板
2. `deleteAclTemplate` - 删除ACL模板  
3. `queryAclTemplates` - 查询ACL模板
4. `getAclIdByName` - 通过名称查询ACL ID
5. `updateAclTemplate` - 更新ACL模板
6. `getAclDetail` - 查询ACL模板详情

### SRv6 Policy相关方法
7. `addSrv6PolicyGroup` - 新增SRv6 Policy应用组
8. `updateSrv6PolicyGroupNetwork` - 更新SRv6 Policy应用组作用域
9. `getSrv6PolicyGroup` - 查询应用组信息
10. `getBfdTemplate` - 查询BFD模板
11. `deleteSrv6PolicyGroup` - 删除SRv6 Policy应用组
12. `startPlanSrv6PolicyGroup` - 规划应用组路径
13. `deploySrv6PolicyGroup` - 部署应用组配置
14. `getSrv6PolicyGroupDetail` - 查询应用组详情
15. `updateSrv6PolicyGroup` - 更新应用组
16. `getCustomNetworkScope` - 查询作用域
17. `getInventoryInfo` - 查询设备硬件版本信息
18. `getSrv6PolicyTrail` - 查询SRv6 Policy业务列表
19. `getSrv6PolicyTrailDeployDetail` - 查询SRv6 Policy业务部署详情

### QoS相关方法
20. `addQosClassifier` - 新增流分类
21. `updateQosClassifier` - 修改流分类
22. `deleteQosClassifier` - 删除流分类
23. `getQosClassifierList` - 查询流分类列表
24. `addQosBehavior` - 新增流行为
25. `getQosBehaviorDetail` - 查询流行为详情
26. `updateQosBehavior` - 修改流行为
27. `deleteQosBehavior` - 删除流行为
28. `addQosPolicy` - 新增流策略
29. `deleteQosPolicy` - 删除流策略
30. `updateQosPolicy` - 修改流策略
31. `getQosPolicyList` - 查询流策略列表
32. `getQosPolicyRuleDetail` - 查询流策略CB对详情
33. `deployQosPolicy` - 部署流策略到设备接口
34. `getQosDeviceList` - 查询QoS设备列表
35. `getQosDeviceInterfaceList` - 查询设备接口列表
36. `getQosPolicyDeployHistory` - 查询流策略部署历史

## 修改模式

每个方法都需要按照以下模式修改：

```java
// 原来的处理方式
if (platformResponse != null && platformResponse.getSuccessful()) {
    return platformResponse; // 或 return true;
} else {
    log.error("平台接口调用失败，响应：{}", responseBody);
    return null; // 或 return false;
}

// 修改后的处理方式
if (platformResponse != null && platformResponse.getSuccessful()) {
    return platformResponse; // 或 return true;
} else {
    // 平台接口调用失败，提取错误信息
    String errorMessage = platformResponse != null ? platformResponse.getMessage() : "平台接口调用失败";
    String errorCode = platformResponse != null ? platformResponse.getCode() : null;
    
    log.error("平台接口调用失败，响应：{}", responseBody);
    throw new PlatformApiException(errorMessage, errorCode, responseBody);
}
```

## 异常处理模式

```java
} catch (PlatformApiException e) {
    // 重新抛出平台API异常
    throw e;
} catch (Exception e) {
    log.error("调用平台接口异常", e);
    throw new PlatformApiException("调用平台接口异常：" + e.getMessage(), e);
}
```

## 特殊情况处理

1. **返回PlatformDeleteResult的方法** - 这些方法已经有特殊的错误处理逻辑，不需要修改
2. **Mock方法** - 不需要修改
3. **工具方法** - 如parseErrorCode等，不需要修改

## 优先级

由于方法较多，按以下优先级处理：
1. 高频使用的核心方法（ACL、SRv6 Policy基础操作）
2. QoS相关方法
3. 其他辅助查询方法
