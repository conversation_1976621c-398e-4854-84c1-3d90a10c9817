### API请求日志定时清理功能测试

# 说明：
# 1. 定时任务会在每天0点10分自动执行
# 2. 可以通过修改配置文件中的 cleanup.api-log.cron 来调整执行时间
# 3. 可以通过修改配置文件中的 cleanup.api-log.keep-days 来调整保留天数

### 查看当前API请求日志数量（如果有相关查询接口的话）
# 注意：需要先有一些API请求记录才能验证清理功能

### 生成一些测试日志记录
GET {{host}}/srv6/getAllTeTunnelTemplate
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token
X-Param-Length: 0

###

### 手动验证定时任务（开发测试时可以临时创建一个手动触发接口）
# 如果需要立即测试清理功能，可以考虑：
# 1. 修改定时任务的cron表达式为更频繁的执行
# 2. 或者创建一个手动触发的测试接口
# 3. 或者在数据库中插入一些历史日期的测试数据

### 配置说明：
# cleanup:
#   api-log:
#     # cron表达式说明：秒 分 时 日 月 周
#     # 0 10 0 * * ? 表示每天0点10分执行
#     # 0 */30 * * * ? 表示每30分钟执行一次（测试用）
#     cron: 0 10 0 * * ?
#     # 保留近3天的数据（不含执行当天）
#     keep-days: 3 