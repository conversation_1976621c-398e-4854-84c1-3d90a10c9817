### 查询应用列表接口测试

### 1. 查询应用列表 - 成功场景
GET http://localhost:8080/srv6/queryAppInfoList
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 2. 查询应用列表 - 无Token场景（预期返回401）
GET http://localhost:8080/srv6/queryAppInfoList
Content-Type: application/json;charset=UTF-8

### 3. 查询应用列表 - 空Token场景（预期返回401）  
GET http://localhost:8080/srv6/queryAppInfoList
Content-Type: application/json;charset=UTF-8
X-Access-Token: 

### 4. 查询应用列表 - 生产环境测试
GET http://prod-server:8080/srv6/queryAppInfoList
Content-Type: application/json;charset=UTF-8
X-Access-Token: {{production_token}} 