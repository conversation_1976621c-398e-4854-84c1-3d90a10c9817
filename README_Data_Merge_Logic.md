# 数据合并逻辑说明

## 概述

在修改后的 `queryLinkInfoListBySiteId` 接口中，实现了智能的数据合并逻辑，将平台链路数据与数据库定制信息进行合并，确保返回完整且准确的链路信息。

## 数据合并策略

### 1. 数据来源

#### 平台数据（主要来源）
- **链路基础信息**：linkId、linkName、linkStatus、bandwidth
- **设备节点信息**：srcNodeId、destNodeId、srcTpId、destTpId
- **网络拓扑信息**：完整的链路连接关系

#### 数据库定制信息（补充来源）
- **业务定制字段**：linkType（链路类型）、linkLevel（链路级别）
- **设备端口信息**：sourceDevicePort、targetDevicePort
- **站点关联信息**：siteId
- **设备ID映射**：sourceDeviceId、targetDeviceId

### 2. 合并逻辑

#### 核心合并方法：`buildLinkInfoFromPlatformData`

```java
// 1. 使用平台数据作为基础
linkInfoDTO.setLinkId(platformLink.getLinkId().toString());
linkInfoDTO.setLinkName(platformLink.getLinkName());
linkInfoDTO.setLinkStatus(translateLinkStatusFromPlatform(platformLink.getLinkStatus()));

// 2. 根据平台linkId查询数据库定制信息
LinkCustomInfo linkCustomInfo = getLinkCustomInfoByPlatformLinkId(platformLink.getLinkId().toString());

// 3. 智能合并定制字段
if (linkCustomInfo != null) {
    // 使用数据库中的定制信息
    linkInfoDTO.setLinkType(linkCustomInfo.getLinkType() != null ? linkCustomInfo.getLinkType() : "");
    linkInfoDTO.setLinkLevel(linkCustomInfo.getLinkLevel() != null ? linkCustomInfo.getLinkLevel() : "");
} else {
    // 数据库中没有定制信息，设置为空串
    linkInfoDTO.setLinkType("");
    linkInfoDTO.setLinkLevel("");
}
```

## 详细合并规则

### 1. 链路基础信息
| 字段 | 数据来源 | 合并规则 |
|------|----------|----------|
| linkId | 平台数据 | 直接使用平台linkId |
| linkName | 平台数据 | 优先使用平台linkName，为空时生成默认名称 |
| linkStatus | 平台数据 | 使用平台状态并进行状态翻译 |
| linkBandWidth | 平台数据 | 使用平台带宽并进行单位转换（kbps→bps） |

### 2. 定制业务字段
| 字段 | 数据来源 | 合并规则 |
|------|----------|----------|
| linkType | 数据库定制 | 查询到则使用，否则为空串 |
| linkLevel | 数据库定制 | 查询到则使用，否则为空串 |
| siteId | 数据库定制 | 优先使用数据库中的站点信息 |

### 3. 设备信息
| 字段 | 数据来源 | 合并规则 |
|------|----------|----------|
| sourceDeviceId | 数据库定制 | 优先使用数据库中的设备ID |
| targetDeviceId | 数据库定制 | 优先使用数据库中的设备ID |
| sourceDeviceName | 平台查询 | 通过平台节点ID查询设备名称 |
| targetDeviceName | 平台查询 | 通过平台节点ID查询设备名称 |
| sourceDeviceIp | 平台查询 | 通过平台节点ID查询设备IP |
| targetDeviceIp | 平台查询 | 通过平台节点ID查询设备IP |
| sourceDevicePort | 数据库定制 | 使用数据库中的端口信息 |
| targetDevicePort | 数据库定制 | 使用数据库中的端口信息 |

## 查询优化

### 1. 数据库查询方法

#### `getLinkCustomInfoByPlatformLinkId`
```java
private LinkCustomInfo getLinkCustomInfoByPlatformLinkId(String platformLinkId) {
    LambdaQueryWrapper<LinkCustomInfo> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(LinkCustomInfo::getPlatformLinkId, platformLinkId);
    queryWrapper.eq(LinkCustomInfo::getIsDeleted, 0);
    
    return linkCustomInfoMapper.selectOne(queryWrapper);
}
```

**特点：**
- 精确匹配平台链路ID
- 过滤软删除记录
- 单条记录查询，性能优良

### 2. 查询性能考虑

#### 查询频率
- 每个平台链路查询一次数据库
- 查询结果可能为空（正常情况）
- 不影响整体查询性能

#### 缓存策略（可选优化）
```java
// 可以考虑添加本地缓存
private final Map<String, LinkCustomInfo> linkCustomInfoCache = new ConcurrentHashMap<>();

private LinkCustomInfo getLinkCustomInfoByPlatformLinkId(String platformLinkId) {
    return linkCustomInfoCache.computeIfAbsent(platformLinkId, this::queryLinkCustomInfoFromDB);
}
```

## 数据一致性保证

### 1. 空值处理
```java
// 确保定制字段不为null
linkInfoDTO.setLinkType(linkCustomInfo.getLinkType() != null ? linkCustomInfo.getLinkType() : "");
linkInfoDTO.setLinkLevel(linkCustomInfo.getLinkLevel() != null ? linkCustomInfo.getLinkLevel() : "");
```

### 2. 异常处理
```java
try {
    LinkCustomInfo linkCustomInfo = getLinkCustomInfoByPlatformLinkId(platformLinkId);
    // 处理逻辑
} catch (Exception e) {
    log.error("查询链路定制信息异常，平台链路ID：{}", platformLinkId, e);
    // 设置默认值，确保接口正常返回
    linkInfoDTO.setLinkType("");
    linkInfoDTO.setLinkLevel("");
}
```

### 3. 日志记录
```java
if (linkCustomInfo != null) {
    log.debug("找到链路定制信息，平台链路ID：{}，链路类型：{}，链路级别：{}", 
             platformLinkId, linkCustomInfo.getLinkType(), linkCustomInfo.getLinkLevel());
} else {
    log.debug("未找到链路定制信息，平台链路ID：{}", platformLinkId);
}
```

## 业务场景

### 1. 完全匹配场景
- **情况**：平台链路在数据库中有对应的定制记录
- **结果**：返回完整的链路信息，包含所有定制字段
- **示例**：企业内部重要链路，有完整的业务分类信息

### 2. 部分匹配场景
- **情况**：平台链路在数据库中有记录，但部分定制字段为空
- **结果**：返回平台基础信息 + 已有的定制信息
- **示例**：新增链路，部分业务信息尚未完善

### 3. 无匹配场景
- **情况**：平台链路在数据库中没有对应记录
- **结果**：返回平台基础信息，定制字段为空串
- **示例**：平台自动发现的新链路，尚未录入业务系统

## 优势分析

### 1. 数据完整性
- 不遗漏任何平台链路
- 保留所有已有的定制信息
- 确保数据的业务价值

### 2. 系统兼容性
- 兼容现有的数据库结构
- 兼容现有的业务流程
- 平滑的系统升级

### 3. 扩展性
- 易于添加新的定制字段
- 支持复杂的数据合并规则
- 为未来功能扩展预留空间

## 总结

通过智能的数据合并逻辑，新的查询接口既保证了数据的完整性（不遗漏平台链路），又保留了业务定制信息的价值，实现了技术需求与业务需求的完美平衡。
