### 测试查询调度策略列表接口

# 1. 基础查询调度策略列表（开发环境）
GET http://localhost:28000/gszh-srv6-api/srv6/queryScheduleList
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-schedule-query

###

# 2. 测试查询调度策略列表（生产环境）
GET http://***********:28000/gszh-srv6-api/srv6/queryScheduleList
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-actual-token-here

###

# 3. 测试查询调度策略列表（无Token，应该返回错误）
GET http://localhost:28000/gszh-srv6-api/srv6/queryScheduleList
Content-Type: application/json;charset=UTF-8

###

# 4. 测试查询调度策略列表（测试模式验证）
# 在测试模式下，会返回模拟数据，包括设备SN等信息
GET http://localhost:28000/gszh-srv6-api/srv6/queryScheduleList
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-with-device-sn

###
