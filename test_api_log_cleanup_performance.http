### API请求日志分批清理性能测试

# 说明：
# 本测试文件用于验证分批清理功能的性能优化效果

### 1. 生成大量测试数据
# 先通过多次API调用生成一些测试日志数据

GET {{host}}/srv6/getAllTeTunnelTemplate
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-1
X-Param-Length: 0

###

GET {{host}}/srv6/getAllTeTunnelTemplate
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-2
X-Param-Length: 0

###

GET {{host}}/srv6/getAllTeTunnelTemplate
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-3
X-Param-Length: 0

###

### 2. 数据库手动测试查询
# 可以在数据库中执行以下SQL来测试分批删除的效果

-- 查看当前日志总数
-- SELECT COUNT(*) FROM api_request_log;

-- 查看需要删除的历史数据数量（假设保留3天）
-- SELECT COUNT(*) FROM api_request_log WHERE create_time < DATE_SUB(CURDATE(), INTERVAL 3 DAY);

-- 手动插入一些历史测试数据（用于测试删除功能）
-- INSERT INTO api_request_log (request_id, request_uri, request_method, client_ip, request_time, duration_ms, create_time)
-- VALUES 
--   ('test-old-1', '/test', 'GET', '127.0.0.1', DATE_SUB(NOW(), INTERVAL 5 DAY), 100, DATE_SUB(NOW(), INTERVAL 5 DAY)),
--   ('test-old-2', '/test', 'GET', '127.0.0.1', DATE_SUB(NOW(), INTERVAL 6 DAY), 100, DATE_SUB(NOW(), INTERVAL 6 DAY)),
--   ('test-old-3', '/test', 'GET', '127.0.0.1', DATE_SUB(NOW(), INTERVAL 7 DAY), 100, DATE_SUB(NOW(), INTERVAL 7 DAY));

### 3. 性能优化配置说明

## 小数据量场景（< 1万条）
## cleanup.api-log.performance-optimization: false  # 可以关闭优化，使用传统删除
## cleanup.api-log.batch-size: 1000
## cleanup.api-log.max-delete-count: 10000
## cleanup.api-log.batch-delay-ms: 0

## 中等数据量场景（1-10万条）
## cleanup.api-log.performance-optimization: true
## cleanup.api-log.batch-size: 2000
## cleanup.api-log.max-delete-count: 50000
## cleanup.api-log.batch-delay-ms: 100

## 大数据量场景（> 10万条）
## cleanup.api-log.performance-optimization: true
## cleanup.api-log.batch-size: 1000
## cleanup.api-log.max-delete-count: 100000
## cleanup.api-log.batch-delay-ms: 200

### 4. 监控日志示例

# 启用性能优化模式时，日志输出示例：
# INFO  - 开始执行API请求日志定时清理任务，保留近3天的数据，性能优化模式：启用
# INFO  - 开始分批删除API请求日志，共15000条记录需要删除，批次大小：1000，最大删除限制：50000
# DEBUG - 第1批删除完成，本批删除1000条，累计删除1000条
# DEBUG - 第2批删除完成，本批删除1000条，累计删除2000条
# ...
# INFO  - API请求日志分批删除完成，共删除15000条记录，分15批执行，总耗时2500ms，保留近3天的数据
# INFO  - API请求日志定时清理任务执行完成（分批模式），共删除15000条记录

### 5. 性能对比测试

# 如需对比两种模式的性能，可以：
# 1. 先设置 performance-optimization: false，观察传统模式的执行时间
# 2. 再设置 performance-optimization: true，观察分批模式的执行时间和对数据库的影响

### 6. 临时测试配置

# 如需立即测试，可以临时修改配置：
# cleanup.api-log.cron: 0 */1 * * * ?  # 每分钟执行一次
# cleanup.api-log.keep-days: 0         # 删除所有数据（仅测试用）
# 注意：测试完成后记得恢复正常配置！ 