### 部署隧道组接口测试

### 1. 部署单个隧道组（规划并部署）
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001],
    "distribute": true
}

### 2. 部署多个隧道组（规划并部署）
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001, 1002, 1003],
    "distribute": true
}

### 3. 只规划不部署单个隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001],
    "distribute": false
}

### 4. 只规划不部署多个隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001, 1002, 1003],
    "distribute": false
}

### 5. 使用默认distribute参数（应该默认为true）
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001, 1002]
}

### 6. 部署不存在的隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [9999],
    "distribute": true
}

### 7. 空隧道组ID集合（应该返回参数验证错误）
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [],
    "distribute": true
}

### 8. 缺少隧道组ID集合参数（应该返回参数验证错误）
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "distribute": true
}

### 9. 混合存在和不存在的隧道组
POST http://localhost:28000/gszh-srv6-api/srv6/deployTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

{
    "teGroupIds": [1001, 9999, 1002],
    "distribute": true
}

### 测试说明
# 1. 确保服务已启动：./gradlew bootRun
# 2. 测试模式已启用，会模拟平台接口调用
# 3. 需要先通过新增隧道组接口创建测试数据
# 4. distribute参数控制是否下发配置：
#    - true: 先规划路径，再部署配置（默认值）
#    - false: 只规划路径，不部署配置
# 5. 支持批量部署，部分成功部分失败会返回详细错误信息
# 6. 部署操作会调用平台的规划和部署接口
