### 隧道组管理接口测试

### 1. 新增隧道组（按设备配置）
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": 1001,
  "name": "TestGroup01",
  "isLoose": true,
  "balanceMode": 2,
  "balanceProportion": "1:2",
  "scheduleStrategyId": 1,
  "color": 100,
  "pathSize": 2,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [1001, 1002],
      "srcDeviceNames": ["Device-1001", "Device-1002"],
      "dstDeviceIds": [1003, 1004],
      "dstDeviceNames": ["Device-1003", "Device-1004"]
    }
  ],
  "hopLimit": 10
}

### 2. 新增隧道组（按业务网络配置）
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": "tegroup002",
  "name": "TestGroup02",
  "isLoose": false,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 2,
  "color": 200,
  "pathSize": 1,
  "planeRoutingType": 2,
  "isVirtualNet": true,
  "virtualNetId": "vnet001",
  "hopLimit": 5
}

### 3. 新增隧道组（负载模式，多路径）
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": "tegroup003",
  "name": "LoadBalance",
  "isLoose": true,
  "balanceMode": 2,
  "balanceProportion": "1:2:3",
  "scheduleStrategyId": 3,
  "color": 300,
  "pathSize": 3,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [2001],
      "srcDeviceNames": ["Source-Device"],
      "dstDeviceIds": [2002, 2003, 2004],
      "dstDeviceNames": ["Dest-Device-1", "Dest-Device-2", "Dest-Device-3"]
    }
  ],
  "hopLimit": 15
}

### 4. 错误测试：隧道组ID重复
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": 1001,
  "name": "DuplicateId",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 400,
  "pathSize": 1,
  "planeRoutingType": 1,
  "isVirtualNet": true,
  "virtualNetId": "vnet002"
}

### 5. 错误测试：参数验证失败（名称格式错误）
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": 1004,
  "name": "Invalid@Name#",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 500,
  "pathSize": 1,
  "planeRoutingType": 1,
  "isVirtualNet": true,
  "virtualNetId": "vnet003"
}

### 6. 错误测试：按设备配置但未提供设备信息
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": 1005,
  "name": "NoDevices",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 600,
  "pathSize": 1,
  "planeRoutingType": 1,
  "isVirtualNet": false
}

### 7. 错误测试：设备ID和名称列表长度不一致
POST http://localhost:8080/srv6/addTeGroup
Content-Type: application/json;charset=UTF-8
X-Access-Token: your-token-here

{
  "id": 1006,
  "name": "MismatchLists",
  "isLoose": true,
  "balanceMode": 1,
  "balanceProportion": "1:1",
  "scheduleStrategyId": 1,
  "color": 700,
  "pathSize": 1,
  "planeRoutingType": 1,
  "isVirtualNet": false,
  "teGroupDcs": [
    {
      "srcDeviceIds": [3001, 3002],
      "srcDeviceNames": ["Device-3001"],
      "dstDeviceIds": [3003],
      "dstDeviceNames": ["Device-3003"]
    }
  ]
} 