# 设备删除和维护接口错误处理优化

## 概述

按照新增设备接口的相同方式，优化了删除设备和维护设备接口的错误处理逻辑。现在这些接口都直接返回平台响应DTO，提供具体的错误信息而不是简单的boolean值。

## 修改内容

### 1. 修改 PlatformApiService 接口

**删除设备接口**：
```java
// 修改前
boolean deleteDevice(PlatformDeleteRequestDTO request);

// 修改后
PlatformDeleteResponseDTO deleteDevice(PlatformDeleteRequestDTO request);
```

**维护设备接口**：
```java
// 修改前
boolean maintainDevice(MaintainNodeDTO request);

// 修改后
MaintainNodeResponseDTO maintainDevice(MaintainNodeDTO request);
```

### 2. 修改 PlatformApiServiceImpl 实现

#### 删除设备方法优化
- **成功情况**: 直接返回平台解析的 `PlatformDeleteResponseDTO`
- **失败情况**: 
  - HTTP 200但业务失败：返回平台的错误响应
  - HTTP非200：尝试解析错误响应，解析失败则构造失败响应
  - 异常情况：构造异常响应

#### 维护设备方法优化
- **成功情况**: 直接返回平台解析的 `MaintainNodeResponseDTO`
- **失败情况**: 
  - HTTP 200但业务失败：返回平台的错误响应
  - HTTP非200：尝试解析错误响应，解析失败则构造失败响应
  - 异常情况：构造异常响应

### 3. 修改 DeviceServiceImpl 业务逻辑

#### 删除设备流程优化
```java
// 修改前
boolean deleteSuccess = platformApiService.deleteDevice(deleteRequest);
if (!deleteSuccess) {
    return ApiResponseDTO.fail(requestId, "调用平台接口删除设备失败");
}

// 修改后
PlatformDeleteResponseDTO deleteResponse = platformApiService.deleteDevice(deleteRequest);
if (deleteResponse == null || !Boolean.TRUE.equals(deleteResponse.getSuccessful())) {
    String errorMessage = deleteResponse != null ? deleteResponse.getMessage() : null;
    if (StrUtil.isBlank(errorMessage)) {
        errorMessage = "调用平台接口删除设备失败";
    }
    return ApiResponseDTO.fail(requestId, errorMessage);
}
```

#### 维护设备流程优化
```java
// 修改前
boolean maintainSuccess = platformApiService.maintainDevice(maintainRequest);
if (!maintainSuccess) {
    log.warn("调用平台接口维护设备失败，继续执行删除操作");
}

// 修改后
MaintainNodeResponseDTO maintainResponse = platformApiService.maintainDevice(maintainRequest);
if (maintainResponse == null || !Boolean.TRUE.equals(maintainResponse.getSuccessful())) {
    String errorMessage = maintainResponse != null ? maintainResponse.getMessage() : null;
    log.warn("调用平台接口维护设备失败，继续执行删除操作，错误信息：{}", errorMessage);
}
```

## 错误处理示例

### 删除设备失败示例

**平台返回**：
```json
{
  "message": "设备不存在或已被删除",
  "code": "DEVICE_NOT_FOUND",
  "result": null,
  "successful": false
}
```

**用户看到**：
```json
{
  "requestId": "req_123",
  "result": 0,
  "failReason": "设备不存在或已被删除"
}
```

### 维护设备失败示例

**平台返回**：
```json
{
  "message": "设备维护失败，设备离线",
  "code": "DEVICE_OFFLINE",
  "result": null,
  "successful": false
}
```

**日志输出**：
```
调用平台接口维护设备失败，继续执行删除操作，设备ID：123，平台节点ID：456，错误信息：设备维护失败，设备离线
```

## 构造响应的场景

### 1. HTTP状态码非200
```java
PlatformDeleteResponseDTO failureResponse = new PlatformDeleteResponseDTO();
failureResponse.setSuccessful(false);
failureResponse.setMessage("平台删除接口调用失败，状态码：500");
failureResponse.setCode("HTTP_ERROR_500");
```

### 2. 解析响应失败
```java
PlatformDeleteResponseDTO failureResponse = new PlatformDeleteResponseDTO();
failureResponse.setSuccessful(false);
failureResponse.setMessage("解析平台删除响应失败");
failureResponse.setCode("PARSE_ERROR");
```

### 3. 异常情况
```java
PlatformDeleteResponseDTO exceptionResponse = new PlatformDeleteResponseDTO();
exceptionResponse.setSuccessful(false);
exceptionResponse.setMessage("调用平台删除接口异常：" + e.getMessage());
exceptionResponse.setCode("EXCEPTION");
```

## 测试验证

### 新增测试文件
1. **PlatformDeleteResponseDTOTest.java** - 删除响应DTO测试
2. **MaintainNodeResponseDTOTest.java** - 维护响应DTO测试

### 测试覆盖
- ✅ 成功响应测试
- ✅ 失败响应测试  
- ✅ 构造失败响应测试

## 优势总结

### 1. 一致性
- 与新增设备接口保持相同的错误处理方式
- 统一的响应结构和错误信息传递机制

### 2. 信息完整性
- 保留平台返回的完整错误信息（message、code等）
- 便于问题诊断和调试

### 3. 可维护性
- 使用现有的DTO结构，无需额外封装
- 代码逻辑清晰，易于理解和维护

### 4. 用户体验
- 提供具体的错误信息而不是通用消息
- 帮助用户快速定位问题原因

## 兼容性

- ✅ **向后兼容**: API接口契约保持不变
- ✅ **功能兼容**: 成功场景行为保持一致
- ✅ **错误处理增强**: 失败场景提供更详细的错误信息

## 总结

此次优化将删除设备和维护设备接口的错误处理方式与新增设备接口保持一致，都采用直接返回平台响应DTO的方式。这样既保持了代码的一致性，又提供了更好的错误信息，提升了整体的用户体验和系统的可维护性。
