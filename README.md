# 工商总行设备管理接口项目

## 项目概述
本项目实现了工商总行定义的设备管理接口，主要功能是对SDWAN设备新增接口进行封装，以满足总行API标准需要。

## 已实现功能

### 4.2.1 新增设备接口
- **接口地址**: `POST /srv6/addDevice`
- **功能描述**: 接收工行控制器下发的设备参数，调用平台接口新增设备，并保存自定义信息

## 项目结构

```
src/main/java/com/h3c/dzkf/
├── controller/
│   └── DeviceController.java          # 设备管理控制器
├── service/
│   ├── DeviceService.java             # 设备服务接口
│   ├── PlatformApiService.java        # 平台API服务接口
│   └── impl/
│       ├── DeviceServiceImpl.java     # 设备服务实现
│       └── PlatformApiServiceImpl.java # 平台API服务实现
├── dao/
│   └── DeviceCustomInfoMapper.java    # 设备自定义信息数据访问层
├── entity/
│   ├── DeviceCustomInfo.java          # 设备自定义信息实体
│   └── dto/
│       ├── AddDeviceRequestDTO.java   # 新增设备请求DTO
│       ├── PlatformDeviceRequestDTO.java # 平台设备请求DTO
│       └── ApiResponseDTO.java        # 统一响应DTO
└── GszhApiApplication.java            # 应用启动类

src/main/resources/
├── mapper/
│   └── DeviceCustomInfoMapper.xml     # MyBatis映射文件
└── application-dev.yml                # 配置文件

sql/
└── device_custom_info.sql             # 数据库表创建脚本

docs/
├── 设备管理接口说明.md                # 接口详细说明文档
└── 设备管理接口测试.postman_collection.json # Postman测试集合
```

## 数据库设计

### device_custom_info 表
存储平台中不存在的设备自定义信息：
- device_id: 设备ID（唯一标识）
- platform_node_id: 平台设备节点ID
- device_site_id: 设备所属站点ID
- device_site: 所属站点名称
- is_rr: 是否为RR设备
- device_plane_id: 所属平面ID
- create_time: 创建时间（首次上线时间）

## 核心逻辑

1. **参数映射**: 将工行接口参数映射为平台接口参数
2. **数据验证**: 验证设备厂商、角色等参数的有效性
3. **平台调用**: 调用平台 `/nfm/physicalNetwork/node/addNode` 接口
4. **数据存储**: 将平台不支持的字段存储到自定义表中
5. **关联维护**: 通过platform_node_id关联平台设备和自定义信息

## 部署步骤

1. **创建数据表**:
   ```sql
   -- 执行 sql/device_custom_info.sql
   ```

2. **配置数据库连接**:
   ```yaml
   spring:
     datasource:
       url: ********************************
       username: root
       password: 123456
   ```

3. **配置ADWAN平台连接**:
   ```yaml
   adwan:
     platform:
       baseUrl: http://***********:30000
       addDeviceUrl: /nfm/physicalNetwork/node/addNode
   ```

4. **启动应用**:
   ```bash
   ./gradlew bootRun
   ```

5. **接口测试**:
   - 导入 `docs/设备管理接口测试.postman_collection.json` 到Postman
   - 先调用Token接口获取访问令牌
   - 使用令牌调用设备新增接口

## 接口示例

### 请求示例
```bash
POST http://localhost:28000/gszh-api/srv6/addDevice
Content-Type: application/json;charset=UTF-8
X-Access-Token: your_token_here

{
    "deviceId": "ICBC_DEV_001",
    "deviceManufacturer": "H3C",
    "deviceName": "核心交换机01",
    "deviceIp": "*************",
    "deviceSn": "SN123456789",
    "deviceModel": "S12500",
    "deviceRole": "PE",
    "deviceSiteId": "SITE_001",
    "deviceSite": "北京数据中心",
    "isRR": 1,
    "devicePlaneId": "PLANE_001",
    "isMarkDevice": 1
}
```

### 响应示例
```json
{
    "requestId": "abc123def456",
    "result": 1,
    "failReason": null,
    "optionField": "设备新增成功"
}
```

## 技术栈
- Spring Boot 2.7.6
- MyBatis Plus 3.5.5
- MySQL 8.x
- Hutool 5.3.3
- FastJSON 2.0.42
- Swagger 2.6.1

## 注意事项
1. 设备ID必须唯一，重复添加会返回失败
2. 设备厂商仅支持H3C、HP、UNIS，其他归为Unknown
3. 设备角色仅支持P、PE、ASBR_PE三种
4. 需要先获取有效的X-Access-Token
5. 创建时间自动记录，用于查询时作为首次上线时间 