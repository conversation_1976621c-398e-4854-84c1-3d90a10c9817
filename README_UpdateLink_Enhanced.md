# 工商总行链路更新接口增强版

## 概述
根据平台接口规范要求，更新链路接口现在采用"先查询、后修改、再提交"的模式，确保不会丢失平台的重要字段信息。

## 工作流程

### 1. 原有流程（已优化）
- 仅根据用户请求构建平台更新请求
- 可能丢失平台的其他重要字段

### 2. 新增强流程
1. **验证链路存在性**：从定制库查询链路信息
2. **获取完整平台信息**：调用平台 `getLink` 接口获取当前完整链路信息
3. **基于现有信息修改**：在平台返回的完整信息基础上，仅修改用户指定的字段
4. **提交更新**：调用平台 `updateLink` 接口提交修改
5. **更新定制库**：更新定制库中的工行定制字段

## 关键改进

### 保留所有平台字段
```java
// 基于平台现有信息构建更新请求，保留所有重要字段
platformUpdateRequest.setLinkId(platformLinkInfo.getLinkId());
platformUpdateRequest.setSrcNodeId(platformLinkInfo.getSrcNodeId());
platformUpdateRequest.setSrcTpId(platformLinkInfo.getSrcTpId());
platformUpdateRequest.setDestNodeId(platformLinkInfo.getDestNodeId());
platformUpdateRequest.setDestTpId(platformLinkInfo.getDestTpId());
platformUpdateRequest.setMetric(platformLinkInfo.getMetric());
platformUpdateRequest.setAvailable(platformLinkInfo.getAvailable());
// ... 更多字段
```

### 智能字段修改
```java
// 根据用户请求修改相应字段
if (StrUtil.isNotBlank(request.getLinkName())) {
    platformUpdateRequest.setLinkName(request.getLinkName());
    platformUpdateRequest.setLinkNameUserSet(1); // 标记为用户设置
}
```

## 接口详细信息

### 请求参数
- **linkId** (String, 必选) - 平台链路ID
- **linkName** (String, 可选) - 新的链路名称
- **linkBandWidth** (String, 可选) - 新的带宽（支持：100Mbps, 1Gbps等格式）
- **linkType** (String, 可选) - 工行定制：链路类型
- **linkLevel** (String, 可选) - 工行定制：链路等级
- **linkStatus** (String, 可选) - 工行定制：链路状态

### 响应参数
- **requestId** (String) - 请求ID
- **linkId** (String) - 链路ID
- **failReason** (String) - 失败原因（成功时为null）
- **optionField** (String) - 已更新的字段列表

## 测试场景

### 场景1：仅更新链路名称
```json
{
  "linkId": "279062257336323",
  "linkName": "新的链路名称"
}
```

**预期行为**：
1. 从平台获取链路完整信息
2. 仅修改 `linkName` 和 `linkNameUserSet` 字段
3. 保留其他所有平台字段不变

### 场景2：仅更新带宽
```json
{
  "linkId": "279062257336323",
  "linkBandWidth": "10Gbps"
}
```

**预期行为**：
1. 解析带宽：10Gbps → 10,000,000,000 bps
2. 更新 `bandwidth` 和 `resvBandwidth` 字段
3. 保留其他所有平台字段不变

### 场景3：同时更新平台和定制字段
```json
{
  "linkId": "279062257336323",
  "linkName": "完整更新的链路",
  "linkBandWidth": "100Gbps",
  "linkType": "BACKUP"
}
```

**预期行为**：
1. 从平台获取完整链路信息
2. 更新平台字段：链路名称和带宽
3. 更新定制库字段：链路类型

## 错误处理

### 1. 链路不存在
- **检查点**：定制库查询
- **错误信息**：`"链路不存在"`

### 2. 平台查询失败
- **检查点**：调用平台 `getLink` 接口
- **错误信息**：`"从平台获取链路信息失败"`

### 3. 平台更新失败
- **检查点**：调用平台 `updateLink` 接口
- **错误信息**：`"平台更新链路失败"`

### 4. 带宽格式错误
- **检查点**：带宽解析
- **错误信息**：`"带宽格式不正确，应为：数字+单位(bps/Kbps/Mbps/Gbps)，如：1000Mbps"`

## 日志输出

### 关键日志点
1. **开始处理**：`"开始处理更新链路请求，请求ID：{}, 链路ID：{}"`
2. **平台查询**：`"需要更新平台字段，先从平台获取完整链路信息，链路ID：{}"`
3. **信息获取成功**：`"成功获取平台链路信息，链路名称：{}，带宽：{} bps"`
4. **字段对比**：`"将更新链路名称：{} -> {}"` / `"将更新链路带宽：{} bps -> {} bps"`
5. **更新成功**：`"平台更新链路成功，请求ID：{}"`

## 测试命令

### cURL测试
```bash
curl -X PUT 'http://localhost:8080/srv6/updateLink' \
  -H 'Content-Type: application/json;charset=UTF-8' \
  -H 'X-Access-Token: your-token-here' \
  -d '{
    "linkId": "279062257336323",
    "linkName": "测试更新链路名称",
    "linkBandWidth": "5Gbps",
    "linkType": "PRIMARY"
  }'
```

### 预期响应
```json
{
  "requestId": "20241223001",
  "linkId": "279062257336323",
  "failReason": null,
  "optionField": "linkName,linkBandWidth,linkType"
}
```

## 优势总结

1. **数据完整性**：不会丢失平台的任何重要字段
2. **操作安全性**：基于现有信息进行修改，避免意外覆盖
3. **字段可追溯**：详细记录哪些字段被修改
4. **错误处理完善**：每个步骤都有相应的错误处理和回滚机制
5. **日志详细**：提供完整的操作追踪日志

这种增强的实现方式符合企业级应用的最佳实践，确保了数据的完整性和操作的可靠性。 