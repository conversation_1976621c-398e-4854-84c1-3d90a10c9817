# Service层异常处理优化

## 概述

本次修改优化了 `addSchedule` 方法中流分类处理、流行为处理、流策略处理失败时的错误处理机制，使用通用的 `ServiceException` 替代平台特定的异常，使其能够返回具体的错误信息，并且能够正确触发Spring事务回滚。

## 修改前的问题

1. **笼统的错误信息**：原有实现只返回"流分类处理失败"、"流行为处理失败"、"流策略处理失败"等笼统信息
2. **异常类型不合适**：使用 `PlatformApiException` 在Service层不够合适，应该使用更通用的Service层异常
3. **事务回滚问题**：需要确保异常能够正确触发Spring事务回滚

## 解决方案

### 1. 创建通用Service异常类

**文件**: `src/main/java/com/h3c/dzkf/common/exceptions/ServiceException.java`

创建了通用的Service层异常类：

```java
public class ServiceException extends RuntimeException {
    private final String errorCode;           // 错误代码
    private final String businessContext;     // 业务上下文信息
    
    // 多个构造函数支持不同的异常信息组合
    // 静态方法支持从平台API异常转换
}
```

**特点**：
- 继承自 `RuntimeException`，能够正确触发Spring事务回滚
- 包含错误代码和业务上下文信息，便于问题排查
- 提供静态方法从平台API异常转换
- 通用性强，适用于所有Service层业务逻辑

### 2. 更新全局异常处理器

在 `GlobalExceptionHandler` 中添加了对 `ServiceException` 的处理：

```java
@ExceptionHandler(ServiceException.class)
public ResponseEntity<ApiResponseDTO> handleServiceException(ServiceException e) {
    // 记录详细的错误信息，包括错误代码和业务上下文
    // 返回统一的API响应格式
}
```

### 3. 修改Service实现类

#### 3.1 修改方法签名

将所有相关方法的异常声明从 `PlatformApiException` 改为 `ServiceException`：

- `processClassifier(AddScheduleRequestDTO request) throws ServiceException`
- `processBehavior(AddScheduleRequestDTO request) throws ServiceException`
- `processPolicy(...) throws ServiceException`
- `createOrUpdatePolicy(...) throws ServiceException`

#### 3.2 具体的异常处理

**processClassifier 方法**：
```java
// 应用组下没有找到应用
throw new ServiceException("CLASSIFIER_ERROR", 
    "应用组下没有找到应用：" + appGroupName, businessContext);

// 流分类创建成功但查询ID失败
throw new ServiceException("CLASSIFIER_ERROR", 
    "流分类创建成功但查询流分类ID失败，流分类名称：" + classifierName, businessContext);
```

**processBehavior 方法**：
```java
// 未找到隧道组模板
throw new ServiceException("BEHAVIOR_ERROR", 
    "未找到隧道组模板，隧道组ID：" + networkId, businessContext);

// 未找到隧道组信息
throw new ServiceException("BEHAVIOR_ERROR", 
    "未找到隧道组信息，隧道组ID：" + networkId, businessContext);
```

**processPolicy 方法**：
```java
// 隧道组ID列表为空
throw new ServiceException("POLICY_ERROR", "隧道组ID列表为空", businessContext);

// 隧道组作用域信息为空
throw new ServiceException("POLICY_ERROR", 
    "隧道组作用域信息为空，隧道组ID：" + networkId, businessContext);

// 所有设备的流策略处理都失败
throw new ServiceException("POLICY_ERROR", errorMsg, businessContext);
```

#### 3.3 异常转换处理

对于平台API异常，统一转换为Service异常。**注意**：由于全局异常处理器已经捕获了 `ServiceException`，所以在Service方法中不需要再捕获 `ServiceException`，让异常直接向上传播即可：

```java
} catch (PlatformApiException e) {
    // 将平台API异常转换为Service异常
    throw ServiceException.fromPlatformException(e, "CLASSIFIER_ERROR", businessContext);
} catch (Exception e) {
    // 其他异常也转换为Service异常
    throw new ServiceException("CLASSIFIER_ERROR", "处理流分类异常：" + e.getMessage(), businessContext, e);
}
// 注意：不需要捕获ServiceException，让它直接传播到全局异常处理器
```

#### 3.4 主方法异常处理优化

在 `addSchedule` 方法中，**不再捕获** `ServiceException`，让异常直接传播到全局异常处理器：

```java
// 3. 流分类处理
Integer classifierId = processClassifier(request);

// 4. 流行为处理
Integer behaviorId = processBehavior(request);

// 5. 流策略处理
List<DevicePolicyInfoDTO> devicePolicyList = processPolicy(request, classifierId, behaviorId);
```

**优势**：
- 避免重复的异常处理逻辑
- 让全局异常处理器统一处理所有 `ServiceException`
- 简化代码结构，提高可维护性

## 修改效果

### 修改前
- 流分类处理失败：`"流分类处理失败"`
- 流行为处理失败：`"流行为处理失败"`
- 流策略处理失败：`"流策略处理失败"`

### 修改后
- 流分类处理失败：`"流分类处理失败：应用组下没有找到应用：测试应用组"`
- 流行为处理失败：`"流行为处理失败：未找到隧道组模板，隧道组ID：123"`
- 流策略处理失败：`"流策略处理失败：隧道组作用域信息为空，隧道组ID：123"`

## 优势

1. **具体的错误信息**：用户可以清楚地知道具体的失败原因
2. **正确的事务处理**：`ServiceException` 继承自 `RuntimeException`，能够正确触发Spring事务回滚
3. **统一的异常处理**：使用通用的Service异常，适用于所有业务逻辑
4. **详细的业务上下文**：包含业务上下文信息，便于问题排查和调试
5. **异常链保持**：保留了原始异常信息，便于深度调试
6. **代码可维护性**：统一的异常处理机制，便于维护和扩展
7. **简化的异常流程**：Service层不重复捕获ServiceException，让异常直接传播到全局处理器，避免不必要的重复处理

## 兼容性

- 该修改不会影响现有的API接口签名
- 返回的错误响应格式保持不变，只是错误信息更加详细
- 不会影响正常的成功流程
- 事务回滚机制得到正确保障

## 测试建议

建议测试以下场景以验证错误处理的改进：

1. **流分类处理失败场景**：
   - 应用组不存在或应用组下没有应用
   - 平台流分类接口调用失败
   - 流分类创建成功但查询ID失败

2. **流行为处理失败场景**：
   - 隧道组模板不存在
   - 隧道组信息不存在
   - 平台流行为接口调用失败

3. **流策略处理失败场景**：
   - 隧道组ID列表为空
   - 隧道组信息不存在
   - 隧道组作用域信息为空或解析失败
   - 作用域中没有设备
   - 设备流策略创建失败

4. **事务回滚测试**：
   - 验证异常发生时数据库事务是否正确回滚
   - 确保不会产生脏数据

通过这些测试可以验证错误信息是否足够详细和准确，以及事务处理是否正确。
