### 删除应用接口测试
###
### 删除逻辑说明：
### 1. 按IPv6优先、IPv4次序删除ACL模板
### 2. 任何ACL删除失败都会导致接口返回失败
### 3. 成功删除的ACL会从数据库中移除，失败的ACL保留
### 4. 只有全部ACL删除成功才会删除应用记录
### 5. 支持重试：删除失败的应用可重新调用删除接口
###

### 1. 删除存在的应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/1001
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 2. 删除不存在的应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/9999
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 3. 删除IPv4+IPv6应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/1002
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 4. 删除多目标IP应用
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/1006
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345

### 5. 测试无效token
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/1003
Content-Type: application/json;charset=UTF-8
X-Access-Token: invalid-token

### 6. 测试缺少token
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/1004
Content-Type: application/json;charset=UTF-8

### 7. 测试无效应用ID
DELETE http://localhost:28000/gszh-srv6-api/srv6/delApp/abc
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-12345 