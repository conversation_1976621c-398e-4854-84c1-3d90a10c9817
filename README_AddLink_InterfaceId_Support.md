# 新增链路接口ID支持功能说明

## 功能概述

在新增链路时，系统现在会从设备接口信息接口获取源设备和目标设备的接口ID，并将这些ID保存到定制库的`link_custom_info`表中，以便后续查询和管理。

## 技术实现

### 1. 数据库表字段扩展

在`LinkCustomInfo`实体类中新增两个字段：
- `sourceInterfaceId`：源设备接口ID（Long类型，从平台设备接口信息接口获取的dataId）
- `targetInterfaceId`：目标设备接口ID（Long类型，从平台设备接口信息接口获取的dataId）

### 2. 业务流程增强

新增链路的业务流程中，接口ID的获取和保存过程：

1. **获取平台节点ID**：根据源设备ID和目标设备ID获取对应的平台节点ID
2. **查询设备接口信息**：调用平台设备接口信息接口，获取每个设备的所有接口列表
3. **匹配端口名称**：通过用户提供的端口名称与平台接口的`ifName`字段匹配
4. **提取接口ID**：从匹配的接口记录中提取`dataId`作为接口ID
5. **类型转换**：将String类型的接口ID转换为Long类型，并进行异常处理
6. **保存到定制库**：将获取的接口ID保存到`link_custom_info`表中

### 3. 代码修改详情

#### LinkCustomInfo.java
```java
/**
 * 源设备接口ID（从平台设备接口信息接口获取的dataId）
 */
private Long sourceInterfaceId;

/**
 * 目标设备接口ID（从平台设备接口信息接口获取的dataId）
 */
private Long targetInterfaceId;
```

#### LinkServiceImpl.java
```java
// 修改buildLinkCustomInfo方法签名，添加Long类型接口ID参数
private LinkCustomInfo buildLinkCustomInfo(AddLinkRequestDTO request, String platformLinkId, 
        Long sourceInterfaceId, Long targetInterfaceId) {
    
    // 设置源设备接口ID
    linkInfo.setSourceInterfaceId(sourceInterfaceId);
    
    // 设置目标设备接口ID
    linkInfo.setTargetInterfaceId(targetInterfaceId);
    
    log.info("保存链路接口ID信息 - 源设备接口ID：{}，目标设备接口ID：{}", 
             sourceInterfaceId, targetInterfaceId);
}

// 在addLink方法中进行类型转换并传递接口ID
Long sourceInterfaceId = null;
Long targetInterfaceId = null;

try {
    sourceInterfaceId = Long.parseLong(sourcePlatformPortId);
    targetInterfaceId = Long.parseLong(targetPlatformPortId);
    log.info("接口ID类型转换成功 - 源设备接口ID：{}，目标设备接口ID：{}", 
             sourceInterfaceId, targetInterfaceId);
} catch (NumberFormatException e) {
    log.error("接口ID类型转换失败，源设备接口ID：{}，目标设备接口ID：{}", 
              sourcePlatformPortId, targetPlatformPortId, e);
    return AddLinkResponseDTO.fail(requestId, "接口ID格式错误");
}

LinkCustomInfo linkInfo = buildLinkCustomInfo(request, platformLinkId, 
                                             sourceInterfaceId, targetInterfaceId);
```

## 接口ID获取过程

### 获取流程
1. **调用平台接口**：`PlatformApiService.getInterfaces(request)`
2. **参数设置**：
   - `nodeId`：设备的平台节点ID
   - `pageNum`：页码（从1开始）
   - `pageSize`：页面大小（设置为1000以获取所有接口）

3. **端口匹配**：遍历返回的接口列表，通过`ifName`字段匹配用户提供的端口名称（忽略大小写）

4. **ID提取**：从匹配的接口记录中提取`dataId`字段作为接口ID（Long类型）

### 示例日志
```
端口匹配成功 - 源设备端口，用户端口：GigabitEthernet1/0/1，平台ifName：GigabitEthernet1/0/1，平台端口ID(dataId)：12345
接口ID类型转换成功 - 源设备接口ID：12345，目标设备接口ID：67890
保存链路接口ID信息 - 源设备接口ID：12345，目标设备接口ID：67890
```

## 数据存储

接口ID将被保存在`link_custom_info`表的以下字段中：
- `source_interface_id`：源设备接口ID（Long类型）
- `target_interface_id`：目标设备接口ID（Long类型）

## 错误处理

系统包含完善的错误处理机制：
1. **接口查询失败**：记录错误日志并返回失败响应
2. **端口匹配失败**：记录所有可用接口信息供调试
3. **类型转换失败**：处理NumberFormatException，返回"接口ID格式错误"
4. **数据保存失败**：触发事务回滚，确保数据一致性

## 类型转换安全性

### 转换过程
- 从平台接口获取的`dataId`是Long类型，经`toString()`转为String
- 在保存到定制库前，使用`Long.parseLong()`转换回Long类型
- 包含完整的异常处理，确保转换失败时有明确的错误信息

### 异常处理示例
```java
try {
    sourceInterfaceId = Long.parseLong(sourcePlatformPortId);
    targetInterfaceId = Long.parseLong(targetPlatformPortId);
} catch (NumberFormatException e) {
    log.error("接口ID类型转换失败，源设备接口ID：{}，目标设备接口ID：{}", 
              sourcePlatformPortId, targetPlatformPortId, e);
    return AddLinkResponseDTO.fail(requestId, "接口ID格式错误");
}
```

## 调试支持

当端口匹配失败时，系统会输出详细的调试信息：
```
=== 平台接口列表（用于调试） ===
dataId: 12345, ifName: GigabitEthernet1/0/1, abbreviatedName: Gi1/0/1, status: UP
dataId: 12346, ifName: GigabitEthernet1/0/2, abbreviatedName: Gi1/0/2, status: DOWN
=== 接口列表结束 ===
```

## 向后兼容性

- 新增字段对现有功能无影响
- 现有的链路记录可能没有接口ID字段，系统会正常处理
- 所有原有的查询和更新操作保持不变

## 注意事项

1. **端口名称格式**：用户提供的端口名称必须与平台接口的`ifName`字段完全匹配（忽略大小写）
2. **接口状态**：系统会记录所有状态的接口，包括UP和DOWN状态
3. **分页处理**：当前设置页面大小为1000，足以处理大多数设备的接口数量
4. **数据类型**：接口ID字段使用Long类型，确保与平台数据类型一致
5. **类型转换**：包含完整的类型转换异常处理，确保系统稳定性
6. **数据一致性**：接口ID的获取和保存在同一个事务中完成，确保数据一致性

通过这个功能，系统现在能够完整地记录链路的端点接口信息，为后续的网络拓扑分析和故障诊断提供重要的数据支持。 