### 测试修改后的查询站点下链路信息接口
### 新逻辑：基于站点下设备的平台节点ID查询链路信息

### 1. 测试查询站点下链路信息（修改后的逻辑）
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

### 2. 测试查询不存在的站点
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=999
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

### 3. 测试查询站点下无设备的情况
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=2
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

### 4. 测试参数验证 - 缺少站点ID
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId
Content-Type: application/json;charset=UTF-8
X-Access-Token: test-token-123456

### 5. 测试参数验证 - 缺少Token
GET http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1
Content-Type: application/json;charset=UTF-8

###
### 修改说明：
### 1. 原逻辑：基于数据库中的链路定制信息去平台查询
###    - 查询 link_custom_info 表获取链路列表
###    - 逐条调用平台接口获取链路详情
###    - 可能导致部分平台链路查询不到
###
### 2. 新逻辑：基于站点下设备的平台节点ID查询链路
###    - 查询站点及子站点下的所有设备
###    - 获取所有设备的平台节点ID
###    - 调用平台链路查询接口，同时设置srcNodeIdList和destNodeIdList
###    - 能够查询到平台上所有相关的链路信息
###
### 3. 主要改进：
###    - 避免遗漏平台上存在但数据库中没有记录的链路
###    - 提高查询的完整性和准确性
###    - 使用合理的分页逻辑（页面大小50）处理大量链路数据
###    - 添加分页查询延迟，避免对平台造成过大压力
###    - 根据平台linkId查询数据库定制信息（linkType、linkLevel等）
###    - 智能数据合并：平台数据 + 数据库定制信息
###    - 保持原有的数据封装格式和接口兼容性
###
