# 查询站点下链路信息接口

## 接口概述

本接口实现了工商总行定义的查询站点下链路信息（包括interlink信息）的功能，根据站点ID查询该站点及其下级站点关联的所有链路信息。

## 接口规范

### 基本信息
- **接口类型**: GET
- **接口地址**: `/srv6/queryLinkInfoListBySiteId`
- **接口描述**: 查询站点下所有链路信息

### 请求参数

#### Header参数
| 参数名称 | 参数描述 | 是否必填 | 示例值 |
|---------|---------|---------|--------|
| Content-Type | 内容类型 | Y | application/json;charset=UTF-8 |
| X-Access-Token | 访问令牌 | Y | 从token接口获取的有效token |

#### Query参数
| 参数名称 | 参数描述 | 是否必填 | 类型 | 示例值 |
|---------|---------|---------|------|--------|
| siteId | 站点ID | Y | Integer | 1 |

### 响应结果

#### 成功响应（HTTP 200）
```json
{
    "requestId": "请求ID，用于溯源",
    "result": 1,
    "total": 2,
    "linkInfoList": [
        {
            "linkId": "278699617746945",
            "siteId": 1,
            "linkStatus": "在线",
            "linkName": "链路名称",
            "carrierName": null,
            "sourceDeviceId": 212,
            "sourceDeviceName": "源设备名称",
            "sourceDeviceIp": "***********",
            "sourceDevicePort": "GigabitEthernet1/1/0",
            "targetDeviceId": 211,
            "targetDeviceName": "目标设备名称",
            "targetDeviceIp": "***********",
            "targetDevicePort": "GigabitEthernet1/1/0",
            "linkType": "专线",
            "linkLevel": "重要",
            "linkBandWidth": "1000000000",
            "upBandWidth": null,
            "downBandWidth": null,
            "receiveRate": "800000000",
            "sendRate": "900000000",
            "receiveBwUsedPercent": "80.00",
            "sendBwUsedPercent": "90.00",
            "receiveDelay": "10",
            "sendDelay": "12",
            "receiveJitter": "2",
            "sendJitter": "3",
            "receiveLoss": "0.01",
            "sendLoss": "0.02",
            "isEncrypt": null,
            "optionField": null
        }
    ]
}
```

#### 失败响应（HTTP 200）
```json
{
    "requestId": "请求ID",
    "result": 0,
    "failReason": "失败原因描述"
}
```

### 响应字段说明

| 字段名称 | 字段描述 | 数据来源 |
|---------|---------|---------|
| requestId | 请求id，用于溯源 | 系统生成 |
| result | 状态：0-失败，1-成功 | 系统生成 |
| total | 链路总数 | 计算得出 |
| linkInfoList | 链路集合 | 汇总数据 |
| linkId | 链路id | 定制库 |
| siteId | 站点id | 定制库 |
| linkStatus | 链路状态（中文） | 平台链路接口翻译 |
| linkName | 链路名称 | 平台链路接口 |
| carrierName | 运营商名称（预留） | 暂为空 |
| sourceDeviceId | 源设备id | 定制库 |
| sourceDeviceName | 源设备名称 | 平台设备接口 |
| sourceDeviceIp | 源设备IP | 平台设备接口 |
| sourceDevicePort | 源设备端口 | 定制库 |
| targetDeviceId | 目标设备id | 定制库 |
| targetDeviceName | 目标设备名称 | 平台设备接口 |
| targetDeviceIp | 目标设备IP | 平台设备接口 |
| targetDevicePort | 目标设备端口 | 定制库 |
| linkType | 链路类型 | 定制库 |
| linkLevel | 链路优先级 | 定制库 |
| linkBandWidth | 链路带宽 | 平台链路接口 |
| upBandWidth | 上行带宽（预留） | 暂为空 |
| downBandWidth | 下行带宽（预留） | 暂为空 |
| receiveRate | 接收速率 | 平台链路总览接口（反向链路） |
| sendRate | 发送速率 | 平台链路总览接口 |
| receiveBwUsedPercent | 接收带宽使用百分比 | 计算得出 |
| sendBwUsedPercent | 发送带宽使用百分比 | 计算得出 |
| receiveDelay | 接收延迟 | 平台链路总览接口 |
| sendDelay | 发送延迟 | 平台链路总览接口 |
| receiveJitter | 接收抖动 | 平台链路总览接口 |
| sendJitter | 发送抖动 | 平台链路总览接口 |
| receiveLoss | 接收丢包率 | 平台链路总览接口 |
| sendLoss | 发送丢包率 | 平台链路总览接口 |
| isEncrypt | 是否加密（预留） | 暂为空 |
| optionField | 厂商自定义字段 | 可扩展 |

## 实现逻辑

### 核心流程
1. **验证请求参数**: 检查siteId是否有效，token是否有效
2. **查询站点层级**: 递归查询当前站点及其所有下级站点
3. **查询链路信息**: 从定制库查询站点关联的链路定制信息
4. **构建响应数据**: 组装链路基本信息和设备信息

### 反向链路逻辑
根据当前链路的源设备ID、目标设备ID、源接口ID、目标接口ID，在定制库中查找目标设备ID作为源设备ID且源设备ID作为目标设备ID的链路，即为反向链路。

### 站点层级查询
通过递归查询实现站点及其下级站点的完整遍历：
- 首先将查询的站点ID加入结果列表
- 递归查询parent_site_id等于当前站点ID的所有子站点
- 对每个子站点继续递归查询其下级站点

## 使用示例

### cURL 示例
```bash
curl -X GET "http://localhost:8080/srv6/queryLinkInfoListBySiteId?siteId=1" \
  -H "Content-Type: application/json;charset=UTF-8" \
  -H "X-Access-Token: your-token-here"
```

### 错误处理

| 错误情况 | 响应内容 |
|---------|---------|
| Token无效 | `{"requestId":"xxx","result":0,"failReason":"Token无效或已过期"}` |
| 参数缺失 | `{"requestId":"xxx","result":0,"failReason":"站点ID不能为空"}` |
| 站点不存在 | 返回空的linkInfoList数组 |
| 系统异常 | `{"requestId":"xxx","result":0,"failReason":"系统异常：具体错误信息"}` |

## 注意事项

1. **性能考虑**: 当站点层级较深或链路数量较多时，查询时间可能较长
2. **数据一致性**: 响应中的设备信息来自平台接口，可能存在延迟
3. **扩展性**: 当前版本返回定制库中的基本信息，后续版本将集成更多平台接口数据
4. **测试模式**: 在测试模式下可跳过Token验证（配置`test.skip-token-validation=true`）

## 集成平台接口

### 链路总览接口 (已实现)

本系统已集成ADWAN平台的链路总览接口，用于获取实时的链路性能数据。

#### 接口信息
- **请求方式**: POST
- **接口地址**: `/oam/linkOverView/getLinkOverView`  
- **功能说明**: 查询链路质量信息（延时、抖动、丢包率）、带宽信息（带宽、带宽利用率）

#### 核心特性
- **时间类型**: 支持实时数据和9种历史时间间隔
  - REALTIME - 实时数据
  - LAST_ONE_HOUR - 最近一小时
  - LAST_THREE_HOUR - 最近三小时
  - LAST_SIX_HOUR - 最近六小时
  - LAST_TWELVE_HOUR - 最近十二小时
  - LAST_ONE_DAY - 最近一天
  - LAST_ONE_WEEK - 最近一周
  - LAST_ONE_MONTH - 最近一个月
  - SELF_DEFINE - 自定义时间范围

- **查询条件**: 支持多种搜索条件
  - 链路名称、源设备名称、目的设备名称
  - 源/目的设备接口名称和地址
  - 性能指标条件筛选（延时、抖动、丢包率、带宽利用率）

- **排序功能**: 支持按以下字段排序
  - linkName - 链路名称
  - linkStatus - 链路状态  
  - srcDeviceName - 源设备
  - destDeviceName - 目的设备
  - packageLossRate - 丢包率
  - jitter - 抖动
  - delay - 时延
  - bandwidth - 带宽
  - bandwidthPercentage - 带宽利用率

#### 响应数据字段
| 字段名称 | 说明 | 类型 |
|---------|------|------|
| linkId | 链路ID | String |
| linkName | 链路名称 | String |
| linkQualityAlarmLevel | 链路质量告警级别 | Integer |
| linkStatus | 链路状态 | Integer |
| srcNodeName | 源设备名称 | String |
| srcTpName | 源设备接口名称 | String |
| srcAddress | 源设备接口地址 | String |
| destNodeName | 目的设备名称 | String |
| destTpName | 目的设备接口名称 | String |
| destAddress | 目的设备接口地址 | String |
| packetLossRate | 丢包率 | Double |
| delay | 时延 | Double |
| jitter | 抖动 | Long |
| bandwidth | 带宽 | Long |
| bandwidthPercent | 带宽利用率 | Double |

#### 使用方式
在`LinkServiceImpl`中通过`platformApiService.getLinkOverView()`调用平台接口，当前支持测试模式mock调用和真实平台调用。

## 版本历史

- **v1.0**: 基础版本，返回定制库中的链路基本信息
- **v1.1**: 集成平台链路总览接口，支持实时性能数据查询
- **v1.2**: （规划中）支持反向链路查询和带宽使用率计算 